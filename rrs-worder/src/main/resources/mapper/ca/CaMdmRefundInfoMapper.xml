<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.ca.dao.CaMdmRefundInfoMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.ca.domain.CaMdmRefundInfo">
    <!--@mbg.generated-->
    <!--@Table ca_mdm_refund_info-->
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="detail_no" jdbcType="VARCHAR" property="detailNo"/>
    <result column="after_sale_id" jdbcType="INTEGER" property="aftersaleId"/>
    <result column="add_time" jdbcType="VARCHAR" property="addTime"/>
    <result column="commodity_code" jdbcType="VARCHAR" property="commodityCode"/>
    <result column="commodity_name" jdbcType="VARCHAR" property="commodityName"/>
    <result column="commodity_sku_code" jdbcType="VARCHAR" property="commoditySkuCode"/>
    <result column="commodity_sku_name" jdbcType="VARCHAR" property="commoditySkuName"/>
    <result column="commodity_sku_price" jdbcType="DECIMAL" property="commoditySkuPrice"/>
    <result column="commodity_sku" jdbcType="INTEGER" property="commoditySku"/>
    <result column="actual_price" jdbcType="DECIMAL" property="actualPrice"/>
    <result column="pay_mode" jdbcType="INTEGER" property="payMode"/>
    <result column="pay_time" jdbcType="VARCHAR" property="payTime"/>
    <result column="username" jdbcType="VARCHAR" property="username"/>
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone"/>
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin"/>
    <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName"/>
    <result column="consignee_phone" jdbcType="VARCHAR" property="consigneePhone"/>
    <result column="consignee_address" jdbcType="VARCHAR" property="consigneeAddress"/>
    <result column="consignee_remark" jdbcType="VARCHAR" property="consigneeRemark"/>
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus"/>
    <result column="apply_reason" jdbcType="INTEGER" property="applyReason"/>
    <result column="apply_datetime" jdbcType="VARCHAR" property="applyDatetime"/>
    <result column="apply_price" jdbcType="DECIMAL" property="applyPrice"/>
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName"/>
    <result column="apply_userphone" jdbcType="VARCHAR" property="applyUserphone"/>
    <result column="apply_remark" jdbcType="VARCHAR" property="applyRemark"/>
    <result column="apply_pics" jdbcType="VARCHAR" property="applyPics"/>
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="check_user_name" jdbcType="VARCHAR" property="checkUserName"/>
    <result column="check_result" jdbcType="TINYINT" property="checkResult"/>
    <result column="check_remark" jdbcType="VARCHAR" property="checkRemark"/>
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
    <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, detail_no, after_sale_id, add_time, commodity_code, commodity_name, commodity_sku_code,
    commodity_sku_name, commodity_sku_price, commodity_sku, actual_price, pay_mode, pay_time,
    username, user_phone, vehicle_vin, consignee_name, consignee_phone, consignee_address,
    consignee_remark, order_status, apply_reason, apply_datetime, apply_price, apply_user_name,
    apply_userphone, apply_remark, apply_pics, refund_status, remark, check_user_name,
    check_result, check_remark, check_time, is_delete, create_by, created_at, update_by,
    updated_at
  </sql>
</mapper>