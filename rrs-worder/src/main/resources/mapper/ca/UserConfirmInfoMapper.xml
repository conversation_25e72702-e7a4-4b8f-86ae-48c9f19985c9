<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.ca.dao.UserConfirmInfoMapper">
    
    <resultMap type="com.bonc.rrs.ca.domain.UserConfirmInfo" id="UserConfirmInfoResult">
        <result property="confirmId"    column="confirm_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="affirmUserName"    column="affirm_user_name"    />
        <result property="affirmUserPhone"    column="affirm_user_phone"    />
        <result property="affirmUserTime"    column="affirm_user_time"    />
        <result property="affirmStatus"    column="affirm_status"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectUserConfirmInfoVo">
        select confirm_id, order_no, affirm_user_name, affirm_user_phone, affirm_user_time, affirm_status, remark from user_confirm_info
    </sql>

    <select id="selectUserConfirmInfoList" parameterType="com.bonc.rrs.ca.domain.UserConfirmInfo" resultMap="UserConfirmInfoResult">
        <include refid="selectUserConfirmInfoVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="affirmUserName != null  and affirmUserName != ''"> and affirm_user_name like concat('%', #{affirmUserName}, '%')</if>
            <if test="affirmUserPhone != null  and affirmUserPhone != ''"> and affirm_user_phone = #{affirmUserPhone}</if>
            <if test="affirmUserTime != null "> and affirm_user_time = #{affirmUserTime}</if>
            <if test="affirmStatus != null "> and affirm_status = #{affirmStatus}</if>
        </where>
    </select>
    
    <select id="selectUserConfirmInfoByConfirmId" parameterType="Long" resultMap="UserConfirmInfoResult">
        <include refid="selectUserConfirmInfoVo"/>
        where confirm_id = #{confirmId}
    </select>
        
    <insert id="insertUserConfirmInfo" parameterType="com.bonc.rrs.ca.domain.UserConfirmInfo" useGeneratedKeys="true" keyProperty="confirmId">
        insert into user_confirm_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">order_no,</if>
            <if test="affirmUserName != null">affirm_user_name,</if>
            <if test="affirmUserPhone != null">affirm_user_phone,</if>
            <if test="affirmUserTime != null">affirm_user_time,</if>
            <if test="affirmStatus != null">affirm_status,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">#{orderNo},</if>
            <if test="affirmUserName != null">#{affirmUserName},</if>
            <if test="affirmUserPhone != null">#{affirmUserPhone},</if>
            <if test="affirmUserTime != null">#{affirmUserTime},</if>
            <if test="affirmStatus != null">#{affirmStatus},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateUserConfirmInfo" parameterType="com.bonc.rrs.ca.domain.UserConfirmInfo">
        update user_confirm_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="affirmUserName != null">affirm_user_name = #{affirmUserName},</if>
            <if test="affirmUserPhone != null">affirm_user_phone = #{affirmUserPhone},</if>
            <if test="affirmUserTime != null">affirm_user_time = #{affirmUserTime},</if>
            <if test="affirmStatus != null">affirm_status = #{affirmStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where confirm_id = #{confirmId}
    </update>

    <delete id="deleteUserConfirmInfoByConfirmId" parameterType="Long">
        delete from user_confirm_info where confirm_id = #{confirmId}
    </delete>

    <delete id="deleteUserConfirmInfoByConfirmIds" parameterType="String">
        delete from user_confirm_info where confirm_id in
        <foreach item="confirmId" collection="array" open="(" separator="," close=")">
            #{confirmId}
        </foreach>
    </delete>
</mapper>