<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.supervise.dao.SuperviseTeleRecordMapper">
    <select id="selectTelePage" resultType="com.bonc.rrs.supervise.dto.SuperviseTeleRecordInfo">
        select
            str.*,
            ifnull(su.employee_name,su.username) as create_user_name,
            ifnull(su2.employee_name,su2.username) as duty_peo_name
        from
            supervise_tele_record str
            left join sys_user su2 on str.duty_peo = su2.user_id ,
            sys_user su
        where
            str.create_user_id = su.user_id
          and str.supervise_id = #{superviseId}
          and str.is_delete = 0
        order by str.id desc
    </select>
    <select id="selectTeleNotRecover" resultType="com.bonc.rrs.supervise.entity.SuperviseTeleRecord">
        select
            str.*
        from
            supervise_tele_record str
                left join supervise_recover_record srr on
                str.id = srr.tele_id and str.is_delete = 0 and srr.is_delete = 0
        where srr.id is null and str.supervise_id = #{superviseId};
    </select>
    <select id="selectOverdueTripList" resultType="com.bonc.rrs.supervise.entity.SuperviseTeleRecord">
        select
            str.*,
            if(NOW() > str.process_time,1,0) as overdue ,
            sd.duration
        from
            supervise_infomation si ,
            supervise_tele_record str
                left join supervise_recover_record srr on
                        str.id = srr.tele_id
                    and srr.is_delete = 0,
            supervise_dictionary sd
        where str.supervise_classification = sd.supervise_classification
          and str.supervise_type = sd.supervise_type
          and si.id = str.supervise_id
          and si.is_delete = 0
          and str.is_delete = 0
          and (str.task_state = 0 or (str.task_state = 1 and TIMESTAMPDIFF(minute, str.task_time  , NOW()) >= 3))
          and si.state != 3
          and srr.id is null
          and ((TIMESTAMPDIFF(minute, str.trip_time , NOW()) > sd.duration) or (NOW() > str.process_time and si.supervise_lv &lt;= 5 and str.auto_up = 0 and str.supervise_type = 3))
        order by str.id
    </select>
</mapper>