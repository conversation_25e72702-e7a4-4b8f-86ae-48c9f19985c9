<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.supervise.dao.SuperviseProductRecoverRecordMapper">

    <select id="selectRecoverList" resultType="com.bonc.rrs.supervise.entity.SuperviseProductRecoverRecord">
        select
            srr.*,
            sf.`path`
        from
            supervise_recover_record srr
                left join sys_file sf on
                srr.file_id = sf.file_id
        where srr.is_delete = 0
          <foreach collection="teleIds" item="teleId" open=" and srr.tele_id in (" close=")" separator=",">
              #{teleId}
          </foreach>
        order by srr.id desc
    </select>
</mapper>