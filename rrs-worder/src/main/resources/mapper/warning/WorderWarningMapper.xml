<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.warning.dao.WorderWarningDao">

    <resultMap id="receiveUserMap" type="com.bonc.rrs.warning.entity.po.ReceiveUserPo">
        <result property="createId" column="create_by"></result>
        <result property="pmId" column="pm_id"></result>
        <result property="dotId" column="dot_id"></result>
        <result property="serviceId" column="service_id"></result>
        <result property="dotAdministrator" column="dotAdminUserName"></result>
        <result property="dotName" column="dot_name"></result>
    </resultMap>

    <select id="getReceiveUser" resultMap="receiveUserMap">
        select a.create_by
             , u1.username createName
             , a.pm_id
             , u2.username pmName
             , a.dot_id
             , a.service_id
             , s.name      serviceName
             , u3.user_id  serviceUserId
             , u3.username serviceUserName
             , c.dot_name
             , a.worder_no worderNo
             , u.user_id   dotAdminUserId
             , u.username  dotAdminUserName
             , u.mobile    dotAdminMobile
             , u1.mobile   createUserMobile
             , u2.mobile   pmUserMobile
             , u3.mobile   serviceUserMobile
        from worder_information a
                 left join dot_information c on c.dot_id = a.dot_id
                 left join dot_contacts b on c.dot_code = b.dot_code
                 left join sys_user u on u.user_id = b.contacts_id
                 left join sys_user u1 on u1.user_id = a.create_by
                 left join sys_user u2 on u2.user_id = a.pm_id
                 left join biz_attendant s on s.id = a.service_id
                 left join sys_user u3 on u3.user_id = s.user_id
        where a.worder_no = #{worderNo}
    </select>

    <!--    <select id="getReceiveUser" resultMap="receiveUserMap">-->
    <!--        select a.create_by, a.pm_id, a.dot_id, a.service_id, b.contacts_name-->
    <!--        from worder_information a-->
    <!--		left join dot_contacts b on a.dot_id = b.dot_code-->
    <!--        where worder_no = #{worderNo}-->
    <!--    </select>-->

    <resultMap id="userInformationMap" type="com.bonc.rrs.warning.entity.po.UserNamePo">
        <result property="userId" column="user_id"></result>
        <result property="userName" column="username"></result>
        <result property="realName" column="name"></result>
    </resultMap>

    <select id="getUserNameById" resultMap="userInformationMap">
        select a.user_id, a.username, b.name
        from sys_user a
                 left join biz_employee b on a.user_id = b.user_id
        where a.user_id = #{userId}
    </select>

    <resultMap id="worderTriggerPoMap" type="com.bonc.rrs.warning.entity.po.WorderTriggerPo">
        <result property="triggerId" column="trigger_id"></result>
        <result property="triggerName" column="trigger_name"></result>
        <result property="eventType" column="event_type"></result>
        <result property="roleType" column="role_type"></result>
        <result property="sendType" column="send_type"></result>
        <result property="sendDesc" column="send_desc"></result>
        <result property="sendTitle" column="send_title"></result>
        <result property="sendTypeValue" column="send_type_value"></result>
        <result property="eventTypeValue" column="event_type_value"></result>
        <!--增加状态字段-->
        <result property="status" column="status"/>
        <!--多少秒后发消息-->
        <result property="sendAfterSeconds" column="send_after_seconds"></result>
        <result property="messageType" column="message_type"></result>
        <result property="sendTimeType" column="send_time_type"></result>
        <result property="sendTimeAccracy" column="send_time_accuracy"></result>
        <result property="delayType" column="delay_type"></result>
        <result property="triggerValidate" column="trigger_validate"></result>
        <result property="eventTypeName" column="detail_name"></result>
    </resultMap>

    <select id="listWorderTriggerById" resultMap="worderTriggerPoMap">
        select a.trigger_id
             , a.trigger_name
             , a.event_type
             , a.role_type
             , a.send_type
             , a.send_desc
             , a.send_title
             , a.send_type_value
             , a.event_type_value
             , a.delay_type
             , a.send_after_seconds
             , a.message_type
             , b.remark
        from worder_trigger a
                 left join sys_dictionary_detail b on a.send_time_type = b.detail_number
        where a.event_type = #{eventType}
          and a.is_delete = '0';
    </select>


    <select id="listTriggerByDetailName" resultMap="worderTriggerPoMap">
        select c.trigger_id
             , c.trigger_name
             , c.event_type
             , c.role_type
             , c.send_type
             , c.send_desc
             , c.send_title
             , c.send_type_value
             , c.event_type_value
             , c.delay_type
             , c.send_after_seconds
             , c.message_type
             , c.send_time_type
             , c.trigger_validate
             , c.send_time_accuracy
        from sys_dictionary a
                 left join sys_dictionary_detail b on a.id = b.dictionary_id
                 left join worder_trigger c on b.detail_number = c.event_type
        where a.dic_number = 'trigger_event'
          and b.remark = #{remark}
          and c.trigger_id is not null
          and c.is_delete = '0'
    </select>

    <select id="executeSql" parameterType="java.lang.String"
            resultType="com.bonc.rrs.warning.entity.po.TriggerSqlResultPo">
        ${sql}
    </select>

    <select id="listWorderTriggerByParams" parameterType="com.bonc.rrs.warning.entity.WorderWarningEntity"
            resultMap="worderTriggerPoMap">
        select a.trigger_id, a.trigger_name, a.event_type, a.role_type
        ,a.send_type, a.send_desc, a.send_title, a.send_type_value, a.event_type_value
        , b.detail_name, a.message_type, a.trigger_validate, a.send_time_accuracy, a.send_time_type
        ,a.send_after_seconds, a.delay_type
        from worder_trigger a
        left join sys_dictionary_detail b on a.event_type = b.detail_number
        left join sys_dictionary c on b.dictionary_id = c.id
        where a.is_delete = '0'
        and c.dic_number = 'trigger_event'
        <if test="triggerId != null and triggerId != ''">
            and a.trigger_id = #{triggerId}
        </if>
    </select>

    <select id="listWorderTrigger" resultMap="worderTriggerPoMap">
        select a.trigger_id
             , a.trigger_name
             , a.event_type
             , a.role_type
             , a.send_type
             , a.send_desc
             , a.send_title
             , a.send_type_value
             , a.event_type_value
             , b.detail_name
             , a.message_type
             , a.delay_type
        from worder_trigger a
                 left join sys_dictionary_detail b on a.event_type = b.detail_number
                 left join sys_dictionary c on b.dictionary_id = c.id
        where a.is_delete = '0'
          and c.dic_number = 'trigger_event'
        order by create_time desc
    </select>

    <insert id="test1">
        INSERT INTO sys_oss(id, url, create_date)
        VALUES (1, '12', NULL);
    </insert>

    <insert id="test2">
        INSERT INTO sys_oss(id, url, create_date)
        VALUES (1, '12', NULL);
    </insert>

    <select id="queryReceiveUserListByWorderNo" resultType="com.bonc.rrs.message.entity.ReceiveUser">
        select
            distinct
            CONVERT(su.user_id, CHAR) as receiveUserId,
            su.mobile as receiveUserMobile,
            CONVERT(sur.role_id, CHAR) as receiveRoleId
        from
            (
                select
                    wi.worder_no,
                    GROUP_CONCAT(distinct(concat('-', wt.brand_id , '-')) separator '|') brand_ids,
                    GROUP_CONCAT(distinct(
                        case wt.service_type_enum
                            when 0 then '1|2|3'
                            else wt.service_type_enum
                        end
                    ) separator '|') service_types,
                    GROUP_CONCAT(distinct(concat(wi_br.regcode, '.*')) separator '|') regcodes
                from
                    worder_information wi
                inner join worder_template wt on
                    wi.template_id = wt.id
                inner join biz_region wi_br on
                    wi.area_id = wi_br.id
                where
                    wi.is_delete = 0
                    and wi.worder_no = #{worderNo}
            ) dga
        inner join
            (
                select
                    mai.user_id,
                    mai.group_id,
                    GROUP_CONCAT(distinct(mai.brand_id) separator '|') brand_ided,
                    GROUP_CONCAT(distinct(concat('-', mai.brand_id , '-')) separator '|') brand_ids,
                    GROUP_CONCAT(distinct(
                        case mai.service_type
                            when 0 then '1|2|3'
                            else mai.service_type
                        end
                    ) separator '|') service_types,
                    GROUP_CONCAT(distinct(concat(mai_br.regcode, '.*')) separator '|') regcodes
                from
                    manager_area_id mai
                inner join biz_region mai_br on
                    mai.area_id = mai_br.id
                group by
                    mai.user_id, mai.group_id, mai.child_group_id
            ) mga
        on
            dga.regcodes regexp mga.regcodes and dga.brand_ids regexp mga.brand_ids and dga.service_types regexp mga.service_types
        inner join sys_user su on mga.user_id = su.user_id
        inner join sys_user_role sur on su.user_id = sur.user_id and sur.role_id in (55, 53, 46, 50)
    </select>
</mapper>