<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.baidumap.dao.BaiduGridBasisDao">

    <select id="selectAreaIdByBrand" resultType="java.lang.Integer">
        select distinct mai.area_id
        from manager_area_id mai,
             (
                 select *
                 from sys_user_role
                 where role_id = 3) sur
        where mai.user_id = sur.user_id
          and mai.area_id is not null
          and mai.brand_id = #{brandId}
    </select>

    <select id="selectSerivceByArea" resultType="java.util.Map">
        select
            distinct
            be.name as employee_name,
            be.user_id as user_id
        from
            manager_area_id mai
                inner join (
                select
                    su.user_id
                from
                    sys_user su
                    inner join sys_user_role sur on
                        su.user_id = sur.user_id
                        and sur.role_id = 3) u on u.user_id = mai.user_id
                inner join biz_employee be on
                    be.user_id = u.user_id
                inner join brand b on
                b.id = mai.brand_id
        where
            mai.area_id in
            <foreach collection="areaIds" item="areaId" open="(" close=")" separator=",">
                #{areaId}
            </foreach>
            <if test="param.brandId != null">
                and mai.brand_id = #{param.brandId}
            </if>
    </select>

    <select id="selectAttendantByDot" resultType="java.util.Map">
        select
            di.dot_id as dotId,
            count(ba.id) as count
        from
            dot_information di
                left join biz_attendant ba on
                di.dot_id = ba.dot_id
        where
            ba.attendant_state = '1' and di.dot_id in
        <foreach collection="dotIds" item="dotId" open="(" close=")" separator=",">
            #{dotId}
        </foreach>
        group by
            di.dot_id
    </select>

    <select id="selectGridByserviceManager" resultType="com.bonc.rrs.baidumap.entity.BaiduGridBasisEntity">
        select bgb.*
        from baidu_grid_basis bgb
        where bgb.state = 0
          <if test="query.areaLevel != null">
              and bgb.area_level = #{query.areaLevel}
          </if>
          <if test="query.gridValue != null and query.gridValue !=''">
              and bgb.grid_name like concat('%',#{query.gridValue},'%')
          </if>
          <if test="areaIds != null and areaIds != ''">
              and ${areaIds}
          </if>
    </select>
    <select id="selectAreaIdByBrandAndManager" resultType="java.lang.Integer">
        select mai.area_id
        from manager_area_id mai
                 inner join sys_user su on
                    su.user_id = mai.user_id
                and su.status = 1
                 inner join sys_user_role sur on
                    su.user_id = sur.user_id
                and sur.role_id = 3
                 inner join biz_employee be on
            be.user_id = su.user_id
        where mai.area_id is not null
        <if test="brandId != null">
            and mai.brand_id = #{brandId}
        </if>
        <if test="serviceManager != null and serviceManager != ''">
            and be.name like concat('%',#{serviceManager},'%')
        </if>
        group by mai.area_id
    </select>
    <select id="selectManagerAndAreaIdAll" resultType="java.util.Map">
        select
            mai.area_id as areaId,
            be.name as name
        from
            manager_area_id mai
            inner join sys_user su on
                    su.user_id = mai.user_id
                and su.status = 1
            inner join sys_user_role sur on
                    su.user_id = sur.user_id
                and sur.role_id = 3
            inner join biz_employee be on
            be.user_id = su.user_id
        where
            mai.area_id is not null
            and be.name is not null
            <if test="param.brandId != null">
                and mai.brand_id = #{param.brandId}
            </if>
        <if test="param.serviceManager != null and param.serviceManager != ''">
            and be.name like concat('%',#{param.serviceManager},'%')
        </if>
        group by
            mai.area_id,
            be.name
    </select>
    <select id="selectAreaIdByDotOrBrand" resultType="java.lang.Integer">
        select
            da.area_id
        from
            dot_area da
            <if test="brandId != null">
                inner join dot_brand db on
                da.dot_id = db.dot_id and da.group_id = db.group_id and db.brand_id = #{brandId}
            </if>
        where 1=1
        <if test="dotId != null">
           and da.dot_id = #{dotId}
        </if>
        group by da.area_id
    </select>
    <select id="selectAreaDotAttendant" resultType="com.bonc.rrs.baidumap.vo.AreaDotAttendant">
        select da.area_id,
               da.dot_id,
               count(ba.id) as count
        from
            dot_area da
            inner join dot_brand db on da.dot_id = db.dot_id and da.group_id = db.group_id
            inner join dot_information di on da.dot_id = di.dot_id
                and di.is_delete = 0
                and di.dot_state = '1'
            left join biz_attendant ba on di.dot_id = ba.dot_id
                and ba.attendant_state = 1
                and ba.attendant_flag = 1
        where da.is_delete = '0'
            <if test="param.brandId != null">
                and db.brand_id = #{param.brandId}
            </if>
            <if test="param.dotId != null">
                and di.dot_id = #{param.dotId}
            </if>
        group by
            da.area_id,
            da.dot_id
    </select>
    <select id="selectGridPage" resultType="com.bonc.rrs.baidumap.entity.BaiduGridBasisEntity">
        select
            *
        from
            baidu_grid_basis bgb
        where 1=1
        <if test="query.state != null">
            and bgb.state = #{query.state}
        </if>
        <if test="query.gridName != null and query.gridName != ''">
            and bgb.grid_name like concat('%', #{query.gridName}, '%')
        </if>
        <if test="regionIds != null">
            and bgb.region_id in
            <foreach collection="regionIds" item="regionId" open="(" close=")" separator=",">
                #{regionId}
            </foreach>
        </if>
        <if test="userAreaIds != null and userAreaIds != ''">
            and ${userAreaIds}
        </if>
        <if test="areaIds != null and areaIds != ''">
            and ${areaIds}
        </if>
        order by bgb.region_id asc
    </select>

    <select id="selectBrandByArea" resultType="java.lang.String">
        select
            distinct
            b.brand_name
        from
            manager_area_id mai ,
            biz_region br,
            brand b
        where mai.brand_id = b.id and mai.area_id = br.id
        and mai.user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        <if test="regcode != null and regcode != ''">
            and br.regcode in (${regcode})
        </if>
    </select>
    <select id="selectAreaIdByUserId" resultType="java.lang.Integer">
        select area_id from manager_area_id where user_id = #{userId} group by area_id
    </select>
</mapper>
