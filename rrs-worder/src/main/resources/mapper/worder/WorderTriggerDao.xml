<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderTriggerDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderTriggerEntity" id="worderTriggerMap">
        <result property="triggerId" column="trigger_id"/>
        <result property="triggerName" column="trigger_name"/>
        <result property="eventType" column="event_type"/>
        <result property="roleType" column="role_type"/>
        <result property="sendType" column="send_type"/>
        <result property="sendTypeValue" column="send_type_value"/>
        <result property="eventTypeValue" column="event_type_value"/>
        <result property="sendTitle" column="send_title"/>
        <result property="sendDesc" column="send_desc"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>