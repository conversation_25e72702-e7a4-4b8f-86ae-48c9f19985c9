<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderIntfMessageDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderIntfMessageEntity" id="worderIntfMessageMap">
        <result property="id" column="id"/>
        <result property="worderId" column="worder_id"/>
        <result property="intfCode" column="intf_code"/>
        <result property="data" column="data"/>
        <result property="isTransfer" column="is_transfer"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <update id="updateTransfer">
        update worder_intf_message set is_transfer = #{isTransfer} where worder_id = #{worderId}
    </update>

    <select id="getDataByWorderId" resultType="string">
        select
            wim.`data`
        from
            worder_intf_message wim
        where
            wim.is_transfer = 0
            and wim.worder_id = 1
        order by
            wim.create_time desc
        limit 1
    </select>

    <select id="queryNoTransferDataByDotId" resultType="com.bonc.rrs.worder.entity.WorderIntfMessageEntity">
        select
            wim.*
        from
            worder_intf_message wim
        inner join worder_information wi on
            wim.worder_id = wi.worder_id
        inner join worder_information_attribute wia on
            wi.worder_id = wia.worder_id
            and wia.is_delete = 0
            and wia.`attribute` = 'pushOrder'
            and wia.attribute_code = 'worder_source'
            and wia.attribute_value = 'byd'
        where
            wim.is_transfer = 0
            and wim.message_type = 1
            and wi.worder_type_id = 5
            and wi.dot_id = #{dotId}
        order by
            create_time desc
    </select>

    <select id="queryNotSaveOrder" resultType="com.bonc.rrs.worder.entity.WorderIntfMessageEntity">
        select
            wim.*
        from
            worder_intf_message wim
        where
            wim.bid = #{bid,jdbcType=INTEGER}
            and (wim.message_type = 0  or (wim.message_type = 2 and wim.create_time >= DATE_SUB(CURDATE(), INTERVAL 5 DAY)))
        order by
            create_time
        limit 50
    </select>

    <update id="updateMessageTypeById">
        update worder_intf_message
        set
            message_type = #{messageType}
            <if test="errorMsg!=null and errorMsg!=''">,error_msg = #{errorMsg}</if>
        where id = #{id}
    </update>

    <update id="updateWorderIdById">
        update worder_intf_message
        set
            message_type = 1, worder_id = #{worderId}
        where id = #{id}
    </update>

    <select id="getOneByOrderCode" resultMap="worderIntfMessageMap">
        select id, bid, worder_id, intf_code, data, is_transfer, create_time, update_time, message_type, error_msg, order_code
        from worder_intf_message
        where bid = #{bid} and order_code = #{orderCode}
        order by id desc
            limit 1;
    </select>
</mapper>