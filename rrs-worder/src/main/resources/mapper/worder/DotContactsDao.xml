<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.DotContactsDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.DotContactsEntity" id="dotContactsMap">
        <result property="contactsId" column="contacts_id"/>
        <result property="dotNo" column="dot_no"/>
        <result property="contactsName" column="contacts_name"/>
        <result property="contactsPhone" column="contacts_phone"/>
        <result property="contactsEmail" column="contacts_email"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>