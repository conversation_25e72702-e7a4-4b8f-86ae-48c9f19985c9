<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderRemarkLogDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderRemarkLogEntity" id="worderRemarkLogMap">
        <result property="id" column="id"/>
        <result property="worderNo" column="worder_no"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="queryConnectTime" resultMap="worderRemarkLogMap">
        select
            *
        from
            worder_remark_log wrl
        where
            (wrl.title like '%预约勘测'
            or wrl.title like '%预约安装'
            or wrl.title like '%首次电联用户'
            or wrl.content like '%下次联系时间%')
            and wrl.worder_no = #{worderNo}
        order by
            wrl.create_time
    </select>

</mapper>