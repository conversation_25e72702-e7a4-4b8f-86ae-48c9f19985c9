<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.TriggerLogsDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.TriggerLogsEntity" id="triggerLogsMap">
        <result property="id" column="id"/>
        <result property="triggerId" column="trigger_id"/>
        <result property="userId" column="user_id"/>
        <result property="sendTitle" column="send_title"/>
        <result property="sendDesc" column="send_desc"/>
        <result property="sendType" column="send_type"/>
        <result property="sendTypeValue" column="send_type_value"/>
        <result property="sendTime" column="send_time"/>
        <result property="sendStatus" column="send_status"/>
        <result property="sendStatusValue" column="send_status_value"/>
    </resultMap>


</mapper>