<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.ExtFieldRelationDao">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.worder.entity.ExtFieldRelation">
    <!--@mbg.generated-->
    <!--@Table ext_field_relation-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bid" jdbcType="SMALLINT" property="bid" />
    <result column="field_type" jdbcType="TINYINT" property="fieldType" />
    <result column="field_id" jdbcType="INTEGER" property="fieldId" />
    <result column="out_field_name" jdbcType="VARCHAR" property="outFieldName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, bid, field_type, field_id, out_field_name
  </sql>
</mapper>