<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.ExtCostDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.ExtCostEntity" id="extCostMap">
        <result property="costId" column="cost_id"/>
        <result property="contName" column="cont_name"/>
        <result property="costType" column="cost_type"/>
        <result property="costUnit" column="cost_unit"/>
        <result property="costDesc" column="cost_desc"/>
        <result property="costTypeValue" column="cost_type_value"/>
        <result property="costUnitValue" column="cost_unit_value"/>
    </resultMap>


</mapper>