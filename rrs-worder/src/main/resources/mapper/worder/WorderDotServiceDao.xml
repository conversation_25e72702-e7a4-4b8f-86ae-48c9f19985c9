<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderDotServiceDao">
    <resultMap type="com.bonc.rrs.worder.entity.WorderInfoEntity" id="informationMap">
    <id column="worder_id" property="worderId"/>
    <result column="worder_type_id" property="worderTypeId"/>
    <result column="full_name" property="worderTypeName"/>
    <result column="worder_no" property="worderNo"/>
    <result column="worder_source_type" property="worderSourceType"/>
    <result column="worder_source_id" property="worderSourceId"/>
    <result column="worder_status" property="worderStatus"/>
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime"/>
    <result column="is_delete" property="isDelete"/>
    <result column="worder_source_type_value" property="worderSourceTypeValue"/>
    <result column="worder_status_value" property="worderStatusValue"/>
    <result column="charge_standard" property="chargeStandard"/>
    <result column="sell_shop" property="sellShop"/>
    <result column="charge_model" property="chargeModel"/>
    <result column="car_brand" property="carBrand"/>
    <result column="charge_code" property="chargeCode"/>
    <result column="buy_date" property="buyDate"/>
    <result column="car_model" property="carModel"/>
    <result column="car_vin" property="carVin"/>
    <result column="factory_link_man" property="factoryLinkMan"/>
    <result column="charge_cd" property="chargeCd"/>
    <result column="link_man_phone" property="linkManPhone"/>
    <result column="user_name" property="userName"/>
    <result column="user_phone" property="userPhone"/>
    <result column="postcode" property="postcode"/>
    <result column="park_type_value" property="parkTypeValue"/>
    <result column="park_type" property="parkType"/>
    <result column="user_certificate" property="userCertificate"/>
    <result column="electric_type" property="electricType"/>
    <result column="parking_type_value" property="parkingTypeValue"/>
    <result column="parking_type" property="parkingType"/>
    <result column="install_num" property="installNum"/>
    <result column="address" property="address"/>
    <result column="get_date" property="getDate"/>
    <result column="address_dup" property="addressDup"/>
    <result column="electric_type_value" property="electricTypeValue"/>
    <result column="files" property="files"/>
    <result column="candidate" property="candidate"/>
    <result column="creator" property="creator"/>
    <result column="create_by" property="createBy"/>
    <result column="update_by" property="updateBy"/>
    <result column="candidate_pm" property="candidatePm"/>
    <result column="candidate_branch" property="candidateBranch"/>
    <result column="candidate_attendant" property="candidateAttendant"/>
    <result column="worder_exec_status" property="worderExecStatus"/>
    <result column="worder_set_status" property="worderSetStatus"/>
    <result column="worder_Incre_status" property="worderIncreStatus"/>
    <result column="worder_exci_status" property="worderExciStatus"/>
    <result column="worder_exec_status_value" property="worderExecStatusValue"/>
    <result column="worder_set_status_value" property="worderSetStatusValue"/>
    <result column="worder_Incre_status_value" property="worderIncreStatusValue"/>
    <result column="worder_exci_status_value" property="worderExciStatusValue"/>
    <result column="pm_id" property="pmId"/>
    <result column="dot_id" property="dotId"/>
    <result column="service_id" property="serviceId"/>
    <result column="template_id" property="templateId"/>
    <result column="company_balance_fee" property="companyBalanceFee"/>
    <result column="dot_balance_fee" property="dotBalanceFee"/>
    <result column="dot_incre_balance_fee" property="dotIncreBalanceFee"/>
    <result column="dot_incre_discount_amount" property="dotIncreDiscountAmount"/>
    <result column="attendant_balance_fee" property="attendantBalanceFee"/>
    <result column="invoice_id" property="invoiceId"/>
    <result column="worder_publish_time" property="worderPublishTime"/>
    <result column="worder_incre_publish_time" property="worderIncrePublishTime"/>
    <result column="worder_finish_time" property="worderFinishTime"/>
    <result column="cus_apply_power_time" property="cusApplyPowerTime"/>
    <result column="cus_expect_power_time" property="cusExpectPowerTime"/>
    <result column="cus_real_power_time" property="cusRealPowerTime"/>
    <result column="description" property="description"/>
    <result column="charge_reach" property="chargeReach"/>
    <result column="convey_appoint_time" property="conveyAppointTime"/>
    <result column="install_appoint_time" property="installAppointTime"/>
    <result column="convey_sign_out_time" property="conveySignOutTime"/>
    <result column="install_sign_out_time" property="installSignOutTime"/>
    <result column="user_type" property="userType"/>
    <result column="company_id" property="companyId"/>
    <result column="company_order_number" property="companyOrderNumber"/>
    <result column="medal_order_number" property="medalOrderNumber"/>
    <result column="convey_order_number" property="conveyOrderNumber"/>
    <result column="install_order_number" property="installOrderNumber"/>
    <result column="user_balance_fee" property="userBalanceFee"/>
    <result column="user_balance_fee_tax" property="userBalanceFeeTax"/>
    <result column="user_balance_fee_sum" property="userBalanceFeeSum"/>
    <result column="user_actual_cost" property="userActualCost"/>
    <result column="colorLabel" property="colorLabel"/>
        <collection property="worderTypeList" ofType="com.bonc.rrs.worder.entity.WorderTypeEntity">
            <id property="id" column="id"/>
            <result property="pid" column="pid"/>
            <result property="pids" column="pids"/>
            <result property="level" column="level"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="createTime" column="create_time"/>
        </collection>



    </resultMap>

    <select id="queryAll" parameterType="Map" resultMap="informationMap">
        select w.*,e.full_name
        from worder_information w
        join worder_type e on e.id = w.worder_type_id
        left join worder_template t on w.template_id = t.id
        <if test="p.company != null and p.company !=''">
            join worder_ext_field f on w.worder_no = f.worder_no
            join ext_field l on f.field_id = l.field_id
        </if>
        where w.is_delete = 0
        <if test="p.worderNo != null and p.worderNo != ''">
            and w.worder_no = #{p.worderNo}
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and e.id  = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and w.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and w.user_name = #{p.userName}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and w.dot_id = #{p.dotId}
        </if>
        <if test="p.startTime != null and  p.startTime !=''">
            and w.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and  p.endTime !=''">
            and w.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.worderExecStatus != null and p.worderExecStatus !=''">
            and w.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderSetStatus != null and  p.worderSetStatus !=''">
            and w.worder_set_status = #{p.worderSetStatus}
        </if>
        <if test="p.worderIncreStatus != null and  p.worderIncreStatus !=''">
            and w.worder_Incre_status = #{p.worderIncreStatus}
        </if>
        <if test="p.worderExciStatus != null and p.worderExciStatus !=''">
            and w.worder_exci_status = #{p.worderExciStatus}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and l.field_desc = 'companyId' and f.field_value = #{p.companyId}
        </if>
        <if test="p.fzx != null and  p.fzx !=''">
            and l.field_desc = 'fzx' and f.field_value = #{p.fzx}
        </if>
        <if test="p.jxs != null and  p.jxs !=''">
            and l.field_desc = 'jxs' and f.field_value = #{p.jxs}
        </if>
        <if test="p.serviceId == 1">
            and
            w.service_id in (
            SELECT
            b.id
            FROM
            biz_attendant b
            LEFT JOIN sys_user s ON b.user_id = s.user_id
            LEFT JOIN worder_information w ON b.id = w.service_id
            where  s.user_id = #{p.userId})
        </if>
        <if test="p.dotIds== 1">
            and
            w.dot_id in (
            select i.dot_id from dot_information i
            LEFT JOIN dot_contacts o on i.dot_code = o.dot_code
            LEFT JOIN sys_user s on o.contacts_id = s.user_id
            where  s.user_id = #{p.userId}  and i.is_delete = 0)
        </if>
        order by w.create_time desc
    </select>
    <select id="queryUserService" parameterType="Long" resultType="string">
         select b.id from biz_attendant b
            LEFT JOIN sys_user s ON b.user_id = s.user_id
            where  s.user_id = #{userId}
    </select>
    <select id="queryUserDot" parameterType="Long" resultType="string">
        select i.dot_id from dot_information i
            LEFT JOIN dot_contacts o on i.dot_code = o.dot_code
            LEFT JOIN sys_user s on o.contacts_id = s.user_id
            where  s.user_id = #{userId}  and i.is_delete = 0
    </select>
</mapper>
