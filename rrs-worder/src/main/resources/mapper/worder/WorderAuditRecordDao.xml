<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderAuditRecordDao">

    <update id="updateCompanyInvoice" parameterType="java.util.Map" >
        update company_invoice
        set status = #{status}
        <where>
            <if test="companyInvoiceNo != null and companyInvoiceNo != ''">
                and company_invoice_no = #{companyInvoiceNo}
            </if>
        </where>
    </update>

    <update id="updateCompanyReceive" parameterType="java.util.Map" >
        update company_receivable
        set status = #{status}
        <where>
            <if test="companyId != null and companyId != ''">
                and company_id = #{companyId}
            </if>
            <if test="companyReceivableNo != null and companyReceivableNo != ''">
                and company_receivable_no = #{companyReceivableNo}
            </if>
        </where>
    </update>

    <update id="updateBalancePublish" parameterType="java.util.Map" >
        update balance_publish
        set status = #{status},batch_status = #{status}
        <where>
            <if test="balancePublishNo != null and balancePublishNo != ''">
                and balance_publish_no = #{balancePublishNo}
            </if>
        </where>
    </update>

    <update id="updateBatchBalancePublish">
        update balance_publish
        set status = #{status} , batch_status = #{status}
        <where>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
        </where>
    </update>

    <select id="getForStatus" resultType="java.util.Map">
        SELECT be.name bizName,
        war.no_pass_reason noPassReason,
        war.audit_status auditStatus
        FROM worder_audit_record war
        JOIN sys_user_employee su ON war.audit_user_id = su.user_id
        JOIN biz_employee be ON be.id = su.employee_id
        WHERE
        war.apply_no = #{applyNo}
        and war.audit_type = #{auditType}
        <if test="auditStatus != null and auditStatus !=''">
        and war.audit_status = #{auditStatus}
        </if>
        ORDER BY war.id DESC
        LIMIT 0,1
    </select>


    <select id="getStimulateReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'incentive_reason'
    </select>
    <select id="getStimulateNegativeReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'negative_incentive_reason'
    </select>

    <select id="getStimulateDotReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'incentive_dot_reason'
    </select>

    <select id="getStimulateDotNegativeReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'negative_incentive_dot_reason'
    </select>

    <select id="getStimulateInfo" resultType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo">
        select
         s.id,
        s.worder_id,
        s.dot_id,
        s.stimulate_type,
        s.status,
        s.stimulate_reason,
        s.stimulate_fee,
        s.user_id,
        s.user_name,
        s.price_type,
        DATE_FORMAT(s.create_time,'%Y-%m-%d') createTime,
        (case
         when s.stimulate_type = 10 then d.dot_name
         when s.stimulate_type = 11 then '日日顺'
         end
        ) stimulateTypeName,
        s.tax_point,
        s.balance_fee,
        s.balance_fee_tax,
        s.fee_tax,
        s.incentive_type
         from worder_pm_stimulate s
         left join dot_information d on s.dot_id = d.dot_id
         where s.id = #{stimulateId} and s.is_delete = 0
    </select>
</mapper>