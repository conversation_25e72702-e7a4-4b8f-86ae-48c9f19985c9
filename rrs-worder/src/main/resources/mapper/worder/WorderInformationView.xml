<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.WorderInformationDaoView">
    <select id="selectItemByParamView" resultType="com.bonc.rrs.worder.entity.RptDataSourceItemEntity">
        select DISTINCT a.ORDINAL_POSITION as id,a.COLUMN_NAME as field_name,a.DATA_TYPE as data_type,a.COLUMN_COMMENT
        as
        display_name,b.table_comment from information_schema.COLUMNS a ,information_schema.TABLES b where
        a.table_name=b.table_name
        <if test="tableCode != null and tableCode != ''">
            AND a.table_name = #{tableCode}
        </if>
        and a.table_schema = (select DATABASE())
        and b.table_schema = (select DATABASE())
    </select>

    <select id="selectItemByParamView2" resultType="string">
        select DISTINCT a.COLUMN_NAME
        from information_schema.COLUMNS a ,information_schema.TABLES b where
        a.table_name=b.table_name
        <if test="tableCode != null and tableCode != ''">
            AND a.table_name = #{tableCode}
        </if>
        and a.table_schema = (select DATABASE())
        and b.table_schema = (select DATABASE())
    </select>
    <select id="selectAllView" parameterType="java.util.Map" resultType="hashmap">
        select * from worder_view w
        where 1=1
        <if test="worderNo != null and worderNo !=''">
            and 工单编号 like concat('%',#{worderNo},'%')
        </if>
        <if test="brandList!=null  ">
            and 品牌id in
            <foreach collection="brandList" item="item" open="(" separator="," close=")">
                ${item}
            </foreach>
        </if>
        <if test="worderExecStatus!=null  ">
            and 工单执行状态 in
            <foreach collection="worderExecStatus" item="item" open="(" separator="," close=")">
                <![CDATA['${item}']]>
            </foreach>
        </if>
        <if test="regionList!=null">
            and
            区域id in(

            select DISTINCT districtId  from (
            select
            c.id districtId
            from biz_region a
            left join biz_region b on a.id = b.pid
            left join biz_region c on b.id = c.pid
            where a.id in
            <foreach collection="regionList" item="item" open="(" separator="," close=")">
                ${item}
            </foreach>
            UNION all
            select
            c.id districtId
            from biz_region a
            left join biz_region b on a.id = b.pid
            left join biz_region c on b.id = c.pid
            where b.id in
            <foreach collection="regionList" item="item" open="(" separator="," close=")">
                ${item}
            </foreach>
            UNION all
            select
            c.id districtId
            from biz_region a
            left join biz_region b on a.id = b.pid
            left join biz_region c on b.id = c.pid
            where c.id in
            <foreach collection="regionList" item="item" open="(" separator="," close=")">
                ${item}
            </foreach>
            ) a where  a.districtId is not null )
        </if>
        <if test="worderTypeId != null and worderTypeId !=''">
            and 工单类型冗余 = #{worderTypeId}
        </if>
        <if test="worderStatus != null and worderStatus!=''">
            and 工单主状态 = #{worderStatus}
        </if>
        <if test="worderSetStatus != null and worderSetStatus !=''">
            and 工单结算状态 = #{worderSetStatus}
        </if>
        <if test="worderIncreStatus != null and worderIncreStatus !=''">
            and 工单增项结算状态 = #{worderIncreStatus}
        </if>
        <if test ="userName != null and userName !=''">
            and 客户姓名 like concat('%',#{userName},'%')
        </if>
        <if test ="createBy != null">
            and 客服 = #{createBy}
        </if>
        <if test ="pmId != null">
            and 服务经理 = #{pmId}
        </if>
        <if test="dotId != null and dotId !=''">
            and 网点 = #{dotId}
        </if>
        <if test="serviceId != null">
            and 服务兵 = #{serviceId}
        </if>
        <if test="company != null and company!=''">
            and 车企名称冗余 = #{company}
        </if>
        <if test="startTime != null and startTime !=''">
            and 工单创建时间 <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null and  endTime !=''">
            and 工单创建时间 <![CDATA[<=]]> #{endTime}
        </if>
        <if test="startSurveyTime != null and startSurveyTime !='' ">
            and 服务兵提交勘测资料时间  <![CDATA[>=]]> #{startSurveyTime}
        </if>
        <if test="endSurveyTime != null and endSurveyTime !='' ">
            and 服务兵提交勘测资料时间  <![CDATA[<=]]> #{endSurveyTime}
        </if>
        <if test="endSurveysTime != null and endSurveysTime !='' ">
            and 预约勘测上门时间  <![CDATA[<=]]> #{endSurveysTime}
        </if>
        <if test="startSurveysTime != null and startSurveysTime !='' ">
            and 预约勘测上门时间  <![CDATA[>=]]> #{startSurveysTime}
        </if>
        <if test="startInstallsTime != null and startInstallsTime !='' ">
            and 预约安装上门时间  <![CDATA[>=]]> #{startInstallsTime}
        </if>
        <if test="endInstallSurveysTime != null and endInstallSurveysTime !='' ">
            and 预约安装上门时间  <![CDATA[<=]]> #{endInstallSurveysTime}
        </if>
        <if test="startInstallTime != null and startInstallTime !='' ">
            and 服务兵提交安装资料时间  <![CDATA[>=]]> #{startInstallTime}
        </if>
        <if test="endInstallSurveyTime != null and endInstallSurveyTime !='' ">
            and 服务兵提交安装资料时间  <![CDATA[<=]]> #{endInstallSurveyTime}
        </if>
        <if test="companyOrderNumber != null and companyOrderNumber !=''">
            and 车企订单号 like concat('%',#{companyOrderNumber},'%')
        </if>
        <if test="areaFlag == 1">
            and 区域id in (
            select r.id
            from biz_region r where regcode REGEXP (
            select GROUP_CONCAT(DISTINCT(b.regcode) SEPARATOR '|')
            from sys_user s
            left join manager_area_id m on s.user_id = m.user_id
            left join biz_region b on m.area_id = b.id
            where s.user_id = #{userId}
            ) and r.type = 3
            )
        </if>
        <if test="brandFlag == 1">
            and 品牌id in (
            select DISTINCT(m.brand_id)
            from manager_area_id m
            left join sys_user s on m.user_id = s.user_id
            left join biz_region b on m.area_id = b.id
            where s.user_id = #{userId}
            )
        </if>

        <if test="startNextContactTime != null and startNextContactTime !='' ">
            and 下次联系时间  <![CDATA[>=]]> #{startNextContactTime}
        </if>
        <if test="endNextContactTime != null and endNextContactTime !='' ">
            and 下次联系时间  <![CDATA[<=]]> #{endNextContactTime}
        </if>
        <if test="startConfirmCompletionTime != null and startConfirmCompletionTime !='' ">
            and 客服确认安装资料时间  <![CDATA[>=]]> #{startConfirmCompletionTime}
        </if>
        <if test="endConfirmCompletionTime != null and endConfirmCompletionTime !='' ">
            and 客服确认安装资料时间  <![CDATA[<=]]> #{endConfirmCompletionTime}
        </if>
        order by 工单创建时间 desc
    </select>
    <select id="selectBaseJS" resultType="java.util.Map">
        SELECT
            wi.worder_no AS `工单编号`,
            wi.company_order_number AS `车企订单号`,
            date_format( wi.create_time, '%Y-%m-%d %H:%i:%s') AS `工单创建日期`,
            wef1.field_value AS `车企派单日期`,
            SUBSTRING_INDEX(wi.address_dup, '_', 1)AS `省份`,
            SUBSTRING_INDEX(SUBSTRING_INDEX(wi.address_dup, '_', 2), '_', -1) AS `地市`,
            SUBSTRING_INDEX(SUBSTRING_INDEX(wi.address_dup, '_', 3), '_', -1) AS `区县`,

            wi.user_name AS `客户姓名`,
            wi.candidate_pm AS `服务经理`,
            su.employee_name AS `客服`,
            di.v_code AS `网点`,
            di.dot_short_name AS `网点简称`,
            wi.candidate_attendant AS `服务兵`,
            wi.template_id AS `工单模板`,

            CASE

                WHEN wi.worder_status = 0 THEN
                    '分配中'
                WHEN wi.worder_status = 1 THEN
                    '勘测中'
                WHEN wi.worder_status = 2 THEN
                    '安装中'
                WHEN wi.worder_status = 3 THEN
                    '结算中'
                WHEN wi.worder_status = 4 THEN
                    '订单完成'
                WHEN wi.worder_status = 5 THEN
                    '勘测结单'
                WHEN wi.worder_status = 6 THEN
                    '取消服务'
                WHEN wi.worder_status = 7 THEN
                    '工单预创建'
                END AS `工单主状态`,
            CASE
                WHEN wi.worder_exec_status = 0 THEN
                    '未派单'
                WHEN wi.worder_exec_status = 1 THEN
                    '网点已接单'
                WHEN wi.worder_exec_status = 2 THEN
                    '待勘测预约'
                WHEN wi.worder_exec_status = 3 THEN
                    '待勘测'
                WHEN wi.worder_exec_status = 4 THEN
                    '勘测资料未提交'
                WHEN wi.worder_exec_status = 5 THEN
                    '勘测资料提交待审核'
                WHEN wi.worder_exec_status = 6 THEN
                    '勘测资料整改中'
                WHEN wi.worder_exec_status = 7 THEN
                    '待客服确认'
                WHEN wi.worder_exec_status = 8 THEN
                    '勘测资料无误待上传车企'
                WHEN wi.worder_exec_status = 9 THEN
                    '等待充电桩及配件'
                WHEN wi.worder_exec_status = 10 THEN
                    '待安装预约'
                WHEN wi.worder_exec_status = 11 THEN
                    '待安装'
                WHEN wi.worder_exec_status = 12 THEN
                    '安装资料未提交'
                WHEN wi.worder_exec_status = 13 THEN
                    '安装资料已提交待审核'
                WHEN wi.worder_exec_status = 14 THEN
                    '安装资料整改中'
                WHEN wi.worder_exec_status = 15 THEN
                    '安装资料待客服确认'
                WHEN wi.worder_exec_status = 16 THEN
                    '安装资料无误待上传车企'
                WHEN wi.worder_exec_status = 17 THEN
                    '安装完成'
                WHEN wi.worder_exec_status = 18 THEN
                    '服务经理未派单'
                WHEN wi.worder_exec_status = 19 THEN
                    '网点未派单'
                WHEN wi.worder_exec_status = 20 THEN
                    '勘测结单'
                WHEN wi.worder_exec_status = 21 THEN
                    '已取消'
                WHEN wi.worder_exec_status = 22 THEN
                    '安装结单'
                WHEN wi.worder_exec_status = 23 THEN
                    '车企回退待客服确认' ELSE '未知状态'
                END AS `工单执行状态`,
            CASE

                WHEN ((
                          `wi`.`user_balance_fee_sum` > 0
                          )
                    AND isnull( `wi`.`user_actual_cost` )) THEN
                    '未付款'
                END
            as '增项付费状态',
                (
                    CASE

                        WHEN wi.worder_set_status = 0 THEN
                            '工单待计算'
                        WHEN wi.worder_set_status = 1 THEN
                            '车企待结算'
                        WHEN wi.worder_set_status = 2 THEN
                            '等待开票'
                        WHEN wi.worder_set_status = 3 THEN
                            '已开票'
                        WHEN wi.worder_set_status = 4 THEN
                            '车企已回款'
                        WHEN wi.worder_set_status = 5 THEN
                            '网点结算已发布'
                        WHEN wi.worder_set_status = 6 THEN
                            '外部系统状态'
                        WHEN wi.worder_set_status = 7 THEN
                            '工单已结算'
                        WHEN wi.worder_set_status = 8 THEN
                            '厂商结算首次审核通过'
                        WHEN wi.worder_set_status = 9 THEN
                            '厂商结算审核不通过'
                        WHEN wi.worder_set_status = 10 THEN
                            '厂商结算二次审核通过'
                        WHEN wi.worder_set_status = 11 THEN
                            '网点工单首次审核通过'
                        WHEN wi.worder_set_status = 12 THEN
                            '网点工单二次审核通过'
                        WHEN wi.worder_set_status = 13 THEN
                            '网点工单三次审核通过'
                        WHEN wi.worder_set_status = 14 THEN
                            '网点工单审核不通过'
                        WHEN wi.worder_set_status = 99 THEN
                            '不结算'
                        WHEN ((
                                  `wci`.`type` = 1
                                  )
                            AND ( wi.worder_set_status = 4 )) THEN
                            '已开票'
                        END
                    ) AS `工单结算状态`,
            CASE
                wi.worder_Incre_status
                WHEN 0 THEN
                    '用户已收费'
                WHEN 1 THEN
                    '增项待结算'
                WHEN 2 THEN
                    '增项结算已发布'
                WHEN 3 THEN
                    '外部系统状态'
                WHEN 4 THEN
                    '增项已结算'
                WHEN 5 THEN
                    '网点增项首次审核通过'
                WHEN 6 THEN
                    '网点增项二次审核通过'
                WHEN 7 THEN
                    '网点增项三次审核通过'
                WHEN 8 THEN
                    '网点增项审核不通过'
                WHEN 9 THEN
                    '收款记账中'
                WHEN 10 THEN
                    '收款已记账'
                WHEN 11 THEN
                    '收入成本记账中'
                WHEN 32 THEN
                    '增项结算发布中'
                WHEN 99 THEN
                    '已收款不记账'
                END AS `工单增项结算状态` ,
          date_format( wi.convey_appoint_time, '%Y-%m-%d %H:%i:%s') AS `预约勘测上门时间`,
            wef2.field_value AS `实际勘测完成日期`,
        date_format( wi.install_appoint_time, '%Y-%m-%d %H:%i:%s') AS `预约安装上门时间`,
        CASE
        WHEN wef3.field_value IS NOT NULL THEN wef3.field_value
        WHEN wef9.field_value IS NOT NULL THEN wef9.field_value
        ELSE NULL -- 如果两个字段都为空，则返回NULL
        END AS `实际安装完成日期`,
        date_format( wi.confirm_completion_time , '%Y-%m-%d %H:%i:%s') AS `客服确认安装资料时间`,
        date_format( wi.next_contact_time, '%Y-%m-%d %H:%i:%s') AS `下次联系时间`,
            wef4.field_value AS `充电桩编码`,

            wef5.field_value AS `电力报装完成时间`,

            wi.area_id AS `区域id`,
            wt.brand_id AS `品牌id`,
            ci.company_name AS `车企名称冗余`,
           wt1.full_name AS `服务类型ID`

        FROM
            worder_information wi
                LEFT JOIN worder_template wt ON wi.template_id = wt.id
                LEFT JOIN company_information ci on wi.company_id = ci.company_id
                LEFT JOIN dot_information di ON wi.dot_id = di.dot_id
                LEFT JOIN sys_user su ON wi.create_by = su.user_id
                LEFT JOIN worder_child_information wci ON wci.worder_id = wi.worder_id AND wci.`balance_source` = '0' AND wci.`balance_type` = '0'
                LEFT JOIN worder_ext_field wef1 ON wef1.worder_no = wi.worder_no  AND wef1.field_id = 154
                LEFT JOIN worder_ext_field wef2 ON wef2.worder_no = wi.worder_no AND wef2.field_id = 1192
                LEFT JOIN worder_ext_field wef3 ON wef3.worder_no = wi.worder_no AND wef3.field_id = 1197
                LEFT JOIN worder_ext_field wef4 ON wef4.worder_no = wi.worder_no 	AND wef4.field_id = 950
                LEFT JOIN worder_ext_field wef5 ON wef5.worder_no = wi.worder_no  	AND wef5.field_id = 1716
                LEFT JOIN worder_ext_field wef9 ON wef9.worder_no = wi.worder_no AND wef9.field_id = 1255

                <if test=" (startSurveyTime != null and startSurveyTime !='')  or
                  (endSurveyTime != null and endSurveyTime !='')  or
                   (startInstallTime != null and startInstallTime !='')  or
                   (endInstallSurveyTime != null and endInstallSurveyTime !='')
                  ">
                   LEFT JOIN v_worder_operation_record vwor ON wi.worder_no = vwor.worder_no
               </if>
            left join  worder_type wt1 on wi.worder_type_id = wt1.id
        WHERE
            wi.is_delete = 0

    <include refid="param"></include>
    </select>

    <select id="selectBaseKF" resultType="java.util.Map">
        SELECT
        wi.worder_no AS `工单编号`,
        wi.company_order_number AS `车企订单号`,
        date_format( wi.create_time, '%Y-%m-%d %H:%i:%s') AS `工单创建日期`,
        wef1.field_value AS `车企派单日期`,
        SUBSTRING_INDEX(wi.address_dup, '_', 1)AS `省份`,
        SUBSTRING_INDEX(SUBSTRING_INDEX(wi.address_dup, '_', 2), '_', -1) AS `地市`,
        SUBSTRING_INDEX(SUBSTRING_INDEX(wi.address_dup, '_', 3), '_', -1) AS `区县`,
        b.brand_name AS `品牌`,
        wi.user_name AS `客户姓名`,

        wi.candidate_pm AS `服务经理`,
        su.employee_name AS `客服`,
        di.v_code AS `网点`,
        di.dot_short_name AS `网点简称`,
        wi.candidate_attendant AS `服务兵`,
        wt.template_name AS `工单模板`,

        CASE

        WHEN wi.worder_status = 0 THEN
        '分配中'
        WHEN wi.worder_status = 1 THEN
        '勘测中'
        WHEN wi.worder_status = 2 THEN
        '安装中'
        WHEN wi.worder_status = 3 THEN
        '结算中'
        WHEN wi.worder_status = 4 THEN
        '订单完成'
        WHEN wi.worder_status = 5 THEN
        '勘测结单'
        WHEN wi.worder_status = 6 THEN
        '取消服务'
        WHEN wi.worder_status = 7 THEN
        '工单预创建'
        END AS `工单主状态`,
        CASE
        WHEN wi.worder_exec_status = 0 THEN
        '未派单'
        WHEN wi.worder_exec_status = 1 THEN
        '网点已接单'
        WHEN wi.worder_exec_status = 2 THEN
        '待勘测预约'
        WHEN wi.worder_exec_status = 3 THEN
        '待勘测'
        WHEN wi.worder_exec_status = 4 THEN
        '勘测资料未提交'
        WHEN wi.worder_exec_status = 5 THEN
        '勘测资料提交待审核'
        WHEN wi.worder_exec_status = 6 THEN
        '勘测资料整改中'
        WHEN wi.worder_exec_status = 7 THEN
        '待客服确认'
        WHEN wi.worder_exec_status = 8 THEN
        '勘测资料无误待上传车企'
        WHEN wi.worder_exec_status = 9 THEN
        '等待充电桩及配件'
        WHEN wi.worder_exec_status = 10 THEN
        '待安装预约'
        WHEN wi.worder_exec_status = 11 THEN
        '待安装'
        WHEN wi.worder_exec_status = 12 THEN
        '安装资料未提交'
        WHEN wi.worder_exec_status = 13 THEN
        '安装资料已提交待审核'
        WHEN wi.worder_exec_status = 14 THEN
        '安装资料整改中'
        WHEN wi.worder_exec_status = 15 THEN
        '安装资料待客服确认'
        WHEN wi.worder_exec_status = 16 THEN
        '安装资料无误待上传车企'
        WHEN wi.worder_exec_status = 17 THEN
        '安装完成'
        WHEN wi.worder_exec_status = 18 THEN
        '服务经理未派单'
        WHEN wi.worder_exec_status = 19 THEN
        '网点未派单'
        WHEN wi.worder_exec_status = 20 THEN
        '勘测结单'
        WHEN wi.worder_exec_status = 21 THEN
        '已取消'
        WHEN wi.worder_exec_status = 22 THEN
        '安装结单'
        WHEN wi.worder_exec_status = 23 THEN
        '车企回退待客服确认' ELSE '未知状态'
        END AS `工单执行状态`,
        CASE

        WHEN ((
        `wi`.`user_balance_fee_sum` > 0
        )
        AND isnull( `wi`.`user_actual_cost` )) THEN
        '未付款'
        END
        as '增项付费状态',
        (
        CASE

        WHEN wi.worder_set_status = 0 THEN
        '工单待计算'
        WHEN wi.worder_set_status = 1 THEN
        '车企待结算'
        WHEN wi.worder_set_status = 2 THEN
        '等待开票'
        WHEN wi.worder_set_status = 3 THEN
        '已开票'
        WHEN wi.worder_set_status = 4 THEN
        '车企已回款'
        WHEN wi.worder_set_status = 5 THEN
        '网点结算已发布'
        WHEN wi.worder_set_status = 6 THEN
        '外部系统状态'
        WHEN wi.worder_set_status = 7 THEN
        '工单已结算'
        WHEN wi.worder_set_status = 8 THEN
        '厂商结算首次审核通过'
        WHEN wi.worder_set_status = 9 THEN
        '厂商结算审核不通过'
        WHEN wi.worder_set_status = 10 THEN
        '厂商结算二次审核通过'
        WHEN wi.worder_set_status = 11 THEN
        '网点工单首次审核通过'
        WHEN wi.worder_set_status = 12 THEN
        '网点工单二次审核通过'
        WHEN wi.worder_set_status = 13 THEN
        '网点工单三次审核通过'
        WHEN wi.worder_set_status = 14 THEN
        '网点工单审核不通过'
        WHEN wi.worder_set_status = 99 THEN
        '不结算'
        WHEN ((
        `wci`.`type` = 1
        )
        AND ( wi.worder_set_status = 4 )) THEN
        '已开票'
        END
        ) AS `工单结算状态`,
        CASE
        wi.worder_Incre_status
        WHEN 0 THEN
        '用户已收费'
        WHEN 1 THEN
        '增项待结算'
        WHEN 2 THEN
        '增项结算已发布'
        WHEN 3 THEN
        '外部系统状态'
        WHEN 4 THEN
        '增项已结算'
        WHEN 5 THEN
        '网点增项首次审核通过'
        WHEN 6 THEN
        '网点增项二次审核通过'
        WHEN 7 THEN
        '网点增项三次审核通过'
        WHEN 8 THEN
        '网点增项审核不通过'
        WHEN 9 THEN
        '收款记账中'
        WHEN 10 THEN
        '收款已记账'
        WHEN 11 THEN
        '收入成本记账中'
        WHEN 32 THEN
        '增项结算发布中'
        WHEN 99 THEN
        '已收款不记账'
        END AS `工单增项结算状态` ,
        date_format( wi.convey_appoint_time, '%Y-%m-%d %H:%i:%s') AS `预约勘测上门时间`,
        wef2.field_value AS `实际勘测完成日期`,
        date_format(wi.install_appoint_time, '%Y-%m-%d %H:%i:%s') AS `预约安装上门时间`,
        CASE
        WHEN wef3.field_value IS NOT NULL THEN wef3.field_value
        WHEN wef9.field_value IS NOT NULL THEN wef9.field_value
        ELSE NULL -- 如果两个字段都为空，则返回NULL
        END AS `实际安装完成日期`,
        date_format( wi.confirm_completion_time, '%Y-%m-%d %H:%i:%s') AS `客服确认安装资料时间`,
        date_format(wi.next_contact_time, '%Y-%m-%d %H:%i:%s') AS `下次联系时间`,
        wef4.field_value AS `充电桩编码`,
        wef10.field_value AS `充电桩安装方式`,
        wef5.field_value AS `电力报装完成时间`,
        wef6.field_value AS `取电方式-比亚迪`,
        wef7.field_value AS `车架号`,
        wef8.field_value AS `是否预勘测订单`,
        CASE

        WHEN ( `wia`.`attribute_value` = 'Y' ) THEN
        '是' ELSE `wia`.`attribute_value`
        END
        AS '是否预勘测转正',
        wi.area_id AS `区域id`,
        wt.brand_id AS `品牌id`,
        ci.company_name AS `车企名称冗余`,

        wt1.full_name AS `服务类型ID`,

        date_format(  (
        SELECT min(create_time) from worder_operation_record
        where worder_no = wi.worder_no and  worder_status = 2 and  worder_exec_status = 13
        group by worder_no
        ) , '%Y-%m-%d %H:%i:%s' ) as '服务兵提交安装资料时间',
        date_format(  (
        SELECT max(gmt_create) from worder_audit_result
        where deleted = 0 and  worder_no = wi.worder_no and  worder_status = 2 and worder_audit_status = 21
        group by worder_no
        ), '%Y-%m-%d %H:%i:%s' )as '客服审核安装资料不通过时间',
        date_format(  (
        SELECT max(create_time) from worder_operation_record
        where worder_no = wi.worder_no and  worder_status = 2 and ( worder_exec_status = 17   or worder_exec_status = 22  )
        group by worder_no
        ), '%Y-%m-%d %H:%i:%s' ) as '车企确认安装完成时间',
        (
        SELECT 	REPLACE (
        REPLACE (
        group_concat(
        concat(
        REPLACE ( `title`, ',', '，' ),
        ' ',
        REPLACE ( `content`, ',', '，' ))
        ORDER BY
        `create_time` DESC,
        '#' ASC SEPARATOR ','
        ),
        CHAR ( 10 ),
        ''
        ),
        '#,',
        '#'
        ) AS `worder_remark`
        from worder_remark_log
        where   worder_no = wi.worder_no and NOT ((
        `title` LIKE concat( `worder_no`, '%' ))

        )  group by worder_no  ) as '工单新增备注',
        (
        SELECT  case
        when max(no_install_reason_id)  = 1 then '上门时间'
        when max(no_install_reason_id)  = 2 then '下次联系时间-具备安装条件，用户无时间'
        when max(no_install_reason_id)  = 3 then '下次联系时间-具备安装条件，服务预排'
        when max(no_install_reason_id)  = 4 then '下次联系时间-不具备安装条件，电力报装中'
        when max(no_install_reason_id)  = 5 then '下次联系时间-不具备安装条件，其他'
        end
        from worder_remark_log
        where   worder_no = wi.worder_no and NOT ((
        `title` LIKE concat( `worder_no`, '%' ))

        )  group by worder_no  ) as '未安装原因',

        date_format( wi.create_time, '%Y-%m-%d %H:%i:%s') AS `工单创建时间`,
        date_format( vwor.cust_pm_time, '%Y-%m-%d %H:%i:%s') as '客服派单给项目经理的时间',
        CASE
        WHEN ((
        `vwor`.`dot_attend_time` IS NOT NULL
        )
        AND isnull( `vwor`.`pm_dot_time` )) THEN
        date_format(`vwor`.`cust_pm_time`, '%Y-%m-%d %H:%i:%s')
        ELSE date_format(`vwor`.`pm_dot_time`, '%Y-%m-%d %H:%i:%s')
        END as '项目经理派单给网点时间',
        date_format(`vwor`.`dot_attend_time`, '%Y-%m-%d %H:%i:%s') AS '网点分配给服务兵时间'
        FROM
        worder_information wi
        LEFT JOIN worder_template wt ON wi.template_id = wt.id
        LEFT JOIN brand b on wt.brand_id = b.id
        LEFT JOIN company_information ci on wi.company_id = ci.company_id
        LEFT JOIN dot_information di ON wi.dot_id = di.dot_id
        LEFT JOIN sys_user su ON wi.create_by = su.user_id
        LEFT JOIN worder_child_information wci ON wci.worder_id = wi.worder_id AND wci.`balance_source` = '0' AND wci.`balance_type` = '0'
        LEFT JOIN worder_ext_field wef1 ON wef1.worder_no = wi.worder_no  AND wef1.field_id = 154
        LEFT JOIN worder_ext_field wef2 ON wef2.worder_no = wi.worder_no AND wef2.field_id = 1192
        LEFT JOIN worder_ext_field wef3 ON wef3.worder_no = wi.worder_no AND wef3.field_id = 1197
        LEFT JOIN worder_ext_field wef4 ON wef4.worder_no = wi.worder_no 	AND wef4.field_id = 950
        LEFT JOIN worder_ext_field wef5 ON wef5.worder_no = wi.worder_no  	AND wef5.field_id = 1716
        LEFT JOIN worder_ext_field wef6 ON wef6.worder_no = wi.worder_no  	AND wef6.field_id = 1733
        LEFT JOIN worder_ext_field wef7 ON wef7.worder_no = wi.worder_no  	AND wef7.field_id = 153
        LEFT JOIN worder_ext_field wef8 ON wef8.worder_no = wi.worder_no  	AND wef8.field_id = 1224
        LEFT JOIN worder_ext_field wef9 ON wef9.worder_no = wi.worder_no AND wef9.field_id = 1255
        LEFT JOIN worder_ext_field wef10 ON wef10.worder_no = wi.worder_no AND wef10.field_id = 921
        left join worder_information_attribute wia on wi.worder_id = wia.worder_id
        and wia.attribute_code = 'presurvey-formal' and wia.attribute = 'presurvey'  and wia.is_delete = 0
       left join  worder_type wt1 on wi.worder_type_id = wt1.id


            LEFT JOIN v_worder_operation_record vwor ON wi.worder_no = vwor.worder_no


        WHERE
        wi.is_delete = 0

        <include refid="param"></include>


    </select>


    <select id="selectBaseBJ" resultType="java.util.Map">
        SELECT
        wi.worder_no AS `工单编号`,
        wi.company_order_number AS `车企订单号`,
        date_format( wi.create_time, '%Y-%m-%d %H:%i:%s') AS `工单创建日期`,
        SUBSTRING_INDEX(wi.address_dup, '_', 1)AS `省份`,
        SUBSTRING_INDEX(SUBSTRING_INDEX(wi.address_dup, '_', 2), '_', -1) AS `地市`,
        SUBSTRING_INDEX(SUBSTRING_INDEX(wi.address_dup, '_', 3), '_', -1) AS `区县`,
        b.brand_name AS `品牌`,
        wi.user_name AS `客户姓名`,
        wi.candidate_pm AS `服务经理`,
        su.employee_name AS `客服`,
        di.v_code AS `网点`,
        di.dot_short_name AS `网点简称`,
        wi.candidate_attendant AS `服务兵`,
        wt.template_name AS `工单模板`,

        CASE

        WHEN wi.worder_status = 0 THEN
        '分配中'
        WHEN wi.worder_status = 1 THEN
        '勘测中'
        WHEN wi.worder_status = 2 THEN
        '安装中'
        WHEN wi.worder_status = 3 THEN
        '结算中'
        WHEN wi.worder_status = 4 THEN
        '订单完成'
        WHEN wi.worder_status = 5 THEN
        '勘测结单'
        WHEN wi.worder_status = 6 THEN
        '取消服务'
        WHEN wi.worder_status = 7 THEN
        '工单预创建'
        END AS `工单主状态`,
        CASE
        WHEN wi.worder_exec_status = 0 THEN
        '未派单'
        WHEN wi.worder_exec_status = 1 THEN
        '网点已接单'
        WHEN wi.worder_exec_status = 2 THEN
        '待勘测预约'
        WHEN wi.worder_exec_status = 3 THEN
        '待勘测'
        WHEN wi.worder_exec_status = 4 THEN
        '勘测资料未提交'
        WHEN wi.worder_exec_status = 5 THEN
        '勘测资料提交待审核'
        WHEN wi.worder_exec_status = 6 THEN
        '勘测资料整改中'
        WHEN wi.worder_exec_status = 7 THEN
        '待客服确认'
        WHEN wi.worder_exec_status = 8 THEN
        '勘测资料无误待上传车企'
        WHEN wi.worder_exec_status = 9 THEN
        '等待充电桩及配件'
        WHEN wi.worder_exec_status = 10 THEN
        '待安装预约'
        WHEN wi.worder_exec_status = 11 THEN
        '待安装'
        WHEN wi.worder_exec_status = 12 THEN
        '安装资料未提交'
        WHEN wi.worder_exec_status = 13 THEN
        '安装资料已提交待审核'
        WHEN wi.worder_exec_status = 14 THEN
        '安装资料整改中'
        WHEN wi.worder_exec_status = 15 THEN
        '安装资料待客服确认'
        WHEN wi.worder_exec_status = 16 THEN
        '安装资料无误待上传车企'
        WHEN wi.worder_exec_status = 17 THEN
        '安装完成'
        WHEN wi.worder_exec_status = 18 THEN
        '服务经理未派单'
        WHEN wi.worder_exec_status = 19 THEN
        '网点未派单'
        WHEN wi.worder_exec_status = 20 THEN
        '勘测结单'
        WHEN wi.worder_exec_status = 21 THEN
        '已取消'
        WHEN wi.worder_exec_status = 22 THEN
        '安装结单'
        WHEN wi.worder_exec_status = 23 THEN
        '车企回退待客服确认' ELSE '未知状态'
        END AS `工单执行状态`,

        wef4.field_value AS `安装充电桩编码`,
        CONCAT(wef6.field_value,",",wef7.field_value) AS `勘测充电桩编码`,
        wef8.field_value AS `客户自提桩`,

        wi.area_id AS `区域id`,
        wt.brand_id AS `品牌id`,
        ci.company_name AS `车企名称冗余`,
        date_format( wi.create_time, '%Y-%m-%d %H:%i:%s') AS `工单创建时间`
        FROM
        worder_information wi
        LEFT JOIN worder_template wt ON wi.template_id = wt.id
        LEFT JOIN brand b on wt.brand_id = b.id
        LEFT JOIN company_information ci on wi.company_id = ci.company_id
        LEFT JOIN dot_information di ON wi.dot_id = di.dot_id
        LEFT JOIN sys_user su ON wi.create_by = su.user_id
        LEFT JOIN worder_child_information wci ON wci.worder_id = wi.worder_id AND wci.`balance_source` = '0' AND wci.`balance_type` = '0'
        LEFT JOIN worder_ext_field wef4 ON wef4.worder_no = wi.worder_no 	AND wef4.field_id = 950
        LEFT JOIN worder_ext_field wef6 ON wef6.worder_no = wi.worder_no  	AND wef6.field_id = 1736
        LEFT JOIN worder_ext_field wef7 ON wef7.worder_no = wi.worder_no  	AND wef7.field_id = 1846
        LEFT JOIN worder_ext_field wef8 ON wef8.worder_no = wi.worder_no  	AND wef8.field_id = 1723

        <if test=" (startSurveyTime != null and startSurveyTime !='')  or
                  (endSurveyTime != null and endSurveyTime !='')  or
                   (startInstallTime != null and startInstallTime !='')  or
                   (endInstallSurveyTime != null and endInstallSurveyTime !='')
                  ">
            LEFT JOIN v_worder_operation_record vwor ON wi.worder_no = vwor.worder_no
        </if>

        WHERE
        wi.is_delete = 0

        <include refid="param"></include>


    </select>
    <select id="selectBaseWD" resultType="java.util.Map">
        SELECT
        wi.worder_no AS `工单编号`,
        wi.company_order_number AS `车企订单号`,
        date_format( wi.create_time, '%Y-%m-%d %H:%i:%s') AS `工单创建日期`,
        wef1.field_value AS `车企派单日期`,
        SUBSTRING_INDEX(wi.address_dup, '_', 1)AS `省份`,
        SUBSTRING_INDEX(SUBSTRING_INDEX(wi.address_dup, '_', 2), '_', -1) AS `地市`,
        SUBSTRING_INDEX(SUBSTRING_INDEX(wi.address_dup, '_', 3), '_', -1) AS `区县`,
        b.brand_name AS `品牌`,
        wi.user_name AS `客户姓名`,

        wi.candidate_pm AS `服务经理`,
        su.employee_name AS `客服`,
        di.v_code AS `网点`,
        wi.candidate_attendant AS `服务兵`,
        wt.template_name AS `工单模板`,

        CASE

        WHEN wi.worder_status = 0 THEN
        '分配中'
        WHEN wi.worder_status = 1 THEN
        '勘测中'
        WHEN wi.worder_status = 2 THEN
        '安装中'
        WHEN wi.worder_status = 3 THEN
        '结算中'
        WHEN wi.worder_status = 4 THEN
        '订单完成'
        WHEN wi.worder_status = 5 THEN
        '勘测结单'
        WHEN wi.worder_status = 6 THEN
        '取消服务'
        WHEN wi.worder_status = 7 THEN
        '工单预创建'
        END AS `工单主状态`,
        CASE
        WHEN wi.worder_exec_status = 0 THEN
        '未派单'
        WHEN wi.worder_exec_status = 1 THEN
        '网点已接单'
        WHEN wi.worder_exec_status = 2 THEN
        '待勘测预约'
        WHEN wi.worder_exec_status = 3 THEN
        '待勘测'
        WHEN wi.worder_exec_status = 4 THEN
        '勘测资料未提交'
        WHEN wi.worder_exec_status = 5 THEN
        '勘测资料提交待审核'
        WHEN wi.worder_exec_status = 6 THEN
        '勘测资料整改中'
        WHEN wi.worder_exec_status = 7 THEN
        '待客服确认'
        WHEN wi.worder_exec_status = 8 THEN
        '勘测资料无误待上传车企'
        WHEN wi.worder_exec_status = 9 THEN
        '等待充电桩及配件'
        WHEN wi.worder_exec_status = 10 THEN
        '待安装预约'
        WHEN wi.worder_exec_status = 11 THEN
        '待安装'
        WHEN wi.worder_exec_status = 12 THEN
        '安装资料未提交'
        WHEN wi.worder_exec_status = 13 THEN
        '安装资料已提交待审核'
        WHEN wi.worder_exec_status = 14 THEN
        '安装资料整改中'
        WHEN wi.worder_exec_status = 15 THEN
        '安装资料待客服确认'
        WHEN wi.worder_exec_status = 16 THEN
        '安装资料无误待上传车企'
        WHEN wi.worder_exec_status = 17 THEN
        '安装完成'
        WHEN wi.worder_exec_status = 18 THEN
        '服务经理未派单'
        WHEN wi.worder_exec_status = 19 THEN
        '网点未派单'
        WHEN wi.worder_exec_status = 20 THEN
        '勘测结单'
        WHEN wi.worder_exec_status = 21 THEN
        '已取消'
        WHEN wi.worder_exec_status = 22 THEN
        '安装结单'
        WHEN wi.worder_exec_status = 23 THEN
        '车企回退待客服确认' ELSE '未知状态'
        END AS `工单执行状态`,
        CASE

        WHEN ((
        `wi`.`user_balance_fee_sum` > 0
        )
        AND isnull( `wi`.`user_actual_cost` )) THEN
        '未付款'
        END
        as '增项付费状态',
        (
        CASE

        WHEN wi.worder_set_status = 0 THEN
        '工单待计算'
        WHEN wi.worder_set_status = 1 THEN
        '车企待结算'
        WHEN wi.worder_set_status = 2 THEN
        '等待开票'
        WHEN wi.worder_set_status = 3 THEN
        '已开票'
        WHEN wi.worder_set_status = 4 THEN
        '车企已回款'
        WHEN wi.worder_set_status = 5 THEN
        '网点结算已发布'
        WHEN wi.worder_set_status = 6 THEN
        '外部系统状态'
        WHEN wi.worder_set_status = 7 THEN
        '工单已结算'
        WHEN wi.worder_set_status = 8 THEN
        '厂商结算首次审核通过'
        WHEN wi.worder_set_status = 9 THEN
        '厂商结算审核不通过'
        WHEN wi.worder_set_status = 10 THEN
        '厂商结算二次审核通过'
        WHEN wi.worder_set_status = 11 THEN
        '网点工单首次审核通过'
        WHEN wi.worder_set_status = 12 THEN
        '网点工单二次审核通过'
        WHEN wi.worder_set_status = 13 THEN
        '网点工单三次审核通过'
        WHEN wi.worder_set_status = 14 THEN
        '网点工单审核不通过'
        WHEN wi.worder_set_status = 99 THEN
        '不结算'
        WHEN ((
        `wci`.`type` = 1
        )
        AND ( wi.worder_set_status = 4 )) THEN
        '已开票'
        END
        ) AS `工单结算状态`,
        CASE
        wi.worder_Incre_status
        WHEN 0 THEN
        '用户已收费'
        WHEN 1 THEN
        '增项待结算'
        WHEN 2 THEN
        '增项结算已发布'
        WHEN 3 THEN
        '外部系统状态'
        WHEN 4 THEN
        '增项已结算'
        WHEN 5 THEN
        '网点增项首次审核通过'
        WHEN 6 THEN
        '网点增项二次审核通过'
        WHEN 7 THEN
        '网点增项三次审核通过'
        WHEN 8 THEN
        '网点增项审核不通过'
        WHEN 9 THEN
        '收款记账中'
        WHEN 10 THEN
        '收款已记账'
        WHEN 11 THEN
        '收入成本记账中'
        WHEN 32 THEN
        '增项结算发布中'
        WHEN 99 THEN
        '已收款不记账'
        END AS `工单增项结算状态` ,
        date_format( wi.convey_appoint_time, '%Y-%m-%d %H:%i:%s') AS `预约勘测上门时间`,
        wef2.field_value AS `实际勘测完成日期`,
        date_format(wi.install_appoint_time, '%Y-%m-%d %H:%i:%s') AS `预约安装上门时间`,
        CASE
        WHEN wef3.field_value IS NOT NULL THEN wef3.field_value
        WHEN wef9.field_value IS NOT NULL THEN wef9.field_value
        ELSE NULL -- 如果两个字段都为空，则返回NULL
        END AS `实际安装完成日期`,
        date_format( wi.confirm_completion_time, '%Y-%m-%d %H:%i:%s') AS `客服确认安装资料时间`,
        date_format(wi.next_contact_time, '%Y-%m-%d %H:%i:%s') AS `下次联系时间`,
        wef4.field_value AS `充电桩编码`,

        wef5.field_value AS `电力报装完成时间`,
        wef6.field_value AS `取电方式-比亚迪`,
        wef7.field_value AS `车架号`,
        wef8.field_value AS `是否预勘测订单`,
        CASE

        WHEN ( `wia`.`attribute_value` = 'Y' ) THEN
        '是' ELSE `wia`.`attribute_value`
        END
        AS '是否预勘测转正',
        wi.area_id AS `区域id`,
        wt.brand_id AS `品牌id`,
        ci.company_name AS `车企名称冗余`,

        wt1.full_name AS `服务类型ID`,

        date_format(  (
        SELECT min(create_time) from worder_operation_record
        where worder_no = wi.worder_no and  worder_status = 2 and  worder_exec_status = 13
        group by worder_no
        ) , '%Y-%m-%d %H:%i:%s' ) as '服务兵提交安装资料时间',
        date_format(  (
        SELECT max(gmt_create) from worder_audit_result
        where deleted = 0 and  worder_no = wi.worder_no and  worder_status = 2 and worder_audit_status = 21
        group by worder_no
        ), '%Y-%m-%d %H:%i:%s' )as '客服审核安装资料不通过时间',
        date_format(  (
        SELECT max(create_time) from worder_operation_record
        where worder_no = wi.worder_no and  worder_status = 2 and ( worder_exec_status = 17   or worder_exec_status = 22  )
        group by worder_no
        ), '%Y-%m-%d %H:%i:%s' ) as '车企确认安装完成时间',
        (
        SELECT 	REPLACE (
        REPLACE (
        group_concat(
        concat(
        REPLACE ( `title`, ',', '，' ),
        ' ',
        REPLACE ( `content`, ',', '，' ))
        ORDER BY
        `create_time` DESC,
        '#' ASC SEPARATOR ','
        ),
        CHAR ( 10 ),
        ''
        ),
        '#,',
        '#'
        ) AS `worder_remark`
        from worder_remark_log
        where   worder_no = wi.worder_no and NOT ((
        `title` LIKE concat( `worder_no`, '%' ))

        )  group by worder_no  ) as '工单新增备注',
        (
        SELECT  case
        when max(no_install_reason_id)  = 1 then '上门时间'
        when max(no_install_reason_id)  = 2 then '下次联系时间-具备安装条件，用户无时间'
        when max(no_install_reason_id)  = 3 then '下次联系时间-具备安装条件，服务预排'
        when max(no_install_reason_id)  = 4 then '下次联系时间-不具备安装条件，电力报装中'
        when max(no_install_reason_id)  = 5 then '下次联系时间-不具备安装条件，其他'
        end
        from worder_remark_log
        where   worder_no = wi.worder_no and NOT ((
        `title` LIKE concat( `worder_no`, '%' ))

        )  group by worder_no  ) as '未安装原因',

        date_format( wi.create_time, '%Y-%m-%d %H:%i:%s') AS `工单创建时间`,
        date_format( vwor.cust_pm_time, '%Y-%m-%d %H:%i:%s') as '客服派单给项目经理的时间',
        CASE
        WHEN ((
        `vwor`.`dot_attend_time` IS NOT NULL
        )
        AND isnull( `vwor`.`pm_dot_time` )) THEN
        date_format(`vwor`.`cust_pm_time`, '%Y-%m-%d %H:%i:%s')
        ELSE date_format(`vwor`.`pm_dot_time`, '%Y-%m-%d %H:%i:%s')
        END as '项目经理派单给网点时间',
        date_format(`vwor`.`dot_attend_time`, '%Y-%m-%d %H:%i:%s') AS '网点分配给服务兵时间'
        FROM
        worder_information wi
        LEFT JOIN worder_template wt ON wi.template_id = wt.id
        LEFT JOIN brand b on wt.brand_id = b.id
        LEFT JOIN company_information ci on wi.company_id = ci.company_id
        LEFT JOIN dot_information di ON wi.dot_id = di.dot_id
        LEFT JOIN sys_user su ON wi.create_by = su.user_id
        LEFT JOIN worder_child_information wci ON wci.worder_id = wi.worder_id AND wci.`balance_source` = '0' AND wci.`balance_type` = '0'
        LEFT JOIN worder_ext_field wef1 ON wef1.worder_no = wi.worder_no  AND wef1.field_id = 154
        LEFT JOIN worder_ext_field wef2 ON wef2.worder_no = wi.worder_no AND wef2.field_id = 1192
        LEFT JOIN worder_ext_field wef3 ON wef3.worder_no = wi.worder_no AND wef3.field_id = 1197
        LEFT JOIN worder_ext_field wef4 ON wef4.worder_no = wi.worder_no 	AND wef4.field_id = 950
        LEFT JOIN worder_ext_field wef5 ON wef5.worder_no = wi.worder_no  	AND wef5.field_id = 1716
        LEFT JOIN worder_ext_field wef6 ON wef6.worder_no = wi.worder_no  	AND wef6.field_id = 1733
        LEFT JOIN worder_ext_field wef7 ON wef7.worder_no = wi.worder_no  	AND wef7.field_id = 153
        LEFT JOIN worder_ext_field wef8 ON wef8.worder_no = wi.worder_no  	AND wef8.field_id = 1224
        LEFT JOIN worder_ext_field wef9 ON wef9.worder_no = wi.worder_no  	AND wef9.field_id = 1255
        left join worder_information_attribute wia on wi.worder_id = wia.worder_id
        and wia.attribute_code = 'presurvey-formal' and wia.attribute = 'presurvey'  and wia.is_delete = 0
        left join  worder_type wt1 on wi.worder_type_id = wt1.id


        LEFT JOIN v_worder_operation_record vwor ON wi.worder_no = vwor.worder_no


        WHERE
        wi.is_delete = 0

        <include refid="param"></include>


    </select>

    <sql id="param">
        <if test="worderNo != null and worderNo !=''">
            and wi.worder_no like concat('%',#{worderNo},'%')
        </if>
        <if test="brandList!=null  ">
            and wt.brand_id in
            <foreach collection="brandList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="worderExecStatus!=null  ">
            and wi.worder_exec_status in
            <foreach collection="worderExecStatus" item="item" open="(" separator="," close=")">
                <![CDATA['${item}']]>
            </foreach>
        </if>
        <if test="regionList!=null">
            and
            wi.area_id in(

            select DISTINCT districtId  from (
            select
            c.id districtId
            from biz_region a
            left join biz_region b on a.id = b.pid
            left join biz_region c on b.id = c.pid
            where a.id in
            <foreach collection="regionList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            UNION all
            select
            c.id districtId
            from biz_region a
            left join biz_region b on a.id = b.pid
            left join biz_region c on b.id = c.pid
            where b.id in
            <foreach collection="regionList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            UNION all
            select
            c.id districtId
            from biz_region a
            left join biz_region b on a.id = b.pid
            left join biz_region c on b.id = c.pid
            where c.id in
            <foreach collection="regionList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            ) a where  a.districtId is not null )
        </if>
        <if test="worderTypeId != null and worderTypeId !=''">
            and wt1.full_name = #{worderTypeId}
        </if>
        <if test="worderStatus != null and worderStatus!=''">
            and wi.worder_status = #{worderStatus}
        </if>


        <if test="worderSetStatus != null and worderSetStatus !=''">

                <if test="worderSetStatus == '工单待计算'">and wi.worder_set_status = 0</if>
                <if test="worderSetStatus == '车企待结算'">and  wi.worder_set_status = 1</if>
                <if test="worderSetStatus == '等待开票'">and wi.worder_set_status = 2</if>

                <if test="worderSetStatus == '已开票'">
                    and  ( wi.worder_set_status = 3 or (wci.type = 1 and wi.worder_set_status = 4 ))
                </if>
                <if test="worderSetStatus == '车企已回款'">and wi.worder_set_status = 4</if>
                <if test="worderSetStatus == '网点结算已发布'">and wi.worder_set_status = 5</if>
                <if test="worderSetStatus == '外部系统状态'">and wi.worder_set_status = 6</if>
                <if test="worderSetStatus == '工单已结算'">and wi.worder_set_status = 7</if>
                <if test="worderSetStatus == '厂商结算首次审核通过'">and wi.worder_set_status = 8</if>
                <if test="worderSetStatus == '厂商结算审核不通过'">and wi.worder_set_status = 9</if>
                <if test="worderSetStatus == '厂商结算二次审核通过'">and wi.worder_set_status = 10</if>
                <if test="worderSetStatus == '网点工单首次审核通过'">and wi.worder_set_status = 11</if>
                <if test="worderSetStatus == '网点工单二次审核通过'">and wi.worder_set_status = 12</if>
                <if test="worderSetStatus == '网点工单三次审核通过'"> and wi.worder_set_status = 13</if>
                <if test="worderSetStatus == '网点工单审核不通过'"> and wi.worder_set_status = 14</if>
                <if test="worderSetStatus == '不结算'"> and wi.worder_set_status = 99</if>
        </if>
        <if test="worderIncreStatus != null and worderIncreStatus !=''">
            <if test="worderIncreStatus == '用户已收费'"> and wi.worder_Incre_status = 0</if>
            <if test="worderIncreStatus == '增项待结算'">and wi.worder_Incre_status =1</if>
            <if test="worderIncreStatus == '增项结算已发布'">and wi.worder_Incre_status =2</if>
            <if test="worderIncreStatus == '外部系统状态'">and wi.worder_Incre_status =3</if>
            <if test="worderIncreStatus == '增项已结算'">and wi.worder_Incre_status =4</if>
            <if test="worderIncreStatus == '网点增项首次审核通过'">and wi.worder_Incre_status =5</if>
            <if test="worderIncreStatus == '网点增项二次审核通过'">and wi.worder_Incre_status =6</if>
            <if test="worderIncreStatus == '网点增项三次审核通过'">and wi.worder_Incre_status =7</if>
            <if test="worderIncreStatus == '网点增项审核不通过'">and wi.worder_Incre_status =8</if>
            <if test="worderIncreStatus == '收款记账中'">and wi.worder_Incre_status =9</if>
            <if test="worderIncreStatus == '收款已记账'">and wi.worder_Incre_status =10</if>
            <if test="worderIncreStatus == '收入成本记账中'">and wi.worder_Incre_status =11</if>
            <if test="worderIncreStatus == '增项结算发布中'">and wi.worder_Incre_status =32</if>
            <if test="worderIncreStatus == '已收款不记账'">and wi.worder_Incre_status =99</if>
        </if>
        <if test ="userName != null and userName !=''">
            and wi.user_name like concat('%',#{userName},'%')
        </if>
        <if test ="createBy != null">
            and su.employee_name = #{createBy}
        </if>
        <if test ="pmId != null">
            and wi.candidate_pm = #{pmId}
        </if>
        <if test="dotId != null and dotId !=''">
            and di.v_code = #{dotId}
        </if>
        <if test="serviceId != null">
            and wi.candidate_attendant = #{serviceId}
        </if>
        <if test="company != null and company!=''">
            and ci.company_name = #{company}
        </if>
        <if test="startTime != null and startTime !=''">
            and wi.create_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null and  endTime !=''">
            and wi.create_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="startSurveyTime != null and startSurveyTime !='' ">
            and vwor.convey_file_time  <![CDATA[>=]]> #{startSurveyTime}
        </if>
        <if test="endSurveyTime != null and endSurveyTime !='' ">
            and vwor.convey_file_time   <![CDATA[<=]]> #{endSurveyTime}
        </if>
        <if test="endSurveysTime != null and endSurveysTime !='' ">
            and  wi.convey_appoint_time  <![CDATA[<=]]> #{endSurveysTime}
        </if>
        <if test="startSurveysTime != null and startSurveysTime !='' ">
            and  wi.convey_appoint_time  <![CDATA[>=]]> #{startSurveysTime}
        </if>
        <if test="startInstallsTime != null and startInstallsTime !='' ">
            and  wi.install_appoint_time  <![CDATA[>=]]> #{startInstallsTime}
        </if>
        <if test="endInstallSurveysTime != null and endInstallSurveysTime !='' ">
            and  wi.install_appoint_time  <![CDATA[<=]]> #{endInstallSurveysTime}
        </if>
        <if test="startInstallTime != null and startInstallTime !='' ">
            and vwor.install_doc_time  <![CDATA[>=]]> #{startInstallTime}
        </if>
        <if test="endInstallSurveyTime != null and endInstallSurveyTime !='' ">
            and vwor.install_doc_time  <![CDATA[<=]]> #{endInstallSurveyTime}
        </if>
        <if test="companyOrderNumber != null and companyOrderNumber !=''">
            and  wi.company_order_number like concat('%',#{companyOrderNumber},'%')
        </if>
        <if test="areaFlag == 1">
            and wi.area_id in (
            select r.id
            from biz_region r where regcode REGEXP (
            select GROUP_CONCAT(DISTINCT(b.regcode) SEPARATOR '|')
            from sys_user s
            left join manager_area_id m on s.user_id = m.user_id
            left join biz_region b on m.area_id = b.id
            where s.user_id = #{userId}
            ) and r.type = 3
            )
        </if>
        <if test="brandFlag == 1">
            and wt.brand_id in (
            select DISTINCT(m.brand_id)
            from manager_area_id m
            left join sys_user s on m.user_id = s.user_id
            left join biz_region b on m.area_id = b.id
            where s.user_id = #{userId}
            )
        </if>

        <if test="startNextContactTime != null and startNextContactTime !='' ">
            and wi.next_contact_time  <![CDATA[>=]]> #{startNextContactTime}
        </if>
        <if test="endNextContactTime != null and endNextContactTime !='' ">
            and wi.next_contact_time  <![CDATA[<=]]> #{endNextContactTime}
        </if>
        <if test="startConfirmCompletionTime != null and startConfirmCompletionTime !='' ">
            and  wi.confirm_completion_time   <![CDATA[>=]]> #{startConfirmCompletionTime}
        </if>
        <if test="endConfirmCompletionTime != null and endConfirmCompletionTime !='' ">
            and  wi.confirm_completion_time   <![CDATA[<=]]> #{endConfirmCompletionTime}
        </if>
        order by wi.create_time desc
    </sql>

</mapper>