<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderDataManagerDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.ExtFieldEntity" id="extFieldMap">
        <result property="fieldId" column="field_id"/>
        <result property="fieldName" column="field_name"/>
        <result property="isNessary" column="is_nessary"/>
        <result property="fieldType" column="field_type"/>
        <result property="fieldTypeValue" column="field_type_value"/>
        <result property="fieldPurpose" column="field_purpose"/>
        <result property="fieldPurposeValue" column="field_purpose_value"/>
        <result property="fieldDesc" column="field_desc"/>
        <result property="fieldSort" column="field_sort"/>
        <result property="fieldGroup" column="field_group"/>
        <result property="filedDicKey" column="filed_dic_key"/>
        <result property="fieldClass" column="field_class"/>
        <result property="isNotnull" column="is_notnull"/>
        <result property="fileSample" column="file_sample"/>
    </resultMap>

    <select id="getByFieldId" resultType="com.bonc.rrs.worder.entity.ExtFieldEntity">
        select * from ext_field where field_id = #{fieldId}
    </select>

    <select id="getList"  resultMap="extFieldMap">
        select * from ext_field
        where field_class = 1 and is_nessary = 1 and deleted = 0
    </select>

    <select id="getFileUrl" resultType="com.bonc.rrs.worder.dto.vo.ExtFileVo">
        select file_id as fileId,old_name as fileName,path as fileUrl from sys_file
      where file_id = #{fileId}
    </select>

    <update id="updateExtField">
        update ext_field
        set
        <if test="fieldName != null and fieldName != ''">
            field_name = #{fieldName},
        </if>
        <if test="fieldType != null and fieldType != ''">
            field_type = #{fieldType},
        </if>
        <if test="fieldTypeValue != null and fieldTypeValue != ''">
            field_type_value = #{fieldTypeValue},
        </if>
        <if test="fieldPurpose != null and fieldPurpose != ''">
            field_purpose = #{fieldPurpose},
        </if>
        <if test="fieldPurposeValue != null and fieldPurposeValue != ''">
            field_purpose_value = #{fieldPurposeValue},
        </if>
        <if test="fieldDesc != null and fieldDesc != ''">
            field_desc = #{fieldDesc},
        </if>
        <if test="isNotnull != null and isNotnull != ''">
            is_notnull = #{isNotnull},
        </if>

        where field_id = #{fieldId}

    </update>
</mapper>