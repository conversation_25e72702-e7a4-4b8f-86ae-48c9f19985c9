<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.BizRegionMappingDao">

    <!-- ResultMap for BizRegionMapping entity -->
    <resultMap id="BizRegionMappingResultMap" type="com.bonc.rrs.worder.entity.BizRegionMappingEntity">
        <id property="id" column="id" />
        <result property="companyName" column="company_name" />
        <result property="name" column="name" />
        <result property="code" column="code" />
        <result property="bizRegionId" column="biz_region_id" />
    </resultMap>

    <!-- SQL statement to select all fields from the biz_region_mapping table -->
    <select id="selectAllBizRegionMapping" resultMap="BizRegionMappingResultMap">
        SELECT * FROM biz_region_mapping
    </select>
</mapper>