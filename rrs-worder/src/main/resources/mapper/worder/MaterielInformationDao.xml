<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.MaterielInformationDao">



    <!-- 可根据自己的需求，是否要使用 -->
   <select id="getMaterielInformation" resultType="com.bonc.rrs.worder.dto.dto.MaterielInformationDto">
       select
       mi.id as materielId,
       mi.materiel_brand ,
       mi.materiel_spec
       from
       sys_dictionary_detail sd ,
       materiel_information mi
       where
       mi.id = sd.detail_number
       and sd.id = 549
   </select>


</mapper>