<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.QueryRegionDao">
    <select id="selectReginCode"  resultType="map">
        select id,name from biz_region where 1=1

        <if test="uniqueCode == 0">
            and pid = 0
        </if>
        <if test="uniqueCode == 1">
            and id <![CDATA[<]]> 376 and id <![CDATA[>=]]> 32
        </if>
        <if test="uniqueCode == 2">
            and id <![CDATA[>=]]> 376
        </if>
    </select>

</mapper>