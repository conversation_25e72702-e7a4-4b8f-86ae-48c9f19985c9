<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.CompanyInformationDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.CompanyInformationEntity" id="companyInformationMap">
        <result property="companyId" column="company_id"/>
        <result property="companyNo" column="company_no"/>
        <result property="companyName" column="company_name"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyVcode" column="company_vcode"/>
        <result property="companyType" column="company_type"/>
        <result property="companyTypeValue" column="company_type_value"/>
        <result property="companySite" column="company_site"/>
        <result property="companyProperty" column="company_property"/>
        <result property="companyPropertyValue" column="company_property_value"/>
        <result property="address" column="address"/>
        <result property="addressDup" column="address_dup"/>
        <result property="mailCode" column="mail_code"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="contact" column="contact"/>
        <result property="contactWay" column="contact_way"/>
        <result property="companyMobile" column="company_mobile"/>
        <result property="companyIntroduction" column="company_introduction"/>
        <result property="companyCertificate" column="company_certificate"/>
        <result property="taxNo" column="tax_no"/>
        <result property="companyBank" column="company_bank"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankNumber" column="bank_number"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="insuranceAmount" column="insurance_amount"/>
        <result property="allowOverDay" column="allow_over_day"/>
        <result property="superiorCarCompany" column="superior_car_company"/>
        <result property="superiorSubCenter" column="superior_sub_center"/>

        <association property="companyContact" javaType="com.bonc.rrs.worder.entity.CompanyContactsEntity">
            <id property="contactsId" column="contacts_id"/>
            <result property="contactsAccount" column="contacts_account"/>
            <result property="contactsName" column="contacts_name"/>
            <result property="contactsPhone" column="contacts_phone"/>
            <result property="contactsEmail" column="contacts_email"/>
        </association>

        <collection property="companyBrandCars" ofType="com.bonc.rrs.worder.entity.CompanyBrandCarEntity">
            <id column="id" property="id" javaType="java.lang.Integer" />
            <result column="brand_id" property="brandId" javaType="java.lang.Integer" />
            <result column="brand_name" property="brandName" javaType="java.lang.String" />
            <result column="car_type_id" property="carTypeId" javaType="java.lang.Integer" />
            <result column="car_type_name" property="carTypeName" javaType="java.lang.String" />
        </collection>

    </resultMap>

    <select id="getCompanyInformation" resultType="com.bonc.rrs.worder.entity.CompanyInformationEntity">
        select
        company_id,
        company_name,
        company_type,
        company_property,
        contact,
        contact_way
        from company_information
        where is_delete = 0
        <if test="p.companyName != null and p.companyName != ''">
            and company_name like concat('%',#{p.companyName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="getByCompanyContact" resultType="com.bonc.rrs.worder.entity.CompanyInformationEntity">
        select
        i.company_id,
        i.company_name,
        i.company_type,
        i.company_property,
        i.contact,
        i.contact_way
        from company_information i
        left join company_contacts c on i.company_id = c.company_id
        where i.is_delete = 0
        <if test="p.companyName != null and p.companyName != ''">
            and i.company_name like concat('%',#{p.companyName},'%')
        </if>
        <if test="p.userId != null and p.userId != ''">
            and c.user_id = #{p.userId}
        </if>
        order by i.create_time desc
    </select>

    <select id="getByCompanyId" parameterType="java.lang.Integer" resultMap="companyInformationMap">
          select c.*,
           t.contacts_account,
           t.contacts_name,
           t.contacts_phone,
           t.contacts_email,
           m.brand_id,
           b.brand_name,
           m.car_type_id,
           p.car_type_name
           from company_information c
           left join company_contacts t on c.company_id = t.company_id
           left join company_brand_car m on c.company_id = m.company_id
           left join brand b on m.brand_id = b.id
           left join car_type p on m.car_type_id = p.id
           where c.company_id = #{companyId} and c.is_delete = 0
    </select>


    <select id="queryAccountWorderByInvoiceId" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.entity.WorderInformationEntity">
        select
            wi.*
        from
            worder_wait_account wwa
        inner join worder_information wi on
            wwa.worder_id = wi.worder_id
        where
            wwa.invoice_id = #{invoiceId}
            and wwa.worder_invoice_type = 0
            and wwa.status = 1
            and wwa.deleted = 0
            and wi.is_delete = 0
    </select>

    <insert id="addCompanyInformation" parameterType="com.bonc.rrs.worder.entity.CompanyInformationEntity" useGeneratedKeys="true" keyProperty="companyId">
          insert into company_information (
                company_no,
                company_name,
                company_code,
                company_vcode,
                company_type,
                company_site,
                company_property,
                address_dup,
                address,
                mail_code,
                create_time,
                is_delete,
                contact,
                contact_way,
                company_mobile,
                company_introduction,
                company_certificate,
                tax_no,
                company_bank,
                bank_name,
                bank_number,
                bank_account,
                insurance_amount,
                allow_over_day,
                superior_car_company,
                superior_sub_center)
          values (
                #{companyNo},
                #{companyName},
                #{companyCode},
                #{companyVcode},
                #{companyType},
                #{companySite},
                #{companyProperty},
                #{addressDup},
                #{address},
                #{mailCode},
                #{createTime},
                #{isDelete},
                #{contact},
                #{contactWay},
                #{companyMobile},
                #{companyIntroduction},
                #{companyCertificate},
                #{taxNo},
                #{companyBank},
                #{bankName},
                #{bankNumber},
                #{bankAccount},
                #{insuranceAmount},
                #{allowOverDay},
                #{superiorCarCompany},
                #{superiorSubCenter}
          )
    </insert>

    <select id="getBrandCarList" resultType="com.bonc.rrs.worder.entity.CompanyBrandCarEntity">
        select
        a.id as brandId,
        a.brand_name as brandName,
        c.id as carTypeId,
        c.car_type_name as carTypeName
        from brand a
        left join brand_car_type b on a.id = b.brand_id
        left join car_type c on b.car_type_id = c.id
    </select>

</mapper>