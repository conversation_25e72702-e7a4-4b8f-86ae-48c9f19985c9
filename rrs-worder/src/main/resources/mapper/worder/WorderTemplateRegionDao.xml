<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderTemplateRegionDao">

    <select id="listRegionRegcode" parameterType="java.lang.String"
            resultType="com.bonc.rrs.worder.entity.po.TemplateRegionRegcodePo">
        select b.id, b.regcode
        from worder_template_region a
        left join biz_region b on a.region_id = b.id
        where a.template_id = #{templateId}
    </select>

    <resultMap id="templateRegionMap" type="com.bonc.rrs.worder.entity.po.TemplateRegionPo">
        <result property="key" column="provinceId"></result>
        <result property="title" column="provinceName"></result>
        <collection property="children" ofType="com.bonc.rrs.worder.entity.po.TemplateRegionPo">
            <result property="key" column="cityId"></result>
            <result property="title" column="cityName"></result>
            <collection property="children" ofType="com.bonc.rrs.worder.entity.po.TemplateRegionPo">
                <result property="key" column="districtId"></result>
                <result property="title" column="districtName"></result>
            </collection>
        </collection>
    </resultMap>
    
    <select id="listTemplateRegion" parameterType="com.bonc.rrs.worder.entity.po.TemplateRegionRegcodePo"
        resultMap="templateRegionMap">
        select a.id provinceId, a.name provinceName, b.id cityId, b.name cityName
        , concat(a.id, '_', b.id, '_', c.id) districtId, c.name districtName
        from biz_region a
        left join biz_region b on a.id = b.pid
        left join biz_region c on b.id = c.pid
        <where>
            c.id is not null
            <if test="province != null and province != ''">
                and a.regcode = #{province}
            </if>
            <if test="city != null and city != ''">
                and b.regcode = #{city}
            </if>
            <if test="district != null and district != ''">
                and c.regcode = #{district}
            </if>
        </where>

    </select>

    <select id="listTemplateSuperRegion" parameterType="com.bonc.rrs.worder.entity.po.TemplateRegionRegcodePo"
            resultMap="templateRegionMap">
        select a.id provinceId, a.name provinceName
        <if test="city != null and city != ''">
            , b.id cityId, b.name cityName
        </if>
        <if test="district != null and district != ''">
            , c.id districtId, c.name districtName
        </if>
        from biz_region a
        <if test="city != null and city != ''">
            left join biz_region b on a.id = b.pid
        </if>
        <if test="district != null and district != ''">
            left join biz_region c on b.id = c.pid
        </if>
        <where>
            <if test="province != null and province != ''">
                and a.regcode = #{province}
            </if>
            <if test="city != null and city != ''">
                and b.regcode = #{city}
            </if>
            <if test="district != null and district != ''">
                and c.regcode = #{district}
            </if>
        </where>

    </select>

    <select id="listRegion" parameterType="com.bonc.rrs.worder.entity.po.TemplateRegionRegcodePo"
            resultType="com.bonc.rrs.worder.dto.vo.BizRegionVo">
        select a.id provinceId, a.name provinceName
        <if test="city != null and city != ''">
            , b.id cityId, b.name cityName
        </if>
        <if test="district != null and district != ''">
            , c.id areaId, c.name areaName
        </if>
        from biz_region a
        <if test="city != null and city != ''">
            left join biz_region b on a.id = b.pid
        </if>
        <if test="district != null and district != ''">
            left join biz_region c on b.id = c.pid
        </if>
        <where>
            <if test="province != null and province != ''">
                and a.regcode = #{province}
            </if>
            <if test="city != null and city != ''">
                and b.regcode = #{city}
            </if>
            <if test="district != null and district != ''">
                and c.regcode = #{district}
            </if>
        </where>
    </select>

</mapper>