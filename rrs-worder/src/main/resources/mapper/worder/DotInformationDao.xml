<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.DotInformationDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.DotInformationEntity" id="dotInformationMap">
        <result property="dotId" column="dot_id"/>
        <result property="dotNo" column="dot_no"/>
        <result property="dotName" column="dot_name"/>
        <result property="dotArea" column="dot_area"/>
        <result property="dotAddress" column="dot_address"/>
        <result property="dotCertificate" column="dot_ certificate"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>
    <select id="selectByDotIds" resultType="java.util.Map">
        select dot_id,dot_name from dot_information where dot_id in
        <foreach collection="dotIdList" item="dotId" open="(" close=")" separator=",">
            #{dotId}
        </foreach>
    </select>
    <select id="selectCountByDotAndBrand" resultType="java.lang.Integer">
        select db.brand_id from dot_brand db where 1=1
        <if test="dotId != null">
           and db.dot_id = #{dotId}
        </if>
        <if test="brandId != null">
            and db.brand_id = #{brandId}
        </if>
    </select>
    <select id="selectByDotValue" resultType="java.util.Map">
        select
            dot_id as dotId,
            dot_name as dotName
        from
            dot_information
        where dot_state = '1' and
            (dot_name like concat('%',#{dotValue},'%') or v_code like concat('%',#{dotValue},'%'))
    </select>

    <select id="getDotTaxPointByDotId" parameterType="Integer" resultType="String">
        select sddd.detail_name
        from dot_information di
        inner join (
            select sdd.detail_number, sdd.detail_name
            from sys_dictionary sd
            inner join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id
            where
                dic_number = 'tax_point') sddd on di.tax_point = sddd.detail_number
        where di.dot_id = #{dotId}
    </select>

    <select id="getListDotName" resultType="java.util.Map">
        select dot_id dotId, dot_name dotName from dot_information where is_delete = 0 and dot_state = '1'
    </select>

    <select id="getDotScore" resultType="Integer" parameterType="Integer">
        select di.dot_score from dot_information di where di.dot_id = #{dotId}
    </select>

    <update id="updateDotScore">
        update dot_information set dot_score = #{dotScore}, modify_score_user_id = #{userId}, modify_time = now() where dot_id = #{dotId}
    </update>

    <select id="selectDotByUserName" resultType="java.lang.String">
        select di.dot_name from dot_information di , dot_contacts dc where di.dot_code = dc.dot_code and dc.contacts_name = #{username} limit 1
    </select>
</mapper>