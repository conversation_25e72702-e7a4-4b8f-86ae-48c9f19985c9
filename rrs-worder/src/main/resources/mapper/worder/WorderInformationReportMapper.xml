<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.WorderInformationReportDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.rrs.worder.entity.WorderInformationReportEntity">
        <id column="id" property="id" />
        <result column="worder_no" property="worderNo" />
        <result column="company_order_number" property="companyOrderNumber" />
        <result column="create_time_new" property="createTimeNew" />
        <result column="company_order_time" property="companyOrderTime" />
        <result column="rn_no" property="rnNo" />
        <result column="suit_name" property="suitName" />
        <result column="pro" property="pro" />
        <result column="city" property="city" />
        <result column="county" property="county" />
        <result column="branch_name" property="branchName" />
        <result column="brand_name" property="brandName" />
        <result column="user_name" property="userName" />
        <result column="candidate_pm" property="candidatePm" />
        <result column="employee_name" property="employeeName" />
        <result column="candidate_branch" property="candidateBranch" />
        <result column="candidate_attendant" property="candidateAttendant" />
        <result column="template_id" property="templateId" />
        <result column="detail_namea" property="detailNamea" />
        <result column="detail_nameb" property="detailNameb" />
        <result column="detail_namec" property="detailNamec" />
        <result column="detail_named" property="detailNamed" />
        <result column="detail_namee" property="detailNamee" />
        <result column="paystate" property="paystate" />
        <result column="apply" property="apply" />
        <result column="invoice_code" property="invoiceCode" />
        <result column="cust_pm_time" property="custPmTime" />
        <result column="pm_send_dot_time" property="pmSendDotTime" />
        <result column="dot_attend_time" property="dotAttendTime" />
        <result column="convey_appoint_time" property="conveyAppointTime" />
        <result column="act_convey_date" property="actConveyDate" />
        <result column="install_appoint_time" property="installAppointTime" />
        <result column="act_install_date" property="actInstallDate" />
        <result column="convey_sign_time" property="conveySignTime" />
        <result column="install_sign_time" property="installSignTime" />
        <result column="pm_convey_audit_fail_time" property="pmConveyAuditFailTime" />
        <result column="cust_convey_audit_fail_time" property="custConveyAuditFailTime" />
        <result column="pm_install_audit_fail_time" property="pmInstallAuditFailTime" />
        <result column="cust_install_audit_fail_time" property="custInstallAuditFailTime" />
        <result column="convey_file_time" property="conveyFileTime" />
        <result column="pm_convey_audit_time" property="pmConveyAuditTime" />
        <result column="cust_convey_audit_time" property="custConveyAuditTime" />
        <result column="comp_convey_audit_time" property="compConveyAuditTime" />
        <result column="confirm_power_time" property="confirmPowerTime" />
        <result column="convey_book_time" property="conveyBookTime" />
        <result column="install_book_time" property="installBookTime" />
        <result column="install_doc_time" property="installDocTime" />
        <result column="pm_install_aduit_time" property="pmInstallAduitTime" />
        <result column="confirm_completion_time" property="confirmCompletionTime" />
        <result column="comp_install_aduit_time" property="compInstallAduitTime" />
        <result column="next_contact_time" property="nextContactTime" />
        <result column="is_auto" property="isAuto" />
        <result column="worder_remark" property="worderRemark" />
        <result column="create_time" property="createTime" />
        <result column="worder_type_id" property="worderTypeId" />
        <result column="area_id" property="areaId" />
        <result column="brand_id" property="brandId" />
        <result column="company_name" property="companyName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, worder_no, company_order_number, create_time_new, company_order_time, rn_no, suit_name, pro, city, county, branch_name, brand_name, user_name, candidate_pm, employee_name, candidate_branch, candidate_attendant, template_id, detail_namea, detail_nameb, detail_namec, detail_named, detail_namee, paystate, apply, invoice_code, cust_pm_time, pm_send_dot_time, dot_attend_time, convey_appoint_time, act_convey_date, install_appoint_time, act_install_date, convey_sign_time, install_sign_time, pm_convey_audit_fail_time, cust_convey_audit_fail_time, pm_install_audit_fail_time, cust_install_audit_fail_time, convey_file_time, pm_convey_audit_time, cust_convey_audit_time, comp_convey_audit_time, confirm_power_time, convey_book_time, install_book_time, install_doc_time, pm_install_aduit_time, confirm_completion_time, comp_install_aduit_time, next_contact_time, is_auto, worder_remark, create_time, worder_type_id, area_id, brand_id, company_name
    </sql>

</mapper>
