<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.WorderVersionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.rrs.worder.entity.WorderVersionEntity">
        <id column="id" property="id"/>
        <result column="time" property="time"/>
        <result column="sort" property="sort"/>
        <result column="version" property="version"/>
        <result column="company" property="company"/>
    </resultMap>

    <select id="getSort" parameterType="java.lang.String" resultType="java.lang.Integer">
        select nextval_safe('create_worder_no',#{currDate})
    </select>

    <select id="getBalanceSort" parameterType="java.lang.String" resultType="java.lang.Integer">
        select nextval_safe('create_balance_no',#{currDate})
    </select>

    <select id="getSuperviseNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select nextval_safe('create_supervise_no',#{currDate})
    </select>

</mapper>
