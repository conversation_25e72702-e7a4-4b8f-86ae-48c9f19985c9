<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.BizEmployeeDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.BizEmployeeEntity" id="bizEmployeeMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="contact" column="contact"/>
        <result property="idCard" column="id_card"/>
        <result property="departmentId" column="department_id"/>
        <result property="address" column="address"/>
        <result property="zipCode" column="zip_code"/>
        <result property="status" column="status"/>
        <result property="genderValue" column="gender_value"/>
    </resultMap>


</mapper>