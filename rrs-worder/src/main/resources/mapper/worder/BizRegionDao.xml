<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.BizRegionDao">
    <resultMap id="BaseResultMap" type="com.bonc.rrs.worder.entity.BizRegionEntity">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="pid" jdbcType="INTEGER" property="pid"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="code" jdbcType="INTEGER" property="code"/>
        <result column="regione" jdbcType="VARCHAR" property="regione"/>
    </resultMap>

    <resultMap id="RegionMap" type="com.bonc.rrs.worder.entity.RegionTreeEntity">
        <result property="key" column="provinceId"></result>
        <result property="title" column="provinceName"></result>
        <result property="value" column="provinceId"></result>
        <result property="label" column="provinceName"></result>
        <collection property="children" ofType="com.bonc.rrs.worder.entity.RegionTreeEntity">
            <result property="key" column="cityId"></result>
            <result property="title" column="cityName"></result>
            <result property="value" column="cityId"></result>
            <result property="label" column="cityName"></result>
            <collection property="children" ofType="com.bonc.rrs.worder.entity.RegionTreeEntity">
                <result property="key" column="districtId"></result>
                <result property="title" column="districtName"></result>
                <result property="value" column="districtId"></result>
                <result property="label" column="districtName"></result>
            </collection>
        </collection>
    </resultMap>

    <select id="listNationTree" parameterType="com.bonc.rrs.worder.entity.RegionTreeEntity"
            resultMap="RegionMap">
        select a.id provinceId, a.name provinceName, b.id cityId, b.name cityName
        , c.id districtId, c.name districtName
        from biz_region a
        left join biz_region b on a.id = b.pid
        left join biz_region c on b.id = c.pid
        <where>
            c.id is not null
            <if test="province != null and province != ''">
                and a.regcode = #{province}
            </if>
            <if test="city != null and city != ''">
                and b.regcode = #{city}
            </if>
            <if test="district != null and district != ''">
                and c.regcode = #{district}
            </if>
        </where>

    </select>

    <select id="getWorderRegionRegcode" parameterType="java.lang.String" resultType="com.bonc.rrs.worder.entity.BizRegionEntity">
        select b.regcode from worder_information a
        left join biz_region b on a.area_id = b.id
        where a.worder_no = #{worderNo}
    </select>
    <select id="selectAreaIdByIds" resultType="com.bonc.rrs.worder.entity.BizRegionEntity" parameterType="java.util.List">
        select b.id from biz_region b
        where b.`type` = 3
        and ( b.pid in ( select r.id from biz_region r
            <where>
                <foreach collection="ids" item="id" open="r.pid in (" close=") or" separator=",">
                    #{id}
                </foreach>
                <foreach collection="ids" item="id" open="r.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </where>
                )
            <foreach collection="ids" item="id" open="or b.id in (" close=")" separator=",">
                #{id}
            </foreach>
            )
    </select>

    <select id="selectParentRegions" resultMap="BaseResultMap">
        select
            t2.*
        from
            (
                select
                    @r as _id,
                    (
                        select
                            @r := pid
                        from
                            biz_region
                        where
                            id = _id) as parent_id,
                    @l := @l + 1 as lvl
                from
                    (
                        select
                            @r := #{id},
                            @l := 0) vars,
                    biz_region h
                where
                    @r != 0) T1
        join biz_region T2
        on
        T1._id = T2.id
        order by
        T1.lvl desc
    </select>

    <select id="getAllRegion" resultType="java.lang.String">
        select regione from biz_region br where regione is not null group by regione
    </select>

    <select id="getRegionByUser" resultType="java.lang.String">
        select br.regione
        from manager_area_id mai,
             biz_region br
        where mai.area_id = br.id
          and user_id = #{userId}
        group by br.regione
    </select>

    <select id="getProvinceByUser" resultMap="BaseResultMap">
        select
            distinct t1.id,t1.name,t1.regione,t1.lx_region,t1.tsl_region
        from
            biz_region t1 ,
            (
                select
                    substr(br.regcode, 1, 3) as regcode
                from
                    manager_area_id mai ,
                    biz_region br
                where
                    mai.area_id = br.id
                  and mai.user_id = #{userId}
                group by
                    br.regcode) t2
        where
            t1.regcode = t2.regcode
    </select>

    <select id="getListByUserAndRegCode" resultMap="BaseResultMap">
        select distinct br.*
        from manager_area_id mai,
             biz_region br
        where mai.area_id = br.id
          and mai.user_id = #{userId}
          and br.regcode like concat(#{regcode}, '%')
    </select>

    <select id="getProvinceByDotUser" resultType="com.bonc.rrs.worder.entity.BizRegionEntity">
        select
            distinct t1.id,
                     t1.name,
                     t1.regione,
                     t1.lx_region,
                     t1.tsl_region
        from
            biz_region t1 ,
            (
                select
                    substr(b.regcode, 1, 3) as regcode
                from
                    sys_user su ,
                    dot_contacts dc ,
                    dot_information di ,
                    dot_area da ,
                    biz_region b
                where
                    su.username = dc.contacts_name
                  and dc.dot_code = di.dot_code
                  and di.dot_id = da.dot_id
                  and da.area_id = b.id
                  and su.user_id = #{userId}
                group by
                    b.regcode) t2
        where
            t1.regcode = t2.regcode
    </select>
    <select id="getListByDotUserAndRegCode" resultType="com.bonc.rrs.worder.entity.BizRegionEntity">
        select
            distinct
            b.*
        from
            sys_user su ,
            dot_contacts dc ,
            dot_information di ,
            dot_area da ,
            biz_region b
        where
            su.username = dc.contacts_name
          and dc.dot_code = di.dot_code
          and di.dot_id = da.dot_id
          and da.area_id = b.id
          and su.user_id = #{userId}
          and b.regcode like concat(#{regcode}, '%')
    </select>

    <select id="getRegionByBydCode" resultType="com.bonc.rrs.worder.entity.BizRegionEntity">
        select br.* from biz_region br where br.byd_code = #{bydCode}
    </select>

    <select id="getTslRegion" resultType="string">
        select br.tsl_region from biz_region br group by br.tsl_region
    </select>

    <select id="getLxRegion" resultType="string">
        select br.lx_region from biz_region br group by br.lx_region
    </select>

    <select id="getOtherRegion" resultType="string">
        select br.regione from biz_region br group by br.regione
    </select>
    <select id="listNationTreeBrand" resultType="com.bonc.rrs.worder.entity.RegionTreeEntity" resultMap="RegionMap">
        select a.id provinceId, a.name provinceName, b.id cityId, b.name cityName
        , c.id districtId, c.name districtName
        from biz_region a
        left join biz_region b on a.id = b.pid
        left join biz_region c on b.id = c.pid
        <where>
            c.id is not null
            <choose>
                <when test="brandId == 18">
                    and a.lx_region = #{regione}
                </when>
                <when test="brandId == 14">
                    and a.tsl_region = #{regione}
                </when>
                <otherwise>
                    and a.regione = #{regione}
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="getListByAreaCode" resultType="java.lang.String">
        SELECT CONCAT(b3.id,',',b2.id,',',b1.id) from biz_region b1
                                          LEFT JOIN biz_region b2 on b1.pid = b2.id
                                          LEFT JOIN biz_region b3 on b2.pid = b3.id

        where b1.code = #{areaCode}
    </select>

    <select id="getRegionByName" resultMap="BaseResultMap">
        select id, code, pid
        from biz_region
        where type = #{level,jdbcType=INTEGER}
          AND name = #{regionName,jdbcType=VARCHAR}
        <if test="pid != null">
            AND pid = #{pid,jdbcType=BIGINT}
        </if>
    </select>

    <select id="getFirstDistrictByCityId" resultMap="BaseResultMap">
        SELECT br_district.id
        FROM biz_region br_city
                 JOIN biz_region br_district ON br_district.pid = br_city.id
        WHERE br_city.id = #{cityId,jdbcType=INTEGER}
          AND br_city.type = 2
          AND br_district.type = 3
        LIMIT 1
    </select>
</mapper>