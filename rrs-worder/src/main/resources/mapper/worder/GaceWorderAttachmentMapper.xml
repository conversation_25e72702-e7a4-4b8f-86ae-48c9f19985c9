<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.GaceWorderAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.worder.entity.GaceWorderAttachment">
    <!--@mbg.generated-->
    <!--@Table gace_worder_attachment-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="worder_id" jdbcType="BIGINT" property="worderId" />
    <result column="att_id" jdbcType="BIGINT" property="attId" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="digi_att_type" jdbcType="VARCHAR" property="digiAttType" />
    <result column="digi_att_type_com" jdbcType="VARCHAR" property="digiAttTypeCom" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, worder_id, att_id, seq, digi_att_type, digi_att_type_com
  </sql>
</mapper>