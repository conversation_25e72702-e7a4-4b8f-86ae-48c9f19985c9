<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.DotStarDesignDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.DotStarDesignEntity" id="dotStarDesignMap">
        <result property="id" column="id"/>
        <result property="starName" column="star_name"/>
        <result property="starMax" column="star_max"/>
        <result property="starMin" column="star_min"/>
        <result property="settleAccount" column="settle_account"/>
        <result property="countPeriod" column="count_period"/>
        <result property="sendScale" column="send_scale"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>



    <select id="listDotStar" resultMap="dotStarDesignMap">
        select *
        from dot_star_design
        where 1=1
        <if test="p.starName != null and p.starName != ''">
            and star_name = #{p.starName}
        </if>
        order by create_time
    </select>

    <insert id="addDotStar" parameterType="com.bonc.rrs.worder.entity.DotStarDesignEntity">
        insert into dot_star_design
        (star_name,star_max,star_min,settle_account,create_time)
        values (
        #{starName},
        #{starMax},
        #{starMin},
        #{settleAccount},
        current_timestamp
        )
    </insert>

    <update id="updateDotStar" parameterType="com.bonc.rrs.worder.entity.DotStarDesignEntity">
        update dot_star_design
        <set>
            <if test="starName != null and starName != ''">
                star_name = #{starName},
            </if>
            <if test="starMax != null">
                star_max = #{starMax},
            </if>
            <if test="starMin != null">
                star_min = #{starMin},
            </if>
            <if test="settleAccount != null">
                settle_account = #{settleAccount},
            </if>
            update_time = current_timestamp
        </set>
        where id = #{id}
    </update>

    <delete id="deleteDotStar" parameterType="java.lang.Integer">
        delete from dot_star_design where id = #{id}
    </delete>

    <select id="getDotStar" parameterType="java.lang.Integer" resultMap="dotStarDesignMap">
        select * from dot_star_design where id <![CDATA[!=]]> #{id}
    </select>

    <select id="getDotNumber" resultType="java.lang.Integer">
        select
        count(*)
        from dot_information d
        join dot_star_design s on s.star_min <![CDATA[<=]]> d.dot_score and s.star_max <![CDATA[>]]> d.dot_score
        where s.star_min = #{starMin} and s.star_max = #{starMax}
    </select>

    <select id="getStarById" parameterType="java.lang.Integer" resultMap="dotStarDesignMap">
        select *
        from dot_star_design
        where id = #{id}
    </select>

    <select id="getDotName" resultType="java.lang.String">
        select
        d.dot_name as dotName
        from dot_information d
        join dot_star_design s on s.star_min <![CDATA[<=]]> d.dot_score and s.star_max <![CDATA[>]]> d.dot_score
        where s.star_min = #{starMin} and s.star_max = #{starMax}
    </select>
</mapper>