<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderFactoryDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderFactoryEntity" id="worderFactoryMap">
        <result property="id" column="id"/>
        <result property="factoryOrderNo" column="factory_order_no"/>
        <result property="worderNo" column="worder_no"/>
        <result property="companyNo" column="company_no"/>
        <result property="sName" column="s_name"/>
        <result property="sPhone" column="s_phone"/>
        <result property="factoryDate" column="factory_date"/>
        <result property="factoryArea" column="factory_area"/>
        <result property="rrsGm" column="rrs_gm"/>
        <result property="pileCd" column="pile_cd"/>
        <result property="pileSpecs" column="pile_specs"/>
        <result property="pileModel" column="pile_model"/>
        <result property="pileNo" column="pile_no"/>
        <result property="isSend" column="is_send"/>
        <result property="pileNumber" column="pile_number"/>
        <result property="vin" column="vin"/>
        <result property="carModel" column="car_model"/>
        <result property="buyDate" column="buy_date"/>
        <result property="brand" column="brand"/>
    </resultMap>


</mapper>