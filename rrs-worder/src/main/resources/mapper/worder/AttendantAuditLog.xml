<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.AttendantAuditLogDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.AttendantAuditLogEntity" id="attendantAuditLogMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="auditeeId" column="auditee_id"/>
        <result property="auditTitle" column="audit_title"/>
        <result property="auditResult" column="audit_result"/>
        <result property="auditSuggestion" column="audit_suggestion"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>