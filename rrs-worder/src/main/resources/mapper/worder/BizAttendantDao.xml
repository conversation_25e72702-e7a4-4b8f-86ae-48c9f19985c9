<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.BizAttendantDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.BizAttendantEntity" id="bizAttendantMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="contact" column="contact"/>
        <result property="idCard" column="id_card"/>
        <result property="dotId" column="dot_id"/>
        <result property="electricianCertificate" column="electrician_certificate"/>
        <result property="idCardImg" column="id_card_img"/>
        <result property="idCardImgBack" column="id_card_img_back"/>
        <result property="genderValue" column="gender_value"/>
        <result property="electricianCertificateContrary" column="electrician_certificate_contrary"/>
        <result property="oneInchPhoto" column="one_inch_photos"/>
    </resultMap>


    <update id="updateContactByUserId">
        update biz_attendant set contact = #{contact} where user_id = #{userId}
    </update>


    <select id="getAdminList" resultType="com.bonc.rrs.worder.entity.BizAttendantEntity">
        select b.*,d.dot_name,su.username, af_dic.name 'attendant_flag_value', tl_dic.name 'technician_level_val'
        from biz_attendant b
        left join dot_information d on d.dot_id = b.dot_id
        left join sys_user su on su.user_id = b.user_id
        left join (
            select
                sdd.detail_number 'code',
                sdd.detail_name 'name'
            from
                sys_dictionary sd
            inner join sys_dictionary_detail sdd on
                sd.id = sdd.dictionary_id
            where
                sd.dic_number = 'attendant_flag'
        ) af_dic on b.attendant_flag = af_dic.code
        left join (
            select
            sdd.detail_number 'code',
            sdd.detail_name 'name'
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'technician_level'
        ) tl_dic on b.technician_level = tl_dic.code
        where b.attendant_state != 3
        <if test="p.name != null and p.name != ''">
            and b.name like concat('%',#{p.name},'%')
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and b.dot_id = #{p.dotId}
        </if>
        <if test="p.contact != null and p.contact != ''">
            and b.contact = #{p.contact}
        </if>
        <if test="p.technicianLevel != null and p.technicianLevel != ''">
            and b.technician_level = #{p.technicianLevel}
        </if>
        <if test="p.attendantState != null and p.attendantState != ''">
            and b.attendant_state = #{p.attendantState}
        </if>
        <if test="p.workLicenseNo != null and p.workLicenseNo != ''">
            and b.work_license_no = #{p.workLicenseNo}
        </if>
        <if test="p.attendantFlag != null and p.attendantFlag != ''">
            and b.attendant_flag = #{p.attendantFlag}
        </if>

        <if test="p.userId != null">
            and exists (
                select
                    1
                from
                    (
                        select
                            da.dot_id, GROUP_CONCAT(distinct(concat(da_br.regcode, '.*')) separator '|') regcodes
                        from
                            dot_area da
                        inner join biz_region da_br on
                            da.area_id = da_br.id
                        group by
                            da.dot_id
                    ) dga
                    inner join
                    (
                        select
                            GROUP_CONCAT(distinct(concat(mai_br.regcode, '.*')) separator '|') regcodes
                        from
                            manager_area_id mai
                        inner join biz_region mai_br on
                            mai.area_id = mai_br.id
                            and mai.user_id = #{p.userId}
                    ) mga
                    on
                        dga.regcodes regexp mga.regcodes
                        or mga.regcodes regexp dga.regcodes
                where
                dga.dot_id = d.dot_id
            )
        </if>

    </select>
    <select id="getDotList" resultType="com.bonc.rrs.worder.entity.BizAttendantEntity">
        select b.*,d.dot_name
        from biz_attendant b
        left join dot_information d on d.dot_id = b.dot_id
        where b.attendant_state != 3
        <if test="p.name != null and p.name != ''">
            and b.name like concat('%',#{p.name},'%')
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and b.dot_id = #{p.dotId}
        </if>
    </select>

    <select id="getBizAttendant" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.entity.BizAttendantEntity">
        select b.*,d.dot_name
        from biz_attendant b
        left join dot_information d on d.dot_id = b.dot_id
        where b.id = #{id}
    </select>

    <insert id="addOperation" parameterType="com.bonc.rrs.worder.dto.vo.AuditAttendantVo">
        insert into worder_operation_record
        (user_id,
        operation_user,
        create_time,
        record,
        affected_user_id,
        affected_user,
        type)
        values(
        #{userId},
        #{operationUser},
        #{createTime},
        #{record},
        #{attendantId},
        #{attendantName},
        #{type}
        )
    </insert>

    <!--获取网点信息-->
    <select id="getDot" resultType="java.util.Map">
        select dot_id as dotId,dot_name as dotName , v_code as vCode  from dot_information
        where 1=1 and is_delete = 0 order by convert(dot_name using gbk)
    </select>

    <select id="getDotByRegionBrandGroup" resultType="java.util.Map">
        select
            distinct
                dga.dot_id as dotId, dga.dot_name as dotName
            from
                (
                    select
                        da.dot_id, di.dot_name,
                        GROUP_CONCAT(distinct(concat('-', db.brand_id , '-')) separator '|') brand_ids,
                        GROUP_CONCAT(distinct(
                            case db.service_type
                                when 0 then '1|2|3'
                                else db.service_type
                            end
                        ) separator '|') service_types,
                        GROUP_CONCAT(distinct(concat(da_br.regcode, '.*')) separator '|') regcodes
                    from
                        dot_area da
                    inner join dot_brand db on
                        da.dot_id = db.dot_id and da.group_id = db.group_id
                    inner join dot_information di on
                        da.dot_id = di.dot_id
                    inner join biz_region da_br on
                        da.area_id = da_br.id
                    group by
                        da.dot_id, db.group_id, db.child_group_id
                ) dga
            inner join
                (
                    select
                        mai.user_id,
                        GROUP_CONCAT(distinct(mai.brand_id) separator '|') brand_ided,
                        GROUP_CONCAT(distinct(concat('-', mai.brand_id , '-')) separator '|') brand_ids,
                        GROUP_CONCAT(distinct(
                            case mai.service_type
                                when 0 then '1|2|3'
                                else mai.service_type
                            end
                        ) separator '|') service_types,
                        GROUP_CONCAT(distinct(concat(mai_br.regcode, '.*')) separator '|') regcodes
                    from
                        manager_area_id mai
                    inner join biz_region mai_br on
                        mai.area_id = mai_br.id
                        and mai.user_id = #{userId}
                    group by
                        mai.user_id, mai.group_id, mai.child_group_id
                ) mga
            on
                (dga.regcodes regexp mga.regcodes and dga.brand_ids regexp mga.brand_ids and dga.service_types regexp mga.service_types)
                or (mga.regcodes regexp dga.regcodes and mga.brand_ids regexp dga.brand_ids and mga.service_types regexp dga.service_types)
        order by dga.dot_id
    </select>

    <select id="getDotByDotId" resultType="java.util.Map">
        select di.dot_id as dotId,di.dot_name as dotName , di.v_code as vCode from sys_user su
        inner join dot_contacts dc on dc.contacts_name = su.username
        inner join dot_information di on di.dot_code = dc.dot_code
        where di.is_delete = 0 and su.user_id = #{userId}
    </select>

    <select id="exportAttendant" resultType="com.bonc.rrs.worder.entity.vo.BizAttendantExcelVo">
        select
        b.id,
        b.name,
        b.gender,
        b.contact,
        b.id_card,
        b.dot_id,
        b.technician_level,
        b.electrician_certificate,
        b.electrician_effective_time,
        b.id_card_img,
        b.id_card_img_back,
        b.id_card_effective_time,
        b.branch_contract,
        b.branch_effective_time,
        b.insurance_contract,
        b.insurance_amount,
        b.insurance_effective_time,
        b.gender_value,
        CASE
        WHEN technician_level = 1 THEN '服务工程师'
        WHEN technician_level = 2 THEN '金牌工程师'
        WHEN technician_level = 3 THEN '辅助工程师'
        ELSE '未知'
        END                 AS technician_level_value,
        b.user_id,
        b.attendant_state,
        b.work_contract,
        b.work_effective_time,
        b.create_time,
        b.update_time,
        b.region_id,
        b.electrician_certificate_contrary,
        b.one_inch_photo,
        b.attendant_flag,
        b.work_license_no,
        i.dot_name,r.`name` as regionName,s.username,(CASE WHEN b.attendant_state = 0 THEN '待审核'
        WHEN b.attendant_state = 1 THEN '审核通过'
        WHEN b.attendant_state = 2 THEN '审核未通过'
        END)  attendantState,
        (
            case when b.attendant_flag = 1 then '有效'
            when b.attendant_flag = -1 then '黑名单'
            when b.attendant_flag = 0 then '离职'
            when b.attendant_flag = 2 then '无效'
            when b.attendant_flag = 3 then '请假'
            end
        ) as attendantFlag,
        i.v_code as codeV,i.contact_name,i.dot_code,i.branch_name,(select name from biz_region where id = i.dot_area) as dotArea,(select name from biz_region where id = i.dot_city) as dotCity  from biz_attendant b
        left join dot_information i on b.dot_id=i.dot_id
        left join biz_region r on b.region_id=r.id
        left join sys_user s on b.user_id=s.user_id
        <where>
        <if test="p.name != null and p.name != ''">
            and  b.name like concat('%',#{p.name},'%')
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and b.dot_id = #{p.dotId}
        </if>
        <if test="p.attendantState != null and p.attendantState != ''">
            and b.attendant_state = #{p.attendantState}
        </if><if test="p.userId != null">
            and exists (
            select
            1
            from
            (
            select
            da.dot_id, GROUP_CONCAT(distinct(concat(da_br.regcode, '.*')) separator '|') regcodes
            from
            dot_area da
            inner join biz_region da_br on
            da.area_id = da_br.id
            group by
            da.dot_id
            ) dga
            inner join
            (
            select
            GROUP_CONCAT(distinct(concat(mai_br.regcode, '.*')) separator '|') regcodes
            from
            manager_area_id mai
            inner join biz_region mai_br on
            mai.area_id = mai_br.id
            and mai.user_id = #{p.userId}
            ) mga
            on
            dga.regcodes regexp mga.regcodes
            or mga.regcodes regexp dga.regcodes
            where
            dga.dot_id = i.dot_id
            )
        </if>
        </where>
    </select>

    <select id="getAttendantByDotId" resultType="com.bonc.rrs.worder.entity.BizAttendantEntity">
        select ba.*,
               di.dot_name,
               di.contact_name,
               di.v_code
        from biz_attendant ba
                 inner join dot_information di
        where ba.dot_id = di.dot_id and di.dot_id in
        <foreach collection="param.dotIdList" item="dot" open="(" close=")" separator=",">
            #{dot}
        </foreach>
        <if test="param.name != null and param.name != ''">
            and ba.name like concat('%',#{param.name},'%')
        </if>
        <if test="param.dotId != null">
            and di.dot_id = #{param.dotId}
        </if>
        <if test="param.contactName != null and param.contactName != ''">
            and di.contact_name like concat('%',#{param.contactName},'%')
        </if>
        order by ba.update_time desc
    </select>
    <select id="findByContent" resultType="java.util.HashMap">
        select d.detail_number, d.detail_name,d.remark
        from sys_dictionary s
                 inner join sys_dictionary_detail d on s.id = d.dictionary_id
        where dic_number = #{dicNumber} and d.detail_number=#{state}
    </select>

    <select id="getNewWorkLicenseNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select next_automatic_value_safe('work_license_no')
    </select>

    <select id="getTechnicianLevelList" parameterType="string" resultType="com.bonc.rrs.worder.entity.dto.TechnicianLevelDto">
        select
            sdd.detail_number id,
            sdd.detail_name name
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'technician_level'
        <if test="source != null and source == 'app'">
            and sdd.detail_number != '2'
        </if>
    </select>

    <select id="queryBecomeDueServiceId" resultType="integer">
        select
            ba.id
        from
            biz_attendant ba
        where
            ba.attendant_flag not in ('-1', '0', '2')
            and STR_TO_DATE(ba.electrician_effective_time, '%Y-%m-%d') <![CDATA[<]]> current_date()
    </select>

    <select id="queryBecomeDueServiceWarning" resultType="com.bonc.rrs.worder.entity.BizAttendantEntity">
        select
            ba.*
        from
            biz_attendant ba
        where
            ba.attendant_flag not in ('-1', '0', '2')
            and ba.electrician_effective_time != ''
            and STR_TO_DATE(ba.electrician_effective_time, '%Y-%m-%d') <![CDATA[<]]> DATE_ADD(CURDATE(), INTERVAL 45 day)
            and STR_TO_DATE(ba.electrician_effective_time, '%Y-%m-%d') <![CDATA[>=]]> DATE_ADD(CURDATE(), INTERVAL 44 day)
    </select>

    <update id="updateBecomeDueByServiceId" parameterType="integer">
        update biz_attendant set attendant_flag = 2 where id in
        <foreach collection="serviceIdList" item="serviceId" open="(" close=")" separator=",">
            #{serviceId}
        </foreach>
    </update>

    <select id="getServiceUserCountByMobile" resultType="integer">
        select
            count(*)
        from
            sys_user su
        inner join sys_user_role sur on
            su.user_id = sur.user_id
        where
            sur.role_id = 5
            and su.status = 1
            and su.mobile = #{mobile}
        <if test="neUserId != null and neUserId > 0">
            and su.user_id != #{neUserId}
        </if>
    </select>

    <select id="getAuditRemark" parameterType="integer" resultType="string">
        select
            wor.record
        from
            worder_operation_record wor
        where
            wor.affected_user_id = #{attendantId}
            and wor.`type` = 4
        order by
            create_time desc
        limit 1
    </select>
</mapper>