<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.WorderTypeDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.rrs.worder.entity.WorderTypeEntity">
        <id column="id" property="id"/>
        <result column="pid" property="pid"/>
        <result column="pids" property="pids"/>
        <result column="level" property="level"/>
        <result column="name" property="name"/>
        <result column="full_name" property="fullName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="baseWorderType" type="com.bonc.rrs.worder.dto.vo.WorderTypeVo">
        <result column="pname" property="pname"/>
        <association property="worderType" javaType="com.bonc.rrs.worder.entity.WorderTypeEntity">
            <id property="id" column="id"/>
            <result property="pid" column="pid"/>
            <result property="pids" column="pids"/>
            <result property="level" column="level"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="createTime" column="create_time"/>
            <result property="updateTime" column="update_time"/>
        </association>
    </resultMap>
    <select id="getList" parameterType="java.lang.Integer" resultMap="baseWorderType">
        select a.name as pname,b.id,
        b.pid,b.pids,b.level,b.name,b.full_name,
        b.create_time,b.update_time
        from worder_type a
        join worder_type b on b.pid = a.id
        where 1=1
        <if test="p.id != null and p.id != ''">
            and b.id = #{p.id}
        </if>

    </select>
</mapper>
