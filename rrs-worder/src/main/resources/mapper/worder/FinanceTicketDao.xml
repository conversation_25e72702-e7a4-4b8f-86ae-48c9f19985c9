<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.FinanceTicketDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.FinanceTicketEntity" id="financeTicketMap">
        <result property="ticketId" column="ticket_id"/>
        <result property="ticketNo" column="ticket_no"/>
        <result property="ticketType" column="ticket_type"/>
        <result property="ticketTypeValue" column="ticket_type_value"/>
        <result property="ticketHead" column="ticket_head"/>
        <result property="ticketSign" column="ticket_sign"/>
        <result property="ticketChoice" column="ticket_choice"/>
        <result property="ticketNumber" column="ticket_number"/>
        <result property="ticketStatus" column="ticket_status"/>
        <result property="ticketStatusValue" column="ticket_status_value"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>