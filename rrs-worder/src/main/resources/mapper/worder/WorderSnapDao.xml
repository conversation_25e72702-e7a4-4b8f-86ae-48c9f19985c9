<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderSnapDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderSnapEntity" id="worderSnapMap">
        <result property="id" column="id"/>
        <result property="worderNo" column="worder_no"/>
        <result property="snapType" column="snap_type"/>
        <result property="snapTypeValue" column="snap_type_value"/>
        <result property="snapLevel" column="snap_level"/>
        <result property="snapDesc" column="snap_desc"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>