<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderSendAuditDao">

    <select id="queryInProcessSendPointAudit" resultType="integer" parameterType="integer">
        select
            count(*)
        from
            worder_send_audit w
        where
            w.status = 0
            and w.worder_id = #{worderId}
    </select>

    <select id="queryAutoSendReason" resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity">
        select
            sdd.*
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'auto_send_reason'
    </select>

    <select id="queryAutoSendList" resultType="com.bonc.rrs.workManager.entity.vo.WorderSendAuditVo">
        select
            wsa.id,
            wsa.worder_id,
            wi.worder_no ,
            wi.worder_type_id ,
            wt.full_name worder_type_name,
            wi.worder_exec_status,
            wes2.detail_name worder_exec_status_name,
            wi.worder_exec_status,
            wsa.after_dot_id ,
            di.dot_name after_dot_name,
            wsa.before_dot_id ,
            di2.dot_name before_dot_name,
            wsa.modify_cause ,
            wsa.send_time,
            CONVERT(wsa.status,CHAR) status,
            wsa.`type`,
            wsa.updated_time,
            adbe.name audit_user,
            pmbe.name pm_name,
            b.brand_name,
            wi.company_order_number,
            wsa.fail_remark,
            wsa.create_time,
            adbe2.name create_by_user
        from
            worder_send_audit wsa
        inner join worder_information wi on
            wsa.worder_id = wi.worder_id
        inner join worder_type wt on
            wi.worder_type_id = wt.id
        inner join worder_template wt2 on
            wi.template_id = wt2.id

        <if test="p.areaFlag == 1">
            inner join (
                select  r.id
                from biz_region r where regcode REGEXP (
                    select GROUP_CONCAT(DISTINCT(b.regcode) SEPARATOR '|')
                    from sys_user s
                    left join manager_area_id m on s.user_id = m.user_id
                    left join biz_region b on m.area_id = b.id
                    where s.user_id = #{p.userId}
                ) and r.type = 3
            ) r on wi.area_id= r.id
        </if>
        <if test="p.brandFlag == 1">
            inner join (
                select DISTINCT(m.brand_id)
                from manager_area_id m
                left join sys_user s on m.user_id = s.user_id
                left join biz_region b on m.area_id = b.id
                where s.user_id = #{p.userId}
                <if test="p.brandId !=null and p.brandId != ''">
                    AND m.brand_id = #{p.brandId}
                </if>
            ) b on wt2.brand_id = b.brand_id
        </if>
        left join dot_information di on
            wsa.after_dot_id = di.dot_id
        left join dot_information di2 on
            wsa.before_dot_id = di2.dot_id
        left join sys_user_employee pmsue on
            wsa.pm_id = pmsue.user_id
        left join biz_employee pmbe on
            pmsue.employee_id = pmbe.id
        left join brand b on
            wt2.brand_id = b.id
        left join sys_user_employee adsue on
            wsa.audit_user_id = adsue.user_id
        left join biz_employee adbe on
            adsue.employee_id = adbe.id
        left join sys_user_employee adsue2 on
            wsa.create_by_id = adsue2.user_id
        left join biz_employee adbe2 on
            adsue2.employee_id = adbe2.id
        left join (
            select
                cast(sdd.detail_number as CHAR) detail_number, sdd.detail_name
            from
                sys_dictionary sd
            inner join sys_dictionary_detail sdd on
                sd.id = sdd.dictionary_id
            where
                sd.dic_number = 'worder_exec_status') wes on
            wsa.worder_exec_status = wes.detail_number
        left join (
            select
                sdd.detail_number, sdd.detail_name
            from
                sys_dictionary sd
            inner join sys_dictionary_detail sdd on
                sd.id = sdd.dictionary_id
            where
                sd.dic_number = 'worder_exec_status') wes2 on
            wi.worder_exec_status = wes2.detail_number
        where
            1 = 1
        <if test="p.status != null">
            and wsa.status = #{p.status}
        </if>
        <if test="p.pmName != null and p.pmName !=''">
            and pmbe.name = #{p.pmName}
        </if>
        <if test="p.auditName != null and p.auditName !=''">
            and adbe.name = #{p.auditName}
        </if>
        <if test="p.afterDotId != null and p.afterDotId !=''">
            and wsa.after_dot_id = #{p.afterDotId}
        </if>
        <if test="p.sendTimeStart != null and p.sendTimeStart !=''">
            and wsa.send_time <![CDATA[>=]]> #{p.sendTimeStart}
        </if>
        <if test="p.sendTimeEnd != null and p.sendTimeEnd !=''">
            and wsa.send_time <![CDATA[<=]]> #{p.sendTimeEnd}
        </if>

        <if test="p.updatedTimeStart != null and p.updatedTimeStart !=''">
            and wsa.updated_time <![CDATA[>=]]> #{p.updatedTimeStart}
        </if>
        <if test="p.updatedTimeEnd != null and p.updatedTimeEnd !=''">
            and wsa.updated_time <![CDATA[<=]]> #{p.updatedTimeEnd}
        </if>
        <if test="p.worderNo != null and p.worderNo !=''">
            and wi.worder_no = #{p.worderNo}
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId !=''">
            and wi.worder_type_id = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus !=''">
            and wi.worder_status = #{p.worderStatus}
        </if>
        <if test="p.worderExecStatus != null and p.worderExecStatus !=''">
            and wi.worder_exec_status = #{p.worderExecStatus}
        </if>

        <if test="p.brandId != null and p.brandId !=''">
            and wt2.brand_id = #{p.brandId}
        </if>

        <if test="p.areas != null and p.areas.size() > 0">
            and wi.area_id in (
                select b.id from biz_region a,biz_region b  where b.pid=a.id  and a.pid in
                <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            and wi.area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        order by wsa.create_time desc
    </select>


    <select id="querySendAuditDetail" resultType="com.bonc.rrs.workManager.entity.vo.WorderSendAuditDetailVo" parameterType="integer">
        select
            wsa.id,
            afterdi.dot_id afterDotId,
            afterdi.dot_name afterDotName,
            beforedi.dot_id beforeDotId,
            beforedi.dot_name beforeDotName,
            wsa.fail_remark,
            wsa.modify_cause
        from
            worder_send_audit wsa
        left join dot_information afterdi on
            wsa.after_dot_id = afterdi.dot_id
        left join dot_information beforedi on
            wsa.before_dot_id = beforedi.dot_id
        where
            wsa.id = #{sendAuditId}
    </select>

    <update id="submitSendAuditUpdate" >
        update worder_send_audit set status = #{status}, audit_user_id = #{auditUserId}, fail_remark = #{failRemark}, updated_time = now() where id = #{id}
    </update>

    <select id="queryDotNameByDotId" resultType="string" parameterType="integer">
        select di.dot_name from dot_information di where di.dot_id = #{dotId}
    </select>

    <select id="queryWorderNoByWorderId" resultType="string" parameterType="integer">
        select wi.worder_no from worder_information wi where wi.worder_id = #{worderId}
    </select>
</mapper>