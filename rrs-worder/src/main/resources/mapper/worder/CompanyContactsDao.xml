<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.CompanyContactsDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.CompanyContactsEntity" id="companyContactsMap">
        <result property="contactsId" column="contacts_id"/>
        <result property="companyId" column="company_id"/>
        <result property="contactsAccount" column="contacts_account"/>
        <result property="contactsName" column="contacts_name"/>
        <result property="contactsPhone" column="contacts_phone"/>
        <result property="contactsEmail" column="contacts_email"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


    <select id="getByCompanyId" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.entity.CompanyContactsEntity">
        select contacts_id,
           company_id,
           contacts_account,
           contacts_name,
           contacts_phone,
           contacts_email,
           user_id,
           create_time,
           modify_time,
           is_delete
           from company_contacts
           where company_id = #{companyId}
    </select>

    <update id="updateByCompanyId" parameterType="com.bonc.rrs.worder.entity.CompanyContactsEntity">
        update company_contacts
        set contacts_account = #{contactsAccount},
        contacts_name = #{contactsName},
        contacts_phone = #{contactsPhone},
        contacts_email = #{contactsEmail}
        where company_id = #{companyId}
    </update>

    <delete id="deleteByCompanyId" parameterType="java.lang.Integer">
        delete from company_contacts where company_id = #{companyId}
    </delete>
</mapper>