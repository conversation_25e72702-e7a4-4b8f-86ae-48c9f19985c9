<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.ExtFieldFactoryDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.ExtFieldFactoryEntity" id="extFieldFactoryMap">
        <result property="id" column="id"/>
        <result property="companyNo" column="company_no"/>
        <result property="fieldId" column="field_id"/>
        <result property="fieldSort" column="field_sort"/>
        <result property="fieldGroup" column="field_group"/>
        <result property="filedDicKey" column="filed_dic_key"/>
        <result property="fieldName" column="field_name"/>
        <result property="isNessary" column="is_nessary"/>
        <result property="fieldType" column="field_type"/>
        <result property="fieldTypeValue" column="field_type_value"/>
        <result property="fieldPurpose" column="field_purpose"/>
        <result property="fieldPurposeValue" column="field_purpose_value"/>
        <result property="fieldGroupValue" column="field_group_value"/>
        <result property="fieldDesc" column="field_desc"/>
    </resultMap>


</mapper>