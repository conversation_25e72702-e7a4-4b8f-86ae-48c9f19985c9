<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worder.dao.WorderOperationRecordMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.worder.entity.WorderOperationRecord">
    <!--@mbg.generated-->
    <!--@Table worder_operation_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="operation_user" jdbcType="VARCHAR" property="operationUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="record" jdbcType="VARCHAR" property="record" />
    <result column="affected_user_id" jdbcType="BIGINT" property="affectedUserId" />
    <result column="affected_user" jdbcType="VARCHAR" property="affectedUser" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="worder_no" jdbcType="VARCHAR" property="worderNo" />
    <result column="accept_send" jdbcType="INTEGER" property="acceptSend" />
    <result column="dot_code" jdbcType="VARCHAR" property="dotCode" />
    <result column="worder_status" jdbcType="INTEGER" property="worderStatus" />
    <result column="worder_exec_status" jdbcType="INTEGER" property="worderExecStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, operation_user, create_time, record, affected_user_id, affected_user, 
    `type`, worder_no, accept_send, dot_code, worder_status, worder_exec_status
  </sql>
</mapper>