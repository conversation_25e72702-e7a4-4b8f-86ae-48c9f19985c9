<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderInformationAttributeDao">
    <insert id="insertBatch" parameterType="com.bonc.rrs.worder.entity.WorderInformationAttributeEntity">
        insert into
        rrs_pro.worder_information_attribute (worder_id, attribute_code, attribute_name, attribute_value, is_delete, `attribute`, create_time)
        values
        <foreach collection="worderInformationAttributeList" item="w" separator=",">
            (#{w.worderId}, #{w.attributeCode}, #{w.attributeName}, #{w.attributeValue}, 0, 'addedMaterialType', now());
        </foreach>

    </insert>

    <insert id="insertOutBound">
        insert into worder_information_attribute (worder_id, attribute_code, attribute_name, attribute_value, is_delete, `attribute`)
        values(#{worderId},'LeaveOrderNumber',#{fieldName}, #{leaveCode}, 0, 'NoOutbound');
    </insert>

    <update id="updateDelete">
        update worder_information_attribute set is_delete = 1 where worder_id = #{worderId} and attribute = #{attribute}  and attribute_code = #{attributeCode}
    </update>
    <select id="selectAttributeByWorderNo"
            resultType="com.bonc.rrs.worder.entity.WorderInformationAttributeEntity">
        select
            wia.*
        from
            worder_information wi ,
            worder_information_attribute wia
        where
            wi.worder_id = wia.worder_id
            and wia.is_delete = 0
          and wi.worder_no = #{worderNo}
          and wia.attribute_code = #{attributeCode} and wia.attribute = #{attribute} limit 1
    </select>
    <select id="selectAttributeByWorderId"
            resultType="com.bonc.rrs.worder.entity.WorderInformationAttributeEntity">
        select wia.*
        from worder_information_attribute wia
        where wia.worder_id = #{worderId}
          and wia.is_delete = 0
          and wia.attribute_code = #{attributeCode}
          and wia.attribute = #{attribute} limit 1
    </select>
    <insert id="insertFixSubmit">
        insert into worder_information_attribute (worder_id, attribute_code, attribute_name, attribute_value, is_delete, `attribute`)
        values(#{worderId},'InstallFixSubmit',#{fieldName},'1',0,'FixDocSubmit');
    </insert>

    <insert id="insertCPIMCancelOrder">
        insert into worder_information_attribute (worder_id, attribute_code, attribute_name, attribute_value, is_delete, `attribute`)
        values(#{worderId},'CPIMCancelOrder',#{fieldName},#{fieldValue},0,#{fieldDesc});
    </insert>

    <insert id="insertCcCancelOrder">
        insert into worder_information_attribute (worder_id, attribute_code, attribute_name, attribute_value, is_delete, `attribute`)
        values(#{worderId},#{attCode},#{fieldName},#{fieldValue},0,#{fieldDesc});
    </insert>

    <insert id="insertMdmCancelOrder">
        insert into worder_information_attribute (worder_id, attribute_code, attribute_name, attribute_value, is_delete, `attribute`)
        values (#{worderId}, 'MDMCancelOrder', #{attributeName}, #{attributeValue}, 0, #{attribute});
    </insert>

    <select id="selectByWorderNo" resultType="com.bonc.rrs.worder.entity.WorderInformationAttributeEntity">
        select wia.*
        from worder_information wi,
             worder_information_attribute wia
        where wi.worder_id = wia.worder_id
          and wia.is_delete = 0
          and wi.worder_no = #{worderNo}
          and wia.attribute_code = #{attributeCode}
        limit 1
    </select>

    <select id="selectAttributeByCompanyaWorderNo" resultType="com.bonc.rrs.worder.entity.WorderInformationAttributeEntity">
        select wia.*
        from worder_information wi,
             worder_information_attribute wia
        where wi.worder_id = wia.worder_id
          and wia.is_delete = 0
          and wi.company_order_number = #{companyaWorderNo,jdbcType=VARCHAR}
          and wia.attribute_code = #{attributeCode}
          and wia.attribute = #{attribute}
        limit 1
    </select>

    <select id="selectAttributeByWorderNoAttVal" resultType="com.bonc.rrs.worder.entity.WorderInformationAttributeEntity">
        select wia.*
        from worder_information wi,
             worder_information_attribute wia
        where wi.worder_id = wia.worder_id
          and wia.is_delete = 0
          and wi.worder_no = #{worderNo}
          and wia.attribute_value = #{attributeValue}
          and wia.attribute_code = #{attributeCode}
          and wia.attribute = #{attribute}
        limit 1
    </select>
</mapper>