<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderTicketDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderTicketEntity" id="worderTicketMap">
        <result property="id" column="id"/>
        <result property="worderNo" column="worder_no"/>
        <result property="ticketNo" column="ticket_no"/>
        <result property="userId" column="user_id"/>
        <result property="userEmail" column="user_email"/>
        <result property="sendStatus" column="send_status"/>
        <result property="sendStatusValue" column="send_status_value"/>
    </resultMap>


</mapper>