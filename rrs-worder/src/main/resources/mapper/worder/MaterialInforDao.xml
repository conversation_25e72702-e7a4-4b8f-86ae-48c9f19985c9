<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.MaterialInforDao">

    <select id="getMaterielName" resultType="com.bonc.rrs.worder.dto.dto.FixedMaterialNameDto">
        select
        distinct	me.materiel_name
        from
        materiel_information me
        where
        me.materiel_name in(
        select
        detail_name
        from
        sys_dictionary_detail sdd
        where
        sdd.dictionary_id = (
        select
        sd.id
        from
        sys_dictionary sd
        where
        sd.dic_number = 'fixed_material') );
        
    </select>
    <select id="getMaterielBrand" resultType="com.bonc.rrs.worder.dto.dto.FixedMaterialBrandDto">
        select distinct d.materiel_brand_value,d.materiel_brand,d.id as materielId  from worder_information a
        left join worder_template b on a.template_id = b.id
        left join balance_rule_detail c on b.attendant_balance_rule_id = c.rule_id
        left join materiel_information d on c.materiel_id = d.id
        where d.materiel_name =#{materielName}  and  a.worder_id =#{worderId}
    </select>


    <select id="getMaterielSpec" resultType="com.bonc.rrs.worder.dto.dto.FixedMaterielSpecDto" >
        select distinct d.id  , d.materiel_spec from worder_information a
        left join worder_template b on a.template_id = b.id
        left join balance_rule_detail c on b.attendant_balance_rule_id = c.rule_id
        left join materiel_information d on c.materiel_id = d.id
        where
        d.materiel_name =#{materielName} and d.materiel_brand_value = #{materielBrandValue}
        and  a.worder_id =#{worderId}
    </select>
</mapper>