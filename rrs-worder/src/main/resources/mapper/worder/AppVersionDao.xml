<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.AppVersionDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.AppVersionEntity" id="appVersionMap">
        <result property="id" column="id"/>
        <result property="versionPro" column="version_pro"/>
        <result property="versionMini" column="version_mini"/>
        <result property="versionCode" column="version_code"/>
        <result property="isUpgrade" column="is_upgrade"/>
        <result property="type" column="type"/>
        <result property="apkUrl" column="apk_url"/>
        <result property="upgradeTip" column="upgrade_tip"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createPerson" column="create_person"/>
        <result property="updatePerson" column="update_person"/>
    </resultMap>


</mapper>