<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderFeeDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderFeeEntity" id="worderFeeMap">
        <result property="id" column="id"/>
        <result property="worderNo" column="worder_no"/>
        <result property="costId" column="cost_id"/>
        <result property="costName" column="cost_name"/>
        <result property="contValue" column="cont_value"/>
        <result property="costList" column="cost_list"/>
        <result property="createTime" column="create_time"/>
        <result property="costType" column="cost_type"/>
        <result property="costDesc" column="cost_desc"/>
        <result property="costTypeValue" column="cost_type_value"/>
    </resultMap>


</mapper>