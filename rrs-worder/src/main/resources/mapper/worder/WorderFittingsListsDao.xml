<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderFittingsListsDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderFittingsListsEntity" id="worderFittingsListsMap">
        <result property="id" column="id"/>
        <result property="worderNo" column="worder_no"/>
        <result property="fittingsNo" column="fittings_no"/>
        <result property="useCount" column="use_count"/>
        <result property="usePrice" column="use_price"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>