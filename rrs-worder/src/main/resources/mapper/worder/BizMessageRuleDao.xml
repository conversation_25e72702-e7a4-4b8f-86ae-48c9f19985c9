<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.BizMessageRuleDao">

    <resultMap type="com.bonc.rrs.worder.entity.BizMessageRuleEntity" id="resultMap">
        <result property="ruleId" column="rule_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="eventType" column="event_type"/>
        <result property="dealRole" column="deal_role"/>
        <result property="operationType" column="operation_type"/>
        <result property="msgType" column="msg_type"/>
        <result property="effectTimeFlag" column="effect_time_flag"/>
        <result property="effectTimeType" column="effect_time_type"/>
        <result property="msgPeriod" column="msg_period"/>
        <result property="ruleTopic" column="rule_topic"/>
        <result property="ruleContext" column="rule_context"/>
        <result property="createTime" column="create_time"/>
        <result property="ruleState" column="rule_state"/>

    </resultMap>

    <!-- 根据dto查询一条记录 -->
    <select id = "queryOneByDto" resultType="com.bonc.rrs.worder.entity.BizMessageRuleEntity" parameterType = "com.bonc.rrs.worder.entity.dto.BizMessageRuleDto">
        select * from biz_message_rule
        where 1 = 1
        and rule_state = 1
        <if test = " eventType != null ">
            and instr(event_type, #{eventType}) > 0
        </if>
        order by create_time desc limit 1
    </select>


</mapper>