<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderFittingsDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderFittingsEntity" id="worderFittingsMap">
        <result property="fittingsId" column="fittings_id"/>
        <result property="fittingsName" column="fittings_name"/>
        <result property="fittingsNo" column="fittings_no"/>
        <result property="fittingsType" column="fittings_type"/>
        <result property="fittingsTypeValue" column="fittings_type_value"/>
        <result property="fittingsModel" column="fittings_model"/>
        <result property="fittingsSpecs" column="fittings_specs"/>
        <result property="calQuantity" column="cal_quantity"/>
        <result property="calUnits" column="cal_units"/>
        <result property="calUnitsValue" column="cal_units_value"/>
        <result property="fittingsPrice" column="fittings_price"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>