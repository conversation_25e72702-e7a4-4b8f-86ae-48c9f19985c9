<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.ExtFieldDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.ExtFieldEntity" id="extFieldMap">
        <result property="fieldId" column="field_id"/>
        <result property="fieldName" column="field_name"/>
        <result property="isNessary" column="is_nessary"/>
        <result property="fieldType" column="field_type"/>
        <result property="fieldTypeValue" column="field_type_value"/>
        <result property="fieldPurpose" column="field_purpose"/>
        <result property="fieldPurposeValue" column="field_purpose_value"/>
        <result property="fieldDesc" column="field_desc"/>
        <result property="fieldSort" column="field_sort"/>
        <result property="fieldGroup" column="field_group"/>
        <result property="fieldDicKeyMark" column="field_dic_key_mark"/>
        <result property="filedDicKey" column="filed_dic_key"/>
        <result property="fieldClass" column="field_class"/>
        <result property="isNotnull" column="is_notnull"/>
        <result property="fileSample" column="file_sample"/>
        <result property="selectData" column="select_data"/>
    </resultMap>

    <select id="getPageList" resultType="com.bonc.rrs.worder.entity.ExtFieldEntity">
       select *
       from ext_field
       where is_nessary = 1 and field_class = 0 and deleted = 0
       <if test="p.fieldType != null and p.fieldType != ''">
           and field_type = #{p.fieldType}
       </if>
       <if test="p.fieldPurpose != null and p.fieldPurpose != ''">
           and field_purpose = #{p.fieldPurpose}
       </if>
    </select>

    <update id="updateExtField">
        update ext_field
        set
        <if test="fieldName != null and fieldName != ''">
           field_name = #{fieldName},
        </if>
        <if test="fieldType != null and fieldType != ''">
            field_type = #{fieldType},
        </if>
        <if test="fieldTypeValue != null and fieldTypeValue != ''">
            field_type_value = #{fieldTypeValue},
        </if>
        <if test="fieldPurpose != null and fieldPurpose != ''">
            field_purpose = #{fieldPurpose},
        </if>
        <if test="fieldPurposeValue != null and fieldPurposeValue != ''">
            field_purpose_value = #{fieldPurposeValue},
        </if>
        <if test="fieldDesc != null and fieldDesc != ''">
            field_desc = #{fieldDesc},
        </if>
        field_dic_key_mark = #{fieldDicKeyMark},
        filed_dic_key = #{filedDicKey},
        <if test="isNotnull != null">
            is_notnull = #{isNotnull},
        </if>
        select_data = #{selectData}
        where field_id = #{fieldId}

    </update>



    <select id="getDicKeyList" resultType="com.bonc.rrs.worder.dto.vo.DicKeyResultVo">
        select b.detail_name as detailName, b.remark as filedDicKey,b.identification as fieldDicKeyMark
        from sys_dictionary a
        left join sys_dictionary_detail b on a.id = b.dictionary_id
        where a.dic_number = #{dicNumber}
    </select>


    <!-- 根据字典键值查询字典项目明细 -->
    <select id = "findByDicNumber" parameterType="java.lang.String" resultType = "com.bonc.rrs.worder.dto.dto.ExtFieldDictionaryDto">
        select sdd.* from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = #{dicNumber}
    </select>
</mapper>