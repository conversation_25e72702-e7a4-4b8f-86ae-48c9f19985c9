<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderExtFieldDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderExtFieldEntity" id="worderExtFieldMap">
        <result property="id" column="id"/>
        <result property="worderNo" column="worder_no"/>
        <result property="fieldId" column="field_id"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldValue" column="field_value"/>
        <result property="createTime" column="create_time"/>
        <result property="fieldValueDup" column="field_value_dup"/>
    </resultMap>


    <select id="getConveyPrimaryKey" resultMap="worderExtFieldMap">
        select a.field_id, a.field_value from worder_ext_field a
        left join ext_field b on a.field_id = b.field_id
        where a.worder_no = #{worderNo} and b.field_desc = #{key}
    </select>


    <select id="getCompanyByWorderNo" parameterType="java.util.Map" resultMap="worderExtFieldMap">
        select a.worder_no, a.field_id , a.field_value from worder_ext_field a
        left join ext_field b on a.field_id = b.field_id
        left join worder_information c on a.worder_no = c.worder_no
        left join worder_template d on c.template_id = d.id
        where a.field_id = d.settle_way+100
        <if test="worderNo != null and worderNo != ''">
            and a.worder_no = #{worderNo}
        </if>
        <if test="worderId != null and worderId != ''">
            and c.worder_id = #{worderId}
        </if>

    </select>
    <update id="updateFieldValue">
         update worder_ext_field set field_value = CONCAT(#{fieldValue},'-')
                where field_value = #{fieldValue} and field_id = #{fieldId}
    </update>
    <update id="setFieldValueNull">
        update worder_ext_field set field_value = ''
        where id = #{id}
    </update>

    <select id="selectWorderInfoByFields" resultType="com.bonc.rrs.worder.entity.WorderInformationEntity">
        select
            wi.worder_no,
            wi.pm_id,
            wi.dot_id
        from
            worder_information wi ,
            worder_ext_field wef
        where
            wi.worder_no = wef.worder_no
          and wi.create_time > DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-%d 00:00:00'), INTERVAL 2 YEAR)
          and wi.worder_exec_status in (17,22)
          and wi.worder_type_id in (2,5)
          and wi.is_delete = 0
        <foreach collection="fieldIds" item="fieldId" open="and wef.field_id in (" close=")" separator=",">
            #{fieldId}
        </foreach>
        <foreach collection="fieldValues" item="fieldValue" open="and wef.field_value in (" close=")" separator=",">
            #{fieldValue}
        </foreach>
          and wi.worder_id != #{worderNo}
	    order by wi.create_time desc limit 1
    </select>

    <select id="getFieldIdByWorderNo" resultType="com.bonc.rrs.worderapp.entity.vo.DataVo">
        select wi.worder_no, ef.field_id, ef.field_type, ef.field_name
        from worder_information wi
                 left join worder_template_field wtf on wi.template_id = wtf.template_id
                 left join ext_field ef on ef.field_id = wtf.field_id
        where wi.worder_no = #{worderNo}
          and ef.field_purpose = #{purpose}
          and ef.field_class = '1'
          and ef.deleted = '0'
        order by wtf.sort, ef.field_id
    </select>

    <select id="selectListFieldRequired" resultMap="worderExtFieldMap">
        select
            wef.*
        from
            worder_ext_field wef ,
            ext_field ef
        where
            wef.worder_no = #{worderNo}
          and wef.field_id = ef.field_id
          and ef.is_notnull = 1
          and ef.field_class = 1
          and ef.field_purpose = #{fieldPurpose}
          and ef.is_nessary = 1
          and ef.deleted = 0
    </select>
    <select id="getFieldsByWorderNo" resultType="com.bonc.rrs.worder.entity.WorderExtFieldEntity">
        select
            wef.* ,
            ef.field_type ,
            ef.field_purpose
        from
            worder_ext_field wef ,
            ext_field ef
        where
            wef.field_id = ef.field_id
          and wef.worder_no = #{worderNo}
    </select>

    <select id="queryBusinessProcessFieldsByWorderNo" resultType="com.bonc.rrs.serviceprovider.po.BusinessProcessWorderExtFieldPo">
        select
            wef.worder_no,
            wef.field_id,
            wef.field_name,
            wef.field_value,
            ef.field_type
        from
            worder_ext_field wef
        inner join ext_field ef on
            wef.field_id = ef.field_id
        where
            wef.worder_no = #{worderNo}
    </select>

    <select id="getFieldIdsByWorderNoAndPurpose" resultType="java.lang.Long">
        select wef.field_id
        from worder_ext_field wef
                 left join ext_field ef on ef.field_id = wef.field_id
        where wef.worder_no = #{worderNo}
          and ef.field_purpose = #{purpose}
          and ef.deleted = 0;
    </select>

    <select id="getSpecificFields" resultMap="worderExtFieldMap">
        select field_id, field_value
        from worder_ext_field
        WHERE worder_no = #{worderNo}
        <if test="fieldIds != null and fieldIds.size() > 0">
            AND field_id IN
            <foreach item="id" index="index" collection="fieldIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>