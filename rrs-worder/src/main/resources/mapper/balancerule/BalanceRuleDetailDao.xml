<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balancerule.dao.BalanceRuleDetailDao">
    <resultMap id="balanceRuleDetailMap" type="com.bonc.rrs.balancerule.entity.BalanceRuleDetailEntity">
        <id property="id" column="id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="materielId" column="materiel_id"/>
        <result property="isSuite" column="is_suite"/>
        <result property="startNum" column="start_num"/>
        <result property="endNum" column="end_num"/>
        <result property="price" column="price"/>
        <result property="materielTypeId" column="materiel_type_id"/>
        <result property="materielNo" column="materiel_no"/>
        <result property="materielName" column="materiel_name"/>
        <result property="materielSpec" column="materiel_spec"/>
        <result property="materielUnitValue" column="materiel_unit_value"/>
        <result property="materielBrandValue" column="materiel_brand_value"/>
        <result property="materielComment" column="materiel_comment"/>
        <result property="materielType" column="materiel_type"/>
    </resultMap>
    <select id="queryDetailByRuleId" parameterType="java.lang.Integer" resultMap="balanceRuleDetailMap">
        select * from
        (
          select a.*, b.materiel_type_id, b.materiel_no, b.materiel_name, b.materiel_spec, b.materiel_unit_value, b.materiel_brand_value,
            b.materiel_comment, CONCAT(c.materiel_type_value, '-', c.materiel_child_type) as materiel_type
          from balance_rule_detail a
          left join materiel_information b on a.materiel_id = b.id
          left join materiel_type c on b.materiel_type_id = c.id
          where a.is_suite = 0 and a.rule_id = #{balanceRuleId}
          union
          select a.*, null as materiel_type_id, c.name as materiel_no, c.name as materiel_name, null as materiel_spec,
            null as materiel_unit_value, null as materiel_brand_value,
            null as materiel_comment, '服务-套包' as materiel_type
          from balance_rule_detail a
          left join suite_information c on a.materiel_id = c.id
          where a.is_suite = 1 and a.rule_id = #{balanceRuleId}
        ) t order by id asc
    </select>
    <select id="selectDotBalanceRuleDetail"
            resultType="com.bonc.rrs.balancerule.entity.BalanceRuleDetailEntity">
        select
            brd.*
        from
            worder_template wt ,
            balance_rule_detail brd
        where
            wt.dot_balance_rule_id = brd.rule_id
          and wt.id = #{templateId}
    </select>
</mapper>