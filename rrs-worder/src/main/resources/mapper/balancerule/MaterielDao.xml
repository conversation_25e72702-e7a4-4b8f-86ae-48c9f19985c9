<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balancerule.dao.MaterielDao">
    <resultMap id="materielMap" type="com.bonc.rrs.balancerule.vo.MaterielVO">
        <result property="materielId" column="id"/>
        <result property="materielTypeId" column="materiel_type_id"/>
        <result property="materielNo" column="materiel_no"/>
        <result property="materielName" column="materiel_name"/>
        <result property="materielSpec" column="materiel_spec"/>
        <result property="materielUnitValue" column="materiel_unit_value"/>
        <result property="materielBrandValue" column="materiel_brand_value"/>
        <result property="materielComment" column="materiel_comment"/>
        <result property="materielType" column="materiel_type"/>
        <result property="isSuite" column="is_suite"/>
       <!-- <result property="materielBrand" column="materiel_brand"/>-->
    </resultMap>
    <select id="queryData" parameterType="java.util.HashMap" resultMap="materielMap">
        SELECT * from (
        <if test="isSuite == null or isSuite==0">
        select a.id, a.materiel_type_id, a.materiel_no, a.materiel_name, a.materiel_spec,
            a.materiel_unit_value, a.materiel_brand_value,
            a.materiel_comment, CONCAT(b.materiel_type_value, '-', b.materiel_child_type) as materiel_type, 0 as is_suite
            from materiel_information a, materiel_type b
            where a.materiel_type_id = b.id
              <if test="materielNo != null and materielNo != ''">
                  <bind name="materielNoLike" value="'%' + materielNo + '%'" />
                and a.materiel_no like #{materielNoLike}
              </if>
              <if test="materielSpec != null and materielSpec != ''">
                  <bind name="materielSpecLike" value="'%' + materielSpec + '%'" />
                  and a.materiel_spec like #{materielSpecLike}
              </if>
            <if test="materielName != null and materielName != ''">
                <bind name="materielNameLike" value="'%' + materielName + '%'" />
                and a.materiel_name like #{materielNameLike}
            </if>
              <if test="materielTypeId != null">
                  and a.materiel_type_id = #{materielTypeId}
              </if>
        </if>
        <if test="isSuite == null">
            UNION
        </if>
        <if test="isSuite == null or isSuite==1">
            select c.id, null as materiel_type_id, c.name as materiel_no, c.name as materiel_name,
            null as materiel_spec, null as materiel_unit_value, null as materiel_brand_value,
            null as materiel_comment, '服务-套包' as materiel_type, 1 as is_suite
            from suite_information c
            <where>
                <if test="materielNo != null and materielNo != ''">
                    <bind name="materielNoLike" value="'%' + materielNo + '%'" />
                    and c.name like #{materielNoLike}
                </if>
                <if test="materielSpec != null and materielSpec != ''">
                    <bind name="materielSpecLike" value="'%' + materielSpec + '%'" />
                    and c.name like #{materielSpecLike}
                </if>
                <if test="materielName != null and materielName != ''">
                    <bind name="materielNameLike" value="'%' + materielName + '%'" />
                    and c.name like #{materielNameLike}
                </if>
            </where>
        </if>
        ) t limit ${startRow}, ${limit}
    </select>
    <select id="queryCount" parameterType="java.util.HashMap" resultType="java.lang.Integer">
        SELECT count(*) from (
        <if test="isSuite == null or isSuite==0">select a.id, a.materiel_type_id, a.materiel_no, a.materiel_name,
            a.materiel_spec, a.materiel_unit_value, a.materiel_brand_value, a.materiel_comment,
            CONCAT(b.materiel_type_value, '-', b.materiel_child_type) as materiel_type, 0 as is_suite
            from materiel_information a, materiel_type b
            where a.materiel_type_id = b.id
                <if test="materielNo != null and materielNo != ''">
                    <bind name="materielNoLike" value="'%' + materielNo + '%'" />
                    and a.materiel_no like #{materielNoLike}
                </if>
                <if test="materielSpec != null and materielSpec != ''">
                    <bind name="materielSpecLike" value="'%' + materielSpec + '%'" />
                    and a.materiel_spec like #{materielSpecLike}
                </if>
                <if test="materielName != null and materielName != ''">
                    <bind name="materielNameLike" value="'%' + materielName + '%'" />
                    and a.materiel_name like #{materielNameLike}
                </if>
                <if test="materielTypeId != null">
                    and a.materiel_type_id = #{materielTypeId}
                </if>
        </if>
        <if test="isSuite == null">
            UNION
        </if>
        <if test="isSuite == null or isSuite==1">
            select c.id, null as materiel_type_id, c.name as materiel_no, c.name as materiel_name,
            null as materiel_spec, null as materiel_unit_value, null as materiel_brand_value,
            null as materiel_comment, '服务-套包' as materiel_type, 1 as is_suite
            from suite_information c
            <where>
                <if test="materielNo != null and materielNo != ''">
                    <bind name="materielNoLike" value="'%' + materielNo + '%'" />
                    and c.name like #{materielNoLike}
                </if>
                <if test="materielSpec != null and materielSpec != ''">
                    <bind name="materielSpecLike" value="'%' + materielSpec + '%'" />
                    and c.name like #{materielSpecLike}
                </if>
                <if test="materielName != null and materielName != ''">
                    <bind name="materielNameLike" value="'%' + materielName + '%'" />
                    and c.name like #{materielNameLike}
                </if>
            </where>
        </if>
        ) t
    </select>
</mapper>