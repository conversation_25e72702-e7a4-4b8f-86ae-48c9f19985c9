<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.gace.dao.WorderCheckItemMapper">

    <resultMap id="BaseResultMap" type="com.bonc.rrs.gace.entity.WorderCheckItem">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ckId" column="ck_id" jdbcType="BIGINT"/>
            <result property="worderId" column="worder_id" jdbcType="BIGINT"/>
            <result property="ckNumber" column="ck_number" jdbcType="VARCHAR"/>
            <result property="ckName" column="ck_name" jdbcType="VARCHAR"/>
            <result property="ckActualValue" column="ck_actual_value" jdbcType="VARCHAR"/>
            <result property="ckActualDescriptionTag" column="ck_actual_description_tag" jdbcType="VARCHAR"/>
            <result property="digiValueType" column="digi_value_type" jdbcType="VARCHAR"/>
            <result property="ckWoChecklistGroupId" column="ck_wo_checklist_group_id" jdbcType="BIGINT"/>
            <result property="ckgName" column="ckg_name" jdbcType="VARCHAR"/>
            <result property="ckgScenario" column="ckg_scenario" jdbcType="VARCHAR"/>
            <result property="digiClgroupSource" column="digi_clgroup_source" jdbcType="VARCHAR"/>
            <result property="ckStandardReference" column="ck_standard_reference" jdbcType="VARCHAR"/>
            <result property="digiAssistInput" column="digi_assist_input" jdbcType="VARCHAR"/>
            <result property="digiMultiselect" column="digi_multiselect" jdbcType="TINYINT"/>
            <result property="ckMustCheckFlag" column="ck_must_check_flag" jdbcType="TINYINT"/>
            <result property="ckActualDescription" column="ck_actual_description" jdbcType="VARCHAR"/>
            <result property="ckSourceStdchecklistId" column="ck_source_stdchecklist_id" jdbcType="BIGINT"/>
            <result property="ckMaxRangeValue" column="ck_max_range_value" jdbcType="DECIMAL"/>
            <result property="digiAnnex" column="digi_annex" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ck_id,worder_id,ck_number,
        ck_name,ck_actual_value,ck_actual_description_tag,
        digi_value_type,ck_wo_checklist_group_id,ckg_name,
        ckg_scenario,digi_clgroup_source,ck_standard_reference,
        digi_assist_input,digi_multiselect,ck_must_check_flag,
        ck_actual_description,ck_source_stdchecklist_id,ck_max_range_value,
        digi_annex
    </sql>
</mapper>
