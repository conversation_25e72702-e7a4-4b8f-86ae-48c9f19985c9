<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.gace.dao.BizRegionGuangqiMapper">

    <resultMap id="BaseResultMap" type="com.bonc.rrs.gace.entity.BizRegionGuangqi">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="positionNameIdentifier" column="position_name_identifier" jdbcType="VARCHAR"/>
        <result property="positionDescriptionChinese" column="position_description_chinese" jdbcType="VARCHAR"/>
        <result property="positionTypeName" column="position_type_name" jdbcType="VARCHAR"/>
        <result property="parentPositionNameIdentifier" column="parent_position_name_identifier" jdbcType="VARCHAR"/>
        <result property="parentPositionDescription" column="parent_position_description" jdbcType="VARCHAR"/>
        <result property="longNameChinese" column="long_name_chinese" jdbcType="VARCHAR"/>
        <result property="longCode" column="long_code" jdbcType="VARCHAR"/>
        <result property="regionId" column="region_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, position_name_identifier, position_description_chinese, position_type_name,
        parent_position_name_identifier, parent_position_description, long_name_chinese,
        long_code, region_id, create_time, update_time
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO biz_region_guangqi (
            position_name_identifier, position_description_chinese, position_type_name,
            parent_position_name_identifier, parent_position_description, long_name_chinese,
            long_code, region_id, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.positionNameIdentifier,jdbcType=VARCHAR},
                #{item.positionDescriptionChinese,jdbcType=VARCHAR},
                #{item.positionTypeName,jdbcType=VARCHAR},
                #{item.parentPositionNameIdentifier,jdbcType=VARCHAR},
                #{item.parentPositionDescription,jdbcType=VARCHAR},
                #{item.longNameChinese,jdbcType=VARCHAR},
                #{item.longCode,jdbcType=VARCHAR},
                #{item.regionId,jdbcType=INTEGER},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!-- 根据位置名称标识查询 -->
    <select id="selectByPositionNameIdentifier" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM biz_region_guangqi
        WHERE position_name_identifier = #{positionNameIdentifier,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <!-- 清空表数据 -->
    <delete id="truncateTable">
        TRUNCATE TABLE biz_region_guangqi
    </delete>

    <!-- 根据位置名称标识更新记录（保护已有的region_id） -->
    <update id="updateByPositionNameIdentifier" parameterType="com.bonc.rrs.gace.entity.BizRegionGuangqi">
        UPDATE biz_region_guangqi
        <set>
            <if test="entity.positionDescriptionChinese != null">
                position_description_chinese = #{entity.positionDescriptionChinese,jdbcType=VARCHAR},
            </if>
            <if test="entity.positionTypeName != null">
                position_type_name = #{entity.positionTypeName,jdbcType=VARCHAR},
            </if>
            <if test="entity.parentPositionNameIdentifier != null">
                parent_position_name_identifier = #{entity.parentPositionNameIdentifier,jdbcType=VARCHAR},
            </if>
            <if test="entity.parentPositionDescription != null">
                parent_position_description = #{entity.parentPositionDescription,jdbcType=VARCHAR},
            </if>
            <if test="entity.longNameChinese != null">
                long_name_chinese = #{entity.longNameChinese,jdbcType=VARCHAR},
            </if>
            <if test="entity.longCode != null">
                long_code = #{entity.longCode,jdbcType=VARCHAR},
            </if>
            <!-- 只有当新的region_id不为空时才更新，保护已有的region_id -->
            <if test="entity.regionId != null">
                region_id = #{entity.regionId,jdbcType=INTEGER},
            </if>
            update_time = #{entity.updateTime,jdbcType=TIMESTAMP}
        </set>
        WHERE position_name_identifier = #{entity.positionNameIdentifier,jdbcType=VARCHAR}
    </update>

    <!-- 批量更新或插入（存在则更新，不存在则插入） -->
    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO biz_region_guangqi (
            position_name_identifier, position_description_chinese, position_type_name,
            parent_position_name_identifier, parent_position_description, long_name_chinese,
            long_code, region_id, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.positionNameIdentifier,jdbcType=VARCHAR},
                #{item.positionDescriptionChinese,jdbcType=VARCHAR},
                #{item.positionTypeName,jdbcType=VARCHAR},
                #{item.parentPositionNameIdentifier,jdbcType=VARCHAR},
                #{item.parentPositionDescription,jdbcType=VARCHAR},
                #{item.longNameChinese,jdbcType=VARCHAR},
                #{item.longCode,jdbcType=VARCHAR},
                #{item.regionId,jdbcType=INTEGER},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            position_description_chinese = VALUES(position_description_chinese),
            position_type_name = VALUES(position_type_name),
            parent_position_name_identifier = VALUES(parent_position_name_identifier),
            parent_position_description = VALUES(parent_position_description),
            long_name_chinese = VALUES(long_name_chinese),
            long_code = VALUES(long_code),
            <!-- 只有当新值不为空时才更新region_id，保护已有值 -->
            region_id = CASE 
                WHEN VALUES(region_id) IS NOT NULL THEN VALUES(region_id)
                ELSE region_id
            END,
            update_time = VALUES(update_time)
    </insert>

</mapper>
