<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.pay.dao.PayDayEndStatisticsMapper">

    <resultMap id="payOperLogEntityResult" type="com.bonc.rrs.pay.entity.entity.PayDayEndStatisticsEntity">
        <result column="day_end_date" property="dayEndDate" />
        <result column="local_bill_num" property="localBillNum" />
        <result column="local_bill_toatal_amount" property="localBillToatalAmount"/>
        <result column="reconciliation_bill_num" property="reconciliationBillNum" />
        <result column="reconciliation_bill_toatal_amount" property="reconciliationBillToatalAmount" />
        <result column="handling_fee" property="handlingFee" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <insert id="insertPayDayEndStatisticsInfo" parameterType="com.bonc.rrs.pay.entity.entity.PayDayEndStatisticsEntity">
        insert into pay_day_end_statistics (
            <trim suffixOverrides=",">
                <if test="dayEndDate != null ">day_end_date,</if>
                <if test="localBillNum != null">local_bill_num,</if>
                <if test="localBillToatalAmount != null">local_bill_toatal_amount,</if>
                <if test="reconciliationBillNum != null ">reconciliation_bill_num,</if>
                <if test="reconciliationBillToatalAmount != null ">reconciliation_bill_toatal_amount,</if>
                <if test="handlingFee != null ">handling_fee,</if>
                <if test="remark != null ">remark,</if>
                <if test="reconciliationStatus != null ">reconciliation_status,</if>
                <if test="payType != null">pay_type,</if>
                create_time,
            </trim>
        ) values (
            <trim suffixOverrides=",">
                <if test="dayEndDate != null ">#{dayEndDate},</if>
                <if test="localBillNum != null  ">#{localBillNum},</if>
                <if test="localBillToatalAmount != null ">#{localBillToatalAmount},</if>
                <if test="reconciliationBillNum != null ">#{reconciliationBillNum},</if>
                <if test="reconciliationBillToatalAmount != null ">#{reconciliationBillToatalAmount},</if>
                <if test="handlingFee != null">#{handlingFee},</if>
                <if test="remark != null">#{remark},</if>
                <if test="reconciliationStatus != null ">#{reconciliationStatus},</if>
                <if test="payType != null">#{payType},</if>
                now(),
            </trim>
        )
    </insert>
</mapper>