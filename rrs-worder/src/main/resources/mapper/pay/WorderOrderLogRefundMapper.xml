<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.pay.dao.WorderOrderLogRefundMapper">

    <resultMap id="WorderOrderLogRefundDTOResult" type="com.bonc.rrs.pay.entity.entity.WorderOrderLogRefundEntity">
        <result property="worder_no" column="worderNo"/>
        <result property="order_no_refund" column="orderNoRefund"/>
        <result property="transaction_id" column="transactionId"/>
        <result property="create_time" column="createTime"/>
        <result property="update_time" column="updateTime"/>
        <result property="Operator_id" column="operatorId"/>
        <result property="pay_actual_amount" column="payActualAmount"/>
        <result property="status" column="status"/>
    </resultMap>

    <select id="selectByTransactionId" resultMap="WorderOrderLogRefundDTOResult">
        select * from worder_order_log_refund where transaction_id =#{transactionId}
    </select>
    <update id="updateByTransactionId">
        update worder_order_log_refund set status =1  where transaction_id =#{transactionId}
    </update>
    <select id="selectCountByTransactionId" resultType="int">
        select count(*) from worder_order_log_refund where transaction_id =#{transactionId}
    </select>
</mapper>