<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.pay.dao.PayCreateImageMapper">

    <resultMap id="payCreateImageDTOResult" type="com.bonc.rrs.pay.entity.entity.PayCreateImageDTO">
        <result property="orderId" column="order_id"/>
        <result property="imgContent" column="img_content"/>
        <result property="intervals" column="intervals"/>
        <result property="unit" column="unit"/>
        <result property="createTime" column="createTime"/>
    </resultMap>

    <select id="findImgContentByOrderId" resultMap="payCreateImageDTOResult">
        select t.order_id,t.img_content,t.intervals,t.unit from pay_create_image t where t.order_id = #{orderId}
    </select>

    <insert id="insertInfo" parameterType="com.bonc.rrs.pay.entity.entity.PayCreateImageDTO">
        insert into pay_create_image (
        order_id,img_content,intervals,unit,enable,create_time
        )
        values
        (
        #{orderId}, #{imgContent}, #{intervals}, #{unit},#{enable},#{createTime}
        )
    </insert>
</mapper>