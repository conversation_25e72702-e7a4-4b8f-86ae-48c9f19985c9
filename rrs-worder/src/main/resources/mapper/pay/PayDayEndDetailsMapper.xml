<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.pay.dao.PayDayEndDetailsMapper">

    <resultMap id="payOperLogEntityResult" type="com.bonc.rrs.pay.entity.entity.PayDayEndDetailsEntity">
        <result column="day_end_date" property="dayEndDate" />
        <result column="transaction_id" property="transactionId" />
        <result column="order_no" property="orderNo"/>
        <result column="trade_time" property="tradeTime" />
        <result column="trade_type" property="tradeType" />
        <result column="trade_status" property="tradeStatus" />
        <result column="handling_fee" property="handlingFee" />
        <result column="rate" property="rate" />
        <result column="total_acount" property="totalAcount" />
    </resultMap>

    <insert id="insertPayDayEndDetaisInfo" parameterType="com.bonc.rrs.pay.entity.entity.PayDayEndDetailsEntity">
        insert into pay_day_end_details (
                day_end_date,transaction_id,order_no,trade_time,trade_type,trade_status,handling_fee,rate,total_acount,remark,create_time
        )
        values
            <foreach collection="payDayEndDetails" item="item" separator=",">
                (
                    #{item.dayEndDate}, #{item.transactionId}, #{item.orderNo}, #{item.tradeTime}, #{item.tradeType}, #{item.tradeStatus},#{item.handlingFee},#{item.rate},#{item.totalAcount},#{item.remark},now()
                )
            </foreach>
    </insert>
</mapper>