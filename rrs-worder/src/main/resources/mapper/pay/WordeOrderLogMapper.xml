<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.pay.dao.WorderOrderLogMapper">
    <resultMap id="worderOrderLogDTOResult" type="com.bonc.rrs.pay.model.entity.WorderOrderLogDTO">
        <result column="order_log_id" property="orderLogId" />
        <result column="order_no" property="orderNo" />
        <result column="transaction_id" property="transactionId" />
        <result column="worder_no" property="worderNo" />
        <result column="pay_actual_amount" property="payActualAmount" />
        <result column="from_user_id" property="fromUserId" />
        <result column="to_user_id" property="toUserId" />
        <result column="order_status" property="orderStatus" />
        <result column="brand_name" property="brandName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="pay_type" property="payType" />
        <result column="pay_area_name" property="payAreaName" />
        <result column="pay_area_id" property="payAreaId" />
        <result column="bookkeeping_status" property="bookkeepingStatus" />
        <result column="apply" property="apply" />
        <result column="pay_able_amuount" property="payAbleAmuount" />
        <result column="pay_discount_amount" property="payDiscountAmount" />
    </resultMap>

    <resultMap id="worderOrderLogDTOResultRefund" type="com.bonc.rrs.pay.model.entity.WorderOrderLogDTO">
        <result column="order_log_id" property="orderLogId" />
        <result column="order_no" property="orderNo" />
        <result column="transaction_id" property="transactionId" />
        <result column="worder_no" property="worderNo" />
        <result column="pay_actual_amount" property="payActualAmount" />
        <result column="from_user_id" property="fromUserId" />
        <result column="to_user_id" property="toUserId" />
        <result column="order_status" property="orderStatus" />
        <result column="brand_name" property="brandName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="pay_type" property="payType" />
        <result column="pay_area_name" property="payAreaName" />
        <result column="pay_area_id" property="payAreaId" />
        <result column="bookkeeping_status" property="bookkeepingStatus" />
        <result column="apply" property="apply" />
        <result column="pay_able_amuount" property="payAbleAmuount" />
        <result column="pay_discount_amount" property="payDiscountAmount" />
        <result column="company_order_number" property="companyOrderNumber" />
    </resultMap>



    <sql id="orderLogParam">
        t.order_log_id,t.order_no,t.transaction_id,t.worder_no,t.pay_actual_amount,t.brand_name,t.from_user_id,t.to_user_id,t.order_status,t.remark,t.create_time
        ,t.update_time,t.pay_type,t.pay_area_name,t.pay_area_id,t.bookkeeping_status,t.apply,t.pay_able_amuount,t.pay_discount_amount
    </sql>

    <select id="queryList"  resultMap="worderOrderLogDTOResultRefund">
        select   t.order_log_id,t.order_no,t.transaction_id,t.worder_no,t.pay_actual_amount,t.pay_type,wi.company_order_number,t.order_status
        from worder_order_log t left join worder_information wi on t.worder_no=wi.worder_no
        where 1=1
        <if test="p.worderNo !=null and p.worderNo!='' ">
            and   t.worder_no=#{p.worderNo}
        </if>
        <if test="p.companyOrderNumber!=null and p.companyOrderNumber!='' ">
            and wi.company_order_number=#{p.companyOrderNumber}
        </if>
        and (t.order_status=2 or  t.order_status=5)
    </select>

    <select id="findOrderLogInfo" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
    </select>

    <insert id="insertOrderLogInfo" parameterType="com.bonc.rrs.pay.model.entity.WorderOrderLogDTO">
        insert into worder_order_log (
        <trim suffixOverrides=",">
            <if test="orderNo !=null ">order_no,</if>
            <if test="worderNo !=null ">worder_no,</if>
            <if test="payActualAmount !=null ">pay_actual_amount,</if>
            <if test="fromUserId !=null ">from_user_id,</if>
            <if test="toUserId !=null ">to_user_id,</if>
            <if test="orderStatus !=null ">order_status,</if>
            <if test="brandName !=null ">brand_name,</if>
            <if test="remark !=null ">remark,</if>
            <if test="payDiscountAmount !=null ">pay_discount_amount,</if>
            <if test="payAbleAmuount !=null ">pay_able_amuount,</if>
            <if test="payAreaId !=null ">pay_area_id,</if>
            <if test="payAreaName !=null ">pay_area_name,</if>
            <if test="payType !=null ">pay_type,</if>
            <if test="transactionId !=null ">transaction_id,</if>
            <if test="completeTime !=null ">complete_time,</if>
            create_time,
            update_time,
        </trim>
        )values (
        <trim suffixOverrides=",">
            <if test="orderNo !=null ">#{orderNo},</if>
            <if test="worderNo !=null ">#{worderNo},</if>
            <if test="payActualAmount !=null ">#{payActualAmount},</if>
            <if test="fromUserId !=null ">#{fromUserId},</if>
            <if test="toUserId !=null ">#{toUserId},</if>
            <if test="orderStatus !=null ">#{orderStatus},</if>
            <if test="brandName !=null ">#{brandName},</if>
            <if test="remark !=null ">#{remark},</if>
            <if test="payDiscountAmount !=null ">#{payDiscountAmount},</if>
            <if test="payAbleAmuount !=null ">#{payAbleAmuount},</if>
            <if test="payAreaId !=null ">#{payAreaId},</if>
            <if test="payAreaName !=null ">#{payAreaName},</if>
            <if test="payType !=null ">#{payType},</if>
            <if test="transactionId !=null ">#{transactionId},</if>
            <if test="completeTime !=null ">#{completeTime},</if>
            <if test="createTime !=null">#{createTime},</if>
            now(),
        </trim>
        )
        <selectKey keyColumn="order_log_id" keyProperty="orderLogId"  order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <select id="findConstantStatusOrderLogInfo" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.order_status = #{orderStatus}
        and t.worder_no
        in
        <foreach collection="worderNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!-- 记账状态 -->
    <update id="updateBookkeepingStatus" >
        update worder_order_log set bookkeeping_status = #{bookkeepingStatus}
        where order_no = #{orderNo}
    </update>

    <select id="findOrderLogInfoList" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.worder_no = #{worderNo} and t.order_status = #{orderStatus}
    </select>
    <update id="updateOrderStatus">
        update worder_order_log set order_status = #{orderStatus}
        where worder_no = #{worderNo} and order_status != #{orderStatus}
    </update>

    <!--    and pay_actual_amount = #{payActualAmount}-->
    <update id="updateOrderStatusByOrderNo">
        update worder_order_log set order_status = #{orderStatus},transaction_id=#{transactionId},update_time=now()
        where order_no = #{orderNo} and order_status != #{orderStatus}
    </update>
    <update id="updateOpenIdByOrderNo">
        update worder_order_log set
        <if test="userId!=null ">
            user_id = #{userId}
        </if>
        <if test="completeTime!=null">
            ,complete_time=#{completeTime}
        </if>
        where order_no = #{orderNo}
    </update>
    <update id="updateStatusByOrderNo">
        update worder_order_log set order_status = #{orderStatus},update_time=now()
        where order_no !=  #{orderNo} and order_status != #{orderStatus} and worder_no=#{worderNo} and order_status != 5
    </update>

    <update id="updateOrderStatusByWorderNo">
        update worder_order_log set order_status = #{orderStatus},update_time=now()
        where worder_no = #{worderNo} and order_status = #{orderStatus1}
    </update>

    <select id="findConstantStatusOrderLogInfoByOrderNo" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.order_status = #{orderStatus}
        and t.order_no = #{orderNo}
    </select>

    <!-- 记账状态 -->
    <update id="updateApplyStatus" >
        update worder_order_log set apply = #{apply}
        where order_no = #{orderNo}
    </update>

    <select id="findOrderLogInfoByOrderNo" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.order_no = #{orderNo}
    </select>
    <update id="updatePushState">
        update worder_order_log set push_state =#{pushState} where order_no = #{orderNo}
    </update>

    <select id="findOrderLogInfoListByStates" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.worder_no = #{worderNo}
        <foreach collection="orderStatus" open=" and (" close=")" item="item" separator="or">
            t.order_status = #{item}
        </foreach>
    </select>
    <select id="getByWorderNoAndStatus" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.worder_no = #{worderNo} and
        t.order_status = #{orderStatus}
    </select>

    <select id="findOrderLogInfoListsByStates" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.worder_no = #{worderNo}
        <foreach collection="orderStatus" open=" and (" close=")" item="item" separator="or">
            t.order_status = #{item}
        </foreach>

    </select>


    <select id="findNoPayOrder" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where 1=1
        <if test="payTypes != null">
            <foreach collection="payTypes" open=" and (" close=")" item="payType" separator="or">
                t.pay_type = #{payType}
            </foreach>
        </if>
        <foreach collection="orderStatus" open=" and (" close=")" item="item" separator="or">
            t.order_status = #{item}
        </foreach>
    </select>

    <select id="findOrderLogRecordCounts" resultType="com.bonc.rrs.pay.entity.entity.LocalPayDataStatisticEntity">
        select
        count(*) counts,sum(t.pay_actual_amount) payActualTotalAmount
        from worder_order_log t
        where   t.order_status = #{orderStatus}
        and t.update_time >= #{startTime}
        and t.update_time  <![CDATA[ <= ]]> #{endTime}
        and t.pay_type = #{payType}
    </select>

    <select id="findOrderLogRecords"   resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where   t.order_status = #{orderStatus}
        and t.pay_type = #{payType}
        and t.update_time >= #{startTime}
        and t.update_time  <![CDATA[ <= ]]> #{endTime}

    </select>

    <select id="findWorderNoWithNoBilled" resultType="java.lang.String">
        select t.worder_no
        from  worder_order_log t
        INNER JOIN worder_billing_order_record t1 ON   t.worder_no = t1.order_no
        INNER JOIN worder_billing_recode t2 ON  t1.serial_no = t2.serial_no
        where  t.bookkeeping_status = #{bookkeepingStatus}
        and t.apply = #{apply}
        <if test="billingStatus != null and billingStatus.size() > 0">
            and  t2.billing_status in
            <foreach collection="billingStatus" item="item" open="(" close=")"  separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getWxOrAliOrderLogInfoList" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.worder_no = #{worderNo} and t.order_status = #{orderStatus} and #{payType}
    </select>

    <select id="getOrderLogInfoListByStates" resultMap="worderOrderLogDTOResult">
        select
        <include refid="orderLogParam"/>
        from worder_order_log t
        where t.worder_no = #{worderNo}
        and t.order_status = #{orderStatus}  and t.pay_type=#{payType}

    </select>
    <select id="getNowTime" resultType="String">
        select now();
    </select>

    <select id="getById"  resultMap="worderOrderLogDTOResultRefund">
        select * from worder_order_log t where t.order_log_id =#{orderLogId}
    </select>

    <update id="updateById">
        update worder_order_log set order_status =5 where order_log_id = #{orderLogId}
    </update>



    <select id="isExsitWorderLog" resultType="java.lang.String">
       select  worder_no from worder_order_log wol where wol.transaction_id = #{transactionId}
    </select>

    <select id="isExsitPayInfo" resultType="java.lang.String">
        select   case when length(ifnull(wi.user_balance_fee_sum,''))=0  then wol.pay_actual_amount else wi.user_balance_fee_sum end  user_balance_fee_sum
            from worder_information wi
            left join worder_ext_field wef  on  wi.worder_no =wef.worder_no
            left join ext_field ef  on  wef.field_id =ef.field_id
            left join worder_order_log wol on wi.worder_no =wol.worder_no  and wol.order_status  ='2'
             where  ef.field_id =5 and  wi.worder_no=#{worderNO}
    </select>




</mapper>