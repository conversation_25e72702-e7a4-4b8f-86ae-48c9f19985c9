<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.pay.dao.PayOperLogMapper">
    <resultMap id="payOperLogEntityResult" type="com.bonc.rrs.pay.entity.entity.PayOperLogEntity">
        <result column="pay_log_id" property="payLogId" />
        <result column="title" property="title" />
        <result column="oper_status" property="operStatus"/>
        <result column="oper_content" property="operContent" />
        <result column="oper_ip" property="operIp" />
        <result column="pay_type_code" property="payTypeCode" />
        <result column="pay_type_name" property="payTypeName" />
        <result column="trade_type" property="tradeType" />
        <result column="create_time" property="createTime" />
        <result column="remark" property="remark" />
    </resultMap>
    <sql id="payOrderLogSql">
        t.pay_log_id,t.order_no,t.result_code,t.result_msg,t.oper_ip,t.pay_type_code,t.pay_type_name,t.trade_type,t.create_time,t.remark
    </sql>
    <select id="findPayOrderLogList" parameterType="com.bonc.rrs.pay.entity.entity.PayOperLogEntity" resultMap="payOperLogEntityResult">
        select
           <include refid="payOrderLogSql"/>
        from   pay_oper_log t
        <where>
            <if test="orderNo != null and orderNo != ''">t.order_no = #{orderNo}</if>
            <if test="resultCode != null and resultCode  != ''">t.result_code = #{resultCode}</if>
            <if test="payTypeCode != null ">t.pay_type_code = #{resultCode}</if>
        </where>
    </select>
    <insert id="insertPayOperLog" parameterType="com.bonc.rrs.pay.entity.entity.PayOperLogEntity">
        insert into pay_oper_log (
            <trim suffixOverrides=",">
                <if test="title != null and title != '' ">title,</if>
                <if test="operStatus != null and operStatus  != ''">oper_status,</if>
                <if test="operContent != null and operContent != ''">oper_content,</if>
                <if test="operIp != null and operIp != ''">oper_ip,</if>
                <if test="payTypeCode != null and payTypeCode != ''">pay_type_code,</if>
                <if test="payTypeName != null and payTypeName != ''">pay_type_name,</if>
                <if test="tradeType!= null ">trade_type,</if>
                create_time,
                <if test="remark != null and remark != ''">remark,</if>
            </trim>
        ) values (
            <trim suffixOverrides=",">
                <if test="title != null and title != '' ">#{title},</if>
                <if test="operStatus != null and operStatus  != ''">#{operStatus},</if>
                <if test="operContent != null and operContent != ''">#{operContent},</if>
                <if test="operIp != null and operIp != '' ">#{operIp},</if>
                <if test="payTypeCode != null and payTypeCode != ''  ">#{payTypeCode},</if>
                <if test="payTypeName != null and payTypeName != ''">#{payTypeName},</if>
                <if test="tradeType != null ">#{tradeType},</if>
                now(),
                <if test="remark  != null and remark != ''">#{remark},</if>
            </trim>
        )
    </insert>
</mapper>