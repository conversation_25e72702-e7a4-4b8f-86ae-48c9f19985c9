<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.pay.dao.WorderPayMapper">

    <select id="list" parameterType="com.bonc.rrs.pay.entity.request.PayInfoRequest"
            resultType="com.bonc.rrs.pay.entity.entity.WorderPayEntity">
        select a.worder_id worderId, a.worder_no worderNo, b.order_status worderPayStatus from worder_information a
        left join worder_order_log b on a.worder_no = b.worder_no
        <where>
            <if test="worderId != null and worderId != ''">
                and a.worder_id = #{worderId}
            </if>
            <if test="worderNo != null and worderNo != ''">
                and a.worder_no = #{worderNo}
            </if>
        </where>
        order by b.order_log_id desc
    </select>

</mapper>