<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.suite.dao.SuiteDetailDao">
    <resultMap id="suiteDetailMap" type="com.bonc.rrs.suite.entity.SuiteDetailEntity">
        <id property="id" column="id"/>
        <result property="suiteId" column="suite_id"/>
        <result property="materielId" column="materiel_id"/>
        <result property="num" column="num"/>
        <result property="materielTypeId" column="materiel_type_id"/>
        <result property="materielNo" column="materiel_no"/>
        <result property="materielName" column="materiel_name"/>
        <result property="materielSpec" column="materiel_spec"/>
        <result property="materielUnitValue" column="materiel_unit_value"/>
        <result property="materielBrandValue" column="materiel_brand_value"/>
        <result property="materielComment" column="materiel_comment"/>
        <result property="materielType" column="materiel_type"/>
    </resultMap>
    <select id="queryDetailBySuiteId" parameterType="java.lang.Integer" resultMap="suiteDetailMap">
        select a.*, b.materiel_type_id,b.materiel_name, b.materiel_no, b.materiel_spec, b.materiel_unit_value, b.materiel_brand_value,
        b.materiel_comment, CONCAT(c.materiel_type_value, '-', c.materiel_child_type) as materiel_type
        from suite_detail a
        left join materiel_information b on a.materiel_id = b.id
        left join materiel_type c on b.materiel_type_id = c.id
        where a.suite_id = #{suiteId}
    </select>
</mapper>