<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.attribute.dao.WorderInformationAttributeMapper">

    <resultMap id="BaseResultMap" type="com.bonc.rrs.attribute.entity.WorderInformationAttribute">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="worderId" column="worder_id" jdbcType="INTEGER"/>
            <result property="attribute" column="attribute" jdbcType="VARCHAR"/>
            <result property="attributeCode" column="attribute_code" jdbcType="VARCHAR"/>
            <result property="attributeName" column="attribute_name" jdbcType="VARCHAR"/>
            <result property="attributeValue" column="attribute_value" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
    </resultMap>

</mapper>
