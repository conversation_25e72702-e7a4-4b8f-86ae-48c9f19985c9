<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.message.dao.MsgDao">

    <resultMap id="worderMessageMap" type="com.bonc.rrs.message.entity.WorderMessageEntity">
        <result property="id" column="id"></result>
        <result property="worderNo" column="worder_no"/>
        <result property="sendUserId" column="send_user_id"></result>
        <result property="receiveUserId" column="receive_user_id"></result>
        <result property="messageTitle" column="message_title"></result>
        <result property="messageContent" column="message_content"/>
        <result property="sendTime" column="send_time"></result>
        <result property="send" column="is_send"></result>
        <result property="read" column="is_read"></result>
        <result property="gmtCreate" column="gmt_create"></result>
        <result property="gmtModified" column="gmt_modified"></result>
    </resultMap>

    <resultMap id="worderDelayMessageMap" type="com.bonc.rrs.message.entity.WorderMessageEntity">
        <result property="id" column="id"></result>
        <result property="worderNo" column="worder_no"/>
        <result property="sendUserId" column="send_user_id"></result>
        <result property="receiveUserId" column="receive_user_id"></result>
        <result property="receiveUserMobile" column="receive_user_mobile"></result>
        <result property="messageTitle" column="message_title"></result>
        <result property="messageContent" column="message_content"/>
        <result property="messageType" column="message_type"></result>
        <result property="sendTime" column="send_time"></result>
        <result property="sendType" column="send_type"></result>
        <result property="processState" column="process_state"></result>
        <result property="worderExecStatus" column="worder_exec_status"></result>
        <result property="detailNumber" column="detail_number"/>
        <result property="triggerValidate" column="trigger_validate"/>
        <result property="sendTimeStamp" column="send_time_stamp"/>
        <result property="gmtCreate" column="gmt_create"></result>
        <result property="gmtModified" column="gmt_modified"></result>
    </resultMap>

    <select id="listWorderMessage" parameterType="com.bonc.rrs.message.entity.MessageQueryEntity"
            resultMap="worderMessageMap">
        select id, worder_no, send_user_id, receive_user_id, message_title, message_content
        , send_time, is_send, is_read, gmt_create, gmt_modified
        from worder_message
        where receive_user_id = #{userId}
        <if test="messageType != null and messageType != ''">
            and message_type = #{messageType}
        </if>
        <if test="read != null and read != ''">
            and is_read = #{read}
        </if>
        order by gmt_create desc
    </select>

    <select id="getMessageCount" parameterType="com.bonc.rrs.message.entity.MessageQueryEntity"
            resultType="java.lang.Integer">
        select count(1) total
        from worder_message
        where receive_user_id = #{userId}
        <if test="messageType != null and messageType != ''">
            and message_type = #{messageType}
        </if>
        <if test="read != null and read != ''">
            and is_read = #{read}
        </if>
    </select>


    <select id="selectwArningDelayMessage" resultType="com.bonc.rrs.worder.entity.WorderInformationEntity"
            resultMap="worderDelayMessageMap">
        select *
        from warning_delay_message
        where 1 = 1
          and process_state = #{processState}
    </select>

    <insert id="saveWorderMessage" parameterType="com.bonc.rrs.message.entity.WorderMessageEntity">
        insert into worder_message( worder_no, send_user_id, receive_user_id, message_title
                                  , message_content, send_time, is_send, is_read, gmt_create, message_type)
        values ( #{worderNo}, #{sendUserId}, #{receiveUserId}, #{messageTitle}, #{messageContent}
               , #{sendTime}, #{send}, #{read}, now(), #{messageType})
    </insert>

    <insert id="insertWarningDelayMessage" parameterType="com.bonc.rrs.message.entity.WorderMessageEntity"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into warning_delay_message(worder_no, send_user_id, receive_user_id, receive_user_mobile, message_title,
                                          message_content, message_type, send_time, send_type, process_state,
                                          worder_exec_status, detail_number, trigger_validate, send_time_stamp,
                                          gmt_create)
        values (#{worderNo}, #{sendUserId}, #{receiveUserId}, #{receiveUserMobile}, #{messageTitle}, #{messageContent},
                #{messageType}, #{sendTime}, #{sendType}, #{processState}, #{worderExecStatus}, #{detailNumber},
                #{triggerValidate}, #{sendTimeStamp}, now())
    </insert>

    <update id="updateWorderMessageRead" parameterType="java.util.List">
        update worder_message
        set gmt_modified = now(), is_read = '1'
        <where>
            <foreach collection="list" item="id" open="id in (" separator="," close=")">
                #{id}
            </foreach>
        </where>

    </update>

    <update id="updateWarningDelayMessageProcessState">
        update warning_delay_message
        set process_state = #{processState},
            gmt_modified  = now()
        where id = #{id}
    </update>

</mapper>