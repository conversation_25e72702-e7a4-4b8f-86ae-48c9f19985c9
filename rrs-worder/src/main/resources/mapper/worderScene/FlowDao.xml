<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.FlowDao">
    <select id="findFlowByFlowId" parameterType="java.lang.Integer"
            resultType="com.bonc.rrs.worder.entity.FlowEntity">
        select f.flow_id, f.name, f.default_flow_child_code from flow f
        where f.is_delete = 0
        and f.flow_id = #{flowId}
    </select>
</mapper>