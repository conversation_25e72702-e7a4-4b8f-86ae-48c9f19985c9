<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.sequence.dao.SequenceDao">

    <!-- ResultMap for Sequence Entity -->
    <resultMap id="sequenceResultMap" type="com.bonc.rrs.sequence.entity.SequenceEntity">
        <id column="sequence_name" property="sequenceName" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="INTEGER"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>