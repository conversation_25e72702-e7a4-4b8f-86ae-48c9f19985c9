<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.byd.dao.BydFaultCodesMappingMapper">

    <resultMap id="BaseResultMap" type="com.bonc.rrs.byd.domain.BydFaultCodesMapping">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="fieldId" column="field_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,code,name,field_id
    </sql>

    <select id="selectByFaultCode" resultType="com.bonc.rrs.byd.domain.BydFaultCodesMapping">
        SELECT bycm.code, bycm.name, bycm.type, bycm.field_id, ef.field_name, ef.field_class, ef.field_type, ef.is_notnull, ef.is_nessary
        FROM byd_fault_codes_mapping bycm
            INNER JOIN byd_fault_codes byc on (FIND_IN_SET(bycm.code, byc.image_code) OR FIND_IN_SET(bycm.code, byc.video_code) > 0)
            LEFT JOIN ext_field ef ON bycm.field_id = ef.field_id
        where byc.code = #{faultCode} and byc.is_leaf = 1
    </select>
</mapper>
