<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.byd.dao.ClaimFeeDetailMapper">

    <resultMap id="BaseResultMap" type="com.bonc.rrs.byd.domain.ClaimFeeDetail">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="orderCode" column="order_code" jdbcType="VARCHAR"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="fixUnitPrice" column="fix_unit_price" jdbcType="VARCHAR"/>
            <result property="claimUnitPrice" column="claim_unit_price" jdbcType="VARCHAR"/>
            <result property="unitCount" column="unit_count" jdbcType="VARCHAR"/>
            <result property="unitFlag" column="unit_flag" jdbcType="VARCHAR"/>
            <result property="fixPrice" column="fix_price" jdbcType="VARCHAR"/>
            <result property="claimPrice" column="claim_price" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_code,project_name,description,fix_unit_price,claim_unit_price,
        unit_count,unit_flag,fix_price,claim_price
    </sql>
</mapper>
