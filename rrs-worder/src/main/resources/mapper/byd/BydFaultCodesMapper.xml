<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.byd.dao.BydFaultCodesMapper">

    <resultMap id="BaseResultMap" type="com.bonc.rrs.byd.domain.BydFaultCodes">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="level" column="level" jdbcType="TINYINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
            <result property="parentName" column="parent_name" jdbcType="VARCHAR"/>
            <result property="imageCode" column="image_code" jdbcType="VARCHAR"/>
            <result property="imageName" column="image_name" jdbcType="VARCHAR"/>
            <result property="videoCode" column="video_code" jdbcType="VARCHAR"/>
            <result property="videoName" column="video_name" jdbcType="VARCHAR"/>
            <result property="isLeaf" column="is_leaf" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,level,code,name,parent_code,parent_name,
        image_code,image_name,video_code,video_name,is_leaf
    </sql>
</mapper>
