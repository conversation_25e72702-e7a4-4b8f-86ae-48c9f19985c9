<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.star.dao.StarMapper">
<!--    <resultMap id="StarDTOResullt" type="com.bonc.rrs.star.dto.StarDTO">-->
<!--        <result property="starId" column="star_id"/>-->
<!--        <result property="starName" column="star_name"/>-->
<!--        <result property="settlementCoefficient" column="settlement_coefficient"/>-->
<!--        <result property="computingCyclesId" column="computing_cycles_id"/>-->
<!--        <result property="dispatchRatio" column="dispatch_ratio"/>-->
<!--        <result property="dispatchRatio" column="dispatch_ratio"/>-->
<!--    </resultMap>-->
    <insert id="insertStarInfo" parameterType="com.bonc.rrs.star.dto.StarDTO">
        insert into worker_star(
            <if test="starName!=null">star_name,</if>
            <if test="starRelationId != null and starRelationId != 0 ">star_relation_id,</if>
            <if test="settlementCoefficient!=null">settlement_coefficient,</if>
            <if test="computingCyclesId!=null">computing_cycles_id,</if>
            <if test="dispatchRatio!=null">dispatch_ratio,</if>
            create_time,
            update_time
            )
            values(
            <if test="starName!=null">#{starName},</if>
            <if test="starRelationId != null and starRelationId != 0 ">#{starRelationId},</if>
            <if test="settlementCoefficient!=null">#{settlementCoefficient},</if>
            <if test="computingCyclesId!=null">#{computingCyclesId},</if>
            <if test="dispatchRatio!=null">#{dispatchRatio},</if>
             now(),now()
            )
        <selectKey keyProperty="starId" order="AFTER"  resultType="int">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>
    <!-- 关联信息插入 -->
    <insert id="insertStarDicRelationInfo" parameterType="com.bonc.rrs.star.dto.StarScoreDTO">
        insert into worker_star_dic_relation (star_id,dic_id,star_score)
        values
        <foreach collection="starScoreDTO" item="star" separator=",">
            (#{starId},#{star.dicId},#{star.starScore})
        </foreach>
    </insert>
</mapper>