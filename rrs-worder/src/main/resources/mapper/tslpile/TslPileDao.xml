<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.tslpile.dao.TslPileDao">

    <!-- 分页查询充电桩状态（包含仓库名称） -->
    <select id="selectPageWithWarehouse" resultType="com.bonc.rrs.tslpile.entity.TslPileEntity">
        SELECT p.*, w.news_warehouse_name as warehouse_name
        FROM tsl_pile p
        LEFT JOIN tsl_warehouse w ON p.warehouse_code = w.warehouse_code AND (w.deleted IS NULL OR w.deleted = 0)
        <where>
            <if test="params.companyOrderNumber != null and params.companyOrderNumber != ''">
                AND p.company_order_number LIKE CONCAT('%', #{params.companyOrderNumber}, '%')
            </if>
            <if test="params.expressNo != null and params.expressNo != ''">
                AND p.express_no LIKE CONCAT('%', #{params.expressNo}, '%')
            </if>
            <if test="params.status != null and params.status != ''">
                AND p.status = #{params.status}
            </if>
            <if test="params.warehouseCode != null and params.warehouseCode != ''">
                AND p.warehouse_code LIKE CONCAT('%', #{params.warehouseCode}, '%')
            </if>
        </where>
        ORDER BY p.update_time DESC
    </select>

    <!-- 查询充电桩状态列表（包含仓库名称） -->
    <select id="selectListWithWarehouse" resultType="com.bonc.rrs.tslpile.entity.TslPileEntity">
        SELECT p.*, w.news_warehouse_name as warehouse_name
        FROM tsl_pile p
        LEFT JOIN tsl_warehouse w ON p.warehouse_code = w.warehouse_code AND (w.deleted IS NULL OR w.deleted = 0)
        <where>
            <if test="params.companyOrderNumber != null and params.companyOrderNumber != ''">
                AND p.company_order_number LIKE CONCAT('%', #{params.companyOrderNumber}, '%')
            </if>
            <if test="params.expressNo != null and params.expressNo != ''">
                AND p.express_no LIKE CONCAT('%', #{params.expressNo}, '%')
            </if>
            <if test="params.status != null and params.status != ''">
                AND p.status = #{params.status}
            </if>
            <if test="params.warehouseCode != null and params.warehouseCode != ''">
                AND p.warehouse_code LIKE CONCAT('%', #{params.warehouseCode}, '%')
            </if>
        </where>
        ORDER BY p.update_time DESC
    </select>

</mapper>
