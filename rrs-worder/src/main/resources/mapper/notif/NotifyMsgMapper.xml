<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.notif.dao.NotifyMsgMapper">
    <resultMap id="BaseResultMap" type="com.bonc.rrs.notif.domain.NotifyMsg">
        <!--@mbg.generated-->
        <!--@Table notify_msg-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="out_msg_id" jdbcType="VARCHAR" property="outMsgId"/>
        <result column="notify_type" jdbcType="TINYINT" property="notifyType"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="vin" jdbcType="VARCHAR" property="vin"/>
        <result column="cms_code" jdbcType="VARCHAR" property="cmsCode"/>
        <result column="notify_content" jdbcType="VARCHAR" property="notifyContent"/>
        <result column="read_status" jdbcType="TINYINT" property="readStatus"/>
        <result column="read_channel" jdbcType="TINYINT" property="readChannel"/>
        <result column="read_time" jdbcType="TIMESTAMP" property="readTime"/>
        <result column="read_by_name" jdbcType="VARCHAR" property="readByName"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        out_msg_id,
        notify_type,
        order_code,
        vin,
        cms_code,
        notify_content,
        read_status,
        read_channel,
        read_time,
        read_by_name,
        error_msg,
        is_delete,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <!-- 查询通知列表 -->
    <select id="selectNotifyMsgList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM notify_msg
        WHERE is_delete = 0
        <if test="notifyType != null">
            AND notify_type = #{notifyType}
        </if>
        <if test="orderCode != null and orderCode != ''">
            AND order_code = #{orderCode}
        </if>
        <if test="readStatus != null">
            AND read_status = #{readStatus}
        </if>
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        order by id desc
    </select>

    <!-- 批量更新已读状态 -->
    <update id="updateReadStatus">
        UPDATE notify_msg
        SET read_status  = 1,
            read_time    = #{date},
            read_channel = 1,
            read_by_name = #{username},
            error_msg = null
        WHERE read_status = 0
          and id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateSingleReadStatus">
        UPDATE notify_msg
        SET read_status  = 1,
        read_time    = #{date},
        read_channel = 1,
        read_by_name = #{username},
        error_msg = null
        WHERE read_status = 0
        and id = #{id}
    </update>
</mapper>