<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderapp.dao.WorderOperationRecodeDao">

    <select id="getCreateTime" resultType="java.lang.String">
        select  max(create_time) from worder_operation_record where ((worder_status='1' and worder_exec_status ='5') or (worder_status='2' and worder_exec_status ='13'))  and worder_no ='${worderNo}'

    </select>
    <select id="getDay" resultType="java.lang.Integer">
        SELECT TIMESTAMPDIFF(HOUR,'${beginTime}','${endTime}')
    </select>
    <select id="selectCompletedTime" resultType="java.util.Date">
        select
            create_time
        from
            worder_operation_record wor
        where
            worder_no = #{worderNo}
          and worder_status = 2
          and worder_exec_status in (13, 14)
        order by
            create_time desc limit 1
    </select>

    <select id="selectServeyCompletedTime" resultType="java.util.Date">
        select
            create_time
        from
            worder_operation_record wor
        where
            worder_no = #{worderNo}
          and worder_status = 1
          and worder_exec_status = 5
        order by
            create_time desc limit 1
    </select>
</mapper>