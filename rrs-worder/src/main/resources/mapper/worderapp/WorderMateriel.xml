<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderapp.dao.WorderMaterielDao">

    <resultMap id="materielPriceListMap" type="com.bonc.rrs.worderapp.entity.po.MaterielPriceListPo">
        <result property="materielId" column="materiel_id"></result>
        <result property="worderId" column="worder_id"></result>
        <result property="materielNum" column="num"></result>
        <result property="materielName" column="name"></result>
        <result property="materielUnitPrice" column="price"></result>
    </resultMap>

    <select id="selectMateriel" resultType="Integer">
        select count(*) from worder_used_materiel
            <trim prefix="where" prefixOverrides="and|or">
                <if test="materielId != null">
                    and materiel_id = #{materielId}
                </if>
                <if test="worderId != null">
                    and worder_id = #{worderId}
                </if>
            </trim>
    </select>
    <select id="listSuiteMateriel" resultType="com.bonc.rrs.worderapp.entity.dto.MaterielPriceDto">
        select a.worder_id worderId,mt.id materielId
        ,concat(mt.materiel_type_value, '-', mt.materiel_child_type) materielName,c.num
        ,'0' price, IFNULL(e.num,0) realNum
        from worder_information a
        left join worder_template b on a.template_id = b.id
        left join suite_detail c on b.suite_id = c.suite_id
        left join materiel_information d on d.id = c.materiel_id
		left join worder_used_materiel e on e.worder_id = a.worder_id
		and e.materiel_id = c.materiel_id
        left join materiel_type mt on mt.id = d.materiel_type_id
        where a.worder_id = #{worderId}
    </select>

    <!--    物料费用清单-->
    <select id="listMaterielPrices" resultMap="materielPriceListMap">
        select a.materiel_id, a.num, a.worder_id, b.materiel_name name, d.price
        from worder_used_materiel a
        left join materiel_information b on a.materiel_id = b.id
        left join balance_rule_detail d on a.materiel_id = d.materiel_id
        where a.worder_id = #{worderId}
    </select>

    <select id="getSuiteMaterielNum" parameterType="com.bonc.rrs.worderapp.entity.dto.SuiteMaterielNumQueryDto"
        resultType="java.lang.String">
        select ifnull(c.num, '0') num
        from worder_information a
        left join worder_template b on a.template_id = b.id
        left join suite_detail c on b.suite_id = c.suite_id
        where a.worder_id = #{worderId} and c.materiel_id = #{materielId}
    </select>

    <resultMap id="worderMaterielMap" type="com.bonc.rrs.worderapp.entity.po.WorderMaterielPo">
        <result property="materielId" column="materiel_id"></result>
        <result property="worderId" column="worder_id"></result>
        <result property="materielNum" column="num"></result>
        <result property="materielName" column="name"></result>
        <result property="materielUnit" column="materiel_unit"></result>
        <result property="materielUnitValue" column="materiel_unit_value"></result>
        <result column="materiel_brand" property="brandId"></result>
        <result column="materiel_brand_value" property="brandValue"></result>
        <result column="materiel_spec" property="materielSpec"></result>
    </resultMap>

    <select id="listWorderMateriel" resultMap="worderMaterielMap">
        select a.id,a.materiel_id, a.worder_id, a.num, b.materiel_name name
        , b.materiel_unit, b.materiel_unit_value
        ,b.materiel_brand,c.brand_name materiel_brand_value,b.materiel_spec
        from worder_used_materiel a
        left join materiel_information b on a.materiel_id = b.id
        left join brand c on c.id = a.brand_id
        where a.worder_id = #{worderId}  and  a.materiel_id not  in (select
        me.id
        from
        materiel_information me
        where
        me.materiel_name in(
        select
        detail_name
        from
        sys_dictionary_detail sdd
        where
        sdd.dictionary_id = (
        select
        sd.id
        from
        sys_dictionary sd
        where
        sd.dic_number = 'fixed_material') ))
    </select>
    <select id="fixedListWorderMateriel" resultMap="worderMaterielMap">
        select
        a.id,a.materiel_id, a.worder_id, a.num, b.materiel_name name
        , b.materiel_unit, b.materiel_unit_value
        ,b.materiel_brand,c.brand_name materiel_brand_value,b.materiel_spec
        from
        worder_used_materiel a
        left join materiel_information b on
        a.materiel_id = b.id
        left join brand c on c.id = a.brand_id
        where
        a.worder_id = #{worderId}
        and b.materiel_name in (
        select
        sdd.detail_name
        from
        sys_dictionary_detail sdd
        where
        sdd.dictionary_id =(
        select
        sd.id
        from
        sys_dictionary sd
        where
        sd.dic_number = 'fixed_material'))
    </select>
    <!--    物料类别-->
    <resultMap id="materielTypeMap" type="com.bonc.rrs.worderapp.entity.po.MaterielTypePo">
        <result property="materielTypeId" column="id"></result>
        <result property="materielTypeName" column="name"></result>
    </resultMap>

    <!--    获取物料所有类别-->
    <!--    需要讨论物料类别表是否要改动-->
    <select id="listMaterielType" resultMap="materielTypeMap">
        select distinct a.id, a.name from (
        select d.id, d.materiel_name name from worder_information a
        left join worder_template b on a.template_id = b.id
        left join balance_rule_detail c on b.attendant_balance_rule_id = c.rule_id
        left join materiel_information d on c.materiel_id = d.id
        where a.worder_id = #{worderId}  and
        d.id not  in (select
        me.id
        from
        materiel_information me
        where
        me.materiel_name in(
        select
        detail_name
        from
        sys_dictionary_detail sdd
        where
        sdd.dictionary_id = (
        select
        sd.id
        from
        sys_dictionary sd
        where
        sd.dic_number = 'fixed_material') ))
        union
        select d.id, d.materiel_name name from worder_information a
        left join worder_template b on a.template_id = b.id
        left join balance_rule_detail c on b.company_balance_rule_id = c.rule_id
        left join materiel_information d on c.materiel_id = d.id
        where a.worder_id = #{worderId} and c.is_suite = 0 and
        d.id not  in (select
        me.id
        from
        materiel_information me
        where
        me.materiel_name in(
        select
        detail_name
        from
        sys_dictionary_detail sdd
        where
        sdd.dictionary_id = (
        select
        sd.id
        from
        sys_dictionary sd
        where
        sd.dic_number = 'fixed_material') ))
        ) a
        order by a.id
    </select>


    <!--    物料信息-->
    <resultMap id="materielBrandMap" type="com.bonc.rrs.worderapp.entity.po.MaterielBrandPo">
        <result property="materielBrandId" column="materiel_brand"></result>
        <result property="materielBrandName" column="materiel_brand_value"></result>
    </resultMap>

    <!--    根据物料类别获取品牌-->
    <select id="listMaterielBrandByType" resultMap="materielBrandMap">
        select distinct d.materiel_brand, d.materiel_brand_value from worder_information a
        left join worder_template b on a.template_id = b.id
        left join balance_rule_detail c on b.attendant_balance_rule_id = c.rule_id
        left join materiel_information d on c.materiel_id = d.id
        where a.worder_id = #{worderId}
        <if test="materielTypeId != null and materielTypeId != ''">
            and d.id = #{materielTypeId}
        </if>
    </select>

    <insert id="saveMateriel" useGeneratedKeys="true" keyProperty="id"
        parameterType="com.bonc.rrs.worderapp.entity.vo.MaterielSaveVo">
        insert into worder_used_materiel (worder_id, materiel_id, num, materiel_spec, take_picture, brand_id)
        values (#{worderId}, #{materielId}, #{materielNum}, #{materielSpec}, #{takePictures}, #{brandId})
    </insert>



    <resultMap id="materielSpecMap" type="com.bonc.rrs.worderapp.entity.po.MaterielSpecPo">
        <result property="id" column="id"></result>
        <result property="materielSpec" column="materiel_spec"></result>
    </resultMap>

    <!--    根据物料类别和品牌ID获取规格-->
    <select id="listMaterielSpecByTypeAndBrand" parameterType="com.bonc.rrs.worderapp.entity.vo.MaterielSpecQueryVo"
        resultMap="materielSpecMap">
        select distinct d.id, d.materiel_spec from worder_information a
        left join worder_template b on a.template_id = b.id
        left join balance_rule_detail c on b.attendant_balance_rule_id = c.rule_id
        left join materiel_information d on c.materiel_id = d.id
        where a.worder_id = #{worderId}
        <if test="materielTypeId != null and materielTypeId != ''">
            and d.id = #{materielTypeId}
        </if>
        <if test="materielBrandId != null and materielBrandId != ''">
            and d.materiel_brand = #{materielBrandId}
        </if>
    </select>

    <update id="updateMateriel" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.bonc.rrs.worderapp.entity.vo.MaterielSaveVo">
        update worder_used_materiel
        set num = #{materielNum}
        where id = #{id}
    </update>


    <delete id="deleteMateriel" parameterType="com.bonc.rrs.worderapp.entity.vo.MaterielSaveVo">
        delete from worder_used_materiel
        where id in
        <foreach collection="deleteIds" item="Id" index="index" open="(" close=")" separator=",">
            #{Id}
        </foreach>
    </delete>
    <delete id="deleteByUserMaterielId" parameterType="String" >
        delete from worder_used_materiel
        where id=#{userMaterielId}
    </delete>
    <delete id="deleteMaterielEntity">
        delete from worder_used_materiel where worder_id = #{worderId} and materiel_id = #{materielId}
    </delete>
    <select id="getByWorderId" parameterType="com.bonc.rrs.worderapp.entity.vo.MaterielSaveVo" resultType="java.lang.Integer">
       select id from worder_used_materiel where worder_id = #{worderId}
    </select>

</mapper>