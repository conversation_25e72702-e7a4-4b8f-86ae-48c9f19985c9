<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderapp.dao.WorderOrderDao">

    <resultMap id="fieldPoMap" type="com.bonc.rrs.worderapp.entity.po.FieldPo">
        <result property="worderNo" column="worder_no"></result>
        <result property="fieldId" column="field_id"></result>
        <result property="fieldName" column="field_name"></result>
        <result property="filedDicKey" column="filed_dic_key"></result>
        <result property="notNull" column="is_notnull"></result>
        <result property="fieldType" column="field_type"></result>
        <result property="fieldDicKeyMark" column="field_dic_key_mark"></result>
        <result property="necessary" column="is_nessary"></result>
        <result property="selectData" column="select_data"></result>
    </resultMap>

    <select id="listWorderFieldByTemplateId" parameterType="com.bonc.rrs.worderapp.entity.dto.WorderFieldDto"
        resultMap="fieldPoMap">
        select c.field_id, c.field_name, c.field_type
        ,c.field_desc,c.filed_dic_key, c.is_notnull, c.field_dic_key_mark, c.is_nessary, c.select_data
        from worder_template_field b
        left join ext_field c on b.field_id = c.field_id
        where b.template_id = #{templateId}
        and c.field_purpose = 1
        and c.deleted = '0'
        order by b.sort, c.field_id
    </select>

    <select id="listWorderField" parameterType="com.bonc.rrs.worderapp.entity.dto.WorderFieldDto" resultMap="fieldPoMap">
        select a.worder_no, c.field_id, c.field_name, c.field_type
        ,c.filed_dic_key, c.is_notnull, c.field_dic_key_mark, c.is_nessary, c.select_data
        , d.field_value fieldValues, c.field_class fieldClass, d.field_value_dup fileName,sf.`path` imgPath ,replace(c.field_desc,char(10),'\n') remake
        from worder_information a
        left join worder_template_field b on a.template_id = b.template_id
        left join ext_field c on b.field_id = c.field_id
        left join sys_file sf on c.file_sample = sf.file_id
        left join (select dd.* from worder_ext_field dd ,(select max(id) as id ,field_id,field_name,worder_no
        from worder_ext_field where worder_no = #{worderNo} group by field_id,field_name,worder_no) f where f.id=dd.id ) d  on d.field_id = c.field_id
        where a.worder_no = #{worderNo}
        <if test="fieldPurpose != null and fieldPurpose != ''">
            and c.field_purpose = #{fieldPurpose}
        </if>
        <if test="fieldClass != null and fieldClass != ''">
            and c.field_class = #{fieldClass}
        </if>
        and c.deleted = '0'
        order by b.sort, c.field_id asc
    </select>

    <resultMap id="fieldInfoPoMap" type="com.bonc.rrs.worderapp.entity.po.FieldInfoPo">
        <result property="worderNo" column="worder_no"></result>
        <result property="fieldId" column="field_id"></result>
        <result property="fieldName" column="field_name"></result>
        <result property="filedDicKey" column="filed_dic_key"></result>
        <result property="notNull" column="is_notnull"></result>
        <result property="fieldType" column="field_type"></result>
        <result property="fieldValue" column="field_value"></result>
        <result property="fieldValueDup" column="field_value_dup"></result>
        <result property="fieldTypeValue" column="field_type_value"></result>
        <result property="checkStatus" column="check_status"></result>
        <result property="noPassReason" column="no_pass_reason"></result>
    </resultMap>

    <select id="listWorderFieldInfo" resultMap="fieldInfoPoMap">
        select  d.worder_no, d.field_id, d.field_name, d.field_value
        ,d.field_value_dup, c.field_type, c.filed_dic_key, c.field_type_value, c.is_notnull
        ,d.check_status, d.no_pass_reason, c.field_class fieldClass
        from worder_information a
        left join worder_template_field b on a.template_id = b.template_id
        left join ext_field c on b.field_id = c.field_id
        left join (select dd.* from worder_ext_field dd ,(select max(id) as id ,field_id,field_name,worder_no
        from worder_ext_field where worder_no = #{worderNo} group by field_id,field_name,worder_no) f where f.id=dd.id ) d  on d.field_id = c.field_id
        where a.worder_no = #{worderNo} and d.field_id = b.field_id
        <if test="fieldClass != null and fieldClass != ''">
            and c.field_class = #{fieldClass}
        </if>
        <if test="fieldPurpose != null and fieldPurpose != ''">
            and c.field_purpose = #{fieldPurpose}
        </if>
        order by b.sort, d.field_id asc
    </select>

    <insert id="saveWorderFieldInfo" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.bonc.rrs.worderapp.entity.vo.FieldVo">
        insert into worder_ext_field (worder_no, field_id, field_name, field_value
        , field_value_dup, create_time)
        values (#{worderNo}, #{fieldId}, #{fieldName}, #{fieldValue}, #{fieldValueDup}, now())
    </insert>

    <!--    根据工单ID和字段ID修改工单字段信息-->
    <update id="updateWorderFieldInfo" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.bonc.rrs.worderapp.entity.vo.FieldVo">
        update worder_ext_field
        <set>
            field_value = #{fieldValue},
            field_value_dup = #{fieldValueDup},
            update_time = now()
        </set>
        where worder_no = #{worderNo} and field_id = #{fieldId}
    </update>

    <resultMap id="worderInformationMap" type="com.bonc.rrs.worder.entity.WorderInformationEntity">
        <result property="worderId" column="worder_id"></result>
        <result property="worderNo" column="worder_no"></result>
        <result property="worderStatus" column="worder_status"></result>
        <result property="conveyAppointTime" column="convey_appoint_time"></result>
        <result property="installAppointTime" column="install_appoint_time"></result>
        <result property="conveySignOutTime" column="convey_sign_out_time"></result>
        <result property="installSignOutTime" column="install_sign_out_time"></result>
        <result property="worderExecStatus" column="worder_exec_status"></result>

    </resultMap>

    <select id="getWorderInformation" resultMap="worderInformationMap">
        select a.worder_status, a.worder_id, a.worder_no, a.convey_appoint_time, a.install_appoint_time
        , a.convey_sign_out_time, a.install_sign_out_time, worder_exec_status
        , b.path conveySignUrl, c.path installSignUrl
        from worder_information a
        left join sys_file b on a.convey_sign_picture = b.file_id
        left join sys_file c on a.install_sign_picture = c.file_id
        where worder_no = #{worderNo}
    </select>

    <select id="getWorderDetails" resultType="com.bonc.rrs.worderapp.entity.WorderInformation">
        select a.worder_id worderId,a.worder_type_id,a.company_order_number, a.worder_status worderStatus, a.worder_no worderNo
        , a.convey_appoint_time conveyAppointTime
        , a.install_appoint_time installAppointTime, worder_exec_status worderExecStatus
        , a.user_name clientName, a.user_phone clientPhone, a.address_dup clientAdress
        , b.company_name companyName, a.convey_sign_out_time conveySignOutTime
        , a.install_sign_out_time installSignOutTime
       <!-- , a.charge_model chargeModel, a.car_vin carVin-->
        , a.worder_Incre_status worderIncreStatus
        , a.ticket_status ticketStatus
        , a.user_actual_cost
        ,( select field_value from worder_ext_field where  field_id ='993'  and  worder_no = #{worderNo}) chargeModel
        ,( select field_value from worder_ext_field where  field_id ='153'  and  worder_no = #{worderNo}) carVin
        ,a.install_sign_area, brand_name, brand_id
        from worder_information a
        left join company_information b on a.company_id = b.company_id
        left join worder_template wt on a.template_id = wt.id
        left join brand nr on wt.brand_id = nr.id
        where worder_no = #{worderNo}
    </select>

    <!--    工单信息表信息修改-->
    <update id="updateWorderInformation" parameterType="com.bonc.rrs.worderapp.entity.dto.WorderInformationDto">
        update worder_information
        <set>
            <if test="worderStatus != null and worderStatus != ''">
                worder_status = #{worderStatus},
            </if>
            <if test="worderExecStatus != null and worderExecStatus != ''">
                worder_exec_status = #{worderExecStatus},
            </if>
            <if test="conveyAppointTime != null and conveyAppointTime != ''">
                convey_appoint_time = #{conveyAppointTime},
            </if>
            <if test="installAppointTime != null and installAppointTime != ''">
                install_appoint_time = #{installAppointTime},
            </if>
            <if test="conveySignPicture != null and conveySignPicture != ''">
                convey_sign_picture = #{conveySignPicture},
            </if>
            <if test="installSignPicture != null and installSignPicture != ''">
                install_sign_picture = #{installSignPicture},
            </if>
            <if test="conveySignOutReason != null and conveySignOutReason != ''">
                convey_sign_out_reason = #{conveySignOutReason},
            </if>
            <if test="installSignOutReason != null and installSignOutReason != ''">
                install_sign_out_reason = #{installSignOutReason},
            </if>
            <if test="conveySignOutTime != null and conveySignOutTime != ''">
                convey_sign_out_time = #{conveySignOutTime},
            </if>
            <if test="installSignOutTime != null and installSignOutTime != ''">
                install_sign_out_time = #{installSignOutTime},
            </if>
            <if test="worderLevel != null and worderLevel != ''">
                worder_level = #{worderLevel}
            </if>
            <if test="conveySignArea != null and conveySignArea != ''">
                convey_sign_area = #{conveySignArea},
            </if>
            <if test="installSignArea != null and installSignArea != ''">
                install_sign_area = #{installSignArea},
            </if>
            <if test="conveySignTime != null and conveySignTime != ''">
                convey_sign_time = #{conveySignTime},
            </if>
            <if test="installSignTime != null and installSignTime != ''">
                install_sign_time = #{installSignTime},
            </if>
            <if test="nextContactTime != null and nextContactTime != ''">
                next_contact_time = #{nextContactTime},
            </if>
        </set>
        where worder_no = #{worderNo}
    </update>

    <update id="updateWorderInfo">
        update worder_information
        <set>
            <if test="conveyAppointTime != null and conveyAppointTime != ''">
                convey_appoint_time = #{conveyAppointTime},
            </if>
            <if test="installAppointTime != null and installAppointTime != ''">
                install_appoint_time = #{installAppointTime},
            </if>
            <if test="conveySignPicture != null and conveySignPicture != ''">
                convey_sign_picture = #{conveySignPicture},
            </if>
            <if test="installSignPicture != null and installSignPicture != ''">
                install_sign_picture = #{installSignPicture},
            </if>
            <if test="conveySignOutReason != null and conveySignOutReason != ''">
                convey_sign_out_reason = #{conveySignOutReason},
            </if>
            <if test="installSignOutReason != null and installSignOutReason != ''">
                install_sign_out_reason = #{installSignOutReason},
            </if>
            <if test="conveySignOutTime != null and conveySignOutTime != ''">
                convey_sign_out_time = #{conveySignOutTime},
            </if>
            <if test="installSignOutTime != null and installSignOutTime != ''">
                install_sign_out_time = #{installSignOutTime},
            </if>
            <if test="worderLevel != null and worderLevel != ''">
                worder_level = #{worderLevel}
            </if>
            <if test="conveySignArea != null and conveySignArea != ''">
                convey_sign_area = #{conveySignArea},
            </if>
            <if test="installSignArea != null and installSignArea != ''">
                install_sign_area = #{installSignArea},
            </if>
            <if test="conveySignTime != null and conveySignTime != ''">
                convey_sign_time = #{conveySignTime},
            </if>
            <if test="installSignTime != null and installSignTime != ''">
                install_sign_time = #{installSignTime},
            </if>
            <if test="nextContactTime != null and nextContactTime != ''">
                next_contact_time = #{nextContactTime},
            </if>
        </set>
        where worder_no = #{worderNo}
    </update>

    <update id="updateWorderLevel">
        update worder_information
        <set>
            <if test="worderLevel != null and worderLevel != ''">
                worder_level = #{worderLevel}
            </if>
        </set>
        where worder_no = #{worderNo}
    </update>
    <select id="getWorderFieldInfoCount" parameterType="com.bonc.rrs.worderapp.entity.dto.WorderExtFieldQueryDto"
            resultType="java.lang.Integer">
        select count(1) num from worder_ext_field
        where worder_no = #{worderNo} and field_id = #{fieldId}
    </select>

    <select id="listWorderInformation" parameterType="java.util.Map" resultType="com.bonc.rrs.worderapp.entity.WorderInformation">
        select * from (
            select a.worder_no worderNo, convey_appoint_time conveyAppointTime
            , worder_status worderStatus, worder_exec_status worderExecStatus
            , install_appoint_time installAppointTime, (case when length(wef1.field_value) > 0 then concat(a.user_name,'(',wef1.field_value,'KW)') else a.user_name end) clientName
            , a.user_phone clientPhone, a.address_dup clientAdress,a.create_time
            , c.company_name companyName,wef.field_value sellType,wia.attribute_value stopStatus, 1 colorLabel
            , a.template_id templateId, a.worder_type_id worderTypeId, a.company_order_number as companyOrderNumber
            from worder_information a
            left join biz_attendant b on a.service_id = b.id
            left join company_information c on a.company_id = c.company_id
            left join worder_ext_field wef on a.worder_no = wef.worder_no and wef.field_id = '1697'
            left join worder_ext_field wef1 on a.worder_no = wef1.worder_no and wef1.field_id = '1694'
            left join worder_information_attribute wia on wia.worder_id = a.worder_id and wia.is_delete = 0 and wia.attribute = 'Suspend' and wia.attribute_code = 'Suspend-flag'
            <where>
                a.worder_exec_status in (2, 3, 4, 6, 9, 10, 11, 12, 14) and a.is_delete = 0
                <if test="userId != null and userId != ''">
                    and b.user_id = #{userId}
                </if>
                <if test="finished != null and finished == '0'.toString()">
                    and a.worder_status <![CDATA[<]]> #{worderFinished} and a.worder_status >= #{worderConveyStatus}
                </if>
                <if test="finished != null and finished == '1'.toString()">
                    and a.worder_status = #{worderFinished}
                </if>
                <if test="worderNo != null and worderNo != ''">
                    and a.worder_no = #{worderNo}
                </if>
                <if test="inputValue != null and inputValue != ''">
                    and (a.company_order_number like concat(#{inputValue}, '%') or a.user_name like concat('%', #{inputValue}, '%') or a.user_phone like concat('%', #{inputValue}, '%'))
                </if>

                <choose>
                    <when test="queryType != null and queryType == 'jrdb'">
                        and
                        (
                            (a.worder_exec_status = 4 and a.worder_status = 1)
                            or (a.worder_exec_status = 12 and a.worder_status = 2)

                            or (a.worder_exec_status = 6 and a.worder_status = 1)
                            or (a.worder_exec_status = 14 and a.worder_status = 2)

                            or (
                                a.worder_exec_status = 3
                                and a.worder_status = 1
                                and a.convey_appoint_time <![CDATA[ < ]]> now()
                            )
                            or (
                                a.worder_exec_status = 3
                                and a.worder_status = 1
                                and a.convey_appoint_time <![CDATA[ >= ]]> now()
                                and a.convey_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                            )

                            or (
                                a.worder_exec_status = 11
                                and a.worder_status = 2
                                and a.install_appoint_time <![CDATA[ < ]]> now()
                            )
                            or (
                                a.worder_exec_status = 11
                                and a.worder_status = 2
                                and a.install_appoint_time <![CDATA[ >= ]]> now()
                                and a.install_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                            )
                        )
                    </when>
                    <when test="queryType != null and queryType == 'zldtj'">
                        and
                        (
                            (a.worder_exec_status = 4 and a.worder_status = 1)
                            or (a.worder_exec_status = 12 and a.worder_status = 2)
                        )
                    </when>
                    <when test="queryType != null and queryType == 'zldzg'">
                        and
                        (
                            (a.worder_exec_status = 6 and a.worder_status = 1)
                            or (a.worder_exec_status = 14 and a.worder_status = 2)
                        )
                    </when>
                    <when test="queryType != null and queryType == 'yyykc'">
                        and
                        (
                            (
                                a.worder_exec_status = 3
                                and a.worder_status = 1
                                and a.convey_appoint_time <![CDATA[ < ]]> now()
                            )
                            or (
                                a.worder_exec_status = 3
                                and a.worder_status = 1
                                and a.convey_appoint_time <![CDATA[ >= ]]> now()
                                and a.convey_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                            )
                        )
                    </when>
                    <when test="queryType != null and queryType == 'yyyaz'">
                        and
                        (
                            (
                                a.worder_exec_status = 11
                                and a.worder_status = 2
                                and a.install_appoint_time <![CDATA[ < ]]> now()
                            )
                            or (
                                a.worder_exec_status = 11
                                and a.worder_status = 2
                                and a.install_appoint_time <![CDATA[ >= ]]> now()
                                and a.install_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                            )
                        )
                    </when>
                </choose>
            </where>
        ) a
        UNION
        select * from (
            select a.worder_no worderNo, convey_appoint_time conveyAppointTime
            , worder_status worderStatus, worder_exec_status worderExecStatus
            , install_appoint_time installAppointTime, a.user_name clientName
            , a.user_phone clientPhone, a.address_dup clientAdress,a.create_time
            , c.company_name companyName,wef.field_value sellType,wia.attribute_value stopStatus, 0 colorLabel
            , a.template_id templateId, a.worder_type_id worderTypeId, a.company_order_number as companyOrderNumber
            from worder_information a
            left join biz_attendant b on a.service_id = b.id
            left join company_information c on a.company_id = c.company_id
            left join worder_ext_field wef on a.worder_no = wef.worder_no and field_id = '1697'
        left join worder_information_attribute wia on wia.worder_id = a.worder_id and wia.is_delete = 0 and wia.attribute = 'Suspend' and wia.attribute_code = 'Suspend-flag'
            <where>
                a.worder_exec_status not in (2, 3, 4, 6, 9, 10, 11, 12, 14) and a.is_delete = 0
                <if test="userId != null and userId != ''">
                    and b.user_id = #{userId}
                </if>
                <if test="finished != null and finished == '0'.toString()">
                    and a.worder_status <![CDATA[<]]> #{worderFinished} and a.worder_status >= #{worderConveyStatus}
                </if>
                <if test="finished != null and finished == '1'.toString()">
                    and a.worder_status = #{worderFinished}
                </if>
                <if test="worderNo != null and worderNo != ''">
                    and a.worder_no = #{worderNo}
                </if>
                <if test="inputValue != null and inputValue != ''">
                    and (a.company_order_number = #{inputValue} or a.user_name = #{inputValue} or a.user_phone = #{inputValue})
                </if>

                <choose>
                    <when test="queryType != null and queryType == 'jrdb'">
                        and
                        (
                            (a.worder_exec_status = 4 and a.worder_status = 1)
                            or (a.worder_exec_status = 12 and a.worder_status = 2)

                            or (a.worder_exec_status = 6 and a.worder_status = 1)
                            or (a.worder_exec_status = 14 and a.worder_status = 2)

                            or (
                                a.worder_exec_status = 3
                                and a.worder_status = 1
                                and a.convey_appoint_time <![CDATA[ < ]]> now()
                            )
                            or (
                                a.worder_exec_status = 3
                                and a.worder_status = 1
                                and a.convey_appoint_time <![CDATA[ >= ]]> now()
                                and a.convey_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                            )

                            or (
                                a.worder_exec_status = 11
                                and a.worder_status = 2
                                and a.install_appoint_time <![CDATA[ < ]]> now()
                            )
                            or (
                                a.worder_exec_status = 11
                                and a.worder_status = 2
                                and a.install_appoint_time <![CDATA[ >= ]]> now()
                                and a.install_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                            )
                        )
                    </when>
                    <when test="queryType != null and queryType == 'zldtj'">
                        and
                        (
                            (a.worder_exec_status = 4 and a.worder_status = 1)
                            or (a.worder_exec_status = 12 and a.worder_status = 2)
                        )
                    </when>
                    <when test="queryType != null and queryType == 'zldzg'">
                        and
                        (
                            (a.worder_exec_status = 6 and a.worder_status = 1)
                            or (a.worder_exec_status = 14 and a.worder_status = 2)
                        )
                    </when>
                    <when test="queryType != null and queryType == 'yyykc'">
                        and
                        (
                            (
                                a.worder_exec_status = 3
                                and a.worder_status = 1
                                and a.convey_appoint_time <![CDATA[ < ]]> now()
                            )
                            or (
                                a.worder_exec_status = 3
                                and a.worder_status = 1
                                and a.convey_appoint_time <![CDATA[ >= ]]> now()
                                and a.convey_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                            )
                        )
                    </when>
                    <when test="queryType != null and queryType == 'yyyaz'">
                        and
                        (
                            (
                                a.worder_exec_status = 11
                                and a.worder_status = 2
                                and a.install_appoint_time <![CDATA[ < ]]> now()
                            )
                            or (
                                a.worder_exec_status = 11
                                and a.worder_status = 2
                                and a.install_appoint_time <![CDATA[ >= ]]> now()
                                and a.install_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                            )
                        )
                    </when>
                </choose>
            </where>
        ) b
        order by colorLabel desc,create_time desc
        limit ${currPage}, ${pageSize}
    </select>

    <select id="getFieldInfo" parameterType="java.util.Map" resultType="com.bonc.rrs.worderapp.entity.po.FieldInfoPo">
        select field_id fieldId, field_name fieldName, field_value fieldValue, field_value_dup fieldValueDup
        from worder_ext_field
        where worder_no = #{worderNo} and field_id = #{fieldId}
    </select>

    <select id="getWorderInformationCount" resultType="java.lang.String">
        select count(1)
        from worder_information a
        left join biz_attendant b on a.service_id = b.id
        <where>
            <if test="colorLabel != null and colorLabel == '0'.toString()">
                and a.worder_exec_status in (2, 3, 4, 6, 9, 10, 11, 12, 14)
            </if>
            <if test="userId != null and userId != ''">
                and b.user_id = #{userId}
            </if>
            <if test="finished != null and finished == '0'.toString()">
                and worder_status <![CDATA[<]]> #{worderFinished} and worder_status >= #{worderConveyStatus}
            </if>
            <if test="finished != null and finished == '1'.toString()">
                and worder_status = #{worderFinished}
            </if>
            <if test="worderNo != null and worderNo != ''">
                and a.worder_no = #{worderNo}
            </if>
            <choose>
                <when test="queryType != null and queryType == 'jrdb'">
                    and
                    (
                        (a.worder_exec_status = 4 and a.worder_status = 1)
                        or (a.worder_exec_status = 12 and a.worder_status = 2)

                        or (a.worder_exec_status = 6 and a.worder_status = 1)
                        or (a.worder_exec_status = 14 and a.worder_status = 2)

                        or (
                            a.worder_exec_status = 3
                            and a.worder_status = 1
                            and a.convey_appoint_time <![CDATA[ < ]]> now()
                        )
                        or (
                            a.worder_exec_status = 3
                            and a.worder_status = 1
                            and a.convey_appoint_time <![CDATA[ >= ]]> now()
                            and a.convey_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                        )

                        or (
                            a.worder_exec_status = 11
                            and a.worder_status = 2
                            and a.install_appoint_time <![CDATA[ < ]]> now()
                        )
                        or (
                            a.worder_exec_status = 11
                            and a.worder_status = 2
                            and a.install_appoint_time <![CDATA[ >= ]]> now()
                            and a.install_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                        )
                    )
                </when>
                <when test="queryType != null and queryType == 'zldtj'">
                    and
                    (
                        (a.worder_exec_status = 4 and a.worder_status = 1)
                        or (a.worder_exec_status = 12 and a.worder_status = 2)
                    )
                </when>
                <when test="queryType != null and queryType == 'zldzg'">
                    and
                    (
                        (a.worder_exec_status = 6 and a.worder_status = 1)
                        or (a.worder_exec_status = 14 and a.worder_status = 2)
                    )
                </when>
                <when test="queryType != null and queryType == 'yyykc'">
                    and
                    (
                        (
                            a.worder_exec_status = 3
                            and a.worder_status = 1
                            and a.convey_appoint_time <![CDATA[ < ]]> now()
                        )
                        or (
                            a.worder_exec_status = 3
                            and a.worder_status = 1
                            and a.convey_appoint_time <![CDATA[ >= ]]> now()
                            and a.convey_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                        )
                    )
                </when>
                <when test="queryType != null and queryType == 'yyyaz'">
                    and
                    (
                        (
                            a.worder_exec_status = 11
                            and a.worder_status = 2
                            and a.install_appoint_time <![CDATA[ < ]]> now()
                        )
                        or (
                            a.worder_exec_status = 11
                            and a.worder_status = 2
                            and a.install_appoint_time <![CDATA[ >= ]]> now()
                            and a.install_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                        )
                    )
                </when>
            </choose>
        </where>
    </select>

    <select id="queryTypeTitleData" resultType="map" parameterType="map" >
        select
            SUM(if(
                (wi.worder_exec_status = 4 and wi.worder_status = 1)
                or (wi.worder_exec_status = 12 and wi.worder_status = 2)

                or (wi.worder_exec_status = 6 and wi.worder_status = 1)
                or (wi.worder_exec_status = 14 and wi.worder_status = 2)

                or (
                    wi.worder_exec_status = 3
                    and wi.worder_status = 1
                    and wi.convey_appoint_time <![CDATA[ < ]]> now()
                )
                or (
                    wi.worder_exec_status = 3
                    and wi.worder_status = 1
                    and wi.convey_appoint_time <![CDATA[ >= ]]> now()
                    and wi.convey_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                )

                or (
                    wi.worder_exec_status = 11
                    and wi.worder_status = 2
                    and wi.install_appoint_time <![CDATA[ < ]]> now()
                )
                or (
                    wi.worder_exec_status = 11
                    and wi.worder_status = 2
                    and wi.install_appoint_time <![CDATA[ >= ]]> now()
                    and wi.install_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                )
            , 1, 0)) as `jrdb`,
            SUM(if(
                (wi.worder_exec_status = 4 and wi.worder_status = 1)
                or (wi.worder_exec_status = 12 and wi.worder_status = 2)
                , 1, 0)) as `zldtj`,
            SUM(if(
                (wi.worder_exec_status = 6 and wi.worder_status = 1)
                or (wi.worder_exec_status = 14 and wi.worder_status = 2)
                , 1, 0)) as `zldzg`,
            SUM(if(
                (
                    wi.worder_exec_status = 3
                    and wi.worder_status = 1
                    and wi.convey_appoint_time <![CDATA[ < ]]> now()
                )
                or (
                    wi.worder_exec_status = 3
                    and wi.worder_status = 1
                    and wi.convey_appoint_time <![CDATA[ >= ]]> now()
                    and wi.convey_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                )
                , 1, 0)) as `yyykc`,
            sum(if(
                (
                    wi.worder_exec_status = 11
                    and wi.worder_status = 2
                    and wi.install_appoint_time <![CDATA[ < ]]> now()
                )
                or (
                    wi.worder_exec_status = 11
                    and wi.worder_status = 2
                    and wi.install_appoint_time <![CDATA[ >= ]]> now()
                    and wi.install_appoint_time <![CDATA[ <= ]]> date_format(current_date, '%Y-%m-%d 23:59:59')
                )
                , 1, 0)) as `yyyaz`
        from
            worder_information wi
        left join worder_template wt on
            wi.template_id = wt.id
        left join biz_attendant ba on
            wi.service_id = ba.id
        where
            wi.is_delete = 0
            and wi.worder_status <![CDATA[ < ]]> 3
            <if test="userId != null and userId != ''">
                and ba.user_id = #{userId}
            </if>
    </select>

    <select id="listDicKey" resultType="com.bonc.rrs.worderapp.entity.DicKeyResultEntity">
        select b.dictionary_id dictionaryId, b.detail_name selectValue, b.id selectKey
        from sys_dictionary a
        left join sys_dictionary_detail b on a.id = b.dictionary_id
        where a.id = #{dictionaryId}
    </select>



    <resultMap id="dicKeyMap" type="com.bonc.rrs.worderapp.entity.vo.DicKeyVo">
        <result property="dicName" column="dic_name"></result>
        <collection property="selects" ofType="com.bonc.rrs.worderapp.entity.DicKeyResultEntity">
            <result property="value" column="detail_name"></result>
            <result property="key" column="id"></result>
        </collection>
    </resultMap>

    <select id="listDicKeys" parameterType="java.util.List" >
        select b.dic_name, b.detail_name, b.id
        from sys_dictionary a
        left join sys_dictionary_detail b on a.id = b.dictionary_id
        <where>
            <foreach collection="list" item="dictionaryId" open="a.id = (" close=")" separator=",">
                #{dictionaryId}
            </foreach>
        </where>
    </select>

<!--    select ${id} selectKey, ${name} selectValue
        from ${tableName}-->
    <select id="listTable" parameterType="java.util.Map" statementType="STATEMENT"
        resultType="com.bonc.rrs.worderapp.entity.DicKeyResultEntity">
        ${sql}
    </select>
    <select id="getWorderbyId" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worderapp.entity.dto.WorderInformationDto">
        select *
        from worder_information
        where worder_id=#{worderId}
    </select>
    <select id="getWorderOrderByCompanyOrderNumber"
            resultType="com.bonc.rrs.worderapp.entity.WorderInformation">
        select worder_id,worder_no
        from worder_information
        where company_order_number = #{companyOrderNumber}
        and worder_exec_status != 21
        and worder_status != 5

    </select>


</mapper>
