<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderapp.dao.ServiceManagerDao">

    <select id="listWorder" parameterType="com.bonc.rrs.worderapp.entity.vo.ServiceManagerVo"
            resultType="com.bonc.rrs.worderapp.entity.ServiceManagerEntity">
        select a.worder_id          worderId
             , a.worder_no          worderNo
             , a.worder_status      worderStatus
             , a.worder_exec_status worderExecStatus
             , a.user_name          clientName
             , a.user_phone         clientPhone
             , a.address_dup        clientAdress
             , max(b.create_time)   currentTime
             , d.brand_name         brandName
             , d.id                 brandId
             , wt.id                worderTypeId
             , (case
                    when a.worder_exec_status = 5 then 1
                    when a.worder_exec_status = 13 then 1
                    when a.worder_exec_status = 18 then 1
                    else 0
            end
            )                       colorLabel
        from worder_information a
                 left join worder_operation_record b on a.worder_status = b.worder_status
            and a.worder_exec_status = b.worder_exec_status and a.worder_no = b.worder_no
                 left join worder_template c on c.id = a.template_id
                 left join brand d on d.id = c.brand_id
                 left join worder_type wt on a.worder_type_id = wt.id
        where a.pm_id = #{pmId}
          and a.is_delete = 0
        <if test="status == '1'.toString()">
            and a.worder_status = 0
            and a.worder_exec_status = 18
        </if>
        <if test="status == '2'.toString()">
            and a.worder_status in (1, 2)
            and a.worder_exec_status in (5, 13)
        </if>
        <if test="content != null and content != ''">
            and (a.worder_no = #{content} or a.company_order_number like concat(#{content},'%') or a.user_name like concat('%', #{content}, '%') or a.user_phone like concat('%', #{content}, '%'))
        </if>
        group by a.worder_no, a.worder_id, a.worder_status,
                 a.worder_exec_status, a.user_name, a.user_phone,
                 a.address_dup, d.brand_name, d.id
        order by colorLabel desc, currentTime desc
        limit ${beginNum},${pageSize}
    </select>

    <select id="listWorderCount" parameterType="com.bonc.rrs.worderapp.entity.vo.ServiceManagerVo"
            resultType="java.lang.Integer">
        select count(1) from (
            select a.worder_no
            from worder_information a
            left join worder_operation_record b on a.worder_status = b.worder_status
            and a.worder_exec_status = b.worder_exec_status and a.worder_no = b.worder_no
            left join worder_template c on c.id = a.template_id
            left join brand d on d.id = c.brand_id
            where a.pm_id = #{pmId}
            and a.is_delete = 0
            <if test="status == '1'.toString()">
                and a.worder_status = 0
                and a.worder_exec_status = 18
            </if>
            <if test="status == '2'.toString()">
                and a.worder_status in (1,2)
                and a.worder_exec_status in (5, 13)
            </if>
            <if test="worderNo != null and worderNo != ''">
                and a.worder_no = #{worderNo}
            </if>
            <if test="companyOrderNumber != null and companyOrderNumber != ''">
                and a.company_order_number = #{companyOrderNumber}
            </if>
            group by a.worder_no
        )a
    </select>

    <select id="getWorderCount"  resultType="java.lang.Integer">
       select sum(a.num) from (select count(1) num
        from worder_information a
        where pm_id=#{pmId} and a.worder_status = 0
        and a.worder_exec_status = 18
        UNION ALL
        select count(1) num
        from worder_information a
        where pm_id=#{pmId} and a.worder_status in (1,2)
        and a.worder_exec_status in (5, 13)) a
    </select>

</mapper>