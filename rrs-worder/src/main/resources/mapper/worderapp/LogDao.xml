<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderapp.dao.LogDao">

    <select id="queryAttendantInfo" resultType="com.bonc.rrs.worderapp.entity.AttendantResultEntity">
        SELECT id serviceId, attendant_state attendantState, name attendantName
        FROM biz_attendant WHERE user_id = #{userId}
    </select>

    <select id="queryWorderInfo" resultType="map">
        SELECT name,gender,contact,
        id_card AS idCard,
        id_card_img as idCardImg,
        id_card_img_back as idCardImgBack,
        id_card_effective_time as idCardEffectiveTime,
        electrician_certificate as electricianCertificate,
        electrician_effective_time as electricianEffectiveTime,
        work_contract as workContract,
        work_effective_time as workEffectiveTime,
        insurance_contract as insuranceContract,
        insurance_amount as insuranceAmount,
        insurance_effective_time as insuranceEffectiveTime,
        dot_id as branchId
        FROM biz_attendant WHERE user_id = #{userId}
    </select>

    <select id="queryWorderInfoByMobile" resultType="com.bonc.rrs.worder.entity.BizAttendantEntity">
        select b.*,d.dot_name
        from biz_attendant b
        left join dot_information d on d.dot_id = b.dot_id
         WHERE b.contact = #{mobile}
    </select>

    <update id="updateUser" parameterType="com.bonc.rrs.worderapp.entity.dto.SysUserDto">
        update sys_user
        <set>
            <if test="phoneNumber != null and phoneNumber != ''">
                mobile = #{phoneNumber},
            </if>
            <if test="username != null and username != ''">
                username = #{username},
            </if>
        </set>
        where user_id = #{userId}
    </update>
    <select id="findUserRoles" resultType="java.lang.Long">
        select t1.role_id from sys_user_role t1
                inner join
                sys_role t2 on t1.role_id = t2.role_id
                where  t1.user_id = #{userId}
    </select>

    <select id="getAttendantMobileCount" parameterType="java.lang.String" resultType="com.bonc.rrs.worderapp.entity.po.AttendantMobileCount">
        select userMobileCount, attendantMobileCount from
        (select count(0) userMobileCount from sys_user where mobile = #{mobile}) a,
        (select count(0) attendantMobileCount from biz_attendant where contact = #{mobile}) b
    </select>

    <select id="getAttendantState" parameterType="java.lang.String" resultType="java.lang.Integer">
        select attendant_state attendantState
        from biz_attendant
        WHERE contact = #{mobile}
    </select>

</mapper>