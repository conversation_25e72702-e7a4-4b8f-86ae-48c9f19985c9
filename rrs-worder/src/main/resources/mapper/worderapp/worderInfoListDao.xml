<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderapp.dao.WorderInfolistDao">


    <select id="queryWorderInfoList" resultType="Map">
        select * from worder_info  where user_id = #{userId} limit #{page}, #{pageSize}
    </select>
    <select id="selectInfoCount" resultType="Integer">
        select count(*) from worder_info  where user_id = #{userId}
    </select>
    <update id="updateInfoState">
        UPDATE worder_info set status = '1' where id = #{messageId}
    </update>
    <select id="queryInfoById" resultType="Map">
        select *from worder_info where id = #{messageId}
    </select>

</mapper>