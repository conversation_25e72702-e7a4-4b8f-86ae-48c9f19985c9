package com.bonc.rrs.efficiencyarearule.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.efficiencyarearule.entity.EfficiencyAreaRuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Mapper
public interface EfficiencyAreaRuleMapper extends BaseMapper<EfficiencyAreaRuleEntity> {


  EfficiencyAreaRuleEntity queryOnefficiencyAreaRule(@Param(value = "brand") Integer brand, @Param(value = "areaId") Integer areaId);

    void addEfficiencyAreaRule(@Param("params") Map<String, Object> params);
}
