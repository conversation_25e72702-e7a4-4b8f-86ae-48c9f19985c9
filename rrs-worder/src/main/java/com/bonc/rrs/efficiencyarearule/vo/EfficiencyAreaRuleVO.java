package com.bonc.rrs.efficiencyarearule.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


public class EfficiencyAreaRuleVO implements Serializable {

    private Integer id;

    private String installCity; //安装城市

    private String installCityCode;//安装城市编码

    private String brand; //品牌

    private String brandName;//品牌

    private String isDelete;

    private String execRule;

}
