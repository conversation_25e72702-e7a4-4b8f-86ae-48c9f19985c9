package com.bonc.rrs.efficiencyarearule.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.rrs.suite.entity.SuiteEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import javax.validation.constraints.NotNull;

@Data
@TableName("efficiency_area_rule")
@ApiModel(value = "分级结算规则表")
public class EfficiencyAreaRuleEntity implements Serializable {

    @TableId
    @ApiModelProperty(value = "自增主键")
    @NotNull(message = "ID不能为空", groups = Update.class)
    private Integer id;


    @TableField("install_city")
    @ApiModelProperty(value = "安装城市")
    private String installCity;

    @TableField("install_city_code")
    @ApiModelProperty(value = "安装城市编码")
    private String installCityCode;

    @TableField("brand")
    @ApiModelProperty(value = "品牌")
    private String brand;

    @TableField("brand_name")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @TableField("is_delete")
    @ApiModelProperty(value = "是否删除")
    private String isDelete;

    @TableField("exec_rule")
    @ApiModelProperty(value = "执行规则")
    private String execRule;

    public interface Update {

    }
}
