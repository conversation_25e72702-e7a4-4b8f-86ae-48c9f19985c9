package com.bonc.rrs.lp.domain;

import lombok.Data;

/**
 * 工单更新 DTO
 * businessType = 13
 */
@Data
public class OrderUpdateDTO {
    private String orderNo;
    private String operationTime;
    private String businessType;
    private String orderStatus;
    private BusUpdateInfo busUpdateInfo;

    @Data
    public static class BusUpdateInfo {
        private String address;
        private String carModel;
        private String city;
        private String cityCode;
        private String country;
        private String countryCode;
        private String customName;
        private String deliveryId;
        private String demandProduct;
        private String demandProductModel;
        private Integer orderType;
        private String orderTypePackage;
        private String outOrderNo;
        private String phoneNo;
        private String province;
        private String provinceCode;
    }
}
