package com.bonc.rrs.lp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "leapmotor")
public class LeapMotorConfig {

    /** 服务商 appId */
    private String appId;

    /** 服务商 appSecret */
    private String appSecret;

    /** 零跑 API 地址 */
    private String baseUrl;

    /** 字段映射 */
    private Field field;

    @Data
    public static class Field {

        /** 车架号 */
        private Integer vinId;

        /** 车企系统订单编号 */
        private Integer orderNumber;

        /** 零跑-操作时间-创建 */
        private Integer operationTimeCreate;

        /** 零跑-商品订单号 */
        private Integer productOrderNumber;

        /** 零跑-客户姓名 */
        private Integer customerName;

        /** 零跑-联系电话 */
        private Integer customerPhone;

        /** 零跑-订单类型 */
        private Integer orderType;

        /** 零跑-需求产品 */
        private Integer requiredProduct;

        /** 零跑-需求产品型号 */
        private Integer requiredProductModel;

        /** 零跑-提交日期 */
        private Integer submissionDate;

        /** 零跑-车型 */
        private Integer carModel;

        /** 零跑-套餐类型 */
        private Integer packageType;

        /** 零跑-备注-创建 */
        private Integer remark;

        /** 预计总布线长度 */
        private Integer totalCableLength;

        /** 安装方式 */
        private Integer installationMethod;

        /** 实测电压 */
        private Integer measuredVoltage;

        /** 实际勘测完成日期 */
        private Integer surveyCompletionDate;

        /** 增项收费内容 */
        private Integer additionalChargeContent;

        /** 零跑-服务商首联时间 */
        private Integer serviceStartTime;

        /** 零跑-预计使用产品 */
        private Integer estimatedUseProduct;

        /** 零跑-车位 */
        private Integer parkingSpace;

        /** 零跑-取电点 */
        private Integer powerPoint;

        /** 零跑-是否具备安装条件 */
        private Integer installCondition;

        /** 零跑-预计收桩时间 */
        private Integer expectedPilingTime;

        /** 零跑-预计发桩时间 */
        private Integer expectedDispatchTime;

        /** 零跑-是否带桩上门 */
        private Integer bringPileOnSite;

        /** 零跑-备注-勘测 */
        private Integer noteSurvey;

        /** 零跑-操作时间-勘测 */
        private Integer operationTimeSurvey;

        /** 勘测报告 */
        private Integer surveyReport;

        /** 电源点照片 */
        private Integer powerPointPhoto;

        /** 车位图 */
        private Integer parkingSpaceMap;

        /** 其他 */
        private Integer other;

        /** 零跑-免责申明 */
        private Integer disclaimer;

        /** 零跑-用户告知书 */
        private Integer userNotification;

        /** 零跑-勘测表 */
        private Integer surveyForm;

        /** 领跑充电桩编码 */
        private Integer chargingPileCode;

        /** 敷设方式 */
        private Integer installationMethodType;

        /** 接地电阻 */
        private Integer groundResistance;

        /** 充电桩型号 */
        private Integer chargingPileModel;

        /** 实测电压值（L-N） */
        private Integer measuredVoltageValue;

        /** 车位地址 */
        private Integer parkingSpaceAddress;

        /** 取电方式 */
        private Integer powerSourceMethod;

        /** 充电桩安装方式 */
        private Integer installationMethodType2;

        /** 零跑-实际上门时间 */
        private Integer arrivalTime;

        /** 零跑-服务人员预约上门时间 */
        private Integer serviceAppointmentTime;

        /** 零跑-客户期望上门时间 */
        private Integer expectedVisitTime;

        /** 零跑-客户具备安装条件时间 */
        private Integer installationConditionTime;

        /** 零跑-是否接地 */
        private Integer grounding;

        /** 零跑-实际使用产品 */
        private Integer actualProductUsed;

        /** 零跑-客户现场额外支付金额 */
        private Integer extraAmountPaidOnSite;

        /** 零跑-实际使用线缆米数 */
        private Integer cableLengthUsed;

        /** 零跑-实际安装完成时间 */
        private Integer installationCompletionTime;

        /** 零跑-操作时间-安装 */
        private Integer installationOperationTime;

        /** 电源点图 */
        private Integer powerPointMap;

        /** 漏电保护断路器图 */
        private Integer leakageBreakerDiagram;

        /** 零跑-绑桩控桩照片图 */
        private Integer pileControlPhoto;

        /** 人桩合照 */
        private Integer personPilePhoto;

        /** 安装确认书 */
        private Integer installationConfirmation;

        /** 电缆线标识图 */
        private Integer cableLabelDiagram;

        /** 充电桩-编号 */
        private Integer chargingPileLabel;

        /** 模拟充电测试图 */
        private Integer testChargingDiagram;

        /** 线缆米标图-起始 */
        private Integer cableMeterStartDiagram;

        /** 线缆米标图-终点 */
        private Integer cableMeterEndDiagram;

        /** 走线图-安装 */
        private Integer installationCableRoute;

        /** 增项报价单-安装 */
        private Integer additionalChargeQuote;

        /** 勘测报告-安装 */
        private Integer surveyReportInstallation;

        /** 安装视频 */
        private Integer installationVideo;

        /** 零跑-接地照片 */
        private Integer groundingPhoto;

        /** 零跑-电源接线 */
        private Integer powerConnection;

        /** 零跑-超标工程 */
        private Integer exceedStandardProject;

        /** 零跑-配电箱 */
        private Integer distributionBox;

        /** 零跑-验收单据 */
        private Integer acceptanceDocument;

        /** 零跑-桩体 */
        private Integer pileBody;

        /** 工单类型 */
        private Integer workOrderType;

        /** 车企订单号 */
        private Integer carCompanyOrderNumber;

        /** 车企名称 */
        private Integer carCompanyName;

        /** 工单来源 */
        private Integer workOrderSource;

        /** 客户姓名 */
        private Integer customerName2;

        /** 安装地址 */
        private Integer installationAddress;

        /** 客户邮箱 */
        private Integer customerEmail;

        /** 客户手机 */
        private Integer customerPhone2;

        /** 默认图片-勘测表 */
        private String defaultSurveyForm;

        /** 默认图片-用户告知书 */
        private String defaultUserNotice;

        /** 默认图片-免责声明 */
        private String defaultDisclaimer;
    }
}

