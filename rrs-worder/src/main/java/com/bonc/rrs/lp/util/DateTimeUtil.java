package com.bonc.rrs.lp.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateTimeUtil {

    /**
     * 获取当前时间的格式化字符串
     * @return 格式为 yyyy-MM-dd HH:mm:ss 的当前时间字符串
     */
    public static String getCurrentTimeFormatted() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return now.format(formatter);
    }

    /**
     * 重载方法：可以自定义日期时间格式
     * @param pattern 日期时间格式模式
     * @return 格式化后的当前时间字符串
     */
    public static String getCurrentTimeFormatted(String pattern) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return now.format(formatter);
    }

    // 使用示例
    public static void main(String[] args) {
        // 使用默认格式
        String currentTime = getCurrentTimeFormatted();
        System.out.println("当前时间: " + currentTime);

        // 使用自定义格式
        String customFormat = getCurrentTimeFormatted("yyyy年MM月dd日 HH时mm分ss秒");
        System.out.println("自定义格式: " + customFormat);
    }
}
