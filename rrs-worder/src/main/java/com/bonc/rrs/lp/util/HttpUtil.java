package com.bonc.rrs.lp.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;

/**
 * 通用 HTTP 工具类，支持零跑直连接口
 * 特点：
 *  1. 支持 POST JSON
 *  2. 支持自定义请求头
 *  3. 内置重试机制（最多 3 次）
 *  4. 泛型安全返回
 */
@Slf4j
@Component
public class HttpUtil {

    private final RestTemplate restTemplate;

    public HttpUtil() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 发起 POST 请求
     *
     * @param url     请求地址
     * @param body    请求体
     * @param headers 请求头
     * @param clazz   响应类型
     * @param <T>     响应泛型
     * @return 解析后的响应对象
     * @throws Exception 异常
     */
    public <T> T post(String url, Object body, Map<String, String> headers, Class<T> clazz) throws Exception {
        int maxRetries = 3;
        int attempt = 0;
        Exception lastException = null;

        while (attempt < maxRetries) {
            attempt++;
            try {
                // 1. 构建请求头
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                httpHeaders.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
                if (headers != null) {
                    headers.forEach(httpHeaders::set);
                }

                // 2. 构建请求体
                HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(body), httpHeaders);

                // 3. 发起请求
                ResponseEntity<String> responseEntity = restTemplate.exchange(
                        url,
                        HttpMethod.POST,
                        httpEntity,
                        String.class
                );

                // 4. 判断响应状态
                if (responseEntity.getStatusCode() == HttpStatus.OK) {
                    String responseBody = responseEntity.getBody();
                    log.debug("HTTP POST 成功 [attempt={}] URL={}, 响应={}", attempt, url, responseBody);
                    return JSON.parseObject(responseBody, clazz);
                } else {
                    log.error("HTTP POST 失败 [attempt={}] URL={}, 状态码={}, 响应={}",
                            attempt, url, responseEntity.getStatusCode(), responseEntity.getBody());
                    lastException = new RuntimeException("HTTP 请求失败: " + responseEntity.getStatusCode());
                }
            } catch (HttpStatusCodeException e) {
                log.error("HTTP 请求异常 [attempt={}] URL={}, 状态码={}, 响应={}",
                        attempt, url, e.getStatusCode(), e.getResponseBodyAsString(), e);
                lastException = new RuntimeException("HTTP 请求异常: " + e.getResponseBodyAsString(), e);
            } catch (Exception e) {
                log.error("HTTP POST 异常 [attempt={}] URL={}, 异常={}", attempt, url, e.getMessage(), e);
                lastException = e;
            }

            // 如果不是最后一次，则等待 500ms 后重试
            if (attempt < maxRetries) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
                log.warn("HTTP POST 重试 [attempt={}] URL={}", attempt + 1, url);
            }
        }

        // 如果 3 次都失败，抛出异常
        throw new RuntimeException("HTTP POST 失败，重试 " + maxRetries + " 次后仍未成功", lastException);
    }
}
