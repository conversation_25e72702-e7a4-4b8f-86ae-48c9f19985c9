package com.bonc.rrs.lp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    SERVICE_ACCEPTED("01", "服务商已接单"),
    TEAM_ACCEPTED("02", "工程队已接单"),
    ENGINEER_ACCEPTED("03", "工程人员接单"),
    WAIT_SURVEY("04", "待勘察"),
    WAIT_INSTALL("05", "待安装"),
    INSTALLED("06", "安装完毕"),
    CLOSED("07", "已关闭"),
    PAUSED("08", "已暂停"),
    RECTIFY("09", "待整改"),
    SETTLED("10", "已结算"),
    PAUSE_PENDING_APPROVAL("11", "暂停待审批");

    private final String code;
    private final String desc;

    /**
     * 根据枚举code返回系统状态码
     */
    public static Integer getSystemStatusByCode(String code) {
        switch (code) {
            case "01":
            case "02":
                return 0; // 分配中
            case "03":
            case "04":
                return 1; // 勘测中
            case "05":
            case "06":
                return 2; // 安装中
            case "07": return 6; // 已关闭 → 取消服务
            case "08": return 8; // 已暂停 → 暂停服务
            case "09": return 2; // 待整改 → 安装中
            case "10": return 3; // 已结算 → 结算中
            case "11": return 8; // 暂停待审批 → 暂停服务
            default: return -1; // 未知状态
        }
    }

    /**
     * 根据枚举code返回系统执行状态码
     */
    public static Integer getSystemStatusExecByCode(String code) {
        switch (code) {
            case "01":
            case "02":
                return 1; // 网点已接单
            case "03":
            case "04":
                return 2; // 待勘测预约
            case "05": return 10; //待安装预约
            case "06": return 17; // 安装完成
            case "07": return 21; // 已关闭 → 已取消
            case "08": return 25; // 已暂停 → 暂停服务
            case "09": return 14; // 待整改 → 安装资料整改中
            case "10": return 22; // 已结算 → 安装结单
            case "11": return 25; // 暂停待审批 → 暂停服务
            default: return -1; // 未知状态
        }
    }
}
