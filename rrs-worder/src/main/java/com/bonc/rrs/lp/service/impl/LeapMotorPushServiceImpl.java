package com.bonc.rrs.lp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bonc.rrs.lp.client.LeapMotorClient;
import com.bonc.rrs.lp.config.LeapMotorConfig;
import com.bonc.rrs.lp.domain.*;
import com.bonc.rrs.lp.domain.OrderUpdateDTO.BusUpdateInfo;
import com.bonc.rrs.lp.enums.BusinessTypeEnum;
import com.bonc.rrs.lp.enums.OrderStatusEnum;
import com.bonc.rrs.lp.enums.OrderTypeEnum;
import com.bonc.rrs.lp.service.LeapMotorPushService;
import com.bonc.rrs.lp.util.CryptoUtil;
import com.bonc.rrs.lp.util.DateTimeUtil;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.signlocation.entity.SoldierSignLocationEntity;
import com.bonc.rrs.signlocation.service.SignLocationService;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dao.WorderRemarkLogDao;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.service.*;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.OperationRecord;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.common.pay.common.utils.StringUtils;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WorderStatusEnum;
import com.youngking.lenmoncore.common.entity.UpstreamSystemIdEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 零跑推送业务 Service 实现
 * 仅处理以下业务：
 * 工单派单、工单退回、工单暂停审批、工单直接暂停、工单重启、工单更新
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LeapMotorPushServiceImpl implements LeapMotorPushService {

    private final LeapMotorConfig config;

    private final WorderIntfMessageService worderIntfMessageService;

    private final LeapMotorClient leapMotorClient;

    private final WorderInformationService worderInformationService;

    private final WorderInformationAttributeDao worderInformationAttributeDao;

    private final BizAttendantService bizAttendantService;

    private final WorderExtFieldService worderExtFieldService;

    private final SignLocationService signLocationService;

    private final WorkMsgDao workMsgDao;

    private final WorderRemarkLogDao worderRemarkLogDao;

    private final SysFilesService sysFilesService;

    private final BizRegionService bizRegionService;

    private final SysDictionaryDetailService sysDictionaryDetailService;

    private final FlowCommon flowCommon;


    @Override
    public LeapMotorResponse<Void> handlePushOrder(
            LeapMotorRequest<String> request,
            String authorization,
            String serverTime) {

        try {
            // 1. 校验签名
            validateSignature(authorization, serverTime);

            // 2. 解密 data
            String json = CryptoUtil.decrypt(request.getData(), config.getAppSecret(), request.getAppId(), request.getNonce());

            // 3. 提取 businessType
            String businessType = JSONObject.parseObject(json, BaseBusinessDTO.class).getBusinessType();
            BusinessTypeEnum type = BusinessTypeEnum.fromCode(businessType);

            log.info("[零跑回调] businessType={}, data={}", businessType, json);

            // 4. 分发处理
            switch (type) {
                case DISPATCH:
                    handleDispatch(json);
                    break;
                case RETURN:
                    handleRollback(json);
                    break;
                case UNSUBSCRIBE:
                    handleUnsubscribe(json);
                    break;
                case PAUSE_APPROVAL:
                    handlePauseApproval(json);
                    break;
                case PAUSE:
                    handlePause(json);
                    break;
                case RESTART:
                    handleRestart(json);
                    break;
                case UPDATE:
                    handleUpdate(json);
                    break;
                case CANCEL:
                    handleCancel(json);
                    break;
                default:
                    log.warn("[零跑回调] 暂不处理的业务类型: {}", businessType);
                    break;
            }

            // 5. 返回成功
            return success();

        } catch (SecurityException se) {
            log.error("[零跑回调] 签名校验失败, appId={}, err={}", request.getAppId(), se.getMessage());
            return error("签名校验失败");
        } catch (Exception e) {
            log.error("[零跑回调] 处理失败, appId={}, nonce={}, err={}",
                    request.getAppId(), request.getNonce(), e.getMessage(), e);
            return error("系统内部错误");
        }
    }

    /**
     * 工单派单
     */
    private void handleDispatch(String json) {
        OrderDispatchDTO dto = JSONObject.parseObject(json, OrderDispatchDTO.class);
        log.info("[零跑回调] 工单派单: {}", json);
        log.info("pushOrder " + dto.getOrderNo() + " start");
        // 保存报文
        worderIntfMessageService.save(WorderIntfMessageEntity.builder()
                .intfCode("pushOrder")
                .worderId(0)
                .bid(UpstreamSystemIdEnum.LEAPMOTOR.getCode())
                .data(json)
                .createTime(new Date())
                .isTransfer(0)
                .messageType(0)
                .orderCode(dto.getOrderNo())
                .build());
        log.info("pushOrder " + dto.getOrderNo() + " end");
    }

    /**
     * 工单退回
     */
    private void handleRollback(String json) {
        OrderRollbackDTO dto = JSONObject.parseObject(json, OrderRollbackDTO.class);
        log.info("[零跑回调] 工单退回: {}", json);
        WorderInformationEntity worderInfo = worderInformationService.getByCompanyWorderNo(dto.getOrderNo());
        if (worderInfo == null) {
            throw new RRException("工单:" + dto.getOrderNo() + "不存在");
        }
        WorderInformationEntity entity = new WorderInformationEntity();
        entity.setWorderId(worderInfo.getWorderId());
        entity.setWorderStatus(2);
        entity.setWorderExecStatus(14);
        worderInformationService.updateById(entity);
        flowCommon.updateFlowStatus(worderInfo.getWorderId(), "anzhuangziliaozhenggaizhong");
        setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), "车企工单退回", dto.getRollBackRemark());
        updateAttributeEntity(worderInfo.getWorderNo(), dto.getOrderStatus());
    }

    /**
     * 工单暂停审批
     */
    private void handlePauseApproval(String json) {
        OrderPauseApprovalDTO dto = JSONObject.parseObject(json, OrderPauseApprovalDTO.class);
        log.info("[零跑回调] 工单暂停审批: {}", json);
        WorderInformationEntity worderInfo = worderInformationService.getByCompanyWorderNo(dto.getOrderNo());
        if (worderInfo == null) {
            throw new RRException("工单:" + dto.getOrderNo() + "不存在");
        }
        WorderInformationEntity entity = new WorderInformationEntity();
        entity.setWorderId(worderInfo.getWorderId());
        if ("01".equals(dto.getSuspendApprovalResult())) {
            // 通过暂停申请
            entity.setWorderExecStatus(25);
        } else {
            // 驳回暂停申请，恢复订单状态
            WorderInformationAttributeEntity entity1 = worderInformationAttributeDao.selectAttributeByWorderNo(worderInfo.getWorderNo(), "status_before_pause", "leapMotor");
            WorderInformationAttributeEntity entity2 = worderInformationAttributeDao.selectAttributeByWorderNo(worderInfo.getWorderNo(), "status_exec_before_pause", "leapMotor");
            entity.setWorderStatus(Integer.parseInt(entity1.getAttributeValue()));
            entity.setWorderExecStatus(Integer.parseInt(entity2.getAttributeValue()));
            worderInformationService.updateById(entity);
        }
        worderInformationService.updateById(entity);
        setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), "车企推送工单暂停申请结果", "01".equals(dto.getSuspendApprovalResult()) ? "通过暂停申请" : "驳回暂停申请");
        updateAttributeEntity(worderInfo.getWorderNo(), dto.getOrderStatus());
    }

    /**
     * 工单直接暂停
     */
    private void handlePause(String json) {
        OrderPauseDTO dto = JSONObject.parseObject(json, OrderPauseDTO.class);
        log.info("[零跑回调] 工单直接暂停: {}", json);
        WorderInformationEntity worderInfo = worderInformationService.getByCompanyWorderNo(dto.getOrderNo());
        if (worderInfo == null) {
            throw new RRException("工单:" + dto.getOrderNo() + "不存在");
        }
        WorderInformationEntity entity = new WorderInformationEntity();
        entity.setWorderId(worderInfo.getWorderId());
        entity.setWorderStatus(8);
        entity.setWorderExecStatus(25);
        worderInformationService.updateById(entity);
        setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), "直接暂停工单", "车企直接暂停工单");
        updateAttributeEntity(worderInfo.getWorderNo(), dto.getOrderStatus());
        // 记录暂停前订单状态
        saveWorderBeforeStatus(worderInfo.getWorderId(), worderInfo.getWorderNo(), worderInfo.getWorderStatus(), worderInfo.getWorderExecStatus());
    }

    /**
     * 工单重启
     */
    private void handleRestart(String json) {
        OrderRestartDTO dto = JSONObject.parseObject(json, OrderRestartDTO.class);
        log.info("[零跑回调] 工单重启: {}", json);
        WorderInformationEntity worderInfo = worderInformationService.getByCompanyWorderNo(dto.getOrderNo());
        if (worderInfo == null) {
            throw new RRException("工单:" + dto.getOrderNo() + "不存在");
        }
        WorderInformationEntity entity = new WorderInformationEntity();
        entity.setWorderId(worderInfo.getWorderId());
        String nextFlowCode;
        if (worderInfo.getServiceId() != null) {
            entity.setWorderStatus(1);
            entity.setWorderExecStatus(2);
            nextFlowCode = "daikanceyuyue";
        } else {
            entity.setWorderStatus(0);
            entity.setWorderExecStatus(1);
            nextFlowCode = "wangdianyijiedan";
        }
        worderInformationService.updateById(entity);
        flowCommon.updateFlowStatus(worderInfo.getWorderId(), nextFlowCode);
        setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), "重启工单", "车企重启工单");
        updateAttributeEntity(worderInfo.getWorderNo(), dto.getOrderStatus());
        callbackTeamDispatch(worderInfo.getWorderId());
    }

    /**
     * 工单取消
     */
    private void handleCancel(String json) {
        OrderCancelDTO dto = JSONObject.parseObject(json, OrderCancelDTO.class);
        log.info("[零跑回调] 工单取消: {}", json);
        WorderInformationEntity worderInfo = worderInformationService.getByCompanyWorderNo(dto.getOrderNo());
        if (worderInfo == null) {
            throw new RRException("工单:" + dto.getOrderNo() + "不存在");
        }
        worderInformationService.cancelWorder(worderInfo.getWorderNo(), dto.getOrderNo());
        flowCommon.updateFlowStatus(worderInfo.getWorderId(), "yiquxiao");
        setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), "工单取消", "车企取消工单");
        updateAttributeEntity(worderInfo.getWorderNo(), dto.getOrderStatus());
    }

    /**
     * 用户退订
     */
    private void handleUnsubscribe(String json) {
        OrderUnsubscribeDTO dto = JSONObject.parseObject(json, OrderUnsubscribeDTO.class);
        log.info("[零跑回调] 用户退订: {}", json);
        WorderInformationEntity worderInfo = worderInformationService.getByCompanyWorderNo(dto.getOrderNo());
        if (worderInfo == null) {
            throw new RRException("工单:" + dto.getOrderNo() + "不存在");
        }
        worderInformationService.cancelWorder(worderInfo.getWorderNo(), dto.getOrderNo());
        flowCommon.updateFlowStatus(worderInfo.getWorderId(), "yiquxiao");
        setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), "工单取消", "车企取消工单");
        updateAttributeEntity(worderInfo.getWorderNo(), dto.getOrderStatus());
    }

    /**
     * 工单更新
     */
    private void handleUpdate(String json) {
        OrderUpdateDTO dto = JSONObject.parseObject(json, OrderUpdateDTO.class);
        log.info("[零跑回调] 工单更新: {}", json);
        WorderInformationEntity worderInfo = worderInformationService.getByCompanyWorderNo(dto.getOrderNo());
        if (worderInfo == null) {
            throw new RRException("工单:" + dto.getOrderNo() + "不存在");
        }
        // 之前零跑工单跳过
        LocalDateTime threshold = LocalDateTime.of(2025, 6, 15, 18, 34, 0);

        // 将Date转换为LocalDateTime进行比较
        LocalDateTime createDateTime = worderInfo.getCreateTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        if (createDateTime.isBefore(threshold)) {
            log.info("[零跑回调] 工单更新：工单在切换版本之前，跳过更新");
        }
        worderInfo.setWorderStatus(OrderStatusEnum.getSystemStatusByCode(dto.getOrderStatus()));
        worderInfo.setWorderExecStatus(OrderStatusEnum.getSystemStatusExecByCode(dto.getOrderStatus()));
        BusUpdateInfo busUpdateInfo = dto.getBusUpdateInfo();

        RegionCode regionCode = convertRegion(busUpdateInfo.getProvinceCode(),
                busUpdateInfo.getCityCode(),
                busUpdateInfo.getCountryCode());
        String address = regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + busUpdateInfo.getAddress();
        worderInfo.setAddress(address);
        String userName = busUpdateInfo.getCustomName();
        // 0:带桩上门 1:仅提供安装 2:仅设备 3:家充服务包 ,一般只有1 3
        if (OrderTypeEnum.INSTALLATION_ONLY.getCode() == busUpdateInfo.getOrderType()) {
            userName += "(仅安装)";
        }
        worderInfo.setUserName(userName);
        worderInfo.setUserPhone(busUpdateInfo.getPhoneNo());
        List<WorderExtFieldEntity> fieldList = worderExtFieldService.getFieldsByWorderNo(worderInfo.getWorderNo());
        Map<Integer, WorderExtFieldEntity> fieldMap = fieldList.stream()
                .collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, Function.identity(), (t1, t2) -> t1));
        LeapMotorConfig.Field field =  config.getField();
        List<WorderExtFieldEntity> updateList = new ArrayList<>();
        WorderExtFieldEntity productOrderNumber = fieldMap.get(field.getProductOrderNumber());
        if (null != productOrderNumber) {
            productOrderNumber.setFieldValue(busUpdateInfo.getOutOrderNo());
            updateList.add(productOrderNumber);
        }
        WorderExtFieldEntity carmodel = fieldMap.get(field.getCarModel());
        if (null != carmodel) {
            carmodel.setFieldValue(busUpdateInfo.getCarModel());
            updateList.add(carmodel);
        }
        WorderExtFieldEntity requiredProduct = fieldMap.get(field.getRequiredProduct());
        if (null != requiredProduct) {
            requiredProduct.setFieldValue(busUpdateInfo.getDemandProduct());
            updateList.add(requiredProduct);
        }
        WorderExtFieldEntity requiredProductModel = fieldMap.get(field.getRequiredProductModel());
        if (null != requiredProductModel) {
            requiredProductModel.setFieldValue(busUpdateInfo.getDemandProductModel());
            updateList.add(requiredProductModel);
        }
        WorderExtFieldEntity packageType = fieldMap.get(field.getPackageType());
        if (null != packageType) {
            packageType.setFieldValue(busUpdateInfo.getOrderTypePackage());
            updateList.add(packageType);
        }
        WorderExtFieldEntity installationAddress = fieldMap.get(field.getInstallationAddress());
        if (null != installationAddress) {
            installationAddress.setFieldValue(address);
            updateList.add(packageType);
        }
        worderInformationService.updateById(worderInfo);
        if(!updateList.isEmpty()) {
            worderExtFieldService.updateBatchById(updateList);
        }
        setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), "工单更新", "车企更新工单");
        updateAttributeEntity(worderInfo.getWorderNo(), dto.getOrderStatus());
    }

    @Override
    public LeapMotorResponse<Void> callbackTeamDispatch(Integer worderId) {
        try {
            // 非零跑工单跳过
            if (!checkLpOrderByWorderId(worderId)) {
                return success();
            }
            WorderInfoEntity worderInfo = worderInformationService.getByWorderId(worderId);
            TeamDispatchDTO dto = new  TeamDispatchDTO();
            dto.setOrderNo(worderInfo.getCompanyOrderNumber());
            dto.setOperationTime(DateTimeUtil.getCurrentTimeFormatted());
            dto.setOrderStatus(OrderStatusEnum.ENGINEER_ACCEPTED.getCode());
            List<SysDictionaryDetailEntity> lpTeamList = sysDictionaryDetailService.findByDictNumber("lp_team");
            Map<String, String> lpTeamMap = lpTeamList.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (t1, t2) -> t1));
            dto.setEngineerName(lpTeamMap.get("team_name"));
            dto.setEngineerPhoneNo(lpTeamMap.get("team_phone"));
            log.info("[推送零跑] 工程队派单: {}", JSONObject.toJSON(dto));
            LeapMotorResponse<Void> response = leapMotorClient.teamDispatch(dto);
            if ("200".equals(response.getCode())) {
                updateAttributeEntity(worderInfo.getWorderNo(), OrderStatusEnum.ENGINEER_ACCEPTED.getCode());
            }
            return response;
        } catch (Exception e) {
            log.error("[推送零跑] 工程队派单失败: {}", e.getMessage(), e);
            return error("工程队派单回调失败");
        }
    }

    @Override
    public LeapMotorResponse<Void> callbackReserveSurvey(Integer worderId) {
        try {
            // 非零跑工单跳过
            if (!checkLpOrderByWorderId(worderId)) {
                return success();
            }
            WorderInfoEntity worderInfo = worderInformationService.getByWorderId(worderId);
            WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderInfo.getWorderNo(), "order_status", "leapMotor");
            if (!OrderStatusEnum.ENGINEER_ACCEPTED.getCode().equals(attributeEntity.getAttributeValue())
                    && !OrderStatusEnum.TEAM_ACCEPTED.getCode().equals(attributeEntity.getAttributeValue())
                    && !OrderStatusEnum.PAUSED.getCode().equals(attributeEntity.getAttributeValue())) {
                log.info("[推送零跑] 预约勘察车企订单状态非工程人员接单状态，跳过推送");
                return success();
            }
            ReserveSurveyDTO dto = new  ReserveSurveyDTO();
            dto.setOrderNo(worderInfo.getCompanyOrderNumber());
            dto.setOperationTime(DateTimeUtil.getCurrentTimeFormatted());
            dto.setOrderStatus(OrderStatusEnum.WAIT_SURVEY.getCode());
            List<WorderExtFieldEntity> fieldList = worderExtFieldService.getFieldsByWorderNo(worderInfo.getWorderNo());
            Map<Integer, String> fieldMap = fieldList.stream()
                    .filter(entity -> entity.getFieldId() != null && entity.getFieldValue() != null)
                    .collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, WorderExtFieldEntity::getFieldValue, (t1, t2) -> t1));
            LeapMotorConfig.Field field =  config.getField();
            ReserveSurveyDTO.ReserveReconnaissanceInfo reconnaissanceInfo = new ReserveSurveyDTO.ReserveReconnaissanceInfo();
            reconnaissanceInfo.setGmtPredictCheckStart(formatDateToDateTime(fieldMap.get(field.getExpectedDispatchTime())));
            reconnaissanceInfo.setGmtPredictCheckEnd(formatDateToDateTime(fieldMap.get(field.getExpectedPilingTime())));
            reconnaissanceInfo.setRemark(fieldMap.get(field.getNoteSurvey()));
            reconnaissanceInfo.setWhetherCarry("0");
//            reconnaissanceInfo.setEngineerTeam();
//            reconnaissanceInfo.setRepositoryCode();
            dto.setReserveReconnaissanceInfoParam(reconnaissanceInfo);
            log.info("[推送零跑] 预约勘察: {}", JSONObject.toJSON(dto));
            LeapMotorResponse<Void> response = leapMotorClient.reserveSurvey(dto);
            if ("200".equals(response.getCode())) {
                updateAttributeEntity(worderInfo.getWorderNo(), OrderStatusEnum.WAIT_SURVEY.getCode());
            }
            return response;
        } catch (Exception e) {
            log.error("[推送零跑] 预约勘察失败: {}", e.getMessage(), e);
            return error("预约勘察回调失败");
        }
    }

    @Override
    public LeapMotorResponse<Void> callbackSurvey(Integer worderId) {
        try {
            // 非零跑工单跳过
            if (!checkLpOrderByWorderId(worderId)) {
                return success();
            }
            SurveyDTO dto = new  SurveyDTO();
            WorderInfoEntity worderInfo = worderInformationService.getByWorderId(worderId);
            dto.setOrderNo(worderInfo.getCompanyOrderNumber());
            dto.setOperationTime(DateTimeUtil.getCurrentTimeFormatted());
            dto.setOrderStatus(OrderStatusEnum.WAIT_INSTALL.getCode());
            List<WorderExtFieldEntity> fieldList = worderExtFieldService.getFieldsByWorderNo(worderInfo.getWorderNo());
            Map<Integer, String> fieldMap = fieldList.stream()
                    .filter(entity -> entity.getFieldId() != null && entity.getFieldValue() != null)
                    .collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, WorderExtFieldEntity::getFieldValue, (t1, t2) -> t1));
            LeapMotorConfig.Field field =  config.getField();
            SurveyDTO.ReconnaissanceInfo reconnaissanceInfo = new SurveyDTO.ReconnaissanceInfo();
            reconnaissanceInfo.setGmtCheckStart(formatDateToDateTime(fieldMap.get(field.getExpectedDispatchTime())));
            reconnaissanceInfo.setGmtCheckEnd(formatDateToDateTime(fieldMap.get(field.getExpectedPilingTime())));
            String installEnable = fieldMap.get(field.getInstallCondition());
            if (installEnable.equals("是")) {
                reconnaissanceInfo.setInstallEnable(1);
                String powerPoint = fieldMap.get(field.getPowerPoint());
                if ("物业取电".equals(powerPoint)) {
                    reconnaissanceInfo.setGetPowerPoint(1);
                } else if ("自家取电".equals(powerPoint)) {
                    reconnaissanceInfo.setGetPowerPoint(2);
                } else if ("新能源电报报装".equals(powerPoint)) {
                    reconnaissanceInfo.setGetPowerPoint(3);
                }
                String parkingSpace = fieldMap.get(field.getParkingSpace());
                if ("无车位".equals(parkingSpace)) {
                    reconnaissanceInfo.setParkPlace(1);
                } else if ("产权车位".equals(parkingSpace)) {
                    reconnaissanceInfo.setParkPlace(2);
                } else if ("租赁车位".equals(parkingSpace)) {
                    reconnaissanceInfo.setParkPlace(3);
                } else if ("消防车位".equals(parkingSpace)) {
                    reconnaissanceInfo.setParkPlace(4);
                }
                Map<String, String> fileInfo = getFileMap(Arrays.asList(fieldMap.get(field.getSurveyForm()),
                        fieldMap.get(field.getUserNotification()),
                        fieldMap.get(field.getDisclaimer())
                ));
                reconnaissanceInfo.setUrlCheckForm(getFileUrl(fieldMap.get(field.getSurveyForm()), fileInfo));
                reconnaissanceInfo.setUrlUserNotice(getFileUrl(fieldMap.get(field.getUserNotification()), fileInfo));
                reconnaissanceInfo.setUrlDisclaimer(getFileUrl(fieldMap.get(field.getDisclaimer()), fileInfo));
                reconnaissanceInfo.setGmtPredictInstallEnd(formatDateToDateTime(fieldMap.get(field.getExpectedDispatchTime())));
                reconnaissanceInfo.setGmtPredictInstallStart(formatDateToDateTime(fieldMap.get(field.getExpectedDispatchTime())));
                String predictProducts = fieldMap.get(field.getEstimatedUseProduct());
                if ("5米蓝联蓝牙桩".equals(predictProducts)) {
                    reconnaissanceInfo.setPredictProducts("01");
                } else if ("5米挚达蓝牙桩".equals(predictProducts)) {
                    reconnaissanceInfo.setPredictProducts("02");
                }
                reconnaissanceInfo.setServiceFirstConnectTime(formatDateToDateTime(fieldMap.get(field.getServiceStartTime())));
            } else {
                reconnaissanceInfo.setInstallEnable(0);
//                reconnaissanceInfo.setGmtPredictCheckEndNext();
//                reconnaissanceInfo.setGmtPredictCheckStartNext();
            }
            dto.setReconnaissanceInfoParam(reconnaissanceInfo);
            log.info("[推送零跑] 勘察结果: {}", JSONObject.toJSON(dto));
            LeapMotorResponse<Void> response = leapMotorClient.survey(dto);
            if ("200".equals(response.getCode())) {
                updateAttributeEntity(worderInfo.getWorderNo(), OrderStatusEnum.WAIT_INSTALL.getCode());
            }
            return response;
        } catch (Exception e) {
            log.error("[推送零跑] 勘察结果失败: {}", e.getMessage(), e);
            return error("勘察结果回调失败");
        }
    }

    @Override
    public LeapMotorResponse<Void> callbackSurveyInfo(String worderNo, String firstCallTime) {
        try {
            WorderInformationEntity worderInfo = worderInformationService.getByWorderNo(worderNo);
            // 非零跑工单跳过
            if (!checkLpOrderByWorderId(worderInfo.getWorderId())) {
                return success();
            }
            WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderInfo.getWorderNo(), "order_status", "leapMotor");
            if (!OrderStatusEnum.ENGINEER_ACCEPTED.getCode().equals(attributeEntity.getAttributeValue())
                    && !OrderStatusEnum.TEAM_ACCEPTED.getCode().equals(attributeEntity.getAttributeValue())
                    && !OrderStatusEnum.PAUSED.getCode().equals(attributeEntity.getAttributeValue())) {
                log.info("[推送零跑] 预约勘察车企订单状态非工程人员接单状态，跳过推送");
                return success();
            }
            ReserveSurveyDTO dto = new  ReserveSurveyDTO();
            dto.setOrderNo(worderInfo.getCompanyOrderNumber());
            String endTime = DateTimeUtil.addDaysToDate(firstCallTime, 10);
            dto.setOperationTime(firstCallTime);
            dto.setOrderStatus(OrderStatusEnum.WAIT_SURVEY.getCode());
            ReserveSurveyDTO.ReserveReconnaissanceInfo reconnaissanceInfo = new  ReserveSurveyDTO.ReserveReconnaissanceInfo();
            reconnaissanceInfo.setGmtPredictCheckStart(firstCallTime);
            reconnaissanceInfo.setGmtPredictCheckEnd(endTime);
            reconnaissanceInfo.setRemark("");
            reconnaissanceInfo.setWhetherCarry("0");
//            reconnaissanceInfo.setEngineerTeam();
//            reconnaissanceInfo.setRepositoryCode();
            dto.setReserveReconnaissanceInfoParam(reconnaissanceInfo);
            log.info("[推送零跑] 预约勘察: {}", JSONObject.toJSON(dto));
            LeapMotorResponse<Void> response = leapMotorClient.reserveSurvey(dto);
            if ("200".equals(response.getCode())) {
                SurveyDTO surveyDTO = new  SurveyDTO();
                surveyDTO.setOrderNo(worderInfo.getCompanyOrderNumber());
                surveyDTO.setOperationTime(DateTimeUtil.getCurrentTimeFormatted());
                surveyDTO.setOrderStatus(OrderStatusEnum.WAIT_INSTALL.getCode());
                SurveyDTO.ReconnaissanceInfo reconnaissance = new SurveyDTO.ReconnaissanceInfo();
                reconnaissance.setGmtCheckStart(firstCallTime);
                reconnaissance.setGmtCheckEnd(endTime);
                reconnaissance.setInstallEnable(1);
                reconnaissance.setGetPowerPoint(1);
                reconnaissance.setParkPlace(2);
                reconnaissance.setUrlCheckForm(config.getField().getDefaultSurveyForm());
                reconnaissance.setUrlUserNotice(config.getField().getDefaultUserNotice());
                reconnaissance.setUrlDisclaimer(config.getField().getDefaultDisclaimer());
                reconnaissance.setGmtPredictInstallEnd(endTime);
                reconnaissance.setGmtPredictInstallStart(firstCallTime);
                reconnaissance.setPredictProducts("01");
                reconnaissance.setServiceFirstConnectTime(firstCallTime);
                surveyDTO.setReconnaissanceInfoParam(reconnaissance);
                log.info("[推送零跑] 勘察结果: {}", JSONObject.toJSON(surveyDTO));
                response = leapMotorClient.survey(surveyDTO);
                if ("200".equals(response.getCode())) {
                    WorderInformationEntity entity = new WorderInformationEntity();
                    entity.setWorderId(worderInfo.getWorderId());
                    entity.setWorderStatus(2);
                    entity.setWorderExecStatus(10);
                    worderInformationService.updateById(entity);
                    flowCommon.updateFlowStatus(worderInfo.getWorderId(), "daianzhuangyuyue");
                    updateAttributeEntity(worderInfo.getWorderNo(), OrderStatusEnum.WAIT_INSTALL.getCode());
                }
                return response;
            }
            return response;
        } catch (Exception e) {
            log.error("[推送零跑] 预约勘察失败: {}", e.getMessage(), e);
            return error("预约勘察回调失败");
        }
    }

    @Override
    public LeapMotorResponse<Void> callbackInstall(String worderNo) {
        try {
            InstallDTO dto = new InstallDTO();
            WorderInformationEntity worderInfo = worderInformationService.getByWorderNo(worderNo);
            // 非零跑工单跳过
            if (!checkLpOrderByWorderId(worderInfo.getWorderId())) {
                return success();
            }
            WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderInfo.getWorderNo(), "order_status", "leapMotor");
            if (!OrderStatusEnum.WAIT_INSTALL.getCode().equals(attributeEntity.getAttributeValue())
                    && !OrderStatusEnum.RECTIFY.getCode().equals(attributeEntity.getAttributeValue()) ) {
                log.info("[推送零跑] 安装车企订单状态非待安装或待整改状态，跳过推送");
                return success();
            }
            dto.setOrderNo(worderInfo.getCompanyOrderNumber());
            dto.setOperationTime(DateTimeUtil.getCurrentTimeFormatted());
            dto.setOrderStatus(OrderStatusEnum.INSTALLED.getCode());
            List<WorderExtFieldEntity> fieldList = worderExtFieldService.getFieldsByWorderNo(worderInfo.getWorderNo());
            Map<Integer, String> fieldMap = fieldList.stream()
                    .filter(entity -> entity.getFieldId() != null && entity.getFieldValue() != null)
                    .collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, WorderExtFieldEntity::getFieldValue, (t1, t2) -> t1));
            LeapMotorConfig.Field field =  config.getField();
            InstallDTO.InstallInfo installInfo = new InstallDTO.InstallInfo();
            String installSignTime = worderInfo.getInstallSignTime();
            if (null != installSignTime && installSignTime.endsWith(".0")) {
                installSignTime = installSignTime.substring(0,  installSignTime.length() - 2);
            }
            String installSignOutTime = worderInfo.getInstallSignOutTime();
            if (null != installSignOutTime && installSignOutTime.endsWith(".0")) {
                installSignOutTime = installSignOutTime.substring(0,  installSignOutTime.length() - 2);
            }
            installInfo.setGmtInstallStart(installSignTime);
            installInfo.setGmtInstallEnd(installSignOutTime);
            installInfo.setUseCableLength(fieldMap.get(field.getCableLengthUsed()));
            String extraPay = fieldMap.get(field.getExtraAmountPaidOnSite());
            installInfo.setExtraPay(StringUtils.isBlank(extraPay) ? "0" : extraPay);
            String product = fieldMap.get(field.getActualProductUsed());
            if ("5米蓝联蓝牙桩".equals(product)) {
                installInfo.setProductModel("01");
            } else if ("5米挚达蓝牙桩".equals(product)) {
                installInfo.setProductModel("02");
            }
            Map<String, String> fileInfo = getFileMap(Arrays.asList(fieldMap.get(field.getPileBody()),
                    fieldMap.get(field.getAcceptanceDocument()),
                    fieldMap.get(field.getDistributionBox()),
                    fieldMap.get(field.getExceedStandardProject()),
                    fieldMap.get(field.getPowerConnection()),
                    fieldMap.get(field.getGroundingPhoto())
            ));
            installInfo.setUrlChargePile(getFileUrl(fieldMap.get(field.getPileBody()), fileInfo));
            installInfo.setUrlReceipt(getFileUrl(fieldMap.get(field.getAcceptanceDocument()), fileInfo));
            installInfo.setUrlSwitchBox(getFileUrl(fieldMap.get(field.getDistributionBox()), fileInfo));
            installInfo.setUrlOutLimit(getFileUrl(fieldMap.get(field.getExceedStandardProject()), fileInfo));
            installInfo.setUrlPowerConnection(getFileUrl(fieldMap.get(field.getPowerConnection()), fileInfo));
            String installArea = worderInfo.getAddressDup();
            String[] parts = installArea.trim().split("_");
            if (parts.length >= 4) {
                installInfo.setInstallProvince(parts[0]);
                installInfo.setInstallCity(parts[1]);
                installInfo.setInstallCcountry(parts[2]);
                StringBuilder sb = new StringBuilder();
                for (int i = 3; i < parts.length; i++) {
                    sb.append(parts[i]).append(" ");
                }
                installInfo.setInstallAddress(sb.toString().trim());
            } else {
                return error("安装签到地点未解析出地址，请检查数据");
            }
            SoldierSignLocationEntity signLocation = signLocationService.getSignLocation(worderInfo.getWorderId());
            installInfo.setInstallAddressXy(signLocation.getLocationY() + "_" + signLocation.getLocationX());
            String whetherGrounded = fieldMap.get(field.getGrounding());
            if ("是".equals(whetherGrounded)) {
                installInfo.setWhetherGrounded(1);
                installInfo.setUrlGroundedPicture(getFileUrl(fieldMap.get(field.getGroundingPhoto()), fileInfo));
            } else {
                installInfo.setWhetherGrounded(0);
            }
            installInfo.setCustomerHasInstall(formatDateToDateTime(fieldMap.get(field.getInstallationConditionTime())));
            installInfo.setCustomerExpectInstall(formatDateToDateTime(fieldMap.get(field.getExpectedVisitTime())));
            installInfo.setServiceStaffAppointment(formatDateToDateTime(fieldMap.get(field.getServiceAppointmentTime())));
            installInfo.setGmtVisitStart(formatDateToDateTime(fieldMap.get(field.getArrivalTime())));
            dto.setInstallInfoParam(installInfo);
            log.info("[推送零跑] 安装结果: {}", JSONObject.toJSON(dto));
            LeapMotorResponse<Void> response = leapMotorClient.install(dto);
            if ("200".equals(response.getCode())) {
                updateAttributeEntity(worderInfo.getWorderNo(), OrderStatusEnum.INSTALLED.getCode());
            }
            return response;
        } catch (Exception e) {
            log.error("[推送零跑] 安装结果失败: {}", e.getMessage(), e);
            return error("安装结果回调失败");
        }
    }

    @Override
    public LeapMotorResponse<Void> callbackCloseOrder(String worderNo, String orderCloseRemark) {
        try {
            OrderCloseDTO dto =  new OrderCloseDTO();
            WorderInformationEntity worderInfo = worderInformationService.getByWorderNo(worderNo);
            // 非零跑工单跳过
            if (!checkLpOrderByWorderId(worderInfo.getWorderId())) {
                return success();
            }
            dto.setOrderNo(worderInfo.getCompanyOrderNumber());
            dto.setOperationTime(DateTimeUtil.getCurrentTimeFormatted());
            dto.setOrderStatus(OrderStatusEnum.CLOSED.getCode());
            dto.setOrderCloseRemark(orderCloseRemark);
            log.info("[推送零跑] 工单关闭: {}", JSONObject.toJSON(dto));
            LeapMotorResponse<Void> response = leapMotorClient.closeOrder(dto);
            if ("200".equals(response.getCode())) {
                updateAttributeEntity(worderInfo.getWorderNo(), OrderStatusEnum.CLOSED.getCode());
            }
            return response;
        } catch (Exception e) {
            log.error("[推送零跑] 工单关闭失败: {}", e.getMessage(), e);
            return error("工单关闭回调失败");
        }
    }

    @Override
    public LeapMotorResponse<Void> callbackPauseApply(String worderNo, String orderPauseRemark) {
        try {
            OrderPauseApplyDTO dto = new OrderPauseApplyDTO();
            WorderInformationEntity worderInfo = worderInformationService.getByWorderNo(worderNo);
            // 非零跑工单跳过
            if (!checkLpOrderByWorderId(worderInfo.getWorderId())) {
                return success();
            }
            WorderInformationAttributeEntity attribute = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "order_status", "leapMotor");
            if (OrderStatusEnum.INSTALLED.getCode().equals(attribute.getAttributeValue()) || OrderStatusEnum.RECTIFY.getCode().equals(attribute.getAttributeValue())) {
                return error("该订单已经安装完成并提交信息给车企，不允许暂停");
            }
            dto.setOrderNo(worderInfo.getCompanyOrderNumber());
            dto.setOperationTime(DateTimeUtil.getCurrentTimeFormatted());
            dto.setOrderStatus(OrderStatusEnum.PAUSE_PENDING_APPROVAL.getCode());
            dto.setOrderPauseRemark(orderPauseRemark);
            log.info("[推送零跑] 工单暂停申请: {}", JSONObject.toJSON(dto));
            LeapMotorResponse<Void> response = leapMotorClient.pauseApply(dto);
            if ("200".equals(response.getCode())) {
                WorderInformationEntity entity = new WorderInformationEntity();
                entity.setWorderId(worderInfo.getWorderId());
                entity.setWorderStatus(8);
                entity.setWorderExecStatus(24);
                worderInformationService.updateById(entity);
                SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
                setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), user.getUsername() + "申请工单暂停", orderPauseRemark);
                updateAttributeEntity(worderInfo.getWorderNo(), OrderStatusEnum.PAUSE_PENDING_APPROVAL.getCode());
                // 记录暂停前订单状态
                saveWorderBeforeStatus(worderInfo.getWorderId(), worderInfo.getWorderNo(), worderInfo.getWorderStatus(), worderInfo.getWorderExecStatus());
            }
            return response;
        } catch (Exception e) {
            log.error("[推送零跑] 工单暂停申请失败: {}", e.getMessage(), e);
            return error("工单暂停申请回调失败：" + e.getMessage());
        }
    }

    private void saveWorderBeforeStatus(Integer worderId, String worderNo, Integer status, Integer statusExec) {
        // 记录暂停前订单状态
        WorderInformationAttributeEntity entity1 = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "status_before_pause", "leapMotor");
        if (null == entity1) {
            entity1 = new WorderInformationAttributeEntity();
            entity1.setWorderId(worderId);
            entity1.setAttributeCode("status_before_pause");
            entity1.setAttributeName("停止前订单状态");
            entity1.setAttributeValue(String.valueOf(status));
            entity1.setAttribute("leapMotor");
            worderInformationAttributeDao.insert(entity1);
        } else {
            entity1.setAttributeValue(String.valueOf(status));
            worderInformationAttributeDao.updateById(entity1);
        }

        WorderInformationAttributeEntity entity2 = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "status_exec_before_pause", "leapMotor");
        if (null == entity2) {
            entity2 = new WorderInformationAttributeEntity();
            entity2.setWorderId(worderId);
            entity2.setAttributeCode("status_exec_before_pause");
            entity2.setAttributeName("停止前订单执行状态");
            entity2.setAttributeValue(String.valueOf(statusExec));
            entity2.setAttribute("leapMotor");
            worderInformationAttributeDao.insert(entity2);
        } else {
            entity2.setAttributeValue(String.valueOf(statusExec));
            worderInformationAttributeDao.updateById(entity2);
        }
    }

    @Override
    public LeapMotorResponse<Void> callbackRestart(String worderNo) {
        try {
            OrderRestartDTO dto = new  OrderRestartDTO();
            WorderInformationEntity worderInfo = worderInformationService.getByWorderNo(worderNo);
            // 非零跑工单跳过
            if (!checkLpOrderByWorderId(worderInfo.getWorderId())) {
                return success();
            }
            if (worderInfo.getWorderExecStatus() != 25) {
                throw new RRException("工单:" + worderInfo.getWorderNo() + "未暂停或车企审批未回调，请稍后再试");
            }
            dto.setOrderNo(worderInfo.getCompanyOrderNumber());
            dto.setOperationTime(DateTimeUtil.getCurrentTimeFormatted());
            dto.setOrderStatus(OrderStatusEnum.TEAM_ACCEPTED.getCode());
            log.info("[推送零跑] 工单重启: {}", JSONObject.toJSON(dto));
            LeapMotorResponse<Void> response = leapMotorClient.restart(dto);
            if ("200".equals(response.getCode())) {
                WorderInformationEntity entity = new WorderInformationEntity();
                entity.setWorderId(worderInfo.getWorderId());
                String nextFlowCode;
                if (worderInfo.getServiceId() != null) {
                    entity.setWorderStatus(1);
                    entity.setWorderExecStatus(2);
                    nextFlowCode = "daikanceyuyue";
                } else {
                    entity.setWorderStatus(0);
                    entity.setWorderExecStatus(1);
                    nextFlowCode = "wangdianyijiedan";
                }
                worderInformationService.updateById(entity);
                flowCommon.updateFlowStatus(worderInfo.getWorderId(), nextFlowCode);
                updateAttributeEntity(worderInfo.getWorderNo(), OrderStatusEnum.TEAM_ACCEPTED.getCode());
                SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
                setOperation(dto.getOrderNo(), worderInfo.getWorderNo(), user.getUsername() + "申请工单重启", "工单重启：");
                callbackTeamDispatch(worderInfo.getWorderId());
            }
            return response;
        } catch (Exception e) {
            log.error("[推送零跑] 工单重启失败: {}", e.getMessage(), e);
            return error("工单重启回调失败," + e.getMessage());
        }
    }

    @Override
    public Boolean checkLpOrderByWorderId(Integer worderId) {
        if (worderId == null) {
            return false;
        }
        // 查询是否存在订单属性信息
        LambdaQueryWrapper<WorderInformationAttributeEntity> worderInfoAttrWrapper = Wrappers.lambdaQuery();
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getWorderId, worderId);
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getAttribute, "pushOrder");
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getAttributeCode, "worder_source");
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getIsDelete, 0);
        List<WorderInformationAttributeEntity> worderInformationAttributeEntities = worderInformationAttributeDao.selectList(worderInfoAttrWrapper);
        if (worderInformationAttributeEntities.isEmpty()) {
            return false;
        }
        WorderInformationAttributeEntity worderInformationAttributeEntity = worderInformationAttributeEntities.get(0);
        return "leapMotor".equals(worderInformationAttributeEntity.getAttributeValue());
    }

    private boolean checkLpOrderByCompanyOrderNumber(String companyOrderNumber) {
        WorderInformationEntity worderInfo = worderInformationService.getByCompanyWorderNo(companyOrderNumber);
        if (null == worderInfo) {
            return false;
        }
        return checkLpOrderByWorderId(worderInfo.getWorderId());
    }

    private Map<String, String> getFileMap(List<String> fileIdList) {
        if (CollectionUtil.isEmpty(fileIdList)) {
            return new HashMap<>();
        }
        fileIdList = fileIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return sysFilesService.getSysFileByIds(String.join(",", fileIdList)).stream()
                .collect(Collectors.toMap(file -> file.getFileId().toString(),SysFileEntity::getNewName));
    }

    private String getFileUrl(String key, Map<String, String> fileMap) {
        if (StringUtils.isEmpty(key) || CollectionUtil.isEmpty(fileMap)) {
            return "";
        }
        String[] split = key.split(",");
        List<String> urlList = new ArrayList<>();
        for (String s : split) {
            String name = fileMap.get(s);
            if (StringUtils.isNotEmpty(name)) {
                String url = FileUtils.copyImage(name);
                urlList.add(url);
            }
        }
        return String.join(",", urlList);
    }

    private void setOperation(String orderNo, String worderNo, String title, String record) {
        OperationRecord operationRecord = new OperationRecord();
        operationRecord.setWorderStatus(String.valueOf(WorderStatusEnum.QUXIAOFUWU.getCode()));
        operationRecord.setUserId(1L);
        operationRecord.setOperationUser("系统自动");
        operationRecord.setRecord(title + ":" + record);
        operationRecord.setWorderNo(worderNo);
        operationRecord.setCreateTime(new Date());
        int index = workMsgDao.insertOperation(operationRecord);

        // 保存工单备注
        WorderRemarkLogEntity worderRemarkLogEntity = new WorderRemarkLogEntity();
        worderRemarkLogEntity.setWorderNo(worderNo);
        worderRemarkLogEntity.setUserId(1L);
        worderRemarkLogEntity.setUserName("系统自动");
        worderRemarkLogEntity.setTitle(title);
        worderRemarkLogEntity.setContent(record);
        worderRemarkLogEntity.setCreateTime(new Date());
        worderRemarkLogDao.insert(worderRemarkLogEntity);
    }

    private String formatDateToDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return dateTimeStr;
        }

        String trimmedStr = dateTimeStr.trim();

        // 格式1: 只有日期 "2025-09-03"
        if (trimmedStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
            return trimmedStr + " 00:00:00";
        }

        // 格式2: 日期 + 小时 "2025-09-03 19时" 或 "2025-09-03 09时"
        if (trimmedStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{1,2}时")) {
            String[] parts = trimmedStr.split(" ");
            String datePart = parts[0];
            String hourPart = parts[1].replace("时", "");

            // 处理小时，确保两位数格式
            String hour = String.format("%02d", Integer.parseInt(hourPart));
            return datePart + " " + hour + ":00:00";
        }

        // 如果不是上述格式，返回原字符串
        return dateTimeStr;
    }

    private void updateAttributeEntity(String worderNo, String status) {
        WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "order_status", "leapMotor");
        if (attributeEntity == null) {
            return;
        }
        attributeEntity.setAttributeValue(status);
        attributeEntity.setUpdateTime(null);
        worderInformationAttributeDao.updateById(attributeEntity);
    }

    /**
     * 校验签名
     */
    private void validateSignature(String authorization, String serverTime) throws Exception {
        if (authorization == null || serverTime == null) {
            throw new SecurityException("缺少签名或时间戳");
        }

        // 计算本地签名
        String localSignature = CryptoUtil.generateSignature(config.getAppSecret(), serverTime);

        // 解析请求头里的签名
        String[] parts = authorization.split("signature=");
        if (parts.length < 2) {
            throw new SecurityException("Authorization 格式错误");
        }
        String remoteSignature = parts[1].replace("\"", "").trim();

        if (!localSignature.equalsIgnoreCase(remoteSignature)) {
            throw new SecurityException("签名不一致");
        }
        log.debug("[零跑回调] 签名校验通过");
    }

    /**
     * 转换区域编码
     *
     * @param provinceCode
     * @param cityCode
     * @param areaCode
     * @return
     */
    private RegionCode convertRegion(String provinceCode, String cityCode, String areaCode) {
        RegionCode regionCode = new RegionCode();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(provinceCode)) {
            Long bydProvinceCode = Long.valueOf(provinceCode);
            BizRegionEntity provinceRegion = bizRegionService.getRegionByBydCode(bydProvinceCode);
            if (IntegerEnum.ONE.getValue().equals(provinceRegion.getType())) {
                regionCode.setProvinceCode(provinceRegion.getId());
            } else if (IntegerEnum.TWO.getValue().equals(provinceRegion.getType())) {
                regionCode.setProvinceCode(provinceRegion.getPid());
                regionCode.setCityCode(provinceRegion.getId());
            } else if (IntegerEnum.THIRD.getValue().equals(provinceRegion.getType())) {
                regionCode.setCityCode(provinceRegion.getPid());
                regionCode.setAreaCode(provinceRegion.getId());
                BizRegionEntity cityRegion = bizRegionService.getById(provinceRegion.getPid());
                regionCode.setProvinceCode(cityRegion.getPid());
            }
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(cityCode)) {
            Long bydCityCode = Long.valueOf(cityCode);
            BizRegionEntity cityRegion = bizRegionService.getRegionByBydCode(bydCityCode);
            if (IntegerEnum.TWO.getValue().equals(cityRegion.getType())) {
                regionCode.setCityCode(cityRegion.getId());
            } else if (IntegerEnum.THIRD.getValue().equals(cityRegion.getType())) {
                regionCode.setCityCode(cityRegion.getPid());
                regionCode.setAreaCode(cityRegion.getId());
            }
        }

        // 区编码 未获取到数据 填写市编码
        if (org.apache.commons.lang3.StringUtils.isNotBlank(areaCode)) {
            Long bydAreaCode = Long.valueOf(areaCode);
            BizRegionEntity areaRegion = bizRegionService.getRegionByBydCode(bydAreaCode);
            if (areaRegion != null) {
                regionCode.setAreaCode(areaRegion.getId());
            } else {
                regionCode.setAreaCode(regionCode.getCityCode());
            }
        } else {
            regionCode.setAreaCode(regionCode.getCityCode());
        }
        return regionCode;
    }

    private LeapMotorResponse<Void> success() {
        LeapMotorResponse<Void> response = new LeapMotorResponse<>();
        response.setCode("200");
        response.setSuccess(true);
        response.setMsg("success");
        return response;
    }

    private LeapMotorResponse<Void> error(String msg) {
        LeapMotorResponse<Void> response = new LeapMotorResponse<>();
        response.setCode("500");
        response.setSuccess(false);
        response.setMsg(msg);
        return response;
    }
}
