package com.bonc.rrs.lp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    DISPATCH("01", "工单派单"),
    TEAM_DISPATCH("02", "工程队派单"),
    RESERVE_SURVEY("03", "预约勘察"),
    SURVEY("04", "勘察"),
    INSTALL("05", "安装"),
    RETURN("06", "工单退回"),
    CLOSE("07", "工单关闭"),
    UNSUBSCRIBE("08", "用户退订"),
    PAUSE_APPLY("09", "工单暂停申请"),
    PAUSE_APPROVAL("10", "工单暂停审批"),
    PAUSE("11", "工单直接暂停"),
    RESTART("12", "工单重启"),
    UPDATE("13", "工单更新"),
    CANCEL("14", "工单取消"),
    ;


    private final String code;
    private final String desc;

    /** 根据 code 查找对应枚举 */
    public static BusinessTypeEnum fromCode(String code) {
        for (BusinessTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知业务类型 code: " + code);
    }
}

