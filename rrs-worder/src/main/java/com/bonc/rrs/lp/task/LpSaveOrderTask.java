package com.bonc.rrs.lp.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.lp.config.LeapMotorConfig;
import com.bonc.rrs.lp.domain.LeapMotorResponse;
import com.bonc.rrs.lp.domain.OrderDispatchDTO;
import com.bonc.rrs.lp.enums.OrderTypeEnum;
import com.bonc.rrs.lp.service.LeapMotorPushService;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dao.WorderIntfMessageDao;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.BizRegionService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.bonc.rrs.worder.service.WorderTemplateService;
import com.bonc.rrs.xk.service.XkApiService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import com.youngking.lenmoncore.common.entity.UpstreamSystemIdEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 推送订单入库定时任务
 */
@Slf4j
@Component("LpSaveOrderTask")
public class LpSaveOrderTask implements ITask {

    @Autowired
    private WorderIntfMessageDao worderIntfMessageDao;
    @Autowired
    private BrandService brandService;
    @Autowired
    private BizRegionService bizRegionService;
    @Autowired
    private WorderTemplateService worderTemplateService;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private LeapMotorPushService leapMotorPushService;


    @Autowired
    private  LeapMotorConfig config;

    private void createDefaultLoginUser() {
        DefaultWebSecurityManager defaultWebSecurityManager = new DefaultWebSecurityManager();
        SecurityUtils.setSecurityManager(defaultWebSecurityManager);

        // 由于跳过登陆认证，需要设置固定的登陆信息
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setUserId(89L);
        sysUserEntity.setUsername("系统自动");
        // 创建一个Subject.Builder
        Subject.Builder builder = new Subject.Builder();
        // 设置身份信息
        PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, "系统自动");
        builder.principals(principals);
        // 设置是否已经认证
        builder.authenticated(true);
        // 创建Subject实例
        Subject subject = builder.buildSubject();
        ThreadContext.bind(subject);
    }

    @Lock4j(expire = 330000)
    @Override
    public void run(String params) {
        log.info("零跑推送订单入库定时任务 ---- 开始");
        List<WorderIntfMessageEntity> list = worderIntfMessageDao.queryNotSaveOrder(UpstreamSystemIdEnum.LEAPMOTOR.getCode());
        if (list != null) {
            createDefaultLoginUser();
            for (WorderIntfMessageEntity worderIntfMessageEntity : list) {
                log.info("message save order {} start.", worderIntfMessageEntity.getOrderCode());
                try {
                    OrderDispatchDTO orderDispatchDTO = JSON.parseObject(worderIntfMessageEntity.getData(), OrderDispatchDTO.class);
                    saveOrder(worderIntfMessageEntity.getId(), orderDispatchDTO);
                } catch (Exception e) {
                    updateMessageTypeFail(worderIntfMessageEntity.getId(), e.toString());
                }

                log.info("message save order {} end", worderIntfMessageEntity.getOrderCode());
            }
        }
        log.info("推送订单入库定时任务 ---- 结束");
    }

    private void saveOrder(Integer messageId, OrderDispatchDTO orderDispatchDTO) {
        R r;
        try {
            Integer brandId = getLpBrandId();
            Integer companyId = 726;
            RegionCode regionCode = convertRegion(orderDispatchDTO.getBusDispathInfo().getProvinceCode(),
                    orderDispatchDTO.getBusDispathInfo().getCityCode(),
                    orderDispatchDTO.getBusDispathInfo().getCountryCode());
            if (regionCode.getProvinceCode() == 0L || regionCode.getCityCode() == 0L || regionCode.getAreaCode() == 0L) {
                log.info("message save order " + orderDispatchDTO.getOrderNo() + " 地区不匹配");
                updateMessageTypeFail(messageId, "地区不匹配");
                return;
            }
            List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(brandId,
                    WorderTypeEnum.SERVE_CONVEY_INSTALL.getId(), regionCode.getProvinceCode().intValue(), regionCode.getCityCode().intValue());
            if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
                log.info("message save order " + orderDispatchDTO.getOrderNo() + " 没有对应的工单模板");
                updateMessageTypeFail(messageId, "没有对应的工单模板");
                return;
            }
            worderTemplateDtoList.sort(Comparator.comparing(WorderTemplateDto::getTemplateId).reversed());
            WorderTemplateDto worderTemplateDto = worderTemplateDtoList.get(0);

            if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(orderDispatchDTO.getOrderNo(), worderTemplateDto.getTemplateId())) {
                log.info("message save order " + orderDispatchDTO.getOrderNo() + " 车企订单号已存在，无法创建工单");
                updateMessageTypeRepeatWorder(messageId, "车企订单号已存在，无法创建工单");
                return;
            }

            // 匹配表情符
            String regex = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";
            WorderInfoEntity worderInfoEntity = new WorderInfoEntity();
            String address = regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + orderDispatchDTO.getBusDispathInfo().getAddress();
            address = address.replaceAll(regex, "");

            String userName = orderDispatchDTO.getBusDispathInfo().getCustomName();
            userName = userName.replaceAll(regex, "");
            // 0:带桩上门 1:仅提供安装 2:仅设备 3:家充服务包 ,一般只有1 3
            if (OrderTypeEnum.INSTALLATION_ONLY.getCode() == orderDispatchDTO.getBusDispathInfo().getOrderType()) {
                userName += "(仅安装)";
            }
            String dispatchTime = orderDispatchDTO.getBusDispathInfo().getSubmitTime();

            worderInfoEntity.setPushOrderWorderSource("leapMotor");
            worderInfoEntity.setUserName(userName);
            worderInfoEntity.setUserPhone(orderDispatchDTO.getBusDispathInfo().getPhoneNo());
            worderInfoEntity.setAddress(address);
            worderInfoEntity.setCompanyOrderNumber(orderDispatchDTO.getOrderNo());
            worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());

            worderInfoEntity.setCarBrand(brandId + "");
            worderInfoEntity.setCarModel("4");
            worderInfoEntity.setCompanyId(companyId);

            worderInfoEntity.setPostcode("");
            worderInfoEntity.setWorderSourceTypeValue("");
            worderInfoEntity.setWorderTypeId(WorderTypeEnum.SERVE_CONVEY_INSTALL.getId());

            worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
            worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

            List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getWorkOrderType(), "工单类型", worderInfoEntity.getWorderTypeId()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getCarCompanyOrderNumber(), "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getCarCompanyName(), "车企名称", companyId));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getOperationTimeCreate(), "车企派单日期", dispatchTime));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getProductOrderNumber(), "零跑-商品订单号", orderDispatchDTO.getBusDispathInfo().getOutOrderNo()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getInstallationAddress(), "安装地址", address));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getCustomerName(), "客户姓名", userName));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getCustomerPhone(), "客户手机", worderInfoEntity.getUserPhone()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getOrderType(), "零跑-订单类型", OrderTypeEnum.getDescByCode(orderDispatchDTO.getBusDispathInfo().getOrderType())));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getRequiredProduct(), "零跑-需求产品", orderDispatchDTO.getBusDispathInfo().getNeedProductName()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getRequiredProductModel(), "零跑-需求产品型号", orderDispatchDTO.getBusDispathInfo().getProductModelName()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getSubmissionDate(), "零跑-提交日期", orderDispatchDTO.getBusDispathInfo().getSubmitTime()));
//            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getCarModel(), "零跑-车型", orderDispatchDTO.getBusDispathInfo().getCarModelName()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getPackageType(), "零跑-套餐类型", orderDispatchDTO.getBusDispathInfo().getOrderPackageName()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(config.getField().getRemark(), "零跑-备注-创建", orderDispatchDTO.getBusDispathInfo().getRemark()));
            worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
            log.info("保存工单信息 {}", JSON.toJSONString(worderInfoEntity));
            r = worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
        } catch (Exception e) {
            log.error("message save order " + orderDispatchDTO.getOrderNo() + " 出现异常", e);
            updateMessageTypeFail(messageId, "零跑生成工单失败失败，" + e);
            return;
        }

        if (!IntegerEnum.ZERO.getValue().equals(r.get(WarningConstant.CODE))) {
            log.info("message save order " + orderDispatchDTO.getOrderNo() + r.get("msg"));
            updateMessageTypeFail(messageId, r.get("msg") + "");
            return;
        }
        String worderNo = r.get("worderNo") + "";
        List<WorderInformationEntity> worderInformationEntityList = worderInformationService.getBaseMapper()
                .selectList(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo));
        WorderInformationEntity worderInformationEntity = worderInformationEntityList.get(0);
        worderIntfMessageDao.updateWorderIdById(messageId, worderInformationEntity.getWorderId());

        // 自动派单

        Results results = worderInformationService.goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME, null);
        // 修改工单状态 0
        worderInformationService.updateWorderStatus(worderNo);
        // 推送零跑
        LeapMotorResponse leapMotorResponse = leapMotorPushService.callbackTeamDispatch(worderInformationEntity.getWorderId());
        if (!"200".equals(leapMotorResponse.getCode())) {
            throw new RRException("调用零跑工程队派单接口报错" + leapMotorResponse.getMsg());
        }
    }

    private void updateMessageTypeRepeatWorder(Integer messageId, String errorMsg) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 1, errorMsg);
    }

    /**
     * 转换区域编码
     *
     * @param provinceCode
     * @param cityCode
     * @param areaCode
     * @return
     */
    private RegionCode convertRegion(String provinceCode, String cityCode, String areaCode) {
        RegionCode regionCode = new RegionCode();
        if (StringUtils.isNotBlank(provinceCode)) {
            Long bydProvinceCode = Long.valueOf(provinceCode);
            BizRegionEntity provinceRegion = bizRegionService.getRegionByBydCode(bydProvinceCode);
            if (IntegerEnum.ONE.getValue().equals(provinceRegion.getType())) {
                regionCode.setProvinceCode(provinceRegion.getId());
            } else if (IntegerEnum.TWO.getValue().equals(provinceRegion.getType())) {
                regionCode.setProvinceCode(provinceRegion.getPid());
                regionCode.setCityCode(provinceRegion.getId());
            } else if (IntegerEnum.THIRD.getValue().equals(provinceRegion.getType())) {
                regionCode.setCityCode(provinceRegion.getPid());
                regionCode.setAreaCode(provinceRegion.getId());
                BizRegionEntity cityRegion = bizRegionService.getById(provinceRegion.getPid());
                regionCode.setProvinceCode(cityRegion.getPid());
            }
        }

        if (StringUtils.isNotBlank(cityCode)) {
            Long bydCityCode = Long.valueOf(cityCode);
            BizRegionEntity cityRegion = bizRegionService.getRegionByBydCode(bydCityCode);
            if (IntegerEnum.TWO.getValue().equals(cityRegion.getType())) {
                regionCode.setCityCode(cityRegion.getId());
            } else if (IntegerEnum.THIRD.getValue().equals(cityRegion.getType())) {
                regionCode.setCityCode(cityRegion.getPid());
                regionCode.setAreaCode(cityRegion.getId());
            }
        }

        // 区编码 未获取到数据 填写市编码
        if (StringUtils.isNotBlank(areaCode)) {
            Long bydAreaCode = Long.valueOf(areaCode);
            BizRegionEntity areaRegion = bizRegionService.getRegionByBydCode(bydAreaCode);
            if (areaRegion != null) {
                regionCode.setAreaCode(areaRegion.getId());
            } else {
                regionCode.setAreaCode(regionCode.getCityCode());
            }
        } else {
            regionCode.setAreaCode(regionCode.getCityCode());
        }
        return regionCode;
    }

    /**
     * 获取省市区ID
     *
     * @param code
     * @return Long
     */
    private Long getRegionByCode(String code) {
        QueryWrapper<BizRegionEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("byd_code", code);
        BizRegionEntity province = bizRegionService.getOne(wrapper);
        if (Objects.isNull(province)) {
            return 0L;
        }
        return province.getId();
    }


    public void updateMessageTypeFail(Integer messageId, String errorMsg) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 2, errorMsg);
    }

    private Integer getLpBrandId() {
        QueryWrapper<BrandEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("brand_name", "零跑");
        BrandEntity one = brandService.getOne(queryWrapper);
        if (one == null) {
            throw new RRException("未找到零跑品牌");
        }
        return one.getId();
    }

    private WorderExtFieldEntity setWorderExtFieldEntity(Integer fieldId, String fieldName, Object fieldValue) {
        String value = "";
        if (fieldValue != null) {
            value = String.valueOf(fieldValue);
        }
        return WorderExtFieldEntity.builder()
                .fieldId(fieldId)
                .fieldName(fieldName)
                .fieldValue(value).build();
    }

}
