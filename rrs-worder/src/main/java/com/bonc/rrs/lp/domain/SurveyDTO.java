package com.bonc.rrs.lp.domain;

import lombok.Data;

/**
 * 勘察 DTO
 * businessType = 04
 */
@Data
public class SurveyDTO {
    private String orderNo;
    private String operationTime;
    private String businessType;
    private String orderStatus;

    private ReconnaissanceInfo reconnaissanceInfoParam;

    @Data
    public static class ReconnaissanceInfo {
        private Integer getPowerPoint;
        private String gmtCheckEnd;
        private String gmtCheckStart;
        private String gmtPredictCheckEndNext;
        private String gmtPredictCheckStartNext;
        private String gmtPredictInstallEnd;
        private String gmtPredictInstallStart;
        private Integer installEnable;
        private Integer parkPlace;
        private String predictProducts;
        private String serviceProviderFirstContactTime;
        private String urlCheckForm;
        private String urlDisclaimer;
        private String urlUserNotice;
    }
}
