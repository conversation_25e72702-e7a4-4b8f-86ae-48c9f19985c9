package com.bonc.rrs.lp.domain;

import lombok.Data;

/**
 * 预约勘察 DTO
 * businessType = 03
 */
@Data
public class ReserveSurveyDTO {
    private String orderNo;
    private String operationTime;
    private String businessType;
    private String orderStatus;

    private ReserveReconnaissanceInfo reserveReconnaissanceInfoParam;

    @Data
    public static class ReserveReconnaissanceInfo {
        private String engineerTeam;
        private String gmtPredictCheckStart;
        private String gmtPredictCheckEnd;
        private String remark;
        private Integer repositoryCode;
        /** 是否带桩上门 1=是 0=否 */
        private String whetherCarry;
    }
}
