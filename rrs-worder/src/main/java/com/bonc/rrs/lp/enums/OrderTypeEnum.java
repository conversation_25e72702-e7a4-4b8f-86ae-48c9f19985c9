package com.bonc.rrs.lp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单类型枚举
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum {
    WITH_POLE_HOME(0, "带桩上门"),
    INSTALLATION_ONLY(1, "仅提供安装"),
    DEVICE_ONLY(2, "仅设备"),
    HOME_CHARGE_SERVICE(3, "家充服务包");

    private final int code;
    private final String desc;

    /** 根据 code 查找对应枚举 */
    public static OrderTypeEnum fromCode(Integer code) {
        for (OrderTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知业务类型 code: " + code);
    }

    /**
     * 通过code获取描述
     * @param code 订单类型code
     * @return 对应的描述
     * @throws IllegalArgumentException 如果code不存在
     */
    public static String getDescByCode(int code) {
        return fromCode(code).getDesc();
    }

    @Override
    public String toString() {
        return code + ":" + desc;
    }
}