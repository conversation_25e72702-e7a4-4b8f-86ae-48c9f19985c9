package com.bonc.rrs.lp.controller;


import com.bonc.rrs.invoice.acs.Md5;
import com.bonc.rrs.lp.domain.LeapMotorRequest;
import com.bonc.rrs.lp.domain.LeapMotorResponse;
import com.bonc.rrs.lp.service.LeapMotorPushService;
import com.bonc.rrs.lp.task.LpSaveOrderTask;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 零跑 -> 服务商 回调接口
 * 所有业务统一通过 pushOrder 接口推送
 */
@Slf4j
@RestController
@RequestMapping("/api/leapmotor")
@RequiredArgsConstructor
public class LeapMotorPushController {

    private final LeapMotorPushService pushService;

    private final LpSaveOrderTask lpSaveOrderTask;

    /**
     * 零跑统一回调接口
     *
     * @param request 请求体（加密 data）
     * @param servletRequest 获取请求头用于签名校验
     */
    @PostMapping("/uuevc/directConnection/pushOrder")
    public LeapMotorResponse<Void> pushOrder(
            @RequestBody LeapMotorRequest<String> request,
            HttpServletRequest servletRequest) {

        String authorization = servletRequest.getHeader("Authorization");
        String serverTime = servletRequest.getHeader("server-time");

        log.info("[零跑回调] 接收到推送: appId={}, nonce={}, serverTime={}, authorization={}",
                request.getAppId(), request.getNonce(), serverTime, authorization);
        createDefaultLoginUser();
        return pushService.handlePushOrder(request, authorization, serverTime);
    }

    @PostMapping("/runLpSaveOrderTask")
    public String runLpSaveOrderTask(@RequestParam(required = false) String params) {
        lpSaveOrderTask.run(params);
        return "执行成功";
    }
    @PostMapping("/callbackInstall")
    public LeapMotorResponse<Void> callbackInstall(@RequestParam(required = false) String params) {
        LeapMotorResponse<Void> leapMotorResponse = pushService.callbackInstall(params);
        return leapMotorResponse;
    }

    private void createDefaultLoginUser() {
        // 由于跳过登陆认证，需要设置固定的登陆信息
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setUserId(89L);
        sysUserEntity.setUsername("系统自动");
        // 创建一个Subject.Builder
        Subject.Builder builder = new Subject.Builder();
        // 设置身份信息
        PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, "系统自动");
        builder.principals(principals);
        // 设置是否已经认证
        builder.authenticated(true);
        // 创建Subject实例
        Subject subject = builder.buildSubject();
        ThreadContext.bind(subject);
    }
}
