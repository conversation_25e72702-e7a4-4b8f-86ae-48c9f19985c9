package com.bonc.rrs.lp.domain;

import lombok.Data;

/**
 * 安装 DTO
 * businessType = 05
 */
@Data
public class InstallDTO {
    private String orderNo;
    private String operationTime;
    private String businessType;
    private String orderStatus;

    private InstallInfo installInfoParam;

    @Data
    public static class InstallInfo {
        private String gmtInstallStart;
        private String gmtInstallEnd;
        private String useCableLength;
        private String extraPay;
        private String productModel;
        private String urlChargePile;
        private String urlReceipt;
        private String urlSwitchBox;
        private String urlOutLimit;
        private String urlPowerConnection;
        private String installProvince;
        private String installCity;
        private String installCountry;
        private String installAddress;
        private String installAddressXy;
        private Integer whetherGrounded;
        private String urlGroundedPicture;
        private String customerHasInstall;
        private String customerExpectInstall;
        private String serviceStaffAppointment;
        private String gmtVisitStart;
    }
}
