package com.bonc.rrs.lp.client;

import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.lp.config.LeapMotorConfig;
import com.bonc.rrs.lp.domain.*;
import com.bonc.rrs.lp.enums.BusinessTypeEnum;
import com.bonc.rrs.lp.util.CryptoUtil;
import com.bonc.rrs.lp.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 零跑直连接口统一客户端
 */
@Component
@RequiredArgsConstructor
public class LeapMotorClient {

    private final LeapMotorConfig config;
    private final HttpUtil httpUtil;

    /**
     * 核心 POST 方法
     */
    private <T> LeapMotorResponse<T> post(String path, Object request, Class<T> clazz, BusinessTypeEnum businessType) throws Exception {
        String url = config.getBaseUrl() + path;

        // 1. serverTime & nonce
        String serverTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String nonce = CryptoUtil.generateNonce(businessType.getCode());

        // 2. 加密 data
        String encryptedData = CryptoUtil.encrypt(
                JSONObject.toJSONString(request),
                config.getAppSecret(),
                config.getAppId(),
                nonce
        );

        // 3. 封装请求体
        LeapMotorRequest<Object> leapRequest = new LeapMotorRequest<>();
        leapRequest.setAppId(config.getAppId());
        leapRequest.setNonce(nonce);
        leapRequest.setData(encryptedData);

        // 4. 生成签名
        String signature = CryptoUtil.generateSignature(config.getAppSecret(), serverTime);
        String authorization = String.format(
                "hmac username=\"%s\",algorithm=\"md5-salt\",headers=\"server-time\",signature=\"%s\"",
                config.getAppId(), signature
        );

        // 5. 构造请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("server-time", serverTime);
        headers.put("Content-Type", "application/json;charset=utf-8");

        // 6. 发起请求，注意先拿原始的密文 data
        LeapMotorResponse<String> rawResponse = httpUtil.post(url, leapRequest, headers, LeapMotorResponse.class);

        LeapMotorResponse<T> response = new LeapMotorResponse<>();
        response.setCode(rawResponse.getCode());
        response.setMsg(rawResponse.getMsg());
        response.setSuccess(rawResponse.isSuccess());

        // 7. 如果 data 不为空，则解密
        if (rawResponse.getData() != null) {
            String decrypted = CryptoUtil.decrypt(rawResponse.getData(), config.getAppSecret(), config.getAppId(), nonce);
            T result = JSONObject.parseObject(decrypted, clazz);
            // 将解密后的结果放回 response
            response.setData(result);
        } else {
            response.setData(null);
        }

        return response;
    }

    // ======================= 以下为具体业务方法 =======================

    public LeapMotorResponse<Void> teamDispatch(TeamDispatchDTO request) throws Exception {
        request.setBusinessType(BusinessTypeEnum.TEAM_DISPATCH.getCode());
        return post(LeapMotorApiPaths.CALLBACK_ORDER, request, Void.class, BusinessTypeEnum.TEAM_DISPATCH);
    }

    public LeapMotorResponse<Void> reserveSurvey(ReserveSurveyDTO request) throws Exception {
        request.setBusinessType(BusinessTypeEnum.RESERVE_SURVEY.getCode());
        return post(LeapMotorApiPaths.CALLBACK_ORDER, request, Void.class, BusinessTypeEnum.RESERVE_SURVEY);
    }

    public LeapMotorResponse<Void> survey(SurveyDTO request) throws Exception {
        request.setBusinessType(BusinessTypeEnum.SURVEY.getCode());
        return post(LeapMotorApiPaths.CALLBACK_ORDER, request, Void.class, BusinessTypeEnum.SURVEY);
    }

    public LeapMotorResponse<Void> install(InstallDTO request) throws Exception {
        request.setBusinessType(BusinessTypeEnum.INSTALL.getCode());
        return post(LeapMotorApiPaths.CALLBACK_ORDER, request, Void.class, BusinessTypeEnum.INSTALL);
    }

    public LeapMotorResponse<Void> closeOrder(OrderCloseDTO request) throws Exception {
        request.setBusinessType(BusinessTypeEnum.CLOSE.getCode());
        return post(LeapMotorApiPaths.CALLBACK_ORDER, request, Void.class, BusinessTypeEnum.CLOSE);
    }

    public LeapMotorResponse<Void> pauseApply(OrderPauseApplyDTO request) throws Exception {
        request.setBusinessType(BusinessTypeEnum.PAUSE_APPLY.getCode());
        return post(LeapMotorApiPaths.CALLBACK_ORDER, request, Void.class, BusinessTypeEnum.PAUSE_APPLY);
    }

    public LeapMotorResponse<Void> restart(OrderRestartDTO request) throws Exception {
        request.setBusinessType(BusinessTypeEnum.RESTART.getCode());
        return post(LeapMotorApiPaths.CALLBACK_ORDER, request, Void.class, BusinessTypeEnum.RESTART);
    }

    public LeapMotorResponse<RepositoryInfoDTO> findPageRepository(FindRepositoryDTO request) throws Exception {
        return post(LeapMotorApiPaths.FIND_PAGE_REPO, request, RepositoryInfoDTO.class, BusinessTypeEnum.SEARCH);
    }
}
