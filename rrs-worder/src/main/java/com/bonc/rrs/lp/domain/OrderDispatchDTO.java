package com.bonc.rrs.lp.domain;

import lombok.Data;

/**
 * 工单派单 DTO
 * businessType = 01
 */
@Data
public class OrderDispatchDTO {
    /** 订单编号 */
    private String orderNo;

    /** 操作时间 yyyy-MM-dd HH:mm:ss */
    private String operationTime;

    /** 业务类型 01 */
    private String businessType;

    /** 工单状态 */
    private String orderStatus;

    /** 派单详细信息 */
    private BusDispatchInfo busDispathInfo;

    @Data
    public static class BusDispatchInfo {
        /** 详细地址 */
        private String address;
        /** 车型 */
        private String carModelName;
        /** 城市名称 */
        private String city;
        /** 城市编码 */
        private String cityCode;
        /** 区县名称 */
        private String country;
        /** 区县编码 */
        private String countryCode;
        /** 客户姓名 */
        private String customName;
        /** 工程队编号 */
        private String engineer;
        /** 需求产品名称 */
        private String needProductName;
        /** 套餐类型 */
        private String orderPackageName;
        /** 订单类型：0-带桩上门，1-仅安装，2-仅设备，3-家充服务包 */
        private Integer orderType;
        /** 商品订单号 */
        private String outOrderNo;
        /** 联系电话 */
        private String phoneNo;
        /** 产品型号 */
        private String productModelName;
        /** 省份 */
        private String province;
        /** 省份编码 */
        private String provinceCode;
        /** 备注 */
        private String remark;
        /** 提交时间 yyyy-MM-dd HH:mm:ss */
        private String submitTime;
    }
}
