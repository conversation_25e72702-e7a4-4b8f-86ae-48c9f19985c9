package com.bonc.rrs.lp.util;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.security.MessageDigest;

public class CryptoUtil {
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int TAG_LENGTH_BIT = 128; // GCM认证标签长度
    private static final int IV_LENGTH_BYTE = 12;   // GCM初始向量长度

    /**
     * AES-256-GCM 加密
     *
     * @param plainText 明文
     * @param appSecret 密钥
     * @param appId     应用ID
     * @param nonce     随机数
     * @return Base64编码的密文
     * @throws Exception 加密异常
     */
    public static String encrypt(String plainText, String appSecret, String appId, String nonce) throws Exception {
        try {
            // 1. 准备密钥
            byte[] keyBytes = Base64.getDecoder().decode(appSecret);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");

            // 2. 生成随机IV
            byte[] iv = new byte[IV_LENGTH_BYTE];
            SecureRandom random = new SecureRandom();
            random.nextBytes(iv);

            // 3. 准备AAD（Additional Associated Data）
            String aad = appId + nonce;
            byte[] aadBytes = aad.getBytes(StandardCharsets.UTF_8);

            // 4. 初始化加密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, parameterSpec);
            cipher.updateAAD(aadBytes);

            // 5. 加密数据
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 6. 组合IV + 密文，并Base64编码
            byte[] combined = new byte[iv.length + encryptedBytes.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encryptedBytes, 0, combined, iv.length, encryptedBytes.length);

            return Base64.getEncoder().encodeToString(combined);
        } catch (Exception e) {
            throw new Exception("AES-GCM加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * AES-256-GCM 解密
     *
     * @param cipherText Base64编码的密文
     * @param appSecret  密钥
     * @param appId      应用ID
     * @param nonce      随机数
     * @return 明文
     * @throws Exception 解密异常
     */
    public static String decrypt(String cipherText, String appSecret, String appId, String nonce) throws Exception {
        try {
            // 1. 准备密钥
            byte[] keyBytes = Base64.getDecoder().decode(appSecret);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");

            // 2. 解码Base64并分离IV和密文
            byte[] combined = Base64.getDecoder().decode(cipherText);
            byte[] iv = new byte[IV_LENGTH_BYTE];
            byte[] encryptedData = new byte[combined.length - IV_LENGTH_BYTE];

            System.arraycopy(combined, 0, iv, 0, IV_LENGTH_BYTE);
            System.arraycopy(combined, IV_LENGTH_BYTE, encryptedData, 0, encryptedData.length);

            // 3. 准备AAD
            String aad = appId + nonce;
            byte[] aadBytes = aad.getBytes(StandardCharsets.UTF_8);

            // 4. 初始化解密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, parameterSpec);
            cipher.updateAAD(aadBytes);

            // 5. 解密数据
            byte[] decryptedBytes = cipher.doFinal(encryptedData);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new Exception("AES-GCM解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成随机nonce（业务类型码 + 时间戳 + 6位随机数）
     *
     * @param businessType 业务类型码，如"01"
     * @return 符合规范的nonce
     */
    public static String generateNonce(String businessType) {
        String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%06d", new java.util.Random().nextInt(1000000));
        return businessType + timestamp + random;
    }
    /**
     * 生成随机nonce（业务类型码 + 时间戳 + 6位随机数）
     *
     * @return 符合规范的nonce
     */
    public static String generateNonce() {
        return generateNonce("01");
    }

    /** MD5 签名生成 */
    public static String generateSignature(String appSecret, String serverTime) throws Exception {
        String input = appSecret + "server-time:" + serverTime + appSecret;
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));

        StringBuilder sb = new StringBuilder();
        for (byte b : digest) sb.append(String.format("%02x", b & 0xff));
        return sb.toString();
    }
}

