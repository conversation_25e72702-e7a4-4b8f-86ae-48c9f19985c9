package com.bonc.rrs.lp.service;


import com.bonc.rrs.lp.domain.*;

/**
 * 零跑回调 Service
 */
public interface LeapMotorPushService {

    /**
     * 统一处理零跑的 pushOrder 请求
     *
     * @param request       请求体
     * @param authorization 零跑请求头里的 Authorization
     * @param serverTime    零跑请求头里的 server-time
     */
    LeapMotorResponse<Void> handlePushOrder(
            LeapMotorRequest<String> request,
            String authorization,
            String serverTime);

    /**
     * 工程队派单回调 (2.3)
     */
     LeapMotorResponse<Void> callbackTeamDispatch(Integer worderId);

    /**
     * 预约勘察回调 (2.3)
     */
     LeapMotorResponse<Void> callbackReserveSurvey(ReserveSurveyDTO dto, Integer worderId);

    /**
     * 勘察结果回调 (2.3)
     */
     LeapMotorResponse<Void> callbackSurvey(SurveyDTO dto, Integer worderId);

    /**
     * 安装结果回调 (2.3)
     */
     LeapMotorResponse<Void> callbackInstall(InstallDTO dto, Integer worderId);

    /**
     * 工单关闭回调 (2.3)
     */
     LeapMotorResponse<Void> callbackCloseOrder(OrderCloseDTO dto, Integer worderId);

    /**
     * 工单暂停申请回调 (2.3)
     */
     LeapMotorResponse<Void> callbackPauseApply(OrderPauseApplyDTO dto, Integer worderId);

    /**
     * 工单重启回调 (2.3)
     */
     LeapMotorResponse<Void> callbackRestart(OrderRestartDTO dto, Integer worderId);
}