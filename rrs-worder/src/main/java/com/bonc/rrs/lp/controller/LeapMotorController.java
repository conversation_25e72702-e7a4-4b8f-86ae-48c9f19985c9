package com.bonc.rrs.lp.controller;


import com.bonc.rrs.lp.domain.LeapMotorResponse;
import com.bonc.rrs.lp.domain.OrderPauseApprovalRequest;
import com.bonc.rrs.lp.service.LeapMotorPushService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 零跑 -> 服务商 回调接口
 * 所有业务统一通过 pushOrder 接口推送
 */
@Slf4j
@RestController
@RequestMapping("/leapmotor")
@RequiredArgsConstructor
public class LeapMotorController {

    private final LeapMotorPushService pushService;

    @PostMapping("/pauseApply")
    public LeapMotorResponse<Void> pauseApply(@RequestBody OrderPauseApprovalRequest orderPauseApprovalRequest) {
        LeapMotorResponse<Void> leapMotorResponse = pushService.callbackPauseApply(orderPauseApprovalRequest.getWorderNo(), orderPauseApprovalRequest.getSuspendReason());
        return leapMotorResponse;
    }

    @PostMapping("/restart")
    public LeapMotorResponse<Void> restart(@RequestParam(required = false) String worderNo) {
        LeapMotorResponse<Void> leapMotorResponse = pushService.callbackRestart(worderNo);
        return leapMotorResponse;
    }

}
