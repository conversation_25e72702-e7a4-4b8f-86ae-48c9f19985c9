package com.bonc.rrs.xk.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 到每家服务商处理业务
 * @Author: liujunpeng
 * @Date: 2024/2/27 10:09
 * @Version: 1.0
 */
public interface DmjXkServiceProvidersService {

    /**
     * 验签
     *
     * @param request 请求头
     * @param reqBody 请求参数
     * @return 验签结果
     */
    boolean verificationOfSignatures(HttpServletRequest request, JSONObject reqBody);

    /**
     * 数组入参验签
     * @param request
     * @param dataArray
     * @return
     */
    boolean verificationArrayDataOfSignatures(HttpServletRequest request, JSONArray dataArray);

    /**
     * 安装信息
     * @param pushInstallationInfo 入参
     * @return 响应
     */
    OtherApiResponse pushInstallationInfo(PushInstallationInfo pushInstallationInfo);

    /**
     * 安装附件信息
     * @param pushAccessoriesInfo
     * @return
     */
    OtherApiResponse pushAccessoriesInfo(PushAccessoriesInfo pushAccessoriesInfo);

    /**
     * 安装订单暂停信息推送小咖业务处理
     *
     * @param req 入参
     * @return 响应
     */
    OtherApiResponse orderPuaseInfoPush(PushSusPendOrder req);

    /**
     * 安装订单恢复信息推送小咖业务处理
     *
     * @param req 入参
     * @return 响应
     */
    OtherApiResponse orderRestoreInfoPush(PushRestoreOrder req);

    /**
     * 安装信息提交审核回传news
     * @param req 入参
     * @return 响应
     */
    OtherApiResponse pushSubmitReviewInfo(PushSubmitReviewInfo req);

    /**
     * 厂端审核后推送审核信息
     * @param req 入参
     * @return 响应
     */
    PushApiResponse pushSubmitInfo(PushSubmitInfo req);


    /**
     * news推送比亚迪订单取消审核
     * @param req 入参
     * @return 响应
     */
    PushApiResponse pushCancelOrder(PushCancelOrder req);

    PushApiResponse pushCloseOrder(PushCloseOrder req);

    OtherApiResponse pushCancelReviewInfo(PushSubmitInfo req);

}
