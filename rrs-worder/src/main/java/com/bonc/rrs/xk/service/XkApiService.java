package com.bonc.rrs.xk.service;

import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.response.PageApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;

public interface XkApiService {

    /**
     * 暂停信息推送
     * @param pushSusPendOrder
     * @return
     */
    PushApiResponse pushSusPendOrder(PushSusPendOrder pushSusPendOrder);

    /**
     * 推送恢复信息
     * @param pushRestoreOrder
     */
    PushApiResponse pushRestoreOrder(PushRestoreOrder pushRestoreOrder);
    PushApiResponse pushCancelOrder(PushCancelOrder pushCancelOrder);

    PushApiResponse pushSubmitInfo(PushSubmitInfo pushSubmitInfo);

    PushApiResponse pushCloseOrder(PushCloseOrder PushCloseOrder);

    PushApiResponse pushOrder(PushOrderBody pushOrderBody);

    PushApiResponse pushUpdateOrder(PushUpdateOrder pushUpdateOrder);

    PushApiResponse pushSettle(PushSettleInfo pushSettleInfo);

    PageApiResponse selectOrder(WorderParm worderParm);
}
