package com.bonc.rrs.xk.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bonc.rrs.baidumap.dao.DotSendsRecordMapper;
import com.bonc.rrs.baidumap.entity.DotSendsRecord;
import com.bonc.rrs.balancerule.dao.BalanceRuleDetailDao;
import com.bonc.rrs.balancerule.entity.BalanceRuleDetailEntity;
import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.byd.util.Sha256SignUtils;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.dao.MaterialInforDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dao.WorderRemarkLogDao;
import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.bonc.rrs.worder.entity.MaterielInformationEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.service.DotInformationService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worderapp.controller.WorderOrderController;
import com.bonc.rrs.worderapp.dao.WorderMaterielDao;
import com.bonc.rrs.worderapp.dao.WorderOperationRecodeDao;
import com.bonc.rrs.worderapp.dao.WorderOrderDao;
import com.bonc.rrs.worderapp.entity.WorderOperationRecodeEntity;
import com.bonc.rrs.worderapp.entity.vo.*;
import com.bonc.rrs.workManager.dao.RemarkLogMapper;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.AttendantSendsRecord;
import com.bonc.rrs.workManager.entity.SendWorderRecord;
import com.bonc.rrs.workManager.service.AttendantSendsRecordService;
import com.bonc.rrs.workManager.service.AutidOrderService;
import com.bonc.rrs.workManager.service.SendWorderRecordService;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.bonc.rrs.xk.service.DmjXkServiceProvidersService;
import com.bonc.rrs.xk.service.XkApiService;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.activiti.engine.impl.util.CollectionUtil;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 到每家小咖服务商处理业务
 * @Author: liujunpeng
 * @Date: 2024/3/8 15:01
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class DmjXkServiceProvidersServiceImpl implements DmjXkServiceProvidersService {

    @Value("${xk.AppSecret}")
    private String appSecret;

    final WorderInformationDao worderInformationDao;

    final ProviderBusinessService providerBusinessService;

    final WorderOrderController worderOrderController;

    final SysFilesService sysFilesService;

    final IBydApiService iBydApiService;

    final WorderOperationRecodeDao worderOperationRecodeDao;

    final WorderRemarkLogDao worderRemarkLogDao;

    final BalanceRuleDetailDao balanceRuleDetailDao;

    final MaterialInforDao materialInforDao;

    final WorderMaterielDao worderMaterielDao;

    final FlowCommon flowCommon;

    final XkApiService xkApiService;

    @Autowired(required = false)
    private WorderInformationService worderInformationService;

    @Autowired(required = false)
    private AutidOrderService autidOrderService;

    @Autowired(required = false)
    private RemarkLogMapper remarkLogMapper;

    @Autowired(required = false)
    private SendWorderRecordService sendWorderRecordService;

    @Autowired(required = false)
    private AttendantSendsRecordService attendantSendsRecordService;
    @Resource
    private DotSendsRecordMapper dotSendsRecordMapper;

    @Autowired(required = false)
    private WorkMsgDao workMsgDao;

    @Autowired
    private DotInformationService dotInformationService;

    @Autowired(required = false)
    WorderOrderDao worderOrderDao;

    @Override
    public boolean verificationOfSignatures(HttpServletRequest request, JSONObject reqBody) {
        String nonce=request.getHeader("Nonce");
        String curTime=request.getHeader("Cur_Time");
        String sign=request.getHeader("Sign");
        log.info("Header获取的参数：Nonce==>{},Cur_Time==>{},Sign==>{}",nonce,curTime,sign);
        log.info("推送安装订单基本安装信息:{}",reqBody);
        Map<String, Object> params = JSON.parseObject(reqBody.toJSONString(), Map.class);
        return Sha256SignUtils.verify(sign, appSecret,nonce,curTime,params);
    }

    @Override
    public boolean verificationArrayDataOfSignatures(HttpServletRequest request, JSONArray dataArray) {
        String nonce=request.getHeader("Nonce");
        String curTime=request.getHeader("Cur_Time");
        String sign=request.getHeader("Sign");
        log.info("Header获取的参数：Nonce==>{},Cur_Time==>{},Sign==>{}",nonce,curTime,sign);
        Map<String, Object> params=new HashMap<>();
        params.put("data", dataArray);
        log.info("推送安装订单基本安装信息:{}", params);
        return Sha256SignUtils.verify(sign, appSecret,nonce,curTime,params);
    }

    @Override
    public OtherApiResponse pushInstallationInfo(PushInstallationInfo pushInstallationInfo) {
        // 根据车企订单号查询，工单
        try {
            LambdaQueryWrapper<WorderInformationEntity> worderInfoWrapper = Wrappers.lambdaQuery();
            worderInfoWrapper.eq(WorderInformationEntity::getCompanyOrderNumber, pushInstallationInfo.getOrderCode());
            List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectList(worderInfoWrapper);
            if (worderInformationEntities.isEmpty()) {
                return OtherApiResponse.error(503, "根据订单编号未查询到订单信息");
            }
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(0);
            // 组装保存工单属性报文,保存入库
            FieldSaveVo fieldSaveVo = new FieldSaveVo();
            List<FieldVo> fields = new ArrayList<>();
            // 设置属性
            setField(fields, "950", "充电桩编码", pushInstallationInfo.getWallboxCode(), worderInformationEntity.getWorderNo());
            setField(fields, "1197", "实际安装完成日期", pushInstallationInfo.getInstallationCompletedTime(), worderInformationEntity.getWorderNo());
            setField(fields, "1670", "安装验证码", pushInstallationInfo.getInstallationCode(), worderInformationEntity.getWorderNo());
            setField(fields, "1733", "取电方式-比亚迪", ConstantPool.PowerSupplyMethod.getNameByCode(pushInstallationInfo.getPowerSupplyMethod()), worderInformationEntity.getWorderNo());
            setField(fields, "1677", "线缆品牌", ConstantPool.CableBrand.getNameByCode(pushInstallationInfo.getCableBrand()), worderInformationEntity.getWorderNo());
            setField(fields, "1685", "断路器品牌", pushInstallationInfo.getBreakerBrand(), worderInformationEntity.getWorderNo());
            setField(fields, "1687", "断路器型号", pushInstallationInfo.getBreakerType(), worderInformationEntity.getWorderNo());
            String installStake = pushInstallationInfo.getInstallStake();
            if(StringUtils.isNotBlank(installStake)){
                setField(fields, "1739", "安装立柱", "1".equals(installStake) ? "是" : "否", worderInformationEntity.getWorderNo());
            }
            String installProtectingBox = pushInstallationInfo.getInstallProtectingBox();
            if (StringUtils.isNotBlank(installProtectingBox)) {
                setField(fields, "1740", "是否安装保护箱", "1".equals(installProtectingBox) ? "是" : "否", worderInformationEntity.getWorderNo());
            }
            if("3".equals(pushInstallationInfo.getCableBrand())){
                setField(fields, "1262", "敷设方式", "用户自布线", worderInformationEntity.getWorderNo());
            }
            String groundElectrode = pushInstallationInfo.getGroundElectrode();
            if(StringUtils.isNotBlank(groundElectrode)){
                setField(fields, "1773", "是否接地极", "2".equals(groundElectrode) ? "是" : "否", worderInformationEntity.getWorderNo());
            }
            fieldSaveVo.setFields(fields);
            R r = worderOrderController.saveFieldInfo(fieldSaveVo);
            if (!r.get("code").equals(0)) {
                return OtherApiResponse.error(501, r.get("msg").toString());
            }
            // TODO: 保存用户使用的物料信息
            if (!"3".equals(pushInstallationInfo.getCableBrand()) && StringUtils.isNotBlank(pushInstallationInfo.getCableBrand()) && StringUtils.isNotBlank(pushInstallationInfo.getCableType())) {
                String cableBrandName = ConstantPool.CableBrand.getNameByCode(pushInstallationInfo.getCableBrand());
                // 查询工单模版对应的网点结算规则详情
                List<BalanceRuleDetailEntity> balanceRuleDetailEntities = balanceRuleDetailDao.selectDotBalanceRuleDetail(worderInformationEntity.getTemplateId());
                if (!balanceRuleDetailEntities.isEmpty()) {
                    // 获取物料ID集合
                    List<Integer> materielId = balanceRuleDetailEntities.stream().map(BalanceRuleDetailEntity::getMaterielId).collect(Collectors.toList());
                    // 查询电缆的物料信息
                    List<MaterielInformationEntity> materielInformationEntities = materialInforDao.selectList(new QueryWrapper<MaterielInformationEntity>().in("id", materielId).eq("materiel_type_id", 53));
                    if (!materielInformationEntities.isEmpty()) {
                        // 匹配物料
                        List<MaterielInformationEntity> matchMaterielInformationEntities = materielInformationEntities.stream().filter(item -> pushInstallationInfo.getCableType().equals(item.getMaterielSpec()) || cableBrandName.equals(item.getMaterielBrandValue())).collect(Collectors.toList());
                        if (!matchMaterielInformationEntities.isEmpty()) {
                            MaterielInformationEntity materielInformationEntity = matchMaterielInformationEntities.get(0);

                            worderMaterielDao.deleteMaterielEntity(worderInformationEntity.getWorderId(),materielInformationEntity.getId());
                            // 保存用户使用的物料信息
                            MaterielSaveVo materielSaveVo = new MaterielSaveVo();
                            materielSaveVo.setMaterielId(materielInformationEntity.getId().toString());
                            materielSaveVo.setMaterielSpec(materielInformationEntity.getMaterielSpec());
                            materielSaveVo.setBrandId(materielInformationEntity.getMaterielBrand().toString());
                            materielSaveVo.setWorderId(worderInformationEntity.getWorderId().toString());
                            materielSaveVo.setMaterielNum(pushInstallationInfo.getCableLength());
                            materielSaveVo.setTakePictures("");
                            worderMaterielDao.saveMateriel(materielSaveVo);
                        }
                    }
                }
            }
            boolean b = providerBusinessService.checkBydOrderByWorderId(worderInformationEntity.getWorderId());
            if(b){
                // TODO: 调比亚迪服务商安装信息回传
                pushInstallationInfo.setOperatePerson(ConstantPool.BYD_OPERATOR_NAME);
                OtherApiResponse otherApiResponse = iBydApiService.pushInstallationInfo(pushInstallationInfo);
                if (otherApiResponse.getErrno() != 0) {
                    return OtherApiResponse.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
                }
            }
            // 保存工单操作记录
            ((DmjXkServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, worderInformationEntity.getWorderNo() + "主状态：" + worderInformationEntity.getWorderStatusValue() + "，执行状态：" + worderInformationEntity.getWorderExecStatusValue() + " 安装信息推送成功", "安装信息推送成功", new Date());
        } catch (Exception e) {
            log.error("安装信息推送出现异常", e);
            return OtherApiResponse.error(500, "安装信息推送失败");
        }
        return OtherApiResponse.ok();
    }

    /**
     * 设置工单的扩展属性
     *
     * @param fields     字段集合
     * @param fieldId    字段ID
     * @param fieldName  字段名
     * @param fieldValue 字段值
     * @param worderNo   工单编号
     */
    public void setField(List<FieldVo> fields, String fieldId, String fieldName, String fieldValue, String worderNo) {
        FieldVo fieldVo = new FieldVo();
        fieldVo.setFieldId(fieldId);
        fieldVo.setFieldName(fieldName);
        fieldVo.setFieldValue(fieldValue);
        fieldVo.setWorderNo(worderNo);
        fields.add(fieldVo);
    }

    @Override
    public OtherApiResponse pushAccessoriesInfo(PushAccessoriesInfo pushAccessoriesInfo) {
        try {
            // 根据车企订单号查询，工单
            LambdaQueryWrapper<WorderInformationEntity> worderInfoWrapper = Wrappers.lambdaQuery();
            worderInfoWrapper.eq(WorderInformationEntity::getCompanyOrderNumber, pushAccessoriesInfo.getOrderCode());
            List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectList(worderInfoWrapper);
            if (worderInformationEntities.isEmpty()) {
                return OtherApiResponse.error(503, "根据订单编号未查询到订单信息");
            }
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(0);
            // 组装保存工单属性报文,保存入库
            R r = ((DmjXkServiceProvidersServiceImpl) AopContext.currentProxy()).saveDataField(worderInformationEntity, pushAccessoriesInfo);
            if (!r.get("code").equals(0)) {
                return OtherApiResponse.error(501, r.get("msg").toString());
            }
            boolean b = providerBusinessService.checkBydOrderByWorderId(worderInformationEntity.getWorderId());
            if(b){
                // 调用比亚迪安装附件推送接口
                pushAccessoriesInfo.setOperatePerson(ConstantPool.BYD_OPERATOR_NAME);
                OtherApiResponse otherApiResponse = iBydApiService.pushAccessoriesInfo(pushAccessoriesInfo);
                if (otherApiResponse.getErrno() != 0) {
                    return OtherApiResponse.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
                }
            }
            // 修改订单状态和执行状态
            worderInformationEntity.setWorderStatus(2);
            worderInformationEntity.setWorderStatusValue("安装中");
            worderInformationEntity.setWorderExecStatus(15);
            worderInformationEntity.setWorderExecStatusValue("安装资料待客服确认");
            worderInformationDao.updateById(worderInformationEntity);
            flowCommon.updateFlowStatus(worderInformationEntity.getWorderId(), "anzhuangziliaodaikefuqueren");
            // 保存工单的操作记录
            ((DmjXkServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, worderInformationEntity.getWorderNo() + "主状态：" + worderInformationEntity.getWorderStatusValue() + "，执行状态：" + worderInformationEntity.getWorderExecStatusValue() + " 安装附件推送成功", "安装附件推送成功", new Date());
        } catch (Exception e) {
            log.error("安装附件推送出现异常", e);
            return OtherApiResponse.error(500, "安装附件推送失败");
        }
        return OtherApiResponse.ok();
    }

    /**
     * 保存安装资料信息
     *
     * @param worderInformationEntity 工单
     * @param pushAccessoriesInfo     安装资料信息
     */
    public R saveDataField(WorderInformationEntity worderInformationEntity, PushAccessoriesInfo pushAccessoriesInfo) {
        DataSaveVo dataSaveVo = new DataSaveVo();
        List<DataVo> fields = new ArrayList<>();
        setImageField(fields, "1641", "比亚迪-施工使用线缆", pushAccessoriesInfo.getConstructionImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1642", "比亚迪-充电桩序列码", pushAccessoriesInfo.getSequenceImage(), worderInformationEntity.getWorderNo());
//        setImageField(fields, "1643", "比亚迪-工具垫", pushAccessoriesInfo.getToolBlanketImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1644", "比亚迪-电源点火零电压", pushAccessoriesInfo.getZeroVoltageImage(), worderInformationEntity.getWorderNo());
//        setImageField(fields, "1645", "比亚迪-电源点火地电压", pushAccessoriesInfo.getGroundVoltageImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1646", "比亚迪-放弃电力报装免责声明", pushAccessoriesInfo.getDisclaimersImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1647", "比亚迪-同级负载确认书", pushAccessoriesInfo.getLoadConfirmationImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1648", "比亚迪-用线始端", pushAccessoriesInfo.getLineStartImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1649", "比亚迪-用线末端", pushAccessoriesInfo.getLineEndImage(), worderInformationEntity.getWorderNo());
//        setImageField(fields, "1650", "比亚迪-电源点正面0.5m近景", pushAccessoriesInfo.getPowerSupplyImage(), worderInformationEntity.getWorderNo());
//        setImageField(fields, "1651", "比亚迪-敷设路径全景", pushAccessoriesInfo.getLayingPathImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1652", "比亚迪-漏保上端火零绝缘电阻", pushAccessoriesInfo.getFireZeroResistanceImage(), worderInformationEntity.getWorderNo());
//        setImageField(fields, "1653", "比亚迪-漏保上端火地绝缘电压", pushAccessoriesInfo.getFireGroundResistanceImage(), worderInformationEntity.getWorderNo());
//        setImageField(fields, "1654", "比亚迪-漏保下端火零电压", pushAccessoriesInfo.getFireZeroVoltageImage(), worderInformationEntity.getWorderNo());
//        setImageField(fields, "1655", "比亚迪-漏保下端火地电压", pushAccessoriesInfo.getFireGroundVoltageImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1656", "比亚迪-漏保下端零地电压", pushAccessoriesInfo.getZeroGroundVoltageImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1657", "比亚迪-接地线或接地极", pushAccessoriesInfo.getGroundWireImage(), worderInformationEntity.getWorderNo());
//        setImageField(fields, "1658", "比亚迪-充电桩接线", pushAccessoriesInfo.getConnectionImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1659", "比亚迪-人桩合影", pushAccessoriesInfo.getManPileImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1660", "比亚迪-增项收费单", pushAccessoriesInfo.getIncreaseChargeImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1640", "比亚迪-安装确认单", pushAccessoriesInfo.getConfirmationImage(), worderInformationEntity.getWorderNo());
        setImageField(fields, "1661", "比亚迪-试充照片", pushAccessoriesInfo.getTrialChargeImage(), worderInformationEntity.getWorderNo());
        dataSaveVo.setFields(fields);
        dataSaveVo.setWorderNo(worderInformationEntity.getWorderNo());
        return worderOrderController.saveDataInfo(dataSaveVo);
    }

    /**
     * 设置图片的文件属性
     *
     * @param fields     字段集合
     * @param fieldId    字段ID
     * @param fieldName  字段名
     * @param fieldValue 字段值
     * @param worderNo   工单编号
     */
    public void setImageField(List<DataVo> fields, String fieldId, String fieldName, String fieldValue, String worderNo) {
        // 保存sys_file图片信息
        try {
            String[] fieldValueArr = fieldValue.split(",");
            List<Integer> fieldValues = new ArrayList<>();
            if (StringUtils.isNotBlank(fieldValue)) {
                for (String urlValue : fieldValueArr) {
                    URL url = new URL(urlValue);
                    // 获取整个路径
                    String path = url.getPath();
                    // 用 '/' 分割路径，并获取最后一个部分作为文件名
                    String filename = path.substring(path.lastIndexOf('/') + 1);
                    // 保存图片信息
                    Integer fileId = sysFilesService.saveFilePath(urlValue, filename, filename, null);
                    if (fileId == null) {
                        throw new RRException(fieldName + "，保存出错");
                    }
                    fieldValues.add(fileId);
                }
            }
            DataVo dataVo = new DataVo();
            dataVo.setFieldId(fieldId);
            dataVo.setFieldName(fieldName);
            dataVo.setFieldValue(fieldValues);
            dataVo.setWorderNo(worderNo);
            fields.add(dataVo);
        } catch (MalformedURLException e) {
            throw new RRException(fieldName + "，不合法的URL");
        }
    }

    @Override
    public OtherApiResponse orderPuaseInfoPush(PushSusPendOrder req) {
        try {
            // 调用小咖暂停接口
            req.setSuspendPerson(ConstantPool.BYD_OPERATOR_NAME);
            PushApiResponse pushApiResponse = xkApiService.pushSusPendOrder(req);
            if (!"success".equals(pushApiResponse.getMessage())) {
                return OtherApiResponse.error(501, pushApiResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("推送小咖暂停订单信息出现异常", e);
            return OtherApiResponse.error(500, "推送小咖暂停订单信息失败");
        }
        return OtherApiResponse.ok();
    }

    @Override
    public OtherApiResponse orderRestoreInfoPush(PushRestoreOrder req) {
        try {
            req.setOperatePerson(ConstantPool.BYD_OPERATOR_NAME);
            PushApiResponse pushApiResponse = xkApiService.pushRestoreOrder(req);
            if (!"success".equals(pushApiResponse.getMessage())) {
                return OtherApiResponse.error(501, pushApiResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("推送小咖恢复订单信息出现异常", e);
            return OtherApiResponse.error(500, "推送小咖恢复订单信息失败");
        }
        return OtherApiResponse.ok();
    }

    /**
     * 设置操作记录
     *
     * @param worderInformationEntity 工单对象
     * @param title                   标题
     * @param content                 内容
     * @param optionDate              操作时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void setOperation(WorderInformationEntity worderInformationEntity, String title, String content, Date optionDate) {
        // 保存操作记录
        WorderOperationRecodeEntity worderOperationRecodeEntity = new WorderOperationRecodeEntity();
        worderOperationRecodeEntity.setUserId(ConstantPool.BYD_OPERATOR);
        worderOperationRecodeEntity.setOperationUser(ConstantPool.BYD_OPERATOR_NAME);
        worderOperationRecodeEntity.setRecord(content);
        worderOperationRecodeEntity.setWorderNo(worderInformationEntity.getWorderNo());
        worderOperationRecodeEntity.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(optionDate));
        worderOperationRecodeEntity.setWorderStatus(String.valueOf(worderInformationEntity.getWorderStatus()));
        worderOperationRecodeEntity.setWorderExecStatus(String.valueOf(worderInformationEntity.getWorderExecStatus()));
        worderOperationRecodeDao.insert(worderOperationRecodeEntity);

        // 保存工单备注
        WorderRemarkLogEntity worderRemarkLogEntity = new WorderRemarkLogEntity();
        worderRemarkLogEntity.setWorderNo(worderInformationEntity.getWorderNo());
        worderRemarkLogEntity.setUserId(ConstantPool.BYD_OPERATOR);
        worderRemarkLogEntity.setUserName(ConstantPool.BYD_OPERATOR_NAME);
        worderRemarkLogEntity.setTitle(title);
        worderRemarkLogEntity.setContent(content);
        worderRemarkLogEntity.setCreateTime(optionDate);
        worderRemarkLogDao.insert(worderRemarkLogEntity);
    }


    @Override
    public OtherApiResponse pushSubmitReviewInfo(PushSubmitReviewInfo req) {
        //查询工单信息
        WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", req.getOrderCode()));
        //审核业务 待客服确认流转至安装资料待上传车企
        OtherApiResponse result = autidOrderService.autidApproveInfoXk(worderInformationEntity.getWorderNo());
        if (result.getErrno() != 0) {
            return OtherApiResponse.error(500, result.getErrmsg());
        }
        //服务商提交审核
        return providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderInformationEntity.getWorderNo()).build(), "pushSubmitReviewInfo").getOtherApiResponse();
    }

    @Override
    public PushApiResponse pushSubmitInfo(PushSubmitInfo req){
        return providerBusinessService.callBusiness(BusinessProcessPo.builder().pushSubmitInfo(req).build(), "pushSubmitInfo").getPushApiResponse();
    }
    /**
     * 小咖工单取消接口
     *
     * @param req
     * @return
     */
    @Override
    public PushApiResponse pushCancelOrder(PushCancelOrder req) {
        //查询工单信息
        WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", req.getOrderCode()));
        if (worderInformationEntity == null) {
            return new PushApiResponse("未找到订单编号对应单号!");
        }
        if (worderInformationEntity.getWorderExecStatus().equals("21")) {
            return new PushApiResponse("该单号已经取消!");
        }
        return providerBusinessService.callBusiness(BusinessProcessPo.builder().pushCancelOrder(req).build(), "pushCancelOrder").getPushApiResponse();
    }

    /**
     * 小咖工单取消接口
     *
     * @param req
     * @return
     */
    @Override
    public PushApiResponse pushCloseOrder(PushCloseOrder req) {
        Result pushSubmitReviewInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().pushCloseOrder(req).build(), "pushCloseOrder");
        if (!pushSubmitReviewInfoResult.getCode().equals("0")) {
            return new PushApiResponse(pushSubmitReviewInfoResult.getMsg());
        }
        return new PushApiResponse("success");
    }


    /**
     * 小咖工单取消审核回传接口
     *
     * @param req
     * @return
     */
    @Override
    public OtherApiResponse pushCancelReviewInfo(PushSubmitInfo req) {
        //查询工单信息
        WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", req.getOrderCode()));

        Result result = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderId(worderInformationEntity.getWorderId()).auditRemark(req.getRemark()).auditType(req.getResult()).build(),"pushCancelReviewInfo");
        if (result.getCode()!=0){
            return OtherApiResponse.error(result.getCode(),result.getMsg());
        }
        if (req.getResult().equals("1")) {
            if (worderInformationEntity == null) {
                return OtherApiResponse.error(500, "未找到订单编号对应单号!");
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
            String dateStr = df.format(new Date());// new Date()为获取当前系统时间
            String username = workMsgDao.selectUserNameById(89);
            String str = dateStr + " " + username;
            remarkLogMapper.addOperationLog(worderInformationEntity.getWorderNo(), str + "小咖取消服务", "小咖取消服务", 89);
            remarkLogMapper.updateWordeInfoState(worderInformationEntity.getWorderId(), 3, "取消服务", 21, "取消服务", 6);
            //判断当前工单是否存在服务兵并且是当天派单的，需要减一
            Integer serviceId = worderInformationEntity.getServiceId();
            if (serviceId != null) {
                //查询当前服务兵是否是当天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformationEntity.getWorderId()).eq("send_worder_type", 3).eq("accept_worder_user", serviceId).eq("delete_state", 0).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    List<AttendantSendsRecord> attendantSendsRecords = attendantSendsRecordService.getBaseMapper().selectList(new QueryWrapper<AttendantSendsRecord>().eq("service_id", serviceId).eq("present_date", LocalDate.now()).eq("delete_state", 0));
                    if (attendantSendsRecords != null && !attendantSendsRecords.isEmpty()) {
                        AttendantSendsRecord attendantSendsRecord = attendantSendsRecords.get(0);
                        if (attendantSendsRecord.getOverflow() > 0) {
                            attendantSendsRecord.setOverflow(attendantSendsRecord.getOverflow() - 1);
                        }
                        if (attendantSendsRecord.getSendNum() > 0) {
                            attendantSendsRecord.setSendNum(attendantSendsRecord.getSendNum() - 1);
                        }
                        attendantSendsRecord.setUpdateTime(LocalDateTime.now()).setUpdateUser((long) 89);
                        attendantSendsRecordService.updateById(attendantSendsRecord);
                    }
                }
            }
            Integer dotId = worderInformationEntity.getDotId();
            if (dotId != null) {
                //判断当前网点是否今天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformationEntity.getWorderId()).eq("delete_state", 0).eq("send_worder_type", 2).eq("accept_worder_user", dotId).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    //原网点派单数量-1
                    QueryWrapper<DotInformationEntity> before = new QueryWrapper<>();
                    before.eq("dot_id", dotId);
                    DotInformationEntity dotInformationEntity = dotInformationService.getOne(before);
                    if (dotInformationEntity != null && dotInformationEntity.getCount() > 0) {
                        dotInformationEntity.setCount(dotInformationEntity.getCount() - 1);
                        dotInformationService.updateById(dotInformationEntity);
                    }
                    //当天日期的原网点Id-1
                    List<DotSendsRecord> dotSendsRecordList = dotSendsRecordMapper.selectList(new QueryWrapper<DotSendsRecord>().eq("dot_id", dotId).eq("present_date", LocalDate.now()));
                    if (CollectionUtil.isNotEmpty(dotSendsRecordList) && dotSendsRecordList.get(0).getSendNum() != null && dotSendsRecordList.get(0).getSendNum() > 0) {
                        updateDotSend(dotSendsRecordList.get(0).getSendNum() - 1, dotSendsRecordList.get(0).getId());
                    }
                }
            }
            //如果分配中工单取消更新工单状态后直接返回成功
            if (worderInformationEntity.getWorderStatus() == 0 || worderInformationEntity.getWorderStatus() == 1) {
                return OtherApiResponse.ok();
            }
            //勘测物料以及后续结算处理
            worderInformationService.materielBalanceAccounts(worderInformationEntity.getWorderId());
        }
        return OtherApiResponse.ok();
    }

    //修改网点派单数量表
    public void updateDotSend(Integer sendNum, Integer id) {
        DotSendsRecord dotSendsRecord = new DotSendsRecord().setSendNum(sendNum).setId(id);
        dotSendsRecordMapper.updateById(dotSendsRecord);
    }

}
