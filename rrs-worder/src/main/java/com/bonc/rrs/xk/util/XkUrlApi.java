package com.bonc.rrs.xk.util;

public enum XkUrlApi {

    PUSH_CANCEL("/api/news/cancelOrder", "小咖取消订单接口"),
    PUSH_CLOSE("/api/news/closeOrder", "小咖关闭订单接口"),
    PUSH_SUBMIT_INFO("/api/news/auditing/order", "小咖厂端审核后推送审核信息接口"),
    PUSH_SETTLE("/api/news/pay/order", "news订单结算同步"),
    PUSH_SUSPEND("/api/news/susPendOrder", "订单暂停信息"),
    PUSH_RESTORE("/api/news/restoreOrder", "订单恢复信息"),
    PUSH_ORDER("/api/news/save/order", "小咖安装单基本信息推送接口"),
    PUSH_UPDATE("/api/news/update/order", "小咖安装单基本信息更新接口"),

    SELECT_WORDER("/api/order/getOrderList", "获取小咖订单"),
    ;
    private final String code;
    private final String desc;

    XkUrlApi(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
