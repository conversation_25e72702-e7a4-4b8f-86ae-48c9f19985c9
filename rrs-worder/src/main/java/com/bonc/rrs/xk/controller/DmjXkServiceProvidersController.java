package com.bonc.rrs.xk.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.DmjServiceProvidersService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.xk.service.DmjXkServiceProvidersService;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 到每家小咖服务商处理控制器
 * @Author: liujunpeng
 * @Date: 2024/03/08 10:06
 * @Version: 1.0
 */
@RestController
@RequestMapping("/dmj/openapi/xkApi")
@Api(value = "/dmj/openapi/xkApi", tags = "小咖服务商订单处理控制器")
@RequiredArgsConstructor
public class DmjXkServiceProvidersController {

    final DmjXkServiceProvidersService dmjXkServiceProvidersService;

    final DmjServiceProvidersService dmjServiceProvidersService;

    final ProviderBusinessService providerBusinessService;

    @PostMapping("/pushInstallationInfo")
    @ApiOperation(value = "安装信息", notes = "安装信息")
    OtherApiResponse orderAuditInfoPush(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return OtherApiResponse.error(403, "鉴权失败");
        }
        PushInstallationInfo pushInstallationInfo = JSON.parseObject(req.toJSONString(), PushInstallationInfo.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushInstallationInfo);
        if (StringUtils.isNotBlank(msg)) {
            return OtherApiResponse.error(400, msg);
        }
        // TODO: 业务调用
        return dmjXkServiceProvidersService.pushInstallationInfo(pushInstallationInfo);
    }

    @PostMapping("/pushAccessoriesInfo")
    @ApiOperation(value = "安装附件信息", notes = "安装附件信息")
    OtherApiResponse pushAccessoriesInfo(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return OtherApiResponse.error(403, "鉴权失败");
        }
        PushAccessoriesInfo pushAccessoriesInfo = JSON.parseObject(req.toJSONString(), PushAccessoriesInfo.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushAccessoriesInfo);
        if (StringUtils.isNotBlank(msg)) {
            return OtherApiResponse.error(400, msg);
        }
        // TODO: 业务调用
        return dmjXkServiceProvidersService.pushAccessoriesInfo(pushAccessoriesInfo);
    }


    /**
     * 小咖调用接口
     * @param request
     * @param req
     * @return
     */
    @PostMapping("/pushSubmitReviewInfo")
    @ApiOperation(value = "安装信息提交审核回传news", notes = "安装信息提交审核回传news")
    public OtherApiResponse pushSubmitReviewInfo(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return OtherApiResponse.error(500,"鉴权失败");
        }
        PushSubmitReviewInfo pushSubmitReviewInfo = JSON.parseObject(req.toJSONString(), PushSubmitReviewInfo.class);
        pushSubmitReviewInfo.setOperatePerson(ConstantPool.BYD_OPERATOR_NAME);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushSubmitReviewInfo);
        if (StringUtils.isNotBlank(msg)) {
            return OtherApiResponse.error(500,msg);
        }
        // TODO: 业务调用
        createDefaultLoginUser();
        return dmjXkServiceProvidersService.pushSubmitReviewInfo(pushSubmitReviewInfo);
    }

    /**
     * 小咖调用接口
     * @param request
     * @param req
     * @return
     */
    @PostMapping("/pushCancelReviewInfo")
    @ApiOperation(value = "取消信息提交审核回传news", notes = "取消信息提交审核回传news")
    public OtherApiResponse pushCancelReviewInfo(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return OtherApiResponse.error(500,"鉴权失败");
        }
        PushSubmitInfo pushSubmitInfo = JSON.parseObject(req.toJSONString(), PushSubmitInfo.class);
        pushSubmitInfo.setExaminePerson(ConstantPool.XK_OPERATOR_NAME);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushSubmitInfo);
        if (StringUtils.isNotBlank(msg)) {
            return OtherApiResponse.error(500,msg);
        }
        // TODO: 业务调用
        return dmjXkServiceProvidersService.pushCancelReviewInfo(pushSubmitInfo);
    }

    /**
     * 小咖通知联系信息 -> 通知比亚迪
     */
    @PostMapping("/pushContactInfo")
    public OtherApiResponse pushContactInfo(HttpServletRequest request, @RequestBody JSONObject req) {
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return OtherApiResponse.error(403, "鉴权失败");
        }
        PushContactInfo pushContactInfo = JSON.parseObject(req.toJSONString(), PushContactInfo.class);
        String msg = dmjServiceProvidersService.validateRequiredFields(pushContactInfo);
        if (StringUtils.isNotBlank(msg)) {
            return OtherApiResponse.error(500, msg);
        }
        // TODO: 业务调用
        createDefaultLoginUser();
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder()
                .pushContactInfo(pushContactInfo).build(), "pushContactInfoXk");
        return providerBusinessResult.getOtherApiResponse();
    }

    /**
     * 小咖勘测信息回传 -> 通知比亚迪
     */
    @PostMapping("/pushSurveyInfo")
    public OtherApiResponse PushSurveyInfo(HttpServletRequest request, @RequestBody JSONObject req) {
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return OtherApiResponse.error(403, "鉴权失败");
        }
        PushSurveyInfo pushSurveyInfo = JSON.parseObject(req.toJSONString(), PushSurveyInfo.class);
        String msg = dmjServiceProvidersService.validateRequiredFields(pushSurveyInfo);
        if (StringUtils.isNotBlank(msg)) {
            return OtherApiResponse.error(500, msg);
        }
        if ("1".equals(pushSurveyInfo.getSelfPick())) {
            if (StringUtils.isBlank(pushSurveyInfo.getWallboxCode())) {
                return OtherApiResponse.error(500, "缺少充电桩编码参数");
            } else if (StringUtils.isBlank(pushSurveyInfo.getSelfPickImage())) {
                return OtherApiResponse.error(500, "缺少自提桩申请单参数");
            } else if (StringUtils.isBlank(pushSurveyInfo.getPileSequenceImage())) {
                return OtherApiResponse.error(500, "缺少充电桩序列码照片参数");
            } else if (StringUtils.isBlank(pushSurveyInfo.getDrivingLicenseImage())) {
                return OtherApiResponse.error(500, "缺少行驶证/购车发票参数");
            }
        } else if ("0".equals(pushSurveyInfo.getSelfPick())) {
            if (StringUtils.isBlank(pushSurveyInfo.getEmeterRequestProgress())) {
                return OtherApiResponse.error(500, "缺少是否需要电力报装参数");
            } else if (StringUtils.isBlank(pushSurveyInfo.getSurveyResult())) {
                return OtherApiResponse.error(500, "缺少勘测结论参数");
            } /*else if (StringUtils.isBlank(pushSurveyInfo.getConfirmation())) {
                return OtherApiResponse.error(500, "缺少勘测确认书参数");
            }*/
        }
        // TODO: 业务调用
        createDefaultLoginUser();
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder()
                .pushSurveyInfo(pushSurveyInfo).build(), "pushSurveyInfoXk");
        return providerBusinessResult.getOtherApiResponse();
    }

    private void createDefaultLoginUser() {
        // 由于跳过登陆认证，需要设置固定的登陆信息
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setUserId(89L);
        sysUserEntity.setUsername("系统自动");
        // 创建一个Subject.Builder
        Subject.Builder builder = new Subject.Builder();
        // 设置身份信息
        PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, "系统自动");
        builder.principals(principals);
        // 设置是否已经认证
        builder.authenticated(true);
        // 创建Subject实例
        Subject subject = builder.buildSubject();
        ThreadContext.bind(subject);
    }
}
