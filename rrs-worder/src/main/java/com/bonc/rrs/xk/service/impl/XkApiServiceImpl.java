package com.bonc.rrs.xk.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.response.PageApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.byd.util.Sha256SignUtils;
import com.bonc.rrs.intf.service.IntfLogService;
import com.bonc.rrs.xk.service.XkApiService;
import com.bonc.rrs.xk.util.XkUrlApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 小咖对接Service接口
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class XkApiServiceImpl implements XkApiService {

    @Value("${xk.url}")
    private String urlPath;

    @Value("${xk.AppSecret}")
    private String appSecret;

    @Value("${xk.APP_KEY}")
    private String appKey;

    final IntfLogService intfLogService;



    /**
     * 封装请求头
     *
     * @param map
     * @return
     */
    private HttpHeaders packageParam(Map<String, Object> map) {
        String nonce = String.valueOf(System.currentTimeMillis());
        String curTime = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        //生成签名
        String sign = Sha256SignUtils.getSign(appSecret, nonce, curTime, map);
        //封装参数发送请求
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE.toString());
        headers.setContentType(type);
        headers.add("APP_KEY", appKey);
        headers.add("Nonce", nonce);
        headers.add("Cur_Time", curTime);
        headers.add("sign", sign);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        return headers;
    }

    /**
     * 小咖取消订单接口
     * @param pushCancelOrder 小咖取消订单接口
     * @return
     */
    @Override
    public PushApiResponse pushCancelOrder(PushCancelOrder pushCancelOrder) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushCancelOrder), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushCancelOrder), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.PUSH_CANCEL.getCode();
        log.info("小咖接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PushApiResponse> responseEntity = restTemplate.postForEntity(url, request, PushApiResponse.class);
        log.info("小咖接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.PUSH_CANCEL.getCode(), pushCancelOrder.getOrderCode(), url, XkUrlApi.PUSH_CANCEL.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * 小咖厂端审核后推送审核信息接口
     * @param pushSubmitInfo 小咖厂端审核后推送审核信息接口
     * @return
     */
    @Override
    public PushApiResponse pushSubmitInfo(PushSubmitInfo pushSubmitInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushSubmitInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushSubmitInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.PUSH_SUBMIT_INFO.getCode();
        log.info("小咖接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PushApiResponse> responseEntity = restTemplate.postForEntity(url, request, PushApiResponse.class);
        log.info("小咖接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.PUSH_SUBMIT_INFO.getCode(), pushSubmitInfo.getOrderCode(), url, XkUrlApi.PUSH_SUBMIT_INFO.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * 小咖关闭订单接口
     * @param pushCloseOrder 小咖关闭订单接口
     * @return
     */
    @Override
    public PushApiResponse pushCloseOrder(PushCloseOrder pushCloseOrder) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushCloseOrder), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushCloseOrder), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.PUSH_CLOSE.getCode();
        log.info("小咖接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PushApiResponse> responseEntity = restTemplate.postForEntity(url, request, PushApiResponse.class);
        log.info("小咖接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.PUSH_CLOSE.getCode(), pushCloseOrder.getOrderCode(), url, XkUrlApi.PUSH_CLOSE.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * 小咖结算订单接口
     * @param pushSettleInfo 小咖结算订单接口
     * @return
     */
    @Override
    public PushApiResponse pushSettle(PushSettleInfo pushSettleInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushSettleInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushSettleInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.PUSH_SETTLE.getCode();
        log.info("小咖接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PushApiResponse> responseEntity = restTemplate.postForEntity(url, request, PushApiResponse.class);
        log.info("小咖接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.PUSH_SETTLE.getCode(), pushSettleInfo.getOrderCode(), url, XkUrlApi.PUSH_SETTLE.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public PageApiResponse selectOrder(WorderParm worderParm) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(worderParm), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(worderParm), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.SELECT_WORDER.getCode();
        log.info("小咖安装单基本信息接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PageApiResponse> responseEntity = restTemplate.postForEntity(url, request, PageApiResponse.class);
        log.info("小咖安装单基本信息接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.SELECT_WORDER.getCode(), worderParm.getWorderNo(), url, XkUrlApi.SELECT_WORDER.getDesc(), 2, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }


    @Override
    public PushApiResponse pushSusPendOrder(PushSusPendOrder pushSusPendOrder) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushSusPendOrder), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushSusPendOrder), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.PUSH_SUSPEND.getCode();
        log.info("小咖接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PushApiResponse> responseEntity = restTemplate.postForEntity(url, request, PushApiResponse.class);
        log.info("小咖接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.PUSH_SUSPEND.getCode(), pushSusPendOrder.getOrderCode(), url, XkUrlApi.PUSH_SUSPEND.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public PushApiResponse pushRestoreOrder(PushRestoreOrder pushRestoreOrder) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushRestoreOrder), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushRestoreOrder), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.PUSH_RESTORE.getCode();
        log.info("小咖接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PushApiResponse> responseEntity = restTemplate.postForEntity(url, request, PushApiResponse.class);
        log.info("小咖接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.PUSH_RESTORE.getCode(), pushRestoreOrder.getOrderCode(), url, XkUrlApi.PUSH_RESTORE.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public PushApiResponse pushOrder(PushOrderBody pushOrderBody) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushOrderBody), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushOrderBody), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.PUSH_ORDER.getCode();
        log.info("小咖安装单基本信息接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PushApiResponse> responseEntity = restTemplate.postForEntity(url, request, PushApiResponse.class);
        log.info("小咖安装单基本信息接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.PUSH_ORDER.getCode(), pushOrderBody.getOrderCode(), url, XkUrlApi.PUSH_ORDER.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public PushApiResponse pushUpdateOrder(PushUpdateOrder pushUpdateOrder) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushUpdateOrder), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushUpdateOrder), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + XkUrlApi.PUSH_UPDATE.getCode();
        log.info("小咖安装单基本信息更新接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<PushApiResponse> responseEntity = restTemplate.postForEntity(url, request, PushApiResponse.class);
        log.info("小咖安装单基本信息更新接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(XkUrlApi.PUSH_UPDATE.getCode(), pushUpdateOrder.getOrderCode(), url, XkUrlApi.PUSH_UPDATE.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    public static void main(String[] args) {
        String a = "{\"data\":[{\"areaCode\":\"110114\",\"areaName\":\"昌平区\",\"bringWallbox\":\"1\",\"carBrand\":\"40\",\"carModel\":\"\",\"carOwnerType\":\"30\",\"cityCode\":\"110100\",\"cityName\":\"北京城区\",\"contactAddress\":\"北京市北京城区昌平区北七家镇测试\",\"contactMobile\":\"18301866249\",\"contactName\":\"杨颖子\",\"contactRemark\":\"\",\"dispatchTime\":\"2024-04-03 14:15:56\",\"id\":\"1775406866270121986\",\"orderCode\":\"7320240403427157003\",\"provinceCode\":\"110000\",\"provinceName\":\"北京市\",\"supplierCode\":\"BYD00265\",\"supplierId\":\"efed97fcaa0d31290bcd84ee991db313\",\"type\":\"30\",\"vin\":\"\",\"wallboxName\":\"比亚迪3.3kw充电桩\",\"wallboxPower\":\"3.3\"}]}";
//        PushOrderBody pushOrderBody = JSON.parseObject(a, PushOrderBody.class);
        JSONObject req = JSON.parseObject(a);
        JSONArray paramArray = req.getJSONArray("data");
        JSONArray tempArray =new JSONArray();
        for(int i=0;i<paramArray.size();i++){
            Map<String,Object> tempMap=JSON.parseObject(JSON.toJSONString(paramArray.get(i)),Map.class);
            tempArray.add(new TreeMap<>(tempMap));
        }
        Map<String, Object> params=new HashMap<>();
        params.put("data", tempArray);

        String nonce = String.valueOf(System.currentTimeMillis());
        String curTime = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        String appSecret = "k2hd83nsh4482jak261nsk3k382ha9g4";
        //生成签名
        String sign = Sha256SignUtils.getSign(appSecret, nonce, curTime, params);
        System.out.println("nonce==>" + nonce + ",Cur_Time==>" + curTime + ",Sign==>" + sign);
//
//        System.out.println();
//        String b = "{\"areaCode\": \"420529\", \"areaName\": \"五峰土家族自治县\", \"bringWallbox\": \"0\", \"carBrand\": \"40\", \"carModel\": \"BYD6470ST6HEV1尊荣型(国VI蓝灰内饰)1\", \"carOwnerType\": \"20\", \"carSeries\": \"宋PLUS DM-i1\", \"cityCode\": \"420500\", \"cityName\": \"宜昌市\", \"contactAddress\": \"测试1\", \"contactMobile\": \"18392159571\", \"contactName\": \"杨颖子1\", \"dispatchTime\": \"2024-03-11 15:00:00\", \"orderCode\": \"927060602600873984\", \"provinceCode\": \"420000\", \"provinceName\": \"湖北省\", \"type\": \"20\", \"vin\": \"LGXC74C46N0170821\", \"wallboxName\": \"cAAA1\", \"wallboxPower\": \"3\"}";
//        PushUpdateOrder pushUpdateOrder = JSON.parseObject(b, PushUpdateOrder.class);
//
//        Map<String, Object> mapb = JSON.parseObject(JSON.toJSONString(pushUpdateOrder), Map.class);
//        String signb = Sha256SignUtils.getSign(appSecret, nonce, curTime, mapb);
//        System.out.println("nonce==>" + nonce + ",Cur_Time==>" + curTime + ",Sign==>" + signb);


    }
}
