package com.bonc.rrs.auth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.bonc.rrs.auth.dto.ResultCode;
import com.bonc.rrs.auth.dto.ResultDTO;
import com.bonc.rrs.auth.dto.WorderCreateDTO;
import com.bonc.rrs.auth.dto.WorderQueryDTO;
import com.bonc.rrs.auth.service.ApiWorderService;
import com.bonc.rrs.spider.dto.OrderApiDto;
import com.bonc.rrs.spider.strategy.OrderContext;
import com.bonc.rrs.spider.strategy.OrderCreationStrategy;
import com.bonc.rrs.spider.strategy.OrderStrategyFactory;
import com.bonc.rrs.worder.dao.ExtFieldDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dto.dto.ExtFieldDictionaryDto;
import com.bonc.rrs.worder.entity.BizAttendantEntity;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderOperationRecord;
import com.bonc.rrs.worder.service.*;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.MagrAreaBrand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * API工单服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApiWorderServiceImpl implements ApiWorderService {

    @Autowired
    private WorderInformationService worderInformationService;

    @Autowired
    private BizAttendantService bizAttendantService;

    @Autowired
    private DotInformationService dotInformationService;

    @Autowired
    private WorderExtFieldService worderExtFieldService;

    @Autowired
    private SysUserDao sysUserDao;

    @Autowired
    private ExtFieldDao extFieldDao;

    @Autowired
    private OrderStrategyFactory strategyFactory;

    @Autowired
    private WorderOperationRecordService worderOperationRecordService;

    @Autowired
    private WorderInformationDao worderInformationDao;

    @Override
    public ResultDTO<Map<String, Object>> createWorder(WorderCreateDTO worderCreateDTO, String clientId) {
        try {
            // 记录客户端来源
            log.info("接收到来自客户端[{}]的创建工单请求: {}", clientId, worderCreateDTO);

            OrderApiDto dto = new OrderApiDto();
            BeanUtils.copyProperties(worderCreateDTO, dto);
            OrderCreationStrategy strategy = strategyFactory.getStrategy(dto.getTemplateId());

            WorderInformationEntity worder = worderInformationService.lambdaQuery()
                    .eq(WorderInformationEntity::getCompanyOrderNumber, dto.getCompanyOrderNo())
                    .ne(WorderInformationEntity::getWorderExecStatus, "21")
                    .eq(WorderInformationEntity::getIsDelete, "0")
                    .one();

            if (worder != null) {
                return ResultDTO.error(ResultCode.INTERNAL_ERROR, "工单已存在");
            }

            OrderContext context = strategy.createOrder(dto);

            Throwable error = context.getError();
            if (error != null) {
                log.error("创建工单失败", error);
                return ResultDTO.error(ResultCode.INTERNAL_ERROR, "创建工单失败: " + error.getMessage());

            }

            return ResultDTO.success(MapUtil.<String,Object>builder().put("worderNo", context.getWorderNo()).build());

        } catch (Exception e) {
            log.error("创建工单失败", e);
            return ResultDTO.error(ResultCode.INTERNAL_ERROR, "创建工单失败: " + e.getMessage());
        }
    }

    @Override
    public ResultDTO<List<Map<String, Object>>> getWorderList(WorderQueryDTO queryDTO, String clientId) {
        try {

            // 记录客户端来源
            log.info("接收到来自客户端[{}]的查询工单请求: {}", clientId, queryDTO);
            // 这里应该调用工单服务查询工单列表
            // 此处仅为示例
            List<Map<String, Object>> worderList = new ArrayList<>();

            return ResultDTO.success(worderList);
        } catch (Exception e) {
            log.error("查询工单列表失败", e);
            return ResultDTO.error(ResultCode.INTERNAL_ERROR, "查询工单列表失败: " + e.getMessage());
        }
    }

    @Override
    public ResultDTO<List<Map<String, String>>> getWorderDetailBatch(List<String> companyOrderNos, String clientId) {
        try {
            // 记录客户端来源
            log.info("接收到来自客户端[{}]的批量查询工单详情请求: {}", clientId, companyOrderNos);

            if (companyOrderNos == null || companyOrderNos.isEmpty()) {
                return ResultDTO.error(ResultCode.BAD_REQUEST, "订单号列表不能为空");
            }

            // 最大限制查询50条记录
            if (companyOrderNos.size() > 50) {
                return ResultDTO.error(ResultCode.BAD_REQUEST, "批量查询工单数量不能超过50条");
            }

            List<WorderInformationEntity> worders = worderInformationService.lambdaQuery()
                    .in(WorderInformationEntity::getCompanyOrderNumber, companyOrderNos)
                    .eq(WorderInformationEntity::getIsDelete, "0")
                    .list();

            if (worders == null || worders.isEmpty()) {
                return ResultDTO.error(ResultCode.NOT_FOUND, "未找到任何工单信息");
            }

            List<Map<Integer, String>> dotInfos = dotInformationService.selectByDotIds(worders.stream().map(WorderInformationEntity::getDotId).collect(Collectors.toList()));
            Collection<BizAttendantEntity> bizAttendants = bizAttendantService.listByIds(worders.stream().map(WorderInformationEntity::getServiceId).collect(Collectors.toList()));

            List<Map<String, String>> result = new ArrayList<>();
            for(WorderInformationEntity worderInformation : worders) {
                Map<String, String> item = new HashMap<>();
                result.add(item);
                // 查询工单编号
                String worderNo = worderInformation.getWorderNo();
                item.put("worderNo", worderNo);
                // 查询工单状态
                String status = worderInformation.getWorderStatus().toString();
                item.put("status", status);
                // 查询工单执行状态
                String execStatus = worderInformation.getWorderExecStatus().toString();
                item.put("execStatus", execStatus);
                // 客服
                Long createBy = worderInformation.getCreateBy();
                if (createBy != null) {
                    item.put("csName", worderInformationDao.getUserName(createBy));
                }
                // 预约勘测时间
                String conveyAppointTime = worderInformation.getConveyAppointTime();
                item.put("conveyAppointTime", conveyAppointTime);
                // 提交服务时间
                item.put("createTime", DateUtils.format(worderInformation.getCreateTime()));
                // 派单网点
                Integer dotId = worderInformation.getDotId();
                if (dotId != null) {
                    dotInfos.stream().filter(e -> e.containsValue(dotId))
                            .findFirst()
                            .ifPresent(e -> item.put("dotName", e.get("dot_name")));
                    // 派单网点派单时间
                    WorderOperationRecord record = worderOperationRecordService.lambdaQuery()
                            .select(WorderOperationRecord::getCreateTime)
                            .eq(WorderOperationRecord::getWorderNo, worderNo)
                            .eq(WorderOperationRecord::getType, 3)
                            .eq(WorderOperationRecord::getAffectedUserId, dotId)
                            .orderByDesc(WorderOperationRecord::getId)
                            .last("limit 1")
                            .one();

                    String issueSendTime = DateUtils.format(record.getCreateTime());
                    item.put("issueSendTime", issueSendTime);

                    //服务兵
                    Integer serviceId = worderInformation.getServiceId();
                    if (serviceId != null) {
                        bizAttendants.stream().filter(e -> e.getId().equals(serviceId))
                                .findFirst()
                                .ifPresent(e -> {
                                    item.put("serviceName", e.getName());
                                    item.put("servicePhone", e.getContact());
                                });
                    }
                }

                // 安装完成时间
                List<WorderExtFieldEntity> fields = worderExtFieldService.getFieldsByWorderNo(worderNo);
                //提取fields中field_id=1197的值
                String completeTime = fields.stream().filter(e -> e.getFieldId().equals(1197)).findFirst().map(WorderExtFieldEntity::getFieldValue).orElse("");
                item.put("completeTime", completeTime);
            }

            return ResultDTO.success(result);
        } catch (Exception e) {
            log.error("批量获取工单详情失败", e);
            return ResultDTO.error(ResultCode.INTERNAL_ERROR, "批量获取工单详情失败: " + e.getMessage());
        }
    }

    @Override
    public ResultDTO<Boolean> updateWorderStatus(String worderId, String status, String remark, String clientId) {
        try {
            // 记录客户端来源
            log.info("接收到来自客户端[{}]的更新工单请求: {}", clientId, worderId);
            // 这里应该调用工单服务更新工单状态
            // 此处仅为示例
            log.info("更新工单状态: worderId={}, status={}, remark={}, clientId={}", worderId, status, remark, clientId);
            // 假设更新成功
            return ResultDTO.success(true);
        } catch (Exception e) {
            log.error("更新工单状态失败", e);
            return ResultDTO.error(ResultCode.INTERNAL_ERROR, "更新工单状态失败: " + e.getMessage());
        }
    }

    @Override
    public ResultDTO<Boolean> cancelWorder(String worderId, String reason, String clientId) {
        try {
            // 记录客户端来源
            log.info("接收到来自客户端[{}]的取消工单请求: {}", clientId, worderId);
            // 这里应该调用工单服务取消工单
            // 此处仅为示例
            log.info("取消工单: worderId={}, reason={}, clientId={}", worderId, reason, clientId);
            // 假设取消成功
            return ResultDTO.success(true);
        } catch (Exception e) {
            log.error("取消工单失败", e);
            return ResultDTO.error(ResultCode.INTERNAL_ERROR, "取消工单失败: " + e.getMessage());
        }
    }

    @Override
    public ResultDTO<List<Map<String, Object>>> getWorderFlow(String companyOrderNo, String clientId) {
        try {
            // 记录客户端来源
            log.info("接收到来自客户端[{}]的查询工单流程请求: {}", clientId, companyOrderNo);
            List<WorderInformationEntity> worders = worderInformationService.lambdaQuery()
                    .select(WorderInformationEntity::getWorderNo, WorderInformationEntity::getCompanyOrderNumber, WorderInformationEntity::getWorderStatus, WorderInformationEntity::getWorderExecStatus)
                    .eq(WorderInformationEntity::getCompanyOrderNumber, companyOrderNo)
                    .eq(WorderInformationEntity::getIsDelete, "0")
                    .list();
            if (worders == null || worders.isEmpty()) {
                return ResultDTO.error(ResultCode.NOT_FOUND, "工单不存在");
            }

            List<Map<String, Object>> list = worders.stream().map(e ->
                            MapUtil.<String, Object>builder()
                                    .put("worderNo", e.getWorderNo())
                                    .put("companyOrderNo", e.getCompanyOrderNumber())
                                    .put("status", e.getWorderStatus())
                                    .put("execStatus", e.getWorderExecStatus())
                                    .build())
                    .collect(Collectors.toList());

            return ResultDTO.success(list);
        } catch (Exception e) {
            log.error("获取工单流程失败", e);
            return ResultDTO.error(ResultCode.INTERNAL_ERROR, "获取工单流程失败: " + e.getMessage());
        }
    }

    @Override
    public ResultDTO<List<Map<String, Object>>> getBrandList(String clientId) {
        try {

            // 记录客户端来源
            log.info("接收到来自客户端[{}]的查询品牌列表请求", clientId);

            List<MagrAreaBrand> brands = sysUserDao.listBrand();

            List<Map<String, Object>> list = brands.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());

            return ResultDTO.success(list);
        } catch (Exception e) {
            log.error("查询品牌列表失败", e);
            return ResultDTO.error(ResultCode.INTERNAL_ERROR, "查询品牌列表失败: " + e.getMessage());
        }
    }

    @Override
    public ResultDTO<List<Map<String, Object>>> getWorderStatusList(String clientId) {
        try {

            // 记录客户端来源
            log.info("接收到来自客户端[{}]的查询工单状态列表请求", clientId);

            List<ExtFieldDictionaryDto> worderExecStatusList = extFieldDao.findByDicNumber("worder_exec_status");

            List<Map<String, Object>> list = worderExecStatusList.stream().map(e ->
                    MapUtil.<String, Object>builder()
                            .put("code", e.getDetailNumber())
                            .put("name", e.getDetailName())
                            .build())
                    .collect(Collectors.toList());

            return ResultDTO.success(list);
        } catch (Exception e) {
            log.error("查询工单状态列表失败", e);
            return ResultDTO.error(ResultCode.INTERNAL_ERROR, "查询工单状态列表失败: " + e.getMessage());
        }
    }
}
