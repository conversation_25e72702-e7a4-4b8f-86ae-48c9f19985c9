package com.bonc.rrs.auth.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.Map;

/**
 * JWT工具类
 * <AUTHOR>
 */
public class JwtUtils {

    /**
     * 生成JWT令牌
     *
     * @param clientId 客户端ID
     * @param secret   密钥
     * @param expire   过期时间(小时)
     * @return JWT令牌
     */
    public static String generateToken(String clientId, String secret, long expire) {
        Date nowDate = new Date();
        // 过期时间
        Date expireDate = new Date(nowDate.getTime() + expire * 3600 * 1000);

        return Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setSubject(clientId)
                .setIssuedAt(nowDate)
                .setExpiration(expireDate)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 验证JWT令牌
     *
     * @param token   JWT令牌
     * @param clients 客户端密钥映射
     * @return 客户端ID
     */
    public static String validateToken(String token, Map<String, String> clients) {
        try {
            // 先解析获取clientId
            String clientId = getClientIdFromToken(token);
            if (StringUtils.isEmpty(clientId) || !clients.containsKey(clientId)) {
                return null;
            }

            // 获取密钥
            String secret = clients.get(clientId);

            // 验证token
            Claims claims = Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(token)
                    .getBody();

            // 验证subject是否匹配
            String subject = claims.getSubject();
            if (!clientId.equals(subject)) {
                return null;
            }

            return clientId;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从令牌中获取客户端ID
     *
     * @param token JWT令牌
     * @return 客户端ID
     */
    public static String getClientIdFromToken(String token) {
        try {
            // 不验证签名，只获取payload部分
            Claims claims = Jwts.parser()
                    .parseClaimsJwt(token.substring(0, token.lastIndexOf('.') + 1))
                    .getBody();
            return claims.getSubject();
        } catch (Exception e) {
            return null;
        }
    }
}
