package com.bonc.rrs.auth.service;

import com.bonc.rrs.auth.dto.ResultDTO;
import com.bonc.rrs.auth.dto.WorderCreateDTO;
import com.bonc.rrs.auth.dto.WorderQueryDTO;

import java.util.List;
import java.util.Map;

/**
 * API工单服务接口
 * <AUTHOR>
 */
public interface ApiWorderService {

    /**
     * 创建工单
     *
     * @param worderCreateDTO 工单创建DTO
     * @param clientId 客户端ID
     * @return 创建结果
     */
    ResultDTO<Map<String, Object>> createWorder(WorderCreateDTO worderCreateDTO, String clientId);

    /**
     * 查询工单列表
     *
     * @param queryDTO 查询条件
     * @param clientId 客户端ID
     * @return 工单列表
     */
    ResultDTO<List<Map<String, Object>>> getWorderList(WorderQueryDTO queryDTO, String clientId);

    /**
     * 批量获取工单详情
     *
     * @param worderIds 工单ID列表
     * @param clientId 客户端ID
     * @return 工单详情列表
     */
    ResultDTO<List<Map<String, String>>> getWorderDetailBatch(List<String> worderIds, String clientId);

    /**
     * 更新工单状态
     *
     * @param worderId 工单ID
     * @param status 工单状态
     * @param remark 备注
     * @param clientId 客户端ID
     * @return 更新结果
     */
    ResultDTO<Boolean> updateWorderStatus(String worderId, String status, String remark, String clientId);

    /**
     * 取消工单
     *
     * @param worderId 工单ID
     * @param reason 取消原因
     * @param clientId 客户端ID
     * @return 取消结果
     */
    ResultDTO<Boolean> cancelWorder(String worderId, String reason, String clientId);

    /**
     * 获取工单流程
     *
     * @param worderId 工单ID
     * @param clientId 客户端ID
     * @return 工单流程
     */
    ResultDTO<List<Map<String, Object>>> getWorderFlow(String worderId, String clientId);

    /**
     * 获取品牌列表
     *
     * @param clientId 客户端ID
     * @return 品牌列表
     */
    ResultDTO<List<Map<String, Object>>> getBrandList(String clientId);

    /**
     * 获取工单状态列表
     * @param clientId
     * @return
     */
    ResultDTO<List<Map<String, Object>>> getWorderStatusList(String clientId);
}
