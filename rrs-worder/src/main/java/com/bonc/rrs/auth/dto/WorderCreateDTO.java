package com.bonc.rrs.auth.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工单创建DTO
 * <AUTHOR>
 */
@Data
public class WorderCreateDTO implements java.io.Serializable{
    /**
     * 厂商订单id
     */
    @ApiModelProperty(value = "车企订单id")
    private String companyOrderId;

    /**
     * 厂商页面显示的服务工单号，用于对照查询
     */
    @ApiModelProperty(value = "车企订单号", required = true)
    @NotBlank(message = "车企订单号不能为空")
    private String companyOrderNo;

    /**
     * 车企id
     */
    @ApiModelProperty(value = "车企id", required = true)
    @NotNull(message = "车企id不能为空")
    @Min(value = 1, message = "车企id不能为空")
    private Integer companyId;

    /**
     * 模板编号
     */
    @ApiModelProperty(value = "模板编号", required = true)
    @NotNull(message = "模板编号不能为空")
    @Min(value = 1, message = "模板编号不能为空")
    private Integer templateId;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名", required = true)
    @NotBlank(message = "客户姓名不能为空")
    private String custName;
    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话", required = true)
    @NotBlank(message = "客户电话不能为空")
    private String custPhone;

    /**
     * 客户邮箱
     */
    @ApiModelProperty(value = "客户邮箱")
    private String postCode;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称", required = true)
//    @NotBlank(message = "省份名称不能为空")
    private String provinceName;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称", required = true)
//    @NotBlank(message = "城市名称不能为空")
    private String cityName;

    /**
     * 区域代码
     */
    @ApiModelProperty(value = "区域代码")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
//    @NotBlank(message = "详细地址不能为空")
    private String detailAddress;

    /**
     * 车辆VIN码
     */
    @ApiModelProperty(value = "车辆VIN码")
    private String vinNo;

    /**
     * 车辆品牌
     */
    @ApiModelProperty(value = "车辆品牌")
    private String carBrand;

    /**
     * 工单类型
     * 2--安装，5--勘安，6--维修
     */
    @ApiModelProperty(value = "工单类型", required = true)
//    @NotBlank(message = "工单类型不能为空")
    private String worderType = "5";

    /**
     * 工单级别
     */
    @ApiModelProperty(value = "工单级别")
    private String worderLevel = "A";

    /**
     * 车企派单日期
     * 154
     */
    @ApiModelProperty(value = "车企派单日期")
    private String dispatchDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否自动派单
     */
    @ApiModelProperty(value = "是否自动派单0-自动派单，1-不自动派单")
    private Integer autoDispatch = 0;

    // 存储子类特有属性
    @ApiModelProperty(value = "扩展字段（动态属性）")
    private Map<String,Object> extFields = new LinkedHashMap<>();
}
