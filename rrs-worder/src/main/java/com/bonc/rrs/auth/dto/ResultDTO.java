package com.bonc.rrs.auth.dto;

import lombok.Data;

/**
 * API响应结果
 * <AUTHOR>
 */
@Data
public class ResultDTO<T> {
    /**
     * 状态码
     */
    private int code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 成功响应
     */
    public static <T> ResultDTO<T> success(T data) {
        ResultDTO<T> result = new ResultDTO<>();
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setMessage(ResultCode.SUCCESS.getMessage());
        result.setData(data);
        return result;
    }

    /**
     * 成功响应（无数据）
     */
    public static <T> ResultDTO<T> success() {
        return success(null);
    }

    /**
     * 错误响应
     */
    public static <T> ResultDTO<T> error(ResultCode resultCode, String message) {
        ResultDTO<T> result = new ResultDTO<>();
        result.setCode(resultCode.getCode());
        result.setMessage(message);
        return result;
    }

    /**
     * 错误响应（使用默认错误消息）
     */
    public static <T> ResultDTO<T> error(ResultCode resultCode) {
        return error(resultCode, resultCode.getMessage());
    }
}
