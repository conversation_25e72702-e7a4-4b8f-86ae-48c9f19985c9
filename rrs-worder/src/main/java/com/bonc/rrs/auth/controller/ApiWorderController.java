package com.bonc.rrs.auth.controller;

import com.bonc.rrs.auth.dto.ResultDTO;
import com.bonc.rrs.auth.dto.WorderCreateDTO;
import com.bonc.rrs.auth.service.ApiWorderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 工单API对接控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dmj/api/v1/worder")
public class ApiWorderController {

    @Autowired
    private ApiWorderService apiWorderService;

    /**
     * 创建工单
     */
    @PostMapping("/create")
    public ResultDTO<Map<String, Object>> createWorder(@RequestBody WorderCreateDTO worderCreateDTO, HttpServletRequest request) {
        // 获取客户端ID
        String clientId = (String) request.getAttribute("clientId");
        return apiWorderService.createWorder(worderCreateDTO, clientId);
    }

    /**
     * 获取工单状态
     */
    @GetMapping("/flow/{companyOrderNo}")
    public ResultDTO<List<Map<String, Object>>> getWorderFlow(@PathVariable String companyOrderNo, HttpServletRequest request) {
        // 获取客户端ID
        String clientId = (String) request.getAttribute("clientId");
        return apiWorderService.getWorderFlow(companyOrderNo, clientId);
    }

    /**
     * 获取工单详细信息
     */
    @GetMapping("/detail/{companyOrderNo}")
    public ResultDTO<List<Map<String, String>>> getWorderDetail(@PathVariable String companyOrderNo, HttpServletRequest request) {
        // 获取客户端ID
        String clientId = (String) request.getAttribute("clientId");
        return apiWorderService.getWorderDetailBatch(Collections.singletonList(companyOrderNo), clientId);
    }

    /**
     * 批量获取工单详细信息
     */
    @PostMapping("/detail/batch")
    public ResultDTO<List<Map<String, String>>> getWorderDetailBatch(@RequestBody List<String> companyOrderNos, HttpServletRequest request) {
        // 获取客户端ID
        String clientId = (String) request.getAttribute("clientId");
        return apiWorderService.getWorderDetailBatch(companyOrderNos, clientId);
    }

    /**
     * 获取品牌列表
     */
    @GetMapping("/brand/list")
    public ResultDTO<List<Map<String, Object>>> getWorderDetail(HttpServletRequest request) {
        // 获取客户端ID
        String clientId = (String) request.getAttribute("clientId");
        return apiWorderService.getBrandList(clientId);
    }


    /**
     * 获取工单状态列表
     */
    @GetMapping("/status/list")
    public ResultDTO<List<Map<String, Object>>> getWorderStatus(HttpServletRequest request) {
        // 获取客户端ID
        String clientId = (String) request.getAttribute("clientId");
        return apiWorderService.getWorderStatusList(clientId);
    }
}
