package com.bonc.rrs.auth.controller;

import com.bonc.rrs.auth.config.AuthSecurityConfig;
import com.bonc.rrs.auth.dto.ApiTokenDTO;
import com.bonc.rrs.auth.dto.ResultCode;
import com.bonc.rrs.auth.dto.ResultDTO;
import com.bonc.rrs.auth.utils.JwtUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * API认证控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dmj/api/auth")
public class ApiAuthController {

    @Autowired
    private AuthSecurityConfig securityConfig;

    /**
     * 获取访问令牌
     */
    @PostMapping("/token")
    public ResultDTO<ApiTokenDTO> getToken(@RequestParam String clientId, @RequestParam String clientSecret) {
        // 验证客户端ID和密钥
        if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret)) {
            return ResultDTO.error(ResultCode.BAD_REQUEST, "客户端ID和密钥不能为空");
        }

        // 验证客户端是否存在
        if (!securityConfig.getClients().containsKey(clientId)) {
            return ResultDTO.error(ResultCode.UNAUTHORIZED, "无效的客户端ID");
        }

        // 验证密钥是否正确
        String secret = securityConfig.getClients().get(clientId);
        if (!clientSecret.equals(secret)) {
            return ResultDTO.error(ResultCode.UNAUTHORIZED, "客户端密钥不正确");
        }

        // 生成JWT令牌
        String token = JwtUtils.generateToken(clientId, secret, securityConfig.getTokenExpire());

        // 返回令牌和过期时间
        ApiTokenDTO tokenDTO = new ApiTokenDTO();
        tokenDTO.setAccessToken(token);
        tokenDTO.setExpiresIn(securityConfig.getTokenExpire() * 3600);
        tokenDTO.setTokenType("Bearer");

        return ResultDTO.success(tokenDTO);
    }

    /**
     * 创建客户端（管理接口，应添加额外安全措施）
     */
    /*@PostMapping("/client")
    public ResultDTO<Map<String, String>> createClient(@RequestParam String clientName) {
        if (StringUtils.isEmpty(clientName)) {
            return ResultDTO.error(ResultCode.BAD_REQUEST, "客户端名称不能为空");
        }

        // 生成客户端ID和密钥
        String clientId = UUID.randomUUID().toString().replace("-", "");
        String clientSecret = UUID.randomUUID().toString().replace("-", "");

        // 保存客户端信息
        securityConfig.getClients().put(clientId, clientSecret);

        // 返回客户端信息
        Map<String, String> clientInfo = new HashMap<>();
        clientInfo.put("clientId", clientId);
        clientInfo.put("clientSecret", clientSecret);
        clientInfo.put("clientName", clientName);

        return ResultDTO.success(clientInfo);
    }*/

    /**
     * 删除客户端（管理接口，应添加额外安全措施）
     */
    /*@DeleteMapping("/client/{clientId}")
    public ResultDTO<Void> deleteClient(@PathVariable String clientId) {
        if (StringUtils.isEmpty(clientId)) {
            return ResultDTO.error(ResultCode.BAD_REQUEST, "客户端ID不能为空");
        }

        // 检查客户端是否存在
        if (!securityConfig.getClients().containsKey(clientId)) {
            return ResultDTO.error(ResultCode.NOT_FOUND, "客户端不存在");
        }

        // 删除客户端
        securityConfig.getClients().remove(clientId);

        return ResultDTO.success();
    }*/

    /**
     * 获取所有客户端（管理接口，应添加额外安全措施）
     */
    @GetMapping("/clients")
    public ResultDTO<Map<String, String>> getAllClients() {
        Map<String, String> clientIds = new HashMap<>();

        // 出于安全考虑，不返回密钥，只返回客户端ID列表
        for (String clientId : securityConfig.getClients().keySet()) {
            clientIds.put(clientId, "********");
        }

        return ResultDTO.success(clientIds);
    }
}
