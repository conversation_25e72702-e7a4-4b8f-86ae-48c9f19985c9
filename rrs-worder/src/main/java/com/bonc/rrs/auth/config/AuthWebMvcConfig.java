package com.bonc.rrs.auth.config;

import com.bonc.rrs.auth.interceptor.ApiAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类
 * <AUTHOR>
 */
@Configuration
public class AuthWebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private ApiAuthInterceptor apiAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册API鉴权拦截器，只拦截/api/v1/**开头的请求
        registry.addInterceptor(apiAuthInterceptor)
                .addPathPatterns("/dmj/api/v1/**");
    }
}
