package com.bonc.rrs.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对接接口安全配置
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "api.security")
public class AuthSecurityConfig {

    /**
     * 是否启用接口安全认证
     */
    private boolean enabled = true;

    /**
     * 客户端密钥过期时间（小时）
     */
    private long tokenExpire = 24;

    /**
     * 客户端密钥Map，key为客户端ID，value为密钥
     */
    private Map<String, String> clients = new ConcurrentHashMap<>();
}
