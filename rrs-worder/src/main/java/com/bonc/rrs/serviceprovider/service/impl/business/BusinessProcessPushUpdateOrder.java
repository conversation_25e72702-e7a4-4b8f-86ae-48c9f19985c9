package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushUpdateOrder;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.BusinessProcessWorderExtFieldPo;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.dao.WorderExtFieldDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.bonc.rrs.xk.service.XkApiService;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 服务商业务-安装订单信息更新推送服务商服务商接口
 * @Date 2024/2/26 18:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessProcessPushUpdateOrder extends AbstractPushOrder {

    final BrandService brandService;

    final WorderInformationDao worderInformationDao;

    final SysDictionaryDetailService sysDictionaryDetailService;

    final WorderExtFieldDao worderExtFieldDao;

    final WorderExtFieldService worderExtFieldService;

    final WorderRemarkLogService worderRemarkLogService;

    final XkApiService xkApiService;

    @Override
    public String getProcessCode() {
        return "pushUpdateOrder";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        PushUpdateOrder pushUpdateOrder = businessProcessPo.getPushUpdateOrder();

        R r = null;
        try {
            if (StringUtils.isBlank(pushUpdateOrder.getOrderCode())) {
                return Result.pushApiResponseMessage("安装订单编号不能为空");
            }

            WorderInformationEntity worderInformationEntity = getWorderInformationByCompanyOrderNumber(pushUpdateOrder.getOrderCode());

            if (worderInformationEntity == null) {
                return Result.pushApiResponseMessage("非法安装订单编号");
            }

            BrandEntity brandEntity = brandService.queryBrandByCompanyBrandId(pushUpdateOrder.getCarBrand());
            if (brandEntity == null) {
                return Result.pushApiResponseMessage("非法品牌");
            }

            RegionCode regionCode = convertRegion(pushUpdateOrder.getProvinceCode(), pushUpdateOrder.getCityCode(), pushUpdateOrder.getAreaCode());

            if (regionCode.getProvinceCode() == null || regionCode.getCityCode() == null || regionCode.getAreaCode() == null) {
                return Result.pushApiResponseMessage("地区不匹配");
            }

            WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

            String address = regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + pushUpdateOrder.getContactAddress();

            String bringWallbox = ConstantPool.BringWallbox.getNameByCode(pushUpdateOrder.getBringWallbox());

            String financialAttributionName = ConstantPool.FinancialAttribution.getNameByCode(pushUpdateOrder.getType());;
            String carOwnerTypeName = ConstantPool.CarOwnerType.getNameByCode(pushUpdateOrder.getCarOwnerType());

            List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();
            List<WorderExtFieldEntity> installWorderExtFieldEntityList = new ArrayList<>();

            List<BusinessProcessWorderExtFieldPo> worderExtFieldPoList = worderExtFieldDao.queryBusinessProcessFieldsByWorderNo(worderInformationEntity.getWorderNo());
            Map<Integer, BusinessProcessWorderExtFieldPo> worderExtFieldPoMap = worderExtFieldPoList.stream().collect(Collectors.toMap(BusinessProcessWorderExtFieldPo::getFieldId, Function.identity()));
            worderInfoEntity.setUpdateBy(ConstantPool.NEWS_OPERATOR);
            worderInfoEntity.setWorderId(worderInformationEntity.getWorderId());

            if (worderInformationEntity.getAreaId() != regionCode.getAreaCode().intValue()) {
                worderInfoEntity.setAreaId(regionCode.getAreaCode().intValue());
            }

            if (!address.equals(worderInformationEntity.getAddress())) {
                worderInfoEntity.setAddress(address);
                worderInformationService.handleRedundant(worderInfoEntity);
                worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", address));
            }

            String contactName = pushUpdateOrder.getContactName();
            String currentUserName = worderInfoEntity.getUserName();
            String newUserName;

            if (contactName != null) {
                newUserName = contactName;
                if (org.apache.commons.lang3.StringUtils.equals(bringWallbox, "否")) {
                    newUserName += "-不带桩";
                }
            } else {
                if (currentUserName != null && currentUserName.endsWith("-不带桩") &&
                        !org.apache.commons.lang3.StringUtils.equals(bringWallbox, "否")) {
                    newUserName = currentUserName.substring(0, currentUserName.length() - 4);
                } else {
                    newUserName = currentUserName;
                }
            }

            if (!Objects.equals(newUserName, currentUserName)) {
                worderInfoEntity.setUserName(newUserName);
                worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", newUserName));
            }

            if (pushUpdateOrder.getContactMobile() != null && !pushUpdateOrder.getContactMobile().equals(worderInformationEntity.getUserPhone())) {
                worderInfoEntity.setUserPhone(pushUpdateOrder.getContactMobile());
                worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderInfoEntity.getUserPhone()));
            }

            if (StringUtils.isNotBlank(pushUpdateOrder.getDispatchTime())) {
                if (worderExtFieldPoMap.containsKey(154)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", pushUpdateOrder.getDispatchTime()));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", pushUpdateOrder.getDispatchTime()));
                }
            }
            if (StringUtils.isNotBlank(pushUpdateOrder.getVin())) {
                if (worderExtFieldPoMap.containsKey(153)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(153, "VIN 车架号", pushUpdateOrder.getVin()));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(153, "VIN 车架号", pushUpdateOrder.getVin()));
                }
            }

            if (StringUtils.isNotBlank(pushUpdateOrder.getContactRemark())) {
                if (worderExtFieldPoMap.containsKey(305)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(305, "备注-创建", pushUpdateOrder.getContactRemark()));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(305, "备注-创建", pushUpdateOrder.getContactRemark()));
                }
            }

            if (StringUtils.isNotBlank(pushUpdateOrder.getWallboxName())) {
                if (worderExtFieldPoMap.containsKey(1676)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(1676, "充电桩名称", pushUpdateOrder.getWallboxName()));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(1676, "充电桩名称", pushUpdateOrder.getWallboxName()));
                }
            }

            if (StringUtils.isNotBlank(bringWallbox)) {
                if (worderExtFieldPoMap.containsKey(1678)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(1678, "是否带桩上门", bringWallbox));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(1678, "是否带桩上门", bringWallbox));
                }
            }

            if (StringUtils.isNotBlank(carOwnerTypeName)) {
                if (worderExtFieldPoMap.containsKey(1679)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(1679, "订单类型（选）", carOwnerTypeName));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(1679, "订单类型（选）", carOwnerTypeName));
                }
            }

            if (StringUtils.isNotBlank(financialAttributionName)) {
                if (worderExtFieldPoMap.containsKey(1680)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(1680, "财务归属", financialAttributionName));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(1680, "财务归属", financialAttributionName));
                }
            }

            if (StringUtils.isNotBlank(pushUpdateOrder.getCarSeries())) {
                if (worderExtFieldPoMap.containsKey(1684)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(1684, "比亚迪车系", pushUpdateOrder.getCarSeries()));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(1684, "比亚迪车系", pushUpdateOrder.getCarSeries()));
                }
            }

            if (StringUtils.isNotBlank(pushUpdateOrder.getCarModel())) {
                if (worderExtFieldPoMap.containsKey(1686)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(1686, "比亚迪车型", pushUpdateOrder.getCarModel()));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(1686, "比亚迪车型", pushUpdateOrder.getCarModel()));
                }
            }

            if (StringUtils.isNotBlank(pushUpdateOrder.getWallboxPower())) {
                if (worderExtFieldPoMap.containsKey(1694)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(1694, "充电桩功率", pushUpdateOrder.getWallboxPower()));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(1694, "充电桩功率", pushUpdateOrder.getWallboxPower()));
                }
            }

            if (StringUtils.isNotBlank(pushUpdateOrder.getGunLineLength())) {
                if (worderExtFieldPoMap.containsKey(1667)) {
                    worderExtFieldEntityList.add(setWorderExtFieldEntity(1667, "枪线长度", pushUpdateOrder.getGunLineLength()));
                } else {
                    installWorderExtFieldEntityList.add(setWorderExtFieldEntity(1667, "枪线长度", pushUpdateOrder.getGunLineLength()));
                }
            }

            worderInformationDao.updateWorderInfo(worderInfoEntity);

            if (!CollectionUtils.isEmpty(worderExtFieldEntityList)) {
                worderExtFieldEntityList.forEach(worderExtFieldEntity -> {
                    worderExtFieldEntity.setWorderNo(worderInformationEntity.getWorderNo());
                    worderInformationDao.updateWorderExtField(worderExtFieldEntity);
                });
            }

            if (!CollectionUtils.isEmpty(installWorderExtFieldEntityList)) {
                installWorderExtFieldEntityList.forEach(worderExtFieldEntity -> {
                    worderExtFieldEntity.setWorderNo(worderInformationEntity.getWorderNo());
                    worderInformationDao.updateWorderExtField(worderExtFieldEntity);
                });
                List<WorderExtFieldEntity> installWorderExtFieldEntityListTemp;
                installWorderExtFieldEntityListTemp = installWorderExtFieldEntityList.stream()
                        .map(worderExtFieldEntity -> {
                            worderExtFieldEntity.setWorderNo(worderInformationEntity.getWorderNo());
                            worderExtFieldEntity.setCreateTime(new Date());
                            return worderExtFieldEntity;
                        })
                        .collect(Collectors.toList());
                //添加扩展字段
                worderExtFieldService.saveBatch(installWorderExtFieldEntityListTemp);
            }

            SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
            String title = formatDate.format(new Date()) + " " + ConstantPool.NEWS_OPERATOR_NAME + "更新工单信息";
            saveWorderRemarkLog(worderInformationEntity.getWorderNo(), "安装订单信息更新推送服务商接口更新工单信息", title);

            // 判断是否转单
            if (worderInformationService.checkTransferOrder(worderInformationEntity.getWorderNo())) {
                // 推送小咖
                PushApiResponse xkResponse = xkApiService.pushUpdateOrder(pushUpdateOrder);
                title = formatDate.format(new Date()) + " " + ConstantPool.NEWS_OPERATOR_NAME + "推送小咖更新订单";
                if (ConstantPool.SUCCESS.equals(xkResponse.getMessage())) {
                    saveWorderRemarkLog(worderInformationEntity.getWorderNo(), "安装订单信息更新推送服务商服务商接口推送小咖完成", title);
                } else {
                    saveWorderRemarkLog(worderInformationEntity.getWorderNo(), "安装订单信息更新推送服务商服务商接口推送小咖完成:" + xkResponse.getMessage(), title);
                }
            }

        } catch (Exception e) {
            log.error("安装订单信息更新推送服务商接口下单出现异常", e);
            return Result.pushApiResponseMessage("安装订单信息更新推送服务商接口下单失败");
        }

        return Result.pushApiResponseSuccess();
    }

}
