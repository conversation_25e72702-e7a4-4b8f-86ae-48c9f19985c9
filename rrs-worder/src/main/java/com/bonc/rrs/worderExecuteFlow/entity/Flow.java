package com.bonc.rrs.worderExecuteFlow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("flow")
@ApiModel(value = "流程表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Flow implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流程ID")
    @TableId(value = "flow_id", type = IdType.AUTO)
    private String flowId;

    @ApiModelProperty(value = "流程名称", required = false)
    private String name;

    @ApiModelProperty(value = "流程编码", required = false)
    private String sp;

    @ApiModelProperty(value = "流程字段", required = false)
    private String defaultFlowChildCode;

    @ApiModelProperty(value = "流程内容json", required = false)
    private String flowContent;

    @ApiModelProperty(value = "删除状态;0：未删除 1：已删除", required = false)
    private String isDelete;

    @ApiModelProperty(value = "插入时间", required = false)
    private Date insertTime;

    @ApiModelProperty(value = "更新时间", required = false)
    private Date updateTime;

    @ApiModelProperty(value = "操作人", required = false)
    private String user_id;

    @ApiModelProperty(value = "操作人名称", required = false)
    private String user_name;


}