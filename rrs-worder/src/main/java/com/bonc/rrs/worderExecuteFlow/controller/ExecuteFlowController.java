package com.bonc.rrs.worderExecuteFlow.controller;

import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.flow.FlowUtil;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.youngking.lenmoncore.common.utils.MapUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller
@RequestMapping(value = "/worder/execute")
public class ExecuteFlowController {

    @Autowired(required = false)
    private WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    private WorkMsgDao workMsgDao;
    @Autowired(required = false)
    private FlowUtil flowUtil;

    /*
     * 调用流程接口
     * */
    @RequestMapping(value = "flow", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results selectEntity(@RequestParam(value = "username") String username,
                                @RequestParam(value = "worderno") String worderno,
                                @RequestParam(value = "processState") String processState
    ) {
        String result = null;
        log.info(".......调用流程接口开始.............");
        long statrTime = System.currentTimeMillis();
        log.info(".......调用流程接口请求:" + worderno);
        Map<String, String> resp = new HashMap<String, String>();
        SysUserEntity sysUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        try {
            if (!StringUtils.isEmpty(username) && !StringUtils.isEmpty(worderno) && !StringUtils.isEmpty(processState)) {

                MapUtils params = new MapUtils().put("worder_no", worderno);
                List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectByMap(params);
                if (worderInformationEntities.size() > 0 && worderInformationEntities.get(0) != null) {

                    ExecuteFlowResultPo runFlow = flowUtil.runFlow(worderInformationEntities.get(0), sysUser, processState, null);
                    if (runFlow.getCode() != null && "0".equals(runFlow.getCode())) {
                        return Results.message(0, "流程调用成功", null);
                    } else {
                        return Results.message(3, "流程调用失败", null);
                    }
                } else {
                    return Results.message(201, "工单号[" + worderno + "]未查询到工单信息！", null);
                }

            } else {
                return Results.message(100, "参数不能为空", null);
            }
        } catch (Exception e) {
            return Results.message(110, "接口异常", null);
        }

    }


}
