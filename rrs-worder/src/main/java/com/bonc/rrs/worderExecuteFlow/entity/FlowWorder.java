package com.bonc.rrs.worderExecuteFlow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("flow_worder")
@ApiModel(value = "工单流程表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlowWorder implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流程ID")
    @TableId(value = "flow_worder_id", type = IdType.AUTO)
    private String flowWorderId;

    @ApiModelProperty(value = "场景id", required = false)
    private String flowId;

    @ApiModelProperty(value = "工单id", required = false)
    private String worderId;

    @ApiModelProperty(value = "下一个流程节点编码", required = false)
    private String nextFlowChildCode;

    @ApiModelProperty(value = "删除状态;0：未删除 1：已删除", required = false)
    private String isDelete;


    @ApiModelProperty(value = "插入时间", required = false)
    private Date insertTime;




}