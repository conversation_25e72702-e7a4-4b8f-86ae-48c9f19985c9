package com.bonc.rrs.worderExecuteFlow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@TableName("flow_worder_record")
@ApiModel(value = "流程所走状态表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlowWorderRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "flow_worder_record", type = IdType.AUTO)
    private String flowWorderRecord;

    @ApiModelProperty(value = "工单id", required = false)
    private Integer worderId;

    @ApiModelProperty(value = "场景id", required = false)
    private String flowId;

    @ApiModelProperty(value = "流程节点编码", required = false)
    private String flowChildCode;

    @ApiModelProperty(value = "结果-流程节点编码", required = false)
    private String nextFlowChildCode;

    @ApiModelProperty(value = "执行流程执行码", required = false)
    private String processCode;

    @ApiModelProperty(value = "执行流程状态", required = false)
    private String processStatus;

    @ApiModelProperty(value = "旧工单状态", required = false)
    private Integer oldWorderStatus;

    @ApiModelProperty(value = "旧工单状态", required = false)
    private Integer worderStatus;

    @ApiModelProperty(value = "旧工单执行状态", required = false)
    private Integer oldWorderExecStatus;

    @ApiModelProperty(value = "工单执行状态", required = false)
    private Integer worderExecStatus;

    @ApiModelProperty(value = "插入时间", required = false)
    private String insertTime;

    @ApiModelProperty(value = "用户", required = false)
    private Long userId;

    @ApiModelProperty(value = "结果编码", required = false)
    private String code;

    @ApiModelProperty(value = "结果编码", required = false)
    private String msg;


}