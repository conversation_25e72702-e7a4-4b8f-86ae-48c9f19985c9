package com.bonc.rrs.sparesettlement.controller;

import com.bonc.rrs.sparesettlement.model.request.TicketRequestDTO;
import com.bonc.rrs.sparesettlement.service.SpareSettlementService;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/spareSettlement")
@Api(tags = "发票接口")
public class SpareSettlementController extends AbstractController {

    @Autowired
    private SpareSettlementService spareSettlementService;
    /**
     *  订单api 开发票
     * */
   @PostMapping("/writeAnInvoice")
   @ApiOperation(value = "开发票")
   public R writeAnInvoice(@Validated @RequestBody TicketRequestDTO ticketRequestDTO, BindingResult bindingResult ){
       return  spareSettlementService.writeAnInvoice( ticketRequestDTO,  bindingResult,getUser());
   }
}
