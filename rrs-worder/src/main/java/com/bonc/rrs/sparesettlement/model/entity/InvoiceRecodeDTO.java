package com.bonc.rrs.sparesettlement.model.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Data
public class InvoiceRecodeDTO {
    /** 主键id */
    private Integer invoiceId;
    /** 发票编号 */
    private String invoiceNo;
    /** 操作流水号 */
    private String serialNo;
    /** 发票总金额 */
    private BigDecimal invoiceAmount;
    /** 开票人 */
    private String drawer;
    /** 开票角色 （1: 个人 2:公司） */
    private Integer invoiceType;
    /** 公司名称 */
    private String invoiceCompanyName;
    /** 纳税人识别号 */
    private String taxpayerCode;
    /** 公司地址 */
    private String  companyAddr;
    /** 开票状态（ 0:未知 1:未开票 2：开票中 3:开票成功 4：开票失败 5：已作废） */
    private Integer invoiceStatus;
    /** 支付方式 1:支付宝 2:微信 */
    private String payType;
    /** 支付流水 */
    private String payOrderNo;
    /** 记录创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    /** 备注 */
    private String remark;
    /** 区域名称 */
    private String  areaName;
    /** 发票编号 */
    private String invoiceCode;
    /** 支付方式 1:支付宝 2:微信 */
    private Integer payTypeCode;
    private List<InvoiceOrderItemsDTO> InvoiceOrderItems;
    /**
     * 购货方名称，即发票抬头。
     */
    private String customerName;

    /** 发票类型 81:全电专票 82:全电普票 */
    private String financeInvoiceType;
}
