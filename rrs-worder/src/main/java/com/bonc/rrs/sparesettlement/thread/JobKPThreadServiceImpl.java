package com.bonc.rrs.sparesettlement.thread;

import com.bonc.rrs.sparesettlement.service.SpareSettlementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class JobKPThreadServiceImpl implements Runnable{
    @Resource
    private SpareSettlementService spareSettlementService;

    @Override
    public void run() {
        //操作所有已传数据
        //spareSettlementService.selectAddInvoice();

    }
}
