package com.bonc.rrs.sparesettlement.model.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Data
public class InvoiceOrderItemsDTO {
    /** 商品编号（工单编号） */
    private String orderItemsCode;
    /** 商品名称 */
    private String orderItemsName;
    /** 发票编号（映射worder_invoice_recode的invoce_no） */
    private String invoiceNo;
    /** 商品价格 */
    private BigDecimal orderItemsAmount;
    /** 税率 */
    private BigDecimal orderItemsTaxRate;
    /** 记录创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    /** 备注 */
    private String remark;
    /** 区域名称 */
    private String areaName;
    /** 支付方式 1:微信 2:支付宝 */
    private Integer payType;
    /** 支付单号 */
    private String payOrderNo;
    /** 商品分类编码 */
    private String  orderItemsCatalogCode;
}
