package com.bonc.rrs.sparesettlement.service;

import com.bonc.rrs.sparesettlement.model.request.BillingOperRequestDTO;
import com.bonc.rrs.sparesettlement.model.request.BookkeepingRequest;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import org.springframework.validation.BindingResult;

/**
 * 发票API业务逻辑接口
 * <AUTHOR> 2020-4-11
 * @description
 */
public interface InvoiceAPIService {
        /**
         * 开票操作
         * */
        R billingOper(BillingOperRequestDTO billingOperRequest, BindingResult bindingResult, SysUserEntity userEntity);
        /** 开票回调处理 */
        Object billingCallback(String requestBody);
        /** 生成发票记录 */
        R generateBillingRecord(String worderNo, Long userId);
        R generateBillingRecord(String worderNo, Long userId, Boolean update);
        R generateBillingRecord(Integer worderId, Long userId, Boolean update);
        /** 开票后置处理 */
        Object bookkeepingPostProccessor(BookkeepingRequest bookkeepingRequest);
        /** 开票查询接口 */
        Object billingQuery(String serialNo,String orderNo);

        Object doInvoiceAfterQuery();

        Object test();

        /** 任务调度进行处理申请开票和已经记账而未进行开票的数据进行处理 */
        Object doHadBilledAndInvoicedData();

        /**
         * 根据流水号查询开票信息
         * @param serialNo
         * @return
         */
        String queryInvoice(String serialNo);
}
