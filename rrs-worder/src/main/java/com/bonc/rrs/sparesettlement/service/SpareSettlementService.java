package com.bonc.rrs.sparesettlement.service;

import com.bonc.rrs.sparesettlement.model.request.TicketRequestDTO;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import org.springframework.validation.BindingResult;

import java.util.List;

public interface SpareSettlementService {
    /** 开票 */
    R writeAnInvoice(TicketRequestDTO ticketRequestDTO, BindingResult bindingResult , SysUserEntity user);
    /** 开票回调 */
    R vaoteCountingcallBack(String requestBody);
    /** 新增开票数据 */
    R addAnInvoiceInfo(String worderNO);
}
