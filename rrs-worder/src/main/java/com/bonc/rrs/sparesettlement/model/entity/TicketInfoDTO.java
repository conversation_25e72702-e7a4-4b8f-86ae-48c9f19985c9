package com.bonc.rrs.sparesettlement.model.entity;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 票据信息
 * */
@Data
public class TicketInfoDTO {
    @NotEmpty(message = "订单编号必填")
    private String orderNo;

//    private String subOrderNo; //子订单编号
//
//    private String scanCodeKey; //购货方扫码开票时使用的唯一标识

    @NotEmpty(message = "销货方纳税人识别号必填")
    private String taxpayerCode;

//    private String shopName; //店铺名称

    @NotEmpty(message = "订单创建时间必填")
    private String orderTime;

//    private String contact; //联系人
//
//    private String contactTel; //联系电话
//
//    private String contactMail;//联系邮箱
//
//    private String shippingAddress; //配送地址
//
//    private String taxpayerName; //销货方名称
//
//    private String taxpayerAddress; //销货方地址
//
//    private String taxpayerTel; //销货方电话
//
//    private String taxpayerBankName; //销货方开户银行
//
//    private String taxpayerBankAccount; //销货方银行账号
//
      private String customerNamse; //购货方名称，即发票抬头；；选择扫码开票方案时，即 scanCodeKey 不为空时，为非必填项；
//
//    private String customerCode; //购货方纳税人识别号
//
//    private String customerAddress; //购货方地址
//
//    private String customerTel; //购货方电话
//
//    private String customerBankName; //购货方开户银行
//
//    private String customerBankAccount; //购货方银行账号
//
//    private boolean autoBilling; //是否直接开票。true:直接开具发票；false:只保存订单信息，由购货方扫码开票。默认为 false。

    @NotEmpty(message = "开票人必填")
    private String drawer; //开票人。

//    private String payee; //收款人
//
//    private String reviewer; //复核人

    @NotEmpty(message = "税价合计金额")
    @Min(value = 1,message = "税价合计金额必须大于1分")
    private BigDecimal totalAmount; //税价合计金额。必须大于等于 0.01 元；必须等于明细合计金额；必须小 于等于在税务局进行票种核定时确定 的单张发票开票限额。

//    private String remark; //发票备注
//
    private List<OrderItems> orderItems;
//
//    //扩展参数。一组 Key-Value 形式的数据，会在响应报文中回传给调用方,由调用者和瑞宏网双方根据实际情况协商使用。
//    private Object extendedParams;
    /** 自定义参数 */
    private Object dynamicParams;
}
