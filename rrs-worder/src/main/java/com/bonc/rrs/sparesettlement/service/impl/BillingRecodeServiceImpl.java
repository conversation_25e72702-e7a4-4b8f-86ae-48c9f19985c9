package com.bonc.rrs.sparesettlement.service.impl;

import com.bonc.rrs.sparesettlement.dao.BillingRecodeMapper;
import com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO;
import com.bonc.rrs.sparesettlement.service.BillingRecodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by zhangyibo on 2020-10-15 09:41
 */

@Service
public class BillingRecodeServiceImpl implements BillingRecodeService {


    @Autowired(required = false)
    BillingRecodeMapper billingRecodeMapper;

    @Override
    public List<BillingRecodeDTO> getBillingRecordByWorderNo(String worderNo) {
        return billingRecodeMapper.getBillingRecordByWorderNo(worderNo);
    }

}
