package com.bonc.rrs.sparesettlement.config;

import com.youngking.lenmoncore.common.utils.StringUtils;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.math.BigDecimal;

/**
 * 开票配置
 * <AUTHOR>
 * @description
 */
public class WordConfigs {

    private static Log log = LogFactory.getLog(com.common.pay.alipay.config.Configs.class);

    private static Configuration configs;

    private static String surveyPath;
    private static String reportPath;
    private static String replacePath;
    private static String installPath;
    private static String survey;
    private static String install;


    private WordConfigs(){}
    /**
     * 初始化数据
      */
    public static void init(String filePath){
        if(StringUtils.isEmpty(filePath)){
           throw new RuntimeException("初始化数据失败,路径不能为空");
        }
        /**  初始化配置文件  */
        if(configs == null){
            try {
                configs = new PropertiesConfiguration(filePath);
            } catch (ConfigurationException e) {
                e.printStackTrace();
            }
        }

        /*
            勘测报告的替换文件
         */
        surveyPath = configs.getString("surveyPath");
        /** 勘测报告文件 */
        reportPath = configs.getString("reportPath");
        /*
            安装的替换文件
         */
        replacePath = configs.getString("replacePath");
        /** 安装报告文件 */
        installPath = configs.getString("installPath");
        survey = configs.getString("survey");
    }
    public static String getSurveyPath() {
        return surveyPath;
    }

    public static String getReportPath() {
        return reportPath;
    }
    public static String replacePath() {
        return replacePath;
    }
    public static String installPath() {
        return installPath;
    }
    public static String survey() {
        return survey;
    }
    public static String install() {
        return install;
    }

}
