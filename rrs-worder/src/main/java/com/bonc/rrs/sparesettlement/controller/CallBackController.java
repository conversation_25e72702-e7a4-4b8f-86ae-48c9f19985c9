package com.bonc.rrs.sparesettlement.controller;

import com.bonc.rrs.sparesettlement.service.InvoiceAPIService;
import com.bonc.rrs.sparesettlement.service.SpareSettlementService;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@RestController
@RequestMapping("/callBack")
public class CallBackController {

    @Autowired
    private SpareSettlementService spareSettlementService;
    @Autowired
    private InvoiceAPIService invoiceAPIService;

    /**
     *  接口回调
     *  */
    @PostMapping("/invoiceCallback")
    public R vaoteCountingcallBack(@RequestBody String requestBody){
        System.out.println("开票结果"+requestBody);
        return spareSettlementService.vaoteCountingcallBack(requestBody);
    }
    /**
     * 发票API接口回调
     * */
    @PostMapping("/billingCallback")
    public Object billingCallback(@RequestBody String requestBody){
        return invoiceAPIService.billingCallback(requestBody);
    }
}
