package com.bonc.rrs.sparesettlement.controller;

import com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO;
import com.bonc.rrs.sparesettlement.service.BillingRecodeService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by zhangyibo on 2020-10-15 09:44
 */

@RestController
@RequestMapping("/billing/record")
public class BillingRecodeController {

    @Autowired
    BillingRecodeService billingRecodeService;

    @RequestMapping("/byWorderNo")
    public R getBillingRecordByWorderNo(String worderNo){
        List<BillingRecodeDTO> billingRecodeDTOS = billingRecodeService.getBillingRecordByWorderNo(worderNo);
        BillingRecodeDTO billingRecodeDTO = new BillingRecodeDTO();
        if(billingRecodeDTOS.size() > IntegerEnum.ZERO.getValue()){
            billingRecodeDTO = billingRecodeDTOS.get(IntegerEnum.ZERO.getValue());
        }
        return R.ok().put("billingRecode", billingRecodeDTO);
    }
}
