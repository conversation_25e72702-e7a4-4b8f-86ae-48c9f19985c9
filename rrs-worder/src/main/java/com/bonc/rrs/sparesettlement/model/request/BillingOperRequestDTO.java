package com.bonc.rrs.sparesettlement.model.request;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 开票请求对象
 * <AUTHOR> 2020-4-11
 * @description
 */
@Data
public class BillingOperRequestDTO {
    /** 开票ID */
    @NotEmpty(message = "工单编号不能为空")
    @NotNull(message = "工单编号不能为空")
    private String worderNo;
    /** 性质  1：个人 2：公司*/
    @Max(value = 2,message = "角色不正确")
    @Min(value = 1,message = "角色不正确")
    private Integer role;
    /**  */
    @NotEmpty(message = "发票抬头不能为空")
    private String customerName;
    /**  */
    private String customerCode;
    /** 电子邮件 */
    @Email(message = "邮件格式不正确")
    private String email;
    /** 建筑服务发生地 */
    @NotEmpty(message = "建筑服务发生地不能为空")
    private String jzfwfsd;
    /** 发生地详细地址 */
    @NotEmpty(message = "发生地详细地址不能为空")
    private String fsdxxdz;
    /** 建筑项目名称 */
    @NotEmpty(message = "建筑项目名称不能为空")
    private String jzxmmc;
    /** 跨地市标志 */
    @NotEmpty(message = "跨地市标志不能为空")
    private String kdsbz;
    /** 土地增值税项目编号 */
    private String tdzzsxmbh;
    /** 开票类型 81、全电专票 82、全电普票  */
    private String invoiceType;
}
