package com.bonc.rrs.sparesettlement.model.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 开票请求
 * <AUTHOR>
 * @description
 */
@Data
public class TicketRequestDTO {
    //工单编号
    @NotEmpty(message = "工单编号不能为空")
    private String worderNos;
    //发票针对对象 1: 个人 2:公司
    private Integer ticketRole;
    //公司名称
    private String companyName;
    //纳税人识别号
    private String taxpayerCode;
    //公司地址
    private String companyAddr;
    //开票人
    private String drawer;
}

