package com.bonc.rrs.sparesettlement.model.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Data
public class BillingRecodeDTO {
    /** 操作流水号 */
    private String serialNo;
    /** 请求发送时间。格式为yyyy-MM-dd HH:mm:ss。 */
    private Date postTime;
    /** 订单信息 */
    private BillingOrderRecordDTO billingOrderRecord;
    /** 发票信息 */
    private  InvoiceRecodeDTO invoiceRecode;
    /** 开票状态（ 0:未知  1:未开票  2：开票中 3:开票成功 4：开票失败 5：已作废） */
    private Integer billingStatus;
    /**操作人*/
    private Long    operUserId;
    /** 是否调用记收（1:是 2： 否） */
    private Integer chargeNums;
}
