package com.bonc.rrs.sparesettlement.model.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Data
public class InvoiceDTO {
    private Integer invoiceId;
    private String taxpayerCode; //销货方纳税人识别号。
    private String customerName;//购货方名称，即发票抬头
    private String customerCode;//购货方纳税人识别号或者个人身份证号
    private String drawer;//开票人
    private BigDecimal totalAmount;//税价合计金额
    private String invoiceType;
    private List<ItemsDTO> items;//发票项目明细列表。每张发票最多一百条。
    private String financeInvoiceType;//发票类型 81:全电专票 82:全电普票
}
