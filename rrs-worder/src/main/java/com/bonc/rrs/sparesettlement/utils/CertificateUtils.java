package com.bonc.rrs.sparesettlement.utils;

import sun.misc.BASE64Encoder;

import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;

/**
 * Created by cxh on 2017/12/14.
 */
public class CertificateUtils {
//    public static final String KEY_STORE = "JKS";
//    public static final String X509 = "X.509";
//    private static final int CACHE_SIZE = 2048;
//    private static final int MAX_ENCRYPT_BLOCK = 117;
//    private static final int MAX_DECRYPT_BLOCK = 128;
//    private static final BASE64Decoder base64Decoder = new BASE64Decoder();
    private static final BASE64Encoder base64Encoder = new BASE64Encoder();
    public static String signToBase64(byte[] data, String keyStorePath, String alias, String password)
            throws Exception
    {
        return base64Encoder.encode(sign(data, keyStorePath, alias, password));
    }
    public static byte[] sign(byte[] data, String keyStorePath, String alias, String password)
            throws Exception
    {
        X509Certificate x509Certificate = (X509Certificate)getCertificate(keyStorePath, alias, password);

        KeyStore keyStore = getKeyStore(keyStorePath, password);

        PrivateKey privateKey = (PrivateKey)keyStore.getKey(alias, password.toCharArray());

        Signature signature = Signature.getInstance(x509Certificate.getSigAlgName());
        signature.initSign(privateKey);
        signature.update(data);
        return signature.sign();
    }
    private static Certificate getCertificate(String keyStorePath, String alias, String password)
            throws Exception
    {
        KeyStore keyStore = getKeyStore(keyStorePath, password);
        Certificate certificate = keyStore.getCertificate(alias);
        return certificate;
    }
    private static KeyStore getKeyStore(String keyStorePath, String password)
            throws Exception
    {
        FileInputStream in = new FileInputStream(keyStorePath);
        KeyStore keyStore = KeyStore.getInstance("JKS");
        keyStore.load(in, password.toCharArray());
        in.close();
        return keyStore;
    }
}
