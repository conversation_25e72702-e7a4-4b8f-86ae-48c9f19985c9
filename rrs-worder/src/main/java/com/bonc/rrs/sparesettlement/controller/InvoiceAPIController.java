package com.bonc.rrs.sparesettlement.controller;

import com.bonc.rrs.sparesettlement.dao.BillingRecodeMapper;
import com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO;
import com.bonc.rrs.sparesettlement.model.request.BillingOperRequestDTO;
import com.bonc.rrs.sparesettlement.model.request.BookkeepingRequest;
import com.bonc.rrs.sparesettlement.service.InvoiceAPIService;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 开票api的处理
 * <AUTHOR> 2020-4-11
 * @description 开票api的处理
 */
@RestController
@RequestMapping("/invoiceAPI")
public class InvoiceAPIController extends AbstractController {
    @Autowired
    private InvoiceAPIService invoiceAPIService;
    @Resource
    private BillingRecodeMapper billingRecodeMapper;

    /**
     *新增票据信息
     * */
//    @GetMapping("/payCompleteGenerateInvoiceInfo")
//    public Object payCompleteGenerateInvoiceInfo( @RequestParam("userId") String userId,@RequestParam("worderNo") String worderNo){
//        return invoiceAPIService.payCompleteGenerateInvoiceInfo(worderNo,userId );
//    }

    /**
     *记账调用接口
     * */
    @PostMapping("/bookkeepingPostProccessor")
    public Object bookkeepingPostProccessor(@RequestBody BookkeepingRequest bookkeepingRequest){
        return invoiceAPIService.bookkeepingPostProccessor(bookkeepingRequest);
    }
    /**
     * 用户申请开票处理接口
     * */
    @PostMapping("/userApplyOper")
    public Object userApplyOper(@RequestBody BillingOperRequestDTO BillingOperRequest, BindingResult bindingResult){
        return invoiceAPIService.billingOper(BillingOperRequest,bindingResult,getUser());
    }
    /**
     * 查询历史开票信息
     * */
    @PostMapping("/selectHistoryInfo")
    public R selectHistoryInfo(String worderNo){
        BillingRecodeDTO billingRecodeDTO = billingRecodeMapper.selectHistoryInfo(worderNo);
        return R.ok().put("list",billingRecodeDTO);
    }

    /**
     * 测试
     * @return
     */
    @GetMapping("/test")
    public Object test(){
        return invoiceAPIService.doInvoiceAfterQuery();
    }
}
