package com.bonc.rrs.sparesettlement.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.rrs.pay.dao.WorderOrderLogMapper;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.sparesettlement.config.TicketConfigs;
import com.bonc.rrs.sparesettlement.dao.InvoiceOrderItemsMapper;
import com.bonc.rrs.sparesettlement.dao.InvoiceRecodeMapper;
import com.bonc.rrs.sparesettlement.model.entity.*;
import com.bonc.rrs.sparesettlement.model.request.TicketRequestDTO;
import com.bonc.rrs.sparesettlement.service.SpareSettlementService;
import com.bonc.rrs.sparesettlement.utils.CertificateUtils;
import com.bonc.rrs.sparesettlement.utils.HttpUtils;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.dto.AreaInfoDTO;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Transactional(rollbackFor = {Exception.class,RuntimeException.class})
public class SpareSettlementServiceImpl implements SpareSettlementService {

    @Resource
    private WorderInformationDao worderInformationDao;
    @Resource
    private WorderOrderLogMapper worderOrderLogMapper;
    @Resource
    private SysUserDao sysUserDao;
    @Resource
    private InvoiceRecodeMapper invoiceRecodeMapper;
    @Resource
    private InvoiceOrderItemsMapper invoiceOrderItemsMapper;
    private Logger logger = LoggerFactory.getLogger(SpareSettlementServiceImpl.class);
    /**
     * 处理开票
     * @param ticketRequestDTO 请求对象
     *        user 用户对象
     * */
    @Override
    public R writeAnInvoice(TicketRequestDTO ticketRequestDTO, BindingResult bindingResult, SysUserEntity user) {

        String returnJSON = "";

        /** 数据验证 */
        List<String> resultList = new ArrayList<>();

        if(bindingResult.hasErrors()){
            List<FieldError> errorList = bindingResult.getFieldErrors();
            errorList.forEach(item -> resultList.add(item.getField()+" "+item.getDefaultMessage()));
            return R.error(HttpServletResponse.SC_EXPECTATION_FAILED, JSONObject.toJSONString(errorList));
        }
        /** 验证是否已经开过票 */
        R result =  volidateHadInvoiced(ticketRequestDTO);
        if(result!=null){
            return result;
        }
        /** 处理待开发票调用接口传输数据 */
        TicketInfoDTO ticketInfoDTO = new TicketInfoDTO();

        /** 处理消息发送 */
        List<InvoiceOrderItemsDTO> invoiceOrderItems = new ArrayList<>();
        InvoiceRecodeDTO invoiceRecode = new InvoiceRecodeDTO();
        result =   getTicketInfoObj(ticketRequestDTO,invoiceRecode,invoiceOrderItems,ticketInfoDTO);

        if(result != null){
            return result;
        }
        if(ticketInfoDTO == null){
            return R.error();
        }
        try {
            returnJSON = transmitDate(ticketInfoDTO);
        } catch (Exception e) {
             throw new RuntimeException(""+e.getMessage());
        }
        /** 对返回的报文进行处理 */
        return  dealWithReturnDataPostProccessor(ticketRequestDTO.getWorderNos(),invoiceRecode,invoiceOrderItems,returnJSON);
    }
    /**
     *验证是否已经开过票
     * */
    public R volidateHadInvoiced(TicketRequestDTO ticketRequestDTO){
        Set<String> noWorderNO = new HashSet<>();
        Set<String> list = invoiceOrderItemsMapper.findHadInvoice(Arrays.asList(ticketRequestDTO.getWorderNos()));
        list.forEach(e->{
            noWorderNO.add("工单编号"+e+"已经开票或者处于申请开票中;");
        });
        if(noWorderNO!=null && noWorderNO.size()>0){
            return R.error().putList(noWorderNO);
        }
        return null;
    }
    /** 回调的接口非法 */
    @Override
    public R vaoteCountingcallBack(String requestBody) {
        if(StringUtils.isNullOrEmpty(requestBody)){
            return R.Result("error","返回消息错误");
        }
        /** 结果处理 */
        voteCountingcallBackPostProcessor(requestBody);
        return R.Result("success","处理正确");
    }

    @Override
    public R addAnInvoiceInfo(String worderNO) {
        return null;
    }

    /** 获取的结果的后置处理  */
    R voteCountingcallBackPostProcessor(String requestBody){
       JSONObject jsonObject =  JSONObject.parseObject(requestBody);
        if(jsonObject!=null && jsonObject.size()>0){
            if(jsonObject.containsKey("code") && "0".equals(jsonObject.getString("code"))){
                /** 对数据进行处理 */
                try {
                    return   voteCountingDataProcessor(jsonObject,Constant.INVOICERECODESTATUS.STATUS_SUCCSESS);
                } catch (Exception e) {
                   logger.error("开票失败"+e.getMessage()+"原因:"+e.getCause());
                }
            }else if(jsonObject.containsKey("code") && ("508".equals(jsonObject.getString("code")) || "6".equals(jsonObject.getString("code")) )){
                return R.Result("error","请重试");
            }else if(jsonObject.containsKey("code") && "507".equals(jsonObject.getString("code"))){
                return R.Result("success","返回信息成功");
            }else{
                /** 对数据进行处理 */
                try {
                    return   voteCountingDataProcessor(jsonObject,Constant.INVOICERECODESTATUS.STATUS_FAIL);
                } catch (Exception e) {
                    logger.error("开票失败"+e.getMessage()+"原因:"+e.getCause());
                }
            }
            return R.Result("success","返回信息成功");
        }
        return R.Result("success","返回信息成功");
    }
    /**
     * 对数据进行处理
     * @param ticketStatus 开票表的订单状态
     *        worderTicketStatus 订单的票据状态
     * */
    public R voteCountingDataProcessor(JSONObject jsonObject,Integer ticketStatus) throws Exception {
        /** */
        JSONArray jsonArray =  jsonObject.getJSONArray("items");
        if(jsonObject != null && jsonObject.size() > 0){
            /** 获取发票编号 */
            String orderNo = "";
            if(jsonObject.containsKey("orderNo")&&!"".equals(jsonObject.getString("orderNo"))){
                orderNo = jsonObject.getString("orderNo");
            }else{
                logger.error("返回的发票编号不存在");
                return R.Result("error","请重试");
            }
            /** 更新开票数据数据 */
            //处理开票的编号
            List<String> worderNOs = new ArrayList<>();
            for(int i=0 ; i<jsonObject.size() ; i++){
                if(StringUtils.isNullOrEmpty(jsonObject.getString("code"))){
                    worderNOs.add(jsonObject.getString("code"));
                }
            }
            if(worderNOs==null || worderNOs.size() < 1){
                logger.error("无开票记录数据异常");
                throw new Exception("无开票记录数据异常");
            }
            InvoiceRecodeDTO invoiceRecode = new InvoiceRecodeDTO();
            invoiceRecode.setInvoiceNo(orderNo);
            invoiceRecode.setInvoiceStatus(ticketStatus);
            if(invoiceRecodeMapper.updateInvoiceRecodeStatus(invoiceRecode) < 1){
                logger.error("开票状态数据更新异常");
                throw new Exception("开票状态数据更新异常");
            };
            /** 更新订单的状态 */
            if(worderInformationDao.updateMoreVoteCountingStatus(worderNOs,ticketStatus) < 1){
                logger.error("订单状态数据更新异常");
                throw new Exception("开票状态数据更新异常");
            }
            return R.Result("success","返回信息成功");
        }
        return R.Result("error","请重试");
    }
    /**
     * 获取支付记录 封装成开票对象
     * */
     R getTicketInfoObj(TicketRequestDTO ticketRequestDTO,InvoiceRecodeDTO invoiceRecode,List<InvoiceOrderItemsDTO> invoiceOrderItems,TicketInfoDTO ticketInfoDTO){
         if(StringUtils.isEmpty(ticketRequestDTO.getWorderNos())){
             return R.error("请选择工单");
         }
         List<WorderOrderLogDTO> WorderOrderList =  worderOrderLogMapper.findConstantStatusOrderLogInfo(Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS
                 , Arrays.asList(ticketRequestDTO.getWorderNos()));

         if(WorderOrderList == null || WorderOrderList.size() < 1){
             /***/
             logger.error("订单支付流水不存在");
             return R.error("订单支付流水不存在");
         }
         /** 通过user_id获取用户名称 */
         SysUserEntity sysUserEntity = sysUserDao.selectById(Long.valueOf(WorderOrderList.get(0).getFromUserId()));
         if(sysUserEntity == null){
             logger.error("支付用户不存在");
             return R.error("支付用户不存在");
         }
        /** 处理发 body 票对象 */
         R r = orderChangeToTicet(invoiceOrderItems,ticketInfoDTO,invoiceRecode,ticketRequestDTO,WorderOrderList);
         if(r!=null){
             return r;
         }
         return null;
     }
     /**
      * 订单合成开票对象处理
      * */
     R orderChangeToTicet(List<InvoiceOrderItemsDTO> invoiceOrderItems , TicketInfoDTO ticketInfoDTO,InvoiceRecodeDTO invoiceRecode,
                          TicketRequestDTO ticketRequestDTO , List<WorderOrderLogDTO> WorderOrderList){

         if(ticketInfoDTO == null || invoiceOrderItems==null ){
             logger.error("ticketInfoDTO对象为NULL{}invoiceOrderItems对象为NULL","##");
             return R.error("ticketInfoDTO对象不存在");
         }

         /** 发票主题对象的 处理 */
        R result =   invoiceMainBodyPostProcessor(ticketInfoDTO,invoiceRecode,ticketRequestDTO);
        if(result!=null){
            return result;
        }

        /** 发票项目明细列表对象的处理 */
         result =  invoiceItemsPostProcessor(ticketRequestDTO , WorderOrderList, invoiceOrderItems ,  ticketInfoDTO);
         if(result!=null){
             return result;
         }
         /**复制发票总金额*/
         invoiceRecode.setInvoiceAmount(ticketInfoDTO.getTotalAmount());
         return null;
     }
     /**
      * 发票主题对象的 处理
      * */
     public R invoiceMainBodyPostProcessor(TicketInfoDTO ticketInfoDTO, InvoiceRecodeDTO invoiceRecode, TicketRequestDTO ticketRequestDTO){
        //生成订单编号
         String orderNO =UUID.randomUUID().toString();
         ticketInfoDTO.setOrderNo(orderNO);//发票编号
         //销货方纳税人识别号
         //	订单创建时间
         ticketInfoDTO.setOrderTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(System.currentTimeMillis())));
         //购货方名称，即发票抬头
         if(Constant.BILLINGTYPE.TYPE_COMPANY ==  ticketRequestDTO.getTicketRole()){
             //购货方名称，即发票抬头
             /** 验证抬头信息 */
             if(StringUtils.isEmpty(ticketRequestDTO.getCompanyName()) || StringUtils.isEmpty(ticketRequestDTO.getTaxpayerCode())){
                 logger.error("抬头信息不存在");
                 return R.error("抬头信息不能为空");
             }
             ticketInfoDTO.setCustomerNamse(ticketRequestDTO.getCompanyName());
             ticketInfoDTO.setTaxpayerCode(ticketRequestDTO.getTaxpayerCode());
         }else{
             ticketInfoDTO.setCustomerNamse("个人");
             ticketInfoDTO.setTaxpayerCode("");
         }
         ticketInfoDTO.setDrawer(TicketConfigs.getDrawer());//开票人

         invoiceRecode.setInvoiceNo(orderNO);//发票编号
         invoiceRecode.setDrawer(TicketConfigs.getDrawer());//开票人
         invoiceRecode.setInvoiceType(ticketRequestDTO.getTicketRole());//发票类型
         invoiceRecode.setInvoiceCompanyName(ticketRequestDTO.getCompanyName());//公司名称
         invoiceRecode.setTaxpayerCode(ticketRequestDTO.getTaxpayerCode());//纳税人识别号
         invoiceRecode.setCompanyAddr(ticketRequestDTO.getCompanyAddr());//公司地址
         invoiceRecode.setInvoiceStatus(Constant.INVOICERECODESTATUS.STATUS_LOADING);//正在开票
         return null;
     }
    /**
     * 发票项目明细列表对象的处理
     * */
    public R invoiceItemsPostProcessor(TicketRequestDTO ticketRequestDTO,List<WorderOrderLogDTO> WorderOrderList,List<InvoiceOrderItemsDTO> invoiceOrderItems , TicketInfoDTO ticketInfoDTO){
        BigDecimal totalAmount = new BigDecimal(0);
        List<OrderItems> orderItems = new ArrayList<>();
        OrderItems orderItem = null;
        InvoiceOrderItemsDTO invoiceOrderItem = null;
        /** 获取当前的工单信息 */
        List<AreaInfoDTO> areaNames =  worderInformationDao.findAreaNameByWorderNO(Arrays.asList(ticketRequestDTO.getWorderNos()),2);
        List<WorderOrderLogDTO> worderOrderLogs =  worderOrderLogMapper.findConstantStatusOrderLogInfo(3,Arrays.asList(ticketRequestDTO.getWorderNos()));
        for (int i = 0 ; i < WorderOrderList.size() ; i++){
            WorderOrderLogDTO worderOrderLogDTO =  WorderOrderList.get(i);
            //发票项目明细列表。
            orderItem = new OrderItems();
            orderItem.setName(worderOrderLogDTO.getBrandName());//品牌名称
            orderItem.setCode(worderOrderLogDTO.getWorderNo());//工单编号
            orderItem.setTaxRate(TicketConfigs.getTaxRate());//税率
            orderItem.setAmount(worderOrderLogDTO.getPayActualAmount());//支付金额
            totalAmount = totalAmount.add(worderOrderLogDTO.getPayActualAmount());//支付总金额

            /** 记录发票项目明细列表对象 */
            WorderOrderLogDTO worderOrderLog =  findOrderLog(worderOrderLogs,worderOrderLogDTO.getWorderNo());
            if(worderOrderLog==null){
                logger.error("支付流水不存在");
                return R.error("支付流水不存在");
            }
            invoiceOrderItem = new  InvoiceOrderItemsDTO();
            invoiceOrderItem.setAreaName(findAreaName(areaNames,worderOrderLogDTO.getWorderNo()));//获取区域名称
            invoiceOrderItem.setOrderItemsName(worderOrderLogDTO.getBrandName()!=null?worderOrderLogDTO.getBrandName():"");//品牌名称
            invoiceOrderItem.setOrderItemsCode(worderOrderLogDTO.getWorderNo()!=null?worderOrderLogDTO.getWorderNo():"");//工单编号
            invoiceOrderItem.setInvoiceNo(ticketInfoDTO.getOrderNo()!=null?ticketInfoDTO.getOrderNo():"");
            invoiceOrderItem.setOrderItemsAmount(worderOrderLogDTO.getPayActualAmount());//支付金额
            invoiceOrderItem.setOrderItemsTaxRate(TicketConfigs.getTaxRate());//税率
            invoiceOrderItem.setPayType(worderOrderLog.getPayType());//支付类型
            invoiceOrderItem.setPayOrderNo(worderOrderLog.getOrderNo());//支付流水编号
            invoiceOrderItem.setRemark("");
            orderItems.add(orderItem);
            invoiceOrderItems.add(invoiceOrderItem);
        }
        //发票的总金额
        ticketInfoDTO.setTotalAmount(totalAmount);
        ticketInfoDTO.setOrderItems(orderItems);
        Map<String,String> map = new HashMap<>();
        map.put("callbackUrl",TicketConfigs.getCallbackUrl());
        ticketInfoDTO.setDynamicParams(map);
        return null;
    }
    /**
     * 获取区域名称
     * */
     WorderOrderLogDTO  findOrderLog(List<WorderOrderLogDTO> worderOrderLogs,String worderNo){
        if(worderOrderLogs != null && worderOrderLogs.size() > 0&&worderNo != null){
            for(int i = 0;i<worderOrderLogs.size() ; i++){
                if(worderNo.equals(worderOrderLogs.get(i).getWorderNo())){
                  return  worderOrderLogs.get(i);
                }
            }
        }
        return null;
    }
    /**
     * 获取支付信息
     * */
    String findAreaName(List<AreaInfoDTO> areaNames,String worderNo){
        if(areaNames != null && areaNames.size() > 0&&worderNo != null){
            for(int i = 0;i<areaNames.size() ; i++){
                if(worderNo.equals(areaNames.get(i).getWorderNo())){
                    return areaNames.get(i).getAreaName();
                }
            }
        }
        return "";
    }
    /**
     * 对返回的报文进行处理
     * */
    public R dealWithReturnDataPostProccessor(String worderNOs,InvoiceRecodeDTO  invoiceRecode,List<InvoiceOrderItemsDTO>  invoiceOrderItems, String returnJSON){
//        if(ticketInfoDTO != null && ticketInfoDTO.getOrderItems()!=null && ticketInfoDTO.getOrderItems().size()>0){
//            return R.error(HttpServletResponse.SC_INTERNAL_SERVER_ERROR,"开票失败");
//        }
        /** 处理工单信息 */
        List<WorderVoteCountingDTO> worderVoteCountings = new ArrayList<>();
        // R result =  changeToVoteCounting(ticketInfoDTO,  worderVoteCountings,1, user.getUserId());
//        if(result!=null){
//            return result;
//        }
        JSONObject jsonObject =  null;
        if(StringUtils.isNotBlank(returnJSON)){
            jsonObject =  JSONObject.parseObject(returnJSON);

        }
        int ticketStatus = 0;
        if(jsonObject.containsKey("code")&&"0".equals(jsonObject.getString("code"))){
            ticketStatus = Constant.INVOICERECODESTATUS.STATUS_SUCCSESS;
        }else if(jsonObject.containsKey("code") && "6".equals(jsonObject.getString("code"))){
            ticketStatus = Constant.INVOICERECODESTATUS.STATUS_LOADING;
        }else {
            ticketStatus = Constant.INVOICERECODESTATUS.STATUS_FAIL;
        }
        if(worderInformationDao.updateMoreVoteCountingStatus(Arrays.asList(worderNOs),ticketStatus) < 1){
            throw  new RuntimeException("工单的开票状态更新失败");
        }

        String remark =  jsonObject.containsKey("message")?jsonObject.getString("message"):"";
        invoiceRecode.setRemark(remark);
        invoiceRecode.setInvoiceStatus(ticketStatus);

        /** 主体信息的处理 */
        if(invoiceRecodeMapper.insertInvoiceRecode(invoiceRecode) < 1){
            logger.info("发票记录详情表记录插入失败");
            throw new RuntimeException("发票记录详情表记录插入失败");
        }

        /** 发票记录详情表 */
        if(invoiceOrderItemsMapper.insertInvoiceOrderItems(invoiceOrderItems) < 1){
            logger.info("发票记录详情表记录插入失败");
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RuntimeException("发票记录详情表记录插入失败");
        }

        return R.error(HttpServletResponse.SC_INTERNAL_SERVER_ERROR,"开票失败");
    }
    /**
     * 待开发票调用接口传输数据
     * Created by cxh on 2017/8/7.
     */
    public String transmitDate(TicketInfoDTO ticketInfoDTO) throws Exception {
        String requestJson = JSON.toJSONStringWithDateFormat(ticketInfoDTO, "yyyy-MM-dd HH:mm:ss", new SerializerFeature[] { SerializerFeature.NotWriteRootClassName });
        System.out.println("请求报文：" + requestJson);
        System.out.println("配置参数"+JSONObject.toJSONString(TicketConfigs.getCertificatePasswordPath()));

        //String sign = CertificateUtils.signToBase64(requestJson.getBytes("UTF-8"), "D:/formal/PTTEST08.keystore", "PTTEST08", "PTTEST08");
        String sign = CertificateUtils.signToBase64(requestJson.getBytes("UTF-8"),TicketConfigs.getCertificatePasswordPath()
                            ,TicketConfigs.getCertificateAlias(), TicketConfigs.getCertificatePassword());

         System.out.println("签名字符串：" + sign);

        Map vars = new HashMap();
        vars.put("appCode", URLEncoder.encode(TicketConfigs.getAppCode(), "UTF-8"));
//        vars.put("appCode", URLEncoder.encode("PTTEST08", "UTF-8"));
        vars.put("cmdName", URLEncoder.encode(TicketConfigs.getCmdName(), "UTF-8"));
        vars.put("sign", URLEncoder.encode(sign, "UTF-8"));
        String responseJson = HttpUtils.doPost(TicketConfigs.getBillingUrl(), vars, requestJson, TicketConfigs.getConnectTimeOut(), TicketConfigs.getReadTimeOut());
        System.out.println("响应报文：" + responseJson);
        return responseJson;
    }
    /**
     *表单对象的处理
     * */
    R changeToVoteCounting(TicketInfoDTO ticketInfoDTO,  List<WorderVoteCountingDTO> worderVoteCountings,int status,long userId){
        List<OrderItems> orderItems =  ticketInfoDTO.getOrderItems();
        if(orderItems != null && orderItems.size() > 0){
            WorderVoteCountingDTO worderVoteCounting = null;
            for (int i=0 ; i<orderItems.size();i++){
                worderVoteCounting = new WorderVoteCountingDTO();
                OrderItems orderItem = orderItems.get(i);
                worderVoteCounting.setWorderNo(orderItem.getCode());//工单编号
                worderVoteCounting.setVoteCountingNo(ticketInfoDTO.getOrderNo());//票据编号
                worderVoteCounting.setVoteCountingMoney(new BigDecimal(orderItem.getAmount().doubleValue()));//金额
                worderVoteCounting.setVoteCountingName(orderItem.getName());//开票名称
                worderVoteCounting.setVoteCountingStatus(status);//状态
                worderVoteCounting.setCreateUserId(userId);//用户
                worderVoteCountings.add(worderVoteCounting);
            }
        }
        return null;
    }
}
