package com.bonc.rrs.sparesettlement.model.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Data
public class RetryLogEntity {
    /** 重试记录ID  */
    private Integer retryLogId;
    /** INVOICE:发票  MEMORY:记账 RECORD:记收 */
    private String retryType;
    /** 操作结果代码 SUCCESS:失败 FAIL:失败 TROWNING:异常 */
    private String operResultCode;
    /** 操作消息结果描述 */
    private String operResultMsg;
    /** 操作IP */
    private String operIp;
    /** 创建时间 */
    private Date createTime;
    /** 是否能够重试 false true */
    private boolean enable;
    /** 备注 */
    private String remark;
    /** 每个重试类型的唯一标识 */
    private String uniquelyIdentify;
}
