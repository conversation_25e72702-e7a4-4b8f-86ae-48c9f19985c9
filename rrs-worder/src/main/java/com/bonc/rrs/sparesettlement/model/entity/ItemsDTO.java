package com.bonc.rrs.sparesettlement.model.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Data
public class ItemsDTO {
    private Integer type;//发票行性质 0 正常行、1 折扣行、2 被 折扣行
    private String code;//商品编号（工单编号)
    private String name;//商品名称
    private BigDecimal taxRate;//税率
    private BigDecimal amount;//税价合计金额
    private String catalogCode;//商品分类编码
    private String quantity; //数量
    private String uom; //单位
    private BigDecimal price; //合计金额
}
