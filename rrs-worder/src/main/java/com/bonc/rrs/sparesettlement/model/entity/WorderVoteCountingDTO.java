package com.bonc.rrs.sparesettlement.model.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Data
public class WorderVoteCountingDTO {
    //票据编号
    private String voteCountingNo;
    //工单编号
    private String worderNo;
    //开票金额
    private BigDecimal voteCountingMoney;
    //开票名称
    private String voteCountingName;
    //开票状态（0：未知 1:开票中 2:开票失败 3:开票成功）
    private Integer voteCountingStatus;
    //创建时间
    private Date createTime;
    //开票人
    private Long createUserId;
}
