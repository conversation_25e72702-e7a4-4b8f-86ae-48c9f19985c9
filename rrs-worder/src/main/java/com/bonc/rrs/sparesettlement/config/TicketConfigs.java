package com.bonc.rrs.sparesettlement.config;

import com.youngking.lenmoncore.common.utils.StringUtils;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.math.BigDecimal;

/**
 * 开票配置
 * <AUTHOR>
 * @description
 */
public class TicketConfigs {

    private static Log log = LogFactory.getLog(com.common.pay.alipay.config.Configs.class);

    private static Configuration configs;
    /** 由电子发票平台分配的 appCode */
    private static String appCode;
    /** chinaeinv.api.order.v11.kp_async */
    private static String cmdName;
    private static String queryCmdName;
    /** 销货方纳税人识别号 */
    private static String taxpayerCode;
    /** 证书别名 */
    private static String certificateAlias;
    /** 证书密码 */
    private static String certificatePassword;
    /** 税率 */
    private static BigDecimal taxRate;
    /** 回调接口地址 */
    private static String callbackUrl;
    /** 请求路由 */
    private static String billingUrl;
    /** 证书路径 */
    private static String certificatePasswordPath;
    private static int connectTimeOut;
    private static int readTimeOut;
    /** 备注 */
    private static String remark;
    /** 开票人 */
    private static String  drawer;
    /** 商品名称 */
    private static String  name;
    /** 税类编码 */
    private static String  code;

    private static String unit;

    private static String nums;

    private TicketConfigs(){}
    /**
     * 初始化数据
      */
    public static void init(String filePath){
        if(StringUtils.isEmpty(filePath)){
           throw new RuntimeException("初始化数据失败,路径不能为空");
        }
        /**  初始化配置文件  */
        if(configs == null){
            try {
                configs = new PropertiesConfiguration(filePath);
            } catch (ConfigurationException e) {
                e.printStackTrace();
            }
        }
//        String prefix =  configs.getString("prefix");
        String prefix= System.getProperty("ticket-prefix");
        if (StringUtils.isBlank(prefix)){
            prefix =  configs.getString("prefix");
        }
        /** 由电子发票平台分配的 appCode */
        appCode = configs.getString(prefix+".appCode");
        /**  */
        cmdName = configs.getString(prefix+".cmdName");

        taxpayerCode = configs.getString(prefix+".taxpayerCode");

        certificateAlias = configs.getString(prefix+".certificateAlias");

        certificatePassword = configs.getString(prefix+".certificatePassword");

        taxRate = configs.getBigDecimal(prefix+".taxRate");

        callbackUrl = configs.getString(prefix+".callbackUrl");
        String sysType = System.getProperty("os.name").toLowerCase();
        String sysTypeName = "";
        if(sysType.indexOf("win")!=-1){
            sysTypeName = "windows";
        }else{
            sysTypeName = "linux";
        }
        certificatePasswordPath = configs.getString(prefix+".certificatePasswordPath."+sysTypeName);

        connectTimeOut = configs.getInt(prefix+".connectTimeOut");

        readTimeOut = configs.getInt(prefix+".readTimeOut");
        //备注
        remark = configs.getString(prefix+".remark");
        //开票人
        drawer = configs.getString(prefix+".drawer");
        //请求路径
        billingUrl = configs.getString(prefix+".billingUrl");
        queryCmdName = configs.getString(prefix+".queryCmdName");
        unit = configs.getString(prefix+".unit");
        nums = configs.getString(prefix+".nums");
        name = configs.getString("name");
        code = configs.getString("code");
        log.info(description(prefix));
    }



    public static String description(String prefix) {
        StringBuilder sb = new StringBuilder("Configs{").append("\n");
        sb.append("--------------开票参数配置--------------").append(prefix).append("\n");
        sb.append("调用环境: ").append(prefix).append("\n");
        sb.append("appCode:").append(appCode).append("\n");
        sb.append("cmdName:").append(cmdName).append("\n");
        sb.append("纳税人识别号: ").append(taxpayerCode).append("\n");
        sb.append("回调地址: ").append(callbackUrl).append("\n");
        sb.append("}");
        return sb.toString();
    }




    public static void setAppCode(String appCode) {
        TicketConfigs.appCode = appCode;
    }

    public static void setCmdName(String cmdName) {
        TicketConfigs.cmdName = cmdName;
    }

    public static void setTaxpayerCode(String taxpayerCode) {
        TicketConfigs.taxpayerCode = taxpayerCode;
    }

    public static void setTaxRate(BigDecimal taxRate) {
        TicketConfigs.taxRate = taxRate;
    }

    public static void setCallbackUrl(String callbackUrl) {
        TicketConfigs.callbackUrl = callbackUrl;
    }

    public static void setCertificateAlias(String certificateAlias) {
        TicketConfigs.certificateAlias = certificateAlias;
    }

    public static void setCertificatePassword(String certificatePassword) {
        TicketConfigs.certificatePassword = certificatePassword;
    }

    public static void setUnit(String unit) {
        TicketConfigs.unit = unit;
    }

    public static void setNums(String nums) {
        TicketConfigs.nums = nums;
    }

    public static void setRemark(String remark) {
        TicketConfigs.remark = remark;
    }

    public static void setBillingUrl(String billingUrl) {
        TicketConfigs.billingUrl = billingUrl;
    }

    public static void setCertificatePasswordPath(String certificatePasswordPath) {
        TicketConfigs.certificatePasswordPath = certificatePasswordPath;
    }

    public static void setConnectTimeOut(int connectTimeOut) {
        TicketConfigs.connectTimeOut = connectTimeOut;
    }

    public static void setReadTimeOut(int readTimeOut) {
        TicketConfigs.readTimeOut = readTimeOut;
    }

    public static void setDrawer(String drawer) {
        TicketConfigs.drawer = drawer;
    }

    public static void setQueryCmdName(String queryCmdName) {
        TicketConfigs.queryCmdName = queryCmdName;
    }

    public static String getAppCode() {
        return appCode;
    }

    public static String getCmdName() {
        return cmdName;
    }

    public static String getTaxpayerCode() {
        return taxpayerCode;
    }

    public static BigDecimal getTaxRate() {
        return taxRate;
    }

    public static String getCallbackUrl() {
        return callbackUrl;
    }

    public static String getCertificateAlias() {
        return certificateAlias;
    }

    public static String getCertificatePassword() {
        return certificatePassword;
    }

    public static String getBillingUrl() {
        return billingUrl;
    }

    public static String getRemark() {
        return remark;
    }

    public static String getCertificatePasswordPath() {
        return certificatePasswordPath;
    }

    public static int getConnectTimeOut() {
        return connectTimeOut;
    }

    public static int getReadTimeOut() {
        return readTimeOut;
    }

    public static String getDrawer() {
        return drawer;
    }

    public static String getQueryCmdName() {
        return queryCmdName;
    }

    public static String getName() {
        return name;
    }

    public static void setName(String name) {
        TicketConfigs.name = name;
    }

    public static String getCode() {
        return code;
    }

    public static void setCode(String code) {
        TicketConfigs.code = code;
    }

    public static String getUnit() {
        return unit;
    }

    public static String getNums() {
        return nums;
    }
}
