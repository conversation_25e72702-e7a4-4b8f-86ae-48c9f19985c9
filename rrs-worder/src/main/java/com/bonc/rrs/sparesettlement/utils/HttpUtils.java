package com.bonc.rrs.sparesettlement.utils;

import cn.hutool.http.ssl.DefaultTrustManager;

import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by cxh on 2017/12/13.
 */
public class HttpUtils {
    private static final String DEFAULT_CHARSET = "UTF-8";
    private static final String METHOD_POST = "POST";
    private static final String METHOD_GET = "GET";
    private static final int CONNECTTIMEOUT = 5000;
    private static final int READTIMEOUT = 5000;

    public static String doPost(String url, Map<String, String> urlParams, String requestJson, int connectTimeOut, int readTimeOut)
            throws IOException
    {
        if (isEmpty(url))
            throw new IllegalArgumentException("The parameter 'url' can not be null or blank.");

        url = url + buildQuery(urlParams, "UTF-8");
        HttpURLConnection conn = getConnection(new URL(url), "POST");
        conn.setConnectTimeout(connectTimeOut);
        conn.setReadTimeout(readTimeOut);
        conn.getOutputStream().write(requestJson.getBytes("UTF-8"));
        return getResponseAsString(conn);
    }
    private static String getResponseAsString(HttpURLConnection conn) throws IOException
    {
        InputStream es = conn.getErrorStream();
        if (es == null)
            return getStreamAsString(conn.getInputStream(), "UTF-8");

        String msg = getStreamAsString(es, "UTF-8");
        if (isEmpty(msg))
            throw new IOException(conn.getResponseCode() + " : " + conn.getResponseMessage());

        throw new IOException(msg);
    }

    private static String getStreamAsString(InputStream input, String charset)
            throws IOException
    {
        StringBuilder sb = new StringBuilder();
        BufferedReader bf = null;
        try {
            bf = new BufferedReader(new InputStreamReader(input, charset));
            String str = null;
            while ((str = bf.readLine()) != null) {
                sb.append(str);
            }
            String str1 = sb.toString();

            return str1;
        }
        finally
        {
            if (bf != null)
                bf.close();
        }
    }

    private static boolean isEmpty(String str)
    {
        return ((str == null) || (str.trim().length() == 0));
    }

    public static boolean areNotEmpty(String[] values)
    {
        if ((values == null) || (values.length == 0)) {
            return false;
        }

        String[] arr$ = values; int len$ = arr$.length; for (int i$ = 0; i$ < len$; ++i$) { String value = arr$[i$];
        if (isEmpty(value))
            return false;
    }

        return true;
    }
    private static HttpURLConnection getConnection(URL url, String method)
            throws IOException
    {
        HttpURLConnection conn;
        if ("https".equals(url.getProtocol())) {
            SSLContext ctx;
            try {
                ctx = SSLContext.getInstance("TLS");
                ctx.init(new KeyManager[0], new TrustManager[] { new DefaultTrustManager() }, new SecureRandom());
            }
            catch (Exception e) {
                throw new IOException(e);
            }
            HttpsURLConnection connHttps = (HttpsURLConnection)url.openConnection();
            connHttps.setSSLSocketFactory(ctx.getSocketFactory());
            connHttps.setHostnameVerifier(new HostnameVerifier()
            {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }

            });
            conn = connHttps;
        } else {
            conn = (HttpURLConnection)url.openConnection();
        }
        conn.setRequestMethod(method);
        conn.setDoInput(true);
        conn.setDoOutput(true);
        conn.setRequestProperty("User-Agent", "einv-restclient-java-3.0");
        conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        conn.setRequestProperty("Connection", "Keep-Alive");
        return conn;
    }


    public static String buildQuery(Map<String, String> params, String charset)
            throws UnsupportedEncodingException
    {
        if ((params == null) || (params.isEmpty()))
            return "";

        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Iterator i$ = params.entrySet().iterator(); i$.hasNext(); ) { Map.Entry entry = (Map.Entry)i$.next();
            if (first) {
                sb.append("?");
                first = false;
            } else {
                sb.append("&");
            }
            String key = (String)entry.getKey();
            String value = (String)entry.getValue();
            if (areNotEmpty(new String[] { key, value }))
                sb.append(key).append("=").append(URLEncoder.encode(value, charset));
        }

        return sb.toString();
    }

}
