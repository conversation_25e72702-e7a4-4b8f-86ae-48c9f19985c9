package com.bonc.rrs.pay.annotation;

import com.alibaba.druid.sql.visitor.functions.Char;
import com.bonc.rrs.pay.enums.PayOperProcessor;
import com.bonc.rrs.pay.enums.PayTypeEnum;
import com.bonc.rrs.pay.enums.TradeTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PayOperLog {
    /**  标题 */
    PayOperProcessor title() default PayOperProcessor.GENERATEERCODE;
    /** 支付方式0 */
    PayTypeEnum payTypeCode() default PayTypeEnum.TYPE_WXPAY;
    /** 支付名称 未知/支付宝/微信 */
    PayTypeEnum payTypeName() default PayTypeEnum.TYPE_WXPAY;
    /** 交易方式 in:收款 */
    TradeTypeEnum tradeType() default TradeTypeEnum.IN;
}
