package com.bonc.rrs.pay.controller;

import com.bonc.rrs.pay.service.ReconciliationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ProjectName: rrs-ticket
 * @Package: com.bonc.rrs.pay.controller
 * @ClassName: ReconciliationCtroller
 * @Author: admin
 * @Description: 对账控制层
 * @Date: 2020/6/24 10:49
 * @Version: 1.0
 */
@RestController
@RequestMapping("/reconciliation")
public class ReconciliationCtroller {

    @Autowired
    private ReconciliationService reconciliationService;

    @GetMapping("/test")
    public Object test(){
        return reconciliationService.test();
//        return null;
    }
}
