package com.bonc.rrs.pay.service;

import com.bonc.rrs.pay.entity.entity.WorderPayEntity;
import com.bonc.rrs.pay.entity.request.PayInfoRequest;

import java.util.List;

/**
 * Created by zhangyibo on 2020-10-29 10:11
 */

public interface WorderPayService {


    /**
     * 根据工单编号获取工单支付状态
     * @param worderNo
     * @return
     */
    Integer getPayStatusByWorderNo(String worderNo);

    /**
     * 根据工单编号获取工单支付状态
     * @param worderId
     * @return
     */
    Integer getPayStatusByWorderId(Integer worderId);

    /**
     * 根据工单编号获取工单支付信息
     * @param worderNo
     * @return
     */
    WorderPayEntity getInfoByWorderNo(String worderNo);

    /**
     * 根据工单ID获取工单支付信息
     * @param worderId
     * @return
     */
    WorderPayEntity getInfoByWorderId(Integer worderId);

    /**
     * 一条工单支付信息查询
     * @param payInfoRequest
     * @return
     */
    WorderPayEntity getOne(PayInfoRequest payInfoRequest);

    /**
     * 工单支付信息查询
     * @param payInfoRequest
     * @return
     */
    List<WorderPayEntity> list(PayInfoRequest payInfoRequest);
}
