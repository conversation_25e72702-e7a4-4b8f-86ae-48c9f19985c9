package com.bonc.rrs.pay.manage.factory;

import com.bonc.rrs.branchbalance.service.BranchBalanceService;
import com.bonc.rrs.pay.dao.PayOperLogMapper;
import com.bonc.rrs.pay.entity.entity.PayOperLogEntity;
import com.bonc.rrs.pay.manage.utils.SpringUtils;
import com.bonc.rrs.sparesettlement.service.InvoiceAPIService;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.dto.WorderInfoDTO;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TimerTask;

/**
 * 异步调用工厂
 * <AUTHOR> 2020年3月26日
 * @description 异步调用工厂
 */
public class AsyncFactory {

    /**
     * 异步调用接口
     * */
    public static TimerTask asyncBilling(String orderNO,Long userId) {
        return new TimerTask() {
            private Logger logger = LoggerFactory.getLogger(this.getClass());
            @Override
            public void run() {
                logger.info("------------------开始调用调用asc接口------------------");
                /** 接口调用 */
                WorderInformationDao worderInformationDao = (WorderInformationDao) SpringUtils.getBean("worderInformationDao");
                WorderInfoDTO worderInfoDTO = worderInformationDao.findWorderInfoByOrderNo(orderNO);
                if(worderInfoDTO == null){
                    logger.error("接口调用失败");
                    throw new RuntimeException("接口调用失败");
                }

                /** 生成发票记录 */
                InvoiceAPIService invoiceAPIService = (InvoiceAPIService) SpringUtils.getBean("invoiceAPIService");
                invoiceAPIService.generateBillingRecord(worderInfoDTO.getWorderNo(),userId);

                //计算并保存网点增项结算金额
                BranchBalanceService branchBalanceService = (BranchBalanceService) SpringUtils.getBean("branchBalanceService");
                try{
                    logger.info("-------开始计算------");
                    branchBalanceService.caculateOneDotIncreBalanceFee(worderInfoDTO.getWorderId());
                }catch (Exception e){
                    e.printStackTrace();
                }
                logger.info("-----计算结束----");

                //支付成功后置处理
                invoiceAPIService.generateBillingRecord(worderInfoDTO.getWorderId(), userId, true);

//                更新工单增项结算状态为0-用户已收费
                if(worderInformationDao.updateWorderInformationIncreStatus(worderInfoDTO.getWorderId())<1){
                    logger.error("更新工单增项状态失败");
                    throw new RuntimeException("更新工单增项状态失败");
                }

                /** 调用asc接口的 */
//                WorderInformationAccountService worderInformationAccountService =  (WorderInformationAccountService) SpringUtils.getBean("worderInformationAccountService");
//                worderInformationAccountService.pushAcsAccountIncreReceivables(worderInfoDTO.getWorderId());
//                logger.info("------------------调用调用asc接口的成功------------------");
                //todo 调财务中台

//                WorderInformationAccountService worderInformationAccountService =  (WorderInformationAccountService) SpringUtils.getBean("worderInformationAccountService");
//                worderInformationAccountService.pushFinanceAccountIncreReceivables(worderInfoDTO.getWorderId());
//                logger.info("------------------调用调用财务中台接口的成功------------------");

            }
        };
    }
    /**
     * 开票完成操作
     * */
    public static TimerTask completeBiilingOper(String serialNo){
        return new TimerTask() {
            private Logger logger = LoggerFactory.getLogger(this.getClass());
            @Override
            public void run() {
                /** 获取订单信息 */
                logger.info("------------------开票完成后的操作---------------------");
                WorderInformationDao worderInformationDao =  (WorderInformationDao) SpringUtils.getBean("worderInformationDao");
                WorderInfoDTO worderInfoDTO =  worderInformationDao.findWorderInfoBySerialNo(serialNo);
                if(worderInfoDTO==null){
                    logger.error("工单不存在，发票流水：{}",serialNo);
                    throw new RuntimeException(String.format("工单不存在，发票流水:%s",serialNo));
                }
                logger.info("------------------ 调用pushAcsAccountIncre接口 ---------------------");
                WorderInformationAccountService worderInformationAccountService =  (WorderInformationAccountService) SpringUtils.getBean("worderInformationAccountService");
                worderInformationAccountService.pushAcsAccountIncre(worderInfoDTO.getWorderId());
            }
        };
    }
    /**
     *记录支付日志
     * */
    public static TimerTask recordPayOperLog(PayOperLogEntity payOperLogEntity){
        return new TimerTask() {
            @Override
            public void run() {
                PayOperLogMapper payOperLogMapper =  (PayOperLogMapper) SpringUtils.getBean("payOperLogMapper");
                if(payOperLogMapper.insertPayOperLog(payOperLogEntity) < 1){

                    throw new RuntimeException("支付操作日志操作失败");
                }
            }
        };
    }
}
