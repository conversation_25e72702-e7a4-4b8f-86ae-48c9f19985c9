package com.bonc.rrs.pay.dao;

import com.bonc.rrs.pay.entity.entity.PayCreateImageDTO;
import org.apache.ibatis.annotations.Param;

/**
 * @ProjectName: lenmon-parent
 * @Package: com.bonc.rrs.pay.dao
 * @ClassName: PayCreateImageMapper
 * @Author: admin
 * @Description:
 * @Date: 2020/8/26 17:10
 * @Version: 1.0
 */
public interface PayCreateImageMapper {

    PayCreateImageDTO findImgContentByOrderId(@Param("orderId") Integer orderId);

    int insertInfo(PayCreateImageDTO payCreateImageDTO);
}
