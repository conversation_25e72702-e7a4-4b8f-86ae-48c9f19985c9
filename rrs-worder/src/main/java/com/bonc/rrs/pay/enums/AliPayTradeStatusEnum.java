package com.bonc.rrs.pay.enums;

/**
 * @ProjectName: lenmon-parent
 * @Package: com.bonc.rrs.pay.enums
 * @ClassName: AliPayTradeStatusEnum
 * @Author: admin
 * @Description: 阿里支付的状态
 * @Date: 2020/8/26 14:46
 * @Version: 1.0
 */
public enum  AliPayTradeStatusEnum {

    WAIT_BUYER_PAY("1","交易创建，等待买家付款"),
    TRADE_CLOSED("2","未付款交易超时关闭，或支付完成后全额退款"),
    TRADE_SUCCESS("3","交易支付成功"),
    TRADE_FINISHED("4","交易结束，不可退款");

    private String code;
    private String desc;

    private  AliPayTradeStatusEnum(String code,String desc){
        this.code = code;
        this.desc = desc;
    }
    public static String getDesc(String code){
        for(AliPayTradeStatusEnum value : AliPayTradeStatusEnum.values()){
            if(value.code == code){
                return value.getDesc();
            }
        }
        return null;
    }
    public static String getDescByName(String name){
        for(AliPayTradeStatusEnum value : AliPayTradeStatusEnum.values()){
            if(value.name().equals(name) ){
                return value.getDesc();
            }
        }
        return null;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }


    public void setDesc(String desc) {
        this.desc = desc;
    }
}
