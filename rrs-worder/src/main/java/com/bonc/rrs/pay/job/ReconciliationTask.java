package com.bonc.rrs.pay.job;

import com.bonc.rrs.pay.service.ReconciliationService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ProjectName: rrs-ticket
 * @Package: com.bonc.rrs.pay.job
 * @ClassName: ReconciliationTask
 * @Author: admin
 * @Description: 对账任务调度
 * @Date: 2020/6/28 13:49
 * @Version: 1.0
 */
@Component("reconciliationTask")
@Slf4j
public class ReconciliationTask implements ITask {
    @Autowired
    private ReconciliationService reconciliationService;

    @Override
    public void run(String params) {
        log.info("开始执行--------------微信对账");
        reconciliationService.reconciliation();
        log.info("开始执行--------------支付宝对账");
        reconciliationService.alipayReconciliation();
    }
}
