package com.bonc.rrs.pay.dao;

import com.bonc.rrs.pay.entity.entity.PayOperLogEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Mapper
public interface PayOperLogMapper {
    /** 获取支付操作记录列表 */
    List<PayOperLogEntity> findPayOrderLogList(PayOperLogEntity payOperLogEntity);
    /** 插入支付操作记录 */
    int insertPayOperLog(PayOperLogEntity payOperLogEntity);
}
