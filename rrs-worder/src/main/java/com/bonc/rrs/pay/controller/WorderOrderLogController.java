package com.bonc.rrs.pay.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.bonc.rrs.pay.dao.WorderOrderLogRefundMapper;
import com.bonc.rrs.pay.entity.entity.WorderOrderLogRefundEntity;
import com.bonc.rrs.pay.entity.request.RefundRequest;
import com.bonc.rrs.pay.enums.PayTypeEnum;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.pay.service.PayService;
import com.bonc.rrs.pay.service.WorderOrderLogService;
import com.bonc.rrs.pay.service.imp.PayServiceImp;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.util.JsonUtil;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.pay.controller
 * @description:
 * @date 2022/1/21 15:00
 */
@RestController
@RequestMapping("/orderLog")
public class WorderOrderLogController {
    @Autowired
    WorderOrderLogService worderOrderLogService;
    @Autowired
    PayService payService;
    @Autowired
    WorderOrderLogRefundMapper worderOrderLogRefundMapper;
    private Logger logger = LoggerFactory.getLogger(PayServiceImp.class);
    @GetMapping("/list")
    public R queryList(@RequestParam Map<String,Object> params){
        //logger.info("工单号{}"+params.get("worderNo").toString());
        logger.info("车企订单号{}---------------"+params.get("companyOrderNumber"));
        logger.info("工单号{}"+params.get("worderNo").toString());
        PageUtils pageUtils = worderOrderLogService.queryList(params);
        return R.ok().put("page", pageUtils);
    }

    /**
     * 微信或支付宝退款
     * @param orderLogId
     * @return
     * @throws AlipayApiException
     */
    @PostMapping("/refundAli")
    public R refund(@RequestBody Integer orderLogId) throws AlipayApiException {
        WorderOrderLogDTO worderOrderLogDTO = worderOrderLogService.selectById(orderLogId);
        //生成退款请求号
        String outRequestTradeNo = UUID.randomUUID().toString().trim().replaceAll("-", "");
        //获取用户
        SysUserEntity user = getUser();
        WorderOrderLogRefundEntity entity=new WorderOrderLogRefundEntity();
        entity.setPayActualAmount(worderOrderLogDTO.getPayActualAmount());
        entity.setWorderNo(worderOrderLogDTO.getWorderNo());
        entity.setOrderNoRefund(outRequestTradeNo);
        entity.setTransactionId(worderOrderLogDTO.getTransactionId());
        entity.setOperatorId(user.getUserId());
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = df.format(new Date());
        try {
            entity.setCreateTime(df.parse(format));
            entity.setUpdateTime(df.parse(format));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Integer integer = worderOrderLogRefundMapper.selectCountByTransactionId(worderOrderLogDTO.getTransactionId());
        //支付宝
        if(PayTypeEnum.TYPE_ALIPAY.getStatus()==worderOrderLogDTO.getPayType()){
            AlipayTradeRefundResponse alipayTradeRefundResponse = payService.aliRefund(worderOrderLogDTO.getOrderNo(), worderOrderLogDTO.getPayActualAmount().toString(),outRequestTradeNo);
            if("Success".equals(alipayTradeRefundResponse.getMsg())){
                if(integer<1){
                    int insert = worderOrderLogRefundMapper.insert(entity);
                }
                logger.info("支付宝申请退款响应参数--------------------------{}"+alipayTradeRefundResponse.getBody());
            } else if("40004".equals(alipayTradeRefundResponse.getCode())){
                return R.error("交易不存在");
            }else {
                return R.error(alipayTradeRefundResponse.getMsg());
            }
        }else if(PayTypeEnum.TYPE_WXPAY.getStatus()==worderOrderLogDTO.getPayType()){
            BigDecimal b=new BigDecimal(100);
            BigDecimal multiply = worderOrderLogDTO.getPayAbleAmuount().multiply(b);
            int money = worderOrderLogDTO.getPayAbleAmuount().intValue();
            //微信
            Map<String, String> map = payService.wxRefund(worderOrderLogDTO.getOrderNo(),String.valueOf(money*100));
            logger.info("申请微信退款接口响应参数-----------------------{}"+map.toString());
            if("SUCCESS".equals(map.get("return_code"))){
                if(integer<1){
                    int insert = worderOrderLogRefundMapper.insert(entity);
                }
                return R.ok();
            }else{
                return R.error(map.get("msg"));
            }
        }
        logger.info("支付流水数据----------------{}"+worderOrderLogDTO);
        return R.ok();
    }

    /**
     *
     * 微信或支付宝退款查询
     * @return
     * @throws AlipayApiException
     */
    @PostMapping("/refundSelect")
    public R refundSelect(@RequestBody Integer orderLogId){
        WorderOrderLogDTO worderOrderLogDTO = worderOrderLogService.selectById(orderLogId);
        //根据支付流水号查询 退款流水号
        WorderOrderLogRefundEntity entity = worderOrderLogRefundMapper.selectByTransactionId(worderOrderLogDTO.getTransactionId());
        //获取用户
        SysUserEntity user = getUser();
        if(PayTypeEnum.TYPE_ALIPAY.getStatus()==worderOrderLogDTO.getPayType()){
            try {
                AlipayTradeFastpayRefundQueryResponse response = payService.AliRefundSelect(worderOrderLogDTO.getOrderNo(),entity.getOrderNoRefund());
                logger.info("支付宝退款查询响应----------------{}"+response.getBody());
                logger.info("支付宝退款查询响应11111111----------------{}"+response.getRefundStatus());
                if("REFUND_SUCCESS".equals(response.getRefundStatus())){
                    if(worderOrderLogDTO.getOrderStatus()!=5){
                        //把支付流水表改为退款成功
                        Integer integer = worderOrderLogService.updateById(worderOrderLogDTO.getOrderLogId());
                        entity.setStatus(1);
                        //把退款流水表改为退款成功
                        worderOrderLogRefundMapper.updateByTransactionId(worderOrderLogDTO.getTransactionId());
                        if(integer>0){
                            return R.ok();
                        }
                    }else if(worderOrderLogDTO.getOrderStatus()==5) {
                        return R.ok();
                    }
                }else {
                    return R.error(response.getMsg());
                }
            } catch (AlipayApiException e) {
                e.printStackTrace();
            }
        }else if(PayTypeEnum.TYPE_WXPAY.getStatus()==worderOrderLogDTO.getPayType()){
            //微信
            Map<String, String> map = payService.WxRefundSelect(entity.getOrderNoRefund());
            logger.info("退款查询响应结果-------------------------{}"+map.toString());
            if("SUCCESS".equals(map.get("return_code"))){
                //把支付流水表改为退款成功
                Integer integer = worderOrderLogService.updateById(worderOrderLogDTO.getOrderLogId());
                entity.setStatus(1);
                //把退款流水表改为退款成功
                worderOrderLogRefundMapper.updateByTransactionId(worderOrderLogDTO.getTransactionId());
                if(integer>0){
                    return R.ok();
                }
            }
        }
        return null;
    }

    @PostMapping("/test")
    public Object test(@RequestBody RefundRequest refundRequest) throws AlipayApiException {
        AlipayTradeFastpayRefundQueryResponse response = payService.AliRefundSelect(refundRequest.getOutTradeNo(),refundRequest.getRefundAmount());
        JsonUtil jsonUtil=new JsonUtil();
        logger.info("test响应参数------------------------{}"+response.getBody());
        logger.info("test响应参数------------------------{}"+response.getRefundStatus());
        logger.info("test响应参数------------------------{}"+response.getErrorCode());
        return null;
    }
    /**
     * 获取用户
     * @return
     */
    public SysUserEntity getUser(){
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }
}