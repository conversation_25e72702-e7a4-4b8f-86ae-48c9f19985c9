package com.bonc.rrs.pay.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.pay.entity.entity.WorderOrderLogRefundEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.pay.dao
 * @description:
 * @date 2022/1/27 9:55
 */
@Mapper
public interface WorderOrderLogRefundMapper extends BaseMapper<WorderOrderLogRefundEntity> {
    WorderOrderLogRefundEntity selectByTransactionId(String transactionId);

    Integer updateByTransactionId(String transactionId);

    Integer selectCountByTransactionId(String transactionId);
}