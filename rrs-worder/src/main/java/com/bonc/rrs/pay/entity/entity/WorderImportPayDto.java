package com.bonc.rrs.pay.entity.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@ToString
public class WorderImportPayDto {

    @ExcelProperty(value  = "业务单号")
    private String companyOrderNo;

    @ExcelProperty(value  = "资管订单号")
    private String orderNo;

    @ExcelProperty(value  = "订单日期")
    private String completeTime;


    @ExcelProperty(value  = "总金额")
    private String payActualMmount;


    @ExcelProperty(value  = "商户订单号")
    private String transactionId;



}
