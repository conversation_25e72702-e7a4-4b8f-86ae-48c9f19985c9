/**
 * Copyright (C), 2024,
 */
package com.bonc.rrs.pay.entity.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/28 15:12
 * @Version 1.0.0
 */
@Data
@NoArgsConstructor
@ToString
public class IncreDto {
    private Long invoiceId;
    @ExcelProperty(value = "订单号")
    private String worderNo;
    @ExcelProperty(value = "税号")
    private String customerCode;

    @ExcelProperty(value = "发票抬头")
    private String customerName;
}