package com.bonc.rrs.pay.entity.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ProjectName: rrs-ticket
 * @Package: com.bonc.rrs.pay.entity.entity
 * @ClassName: PayDataStatisticEntity
 * @Author: admin
 * @Description: 支付总金额统计
 * @Date: 2020/6/24 15:43
 * @Version: 1.0
 */
@Data
public class LocalPayDataStatisticEntity {
    /** 实际支付总金额 */
    private BigDecimal payActualTotalAmount;
    /** 支付单数 */
    private Integer counts;
}
