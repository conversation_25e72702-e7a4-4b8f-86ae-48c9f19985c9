package com.bonc.rrs.pay.controller;

import com.bonc.rrs.pay.entity.request.PayInfoRequest;
import com.bonc.rrs.pay.service.WorderOrderLogService;
import com.youngking.lenmoncore.common.utils.R;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * Created by zhangyibo on 2020-10-29 10:07
 * 工单支付
 */


@RestController
@RequestMapping("/worder/pay")
public class WorderPayController {


    @Autowired
    WorderOrderLogService worderOrderLogService;

    /**
     * 工单支付信息查询
     * @param payInfoRequest
     * @return
     */
    @RequestMapping("get")
    public R get(@RequestBody PayInfoRequest payInfoRequest){
        return R.ok();
    }

    /**
     * 支付信息导入
     * @param file
     * type 极狐1 小米 2
     * @return
     * @throws IOException
     */
    @RequestMapping("/worderImportPay")
    @ResponseBody
    public R worderImportPay(@RequestParam("file") MultipartFile file,Integer type)  {
        try {
            if(type == null){
                type = 1;
            }
            //获取文件名
            String fileName = file.getOriginalFilename();
            if (StringUtils.isNotBlank(fileName)) {
                //校验上传文件格式是否为excel文件
                if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
                    return R.error().put("msg", "您上传文件不是excel文件！");
                } else {
                    return  worderOrderLogService.exportPayInfo(file,type);
                }
            } else {
                return R.error().put("msg", "您没有上传文件，请确认！");
            }
        }catch (Exception e){
            return R.error().put("msg", "系统异常:"+e.getMessage());
        }
    }

    /**
     * 增项信息导入
     * @param file
     * @return
     * @throws IOException
     */
    @RequestMapping("/importInvoiceBusinessTypeDetail")
    @ResponseBody
    public R importInvoiceBusinessTypeDetail(@RequestParam("file") MultipartFile file)  {
        try {

            //获取文件名
            String fileName = file.getOriginalFilename();
            if (StringUtils.isNotBlank(fileName)) {
                //校验上传文件格式是否为excel文件
                if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
                    return R.error().put("msg", "您上传文件不是excel文件！");
                } else {
               return  worderOrderLogService.importInvoiceBusinessTypeDetail(file);
                }
            } else {
                return R.error().put("msg", "您没有上传文件，请确认！");
            }
        }catch (Exception e){
            return R.error().put("msg", "系统异常:"+e.getMessage());
        }
    }

    @GetMapping("/findApplyInvoice")
    public R findApplyInvoice(@RequestParam String worderNo)  {
        try {
            return worderOrderLogService.findApplyInvoice(worderNo);
        }catch (Exception e){
            return R.error().put("msg", "系统异常:"+e.getMessage());
        }
    }



}
