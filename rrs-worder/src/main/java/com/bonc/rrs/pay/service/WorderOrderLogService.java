package com.bonc.rrs.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.pay.entity.entity.WorderOrderLogEntity;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.pay.service
 * @description:
 * @date 2022/1/21 14:57
 */
public interface WorderOrderLogService extends IService<WorderOrderLogEntity> {
    PageUtils queryList(Map<String, Object> params);

    WorderOrderLogDTO selectById(Integer orderLogId);

    Integer updateById(Integer orderLogId);

    /**
     *
     * @param file
     * @param type 1 极狐 2小米
     * @return
     */
    R exportPayInfo(MultipartFile file,Integer type) ;

    R findApplyInvoice(String orderNo);

    R importInvoiceBusinessTypeDetail(MultipartFile file);
}