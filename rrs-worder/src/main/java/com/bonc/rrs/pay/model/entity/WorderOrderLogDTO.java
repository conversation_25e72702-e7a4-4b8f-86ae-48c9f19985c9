package com.bonc.rrs.pay.model.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付流水表
 * <AUTHOR>
 * @description
 */
@Data
public class WorderOrderLogDTO {
    private Integer orderLogId;
    /**  订单编号 */
    private String orderNo;
    /**  工单编号（对应工单表的worder_no）*/
    private String transactionId;
    private String worderNo;
    /** 支付金额（单位：分）*/
    private BigDecimal payActualAmount;
    /** 用户ID（支付人）*/
    private Long fromUserId;
    /** 用户ID(收款人)*/
    private Long toUserId;
    /** 订单状态（0:未知 1:支付中 2:支付成功 3:支付失败 4:作废）*/
    private Integer orderStatus;
    /** 备注*/
    private String remark;
    /** 创建时间*/
    private Date createTime;
    /** 更新时间*/
    private Date updateTime;
    /** 品牌名称*/
    private String brandName;
    /**  支付类型 s*/
    private Integer payType;
    /** 支付区域*/
    private String payAreaName;
    /** area_id*/
    private Integer payAreaId;
    /**  记账状态  0:未记账 1：成功 2：失败*/
    private Integer bookkeepingStatus;
    /** 折扣金额 */
    private BigDecimal payDiscountAmount;
    /** 应付金额（单位：分）  */
    private BigDecimal payAbleAmuount;
    /** 是否开票申请开票  0: false  1 :true */
    private boolean apply;
    /** 是否推送 false true */
    private boolean pushState;
    /** 车企订单号*/
    private String companyOrderNumber;

    /** 支付完成时间*/
    private Date completeTime;
    /** 买家id */
    private String userId;
}
