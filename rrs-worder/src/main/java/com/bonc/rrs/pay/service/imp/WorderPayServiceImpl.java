package com.bonc.rrs.pay.service.imp;

import com.bonc.rrs.pay.dao.WorderOrderLogMapper;
import com.bonc.rrs.pay.dao.WorderPayMapper;
import com.bonc.rrs.pay.entity.entity.WorderPayEntity;
import com.bonc.rrs.pay.entity.request.PayInfoRequest;
import com.bonc.rrs.pay.service.WorderPayService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by zhangyibo on 2020-10-29 10:12
 */

@Service
public class WorderPayServiceImpl implements WorderPayService {

    @Autowired(required = false)
    WorderPayMapper worderPayMapper;

    public Integer getWorderPayStatus(WorderPayEntity worderPayEntity){
        return null != worderPayEntity && null != worderPayEntity.getWorderPayStatus() ?
                worderPayEntity.getWorderPayStatus() : IntegerEnum.ZERO.getValue();
    }

    @Override
    public Integer getPayStatusByWorderNo(String worderNo) {
        WorderPayEntity worderPayEntity = getInfoByWorderNo(worderNo);
        return getWorderPayStatus(worderPayEntity);
    }

    @Override
    public Integer getPayStatusByWorderId(Integer worderId) {
        WorderPayEntity worderPayEntity = getInfoByWorderId(worderId);
        return getWorderPayStatus(worderPayEntity);
    }

    @Override
    public WorderPayEntity getInfoByWorderNo(String worderNo) {
        PayInfoRequest payInfoRequest = new PayInfoRequest();
        payInfoRequest.setWorderNo(worderNo);
        return getOne(payInfoRequest);
    }

    @Override
    public WorderPayEntity getInfoByWorderId(Integer worderId) {
        PayInfoRequest payInfoRequest = new PayInfoRequest();
        payInfoRequest.setWorderId(worderId);
        return getOne(payInfoRequest);
    }

    @Override
    public WorderPayEntity getOne(PayInfoRequest payInfoRequest) {
        List<WorderPayEntity> list = worderPayMapper.list(payInfoRequest);
        if(list.size() > IntegerEnum.ZERO.getValue()){
            return list.get(IntegerEnum.ZERO.getValue());
        }
        return null;
    }

    @Override
    public List<WorderPayEntity> list(PayInfoRequest payInfoRequest) {
        return worderPayMapper.list(payInfoRequest);
    }
}
