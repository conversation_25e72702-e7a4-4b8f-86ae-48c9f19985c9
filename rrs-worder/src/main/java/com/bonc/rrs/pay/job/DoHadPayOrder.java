package com.bonc.rrs.pay.job;

import com.bonc.rrs.pay.service.PayService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
/**
 * @ProjectName: rrs-ticket
 * @Package: com.bonc.rrs.pay.job
 * @ClassName: ReconciliationTask
 * @Author: admin
 * @Description: 处理已经支付完成的订单处理
 * @Date: 2020/6/28 13:49
 * @Version: 1.0
 */
@Component("doHadPayOrder")
@Slf4j
public class DoHadPayOrder implements ITask {

    @Autowired
    private PayService payService;
    @Override
    public void run(String params) {
        log.info("开始执行--------------支付宝对账");
        payService.aliPayHadPay();
    }
}
