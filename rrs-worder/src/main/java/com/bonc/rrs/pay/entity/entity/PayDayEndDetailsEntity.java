package com.bonc.rrs.pay.entity.entity;

import com.lowagie.text.pdf.PdfPRow;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ProjectName: rrs-ticket
 * @Package: com.bonc.rrs.pay.entity.entity
 * @ClassName: PayDayEndDetails
 * @Author: admin
 * @Description: 日结对账错误数据详情
 * @Date: 2020/6/28 9:42
 * @Version: 1.0
 */
@Data
public class PayDayEndDetailsEntity {
    /** 日结日期 */
    private String dayEndDate;
    /** 微信支付订单号 即支付流水 */
    private String transactionId;
    /** 订单编号 */
    private String orderNo;
    /** 交易时间 */
    private String tradeTime;
    /** 交易类型 */
    private String tradeType;
    /** 交易状态 */
    private String tradeStatus;
    /** 总金额 */
    private BigDecimal totalAcount;
    /** 退款单号 */
    private String refundOrderNo;
    /** 退款金额 */
    private BigDecimal refundAcount;
    /** 退款类型（ORIGINAL—原路退款；BALANCE—转退到用） */
    private String refundType;
    /** 退款状态 */
    private String refundStatus;
    /** 手续费 */
    private BigDecimal handlingFee;
    /**  该笔交易计费所使用的费率 */
    private String rate;
    /** 备注 */
    private String remark;
}
