
package com.bonc.rrs.pay.entity.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/13 16:17
 * @Version 1.0.0
 */
@Data
@NoArgsConstructor
@ToString
public class DotDispatchRuleDto {

    @ExcelProperty(value  = "城市")
    private String cityName;

    @ExcelProperty(value  = "城市id")
    private Integer cityId;


    @ExcelProperty(value  = "网点id")
    private Integer dotId;

    @ExcelProperty(value  = "网点")
    private String dotName;


    @ExcelProperty(value  = "比例")
    private Integer score;

}