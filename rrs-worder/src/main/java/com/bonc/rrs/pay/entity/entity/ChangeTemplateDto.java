/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.pay.entity.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/9 14:23
 * @Version 1.0.0
 */

@Data
public class ChangeTemplateDto {
    @ExcelProperty(value  = "工单编号")
    private String worderNo;

    @ExcelProperty(value  = "正确模板")
    private String templateName;

    @ExcelProperty(value  = "正确模板ID")
    private String templateId;
}