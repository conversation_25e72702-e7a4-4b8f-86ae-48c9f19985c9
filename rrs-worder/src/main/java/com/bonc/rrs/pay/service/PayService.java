package com.bonc.rrs.pay.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.response.*;
import com.bonc.rrs.pay.entity.request.CycleQueyRequest;
import com.bonc.rrs.pay.entity.request.GoPayRequestDTO;
import com.bonc.rrs.pay.entity.request.PayInfoRequest;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.common.pay.alipay.model.result.AlipayNotifyParamResult;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @see
 */
public interface PayService {
     Object goPay(HttpServletRequest request, GoPayRequestDTO goPayRequest, SysUserEntity userEntity,Date createDate);

     void refundMoney();

     /** 支付宝支付生成二维码 */
     Object aliPay(GoPayRequestDTO goPayRequest, SysUserEntity userEntity,
                   Date createDate);

     /** 支付宝支付生成二维码 */
     Object aliPayment(GoPayRequestDTO goPayRequest, SysUserEntity userEntity,
                       Date createDate);


     /** 微信支付生成二维码 */
     Object wxPay(HttpServletRequest request,GoPayRequestDTO goPayRequest, SysUserEntity userEntity,Date createDate);

     /** 异步回调处理 */
     Object synchronouNotify(HttpServletRequest request);

     Object test();
     /** 获取微信支付信息 */
     Object findPayInfo(PayInfoRequest payInfoRequest);
     /** 微信支付查询 */
     Map<String, String> orderQuery(String outTradeNo);

     /**
      * 查询订单情况接口
      * @param cycleQueyRequest
      * @return
      */
     Object  payCycleQuey( CycleQueyRequest cycleQueyRequest);

     /**
      * 获取沙箱环境的信息
      * @return
      */
     public String getSandboxSignKey();
     /**
      * 通过工单编号查询工单是否支付
      * */
     Object findWorderHadPay(String worderNo);

     public String alipayCallback(HttpServletRequest request);

     /** 支付宝已经支付完成的订单处理 */

     Object aliPayHadPay();

     Object  payQueryOrderNo(CycleQueyRequest cycleQueyRequest);

     AlipayTradeQueryResponse aliPaySelect(String orderNO);

     String weChatSelect(String orderNO);

     Map<String,String>  wxCloseOrder(List<WorderOrderLogDTO> worderOrderLogDTOS);

     AlipayTradeCloseResponse aLiCloseOrder(String outTradeNo);

     AlipayTradeCancelResponse aliRevoke(String outTradeNo);

     void doWorderNotifyInfoPayment( WorderOrderLogDTO worderOrderLogDTO);

     /**
      * 支付宝退款接口
      * @param outTradeNo
      * @param refundAmount
      * @return
      */
     AlipayTradeRefundResponse aliRefund(String outTradeNo,String refundAmount,String outRequestTradeNo) throws AlipayApiException;

     /**
      * 微信退款接口
      * @param outTradeNo
      * @param amount
      * @return
      */
     Map<String, String> wxRefund(String outTradeNo,String amount);

     /**
      * 支付宝退款接口查询
      * @param outTradeNo
      * @return
      */
     AlipayTradeFastpayRefundQueryResponse AliRefundSelect(String outTradeNo,String outRequestTradeNo) throws AlipayApiException;

     /**
      * 微信退款接口查询
      * @param outRefundNo
      * @return
      */
     Map<String, String> WxRefundSelect(String outRefundNo);

     public void doWorderPaymentUserIdAndCompleteTime( AlipayNotifyParamResult param);

     public void doWorderWxmentUserIdAndCompleteTime(Map<String,String> map);
}
