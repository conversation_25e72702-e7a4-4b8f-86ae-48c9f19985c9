package com.bonc.rrs.pay.entity.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ProjectName: rrs-ticket
 * @Package: com.bonc.rrs.pay.entity.entity
 * @ClassName: PayDayEndStatisticsEntity
 * @Author: admin
 * @Description: 日结数据统计
 * @Date: 2020/6/24 11:11
 * @Version: 1.0
 */
@Data
public class PayDayEndStatisticsEntity {
    /** 日结日期 */
    private String dayEndDate;
    /** 本地总交易单数 */
    private Integer localBillNum;
    /** 本地账总交易额 */
    private BigDecimal localBillToatalAmount;
    /** 对账总交易单数 */
    private Integer reconciliationBillNum;
    /** 对账账总交易额  */
    private BigDecimal reconciliationBillToatalAmount;
    /** 手续费总金额 */
    private BigDecimal handlingFee;
    /** 执行时间 */
    private Date createTime;
    /** 备注 */
    private String remark;
    /** SUCCESS：成功 WARN :警告 FAIL:失败 */
    private String reconciliationStatus;
    /** 支付类型（1:支付宝 2：微信） */
    private Integer payType;
}
