package com.bonc.rrs.pay.dao;

import com.bonc.rrs.pay.entity.entity.WorderPayEntity;
import com.bonc.rrs.pay.entity.request.PayInfoRequest;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Created by zhangyibo on 2020-10-29 10:10
 */

@Mapper
public interface WorderPayMapper {

    /**
     * 工单支付信息查询
     * @param payInfoRequest
     * @return
     */
    List<WorderPayEntity> list(PayInfoRequest payInfoRequest);

}
