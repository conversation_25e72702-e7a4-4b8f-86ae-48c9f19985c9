package com.bonc.rrs.pay.enums;

/**
 * <AUTHOR>
 * @description
 * @see
 */
public enum  PayTypeEnum {

    TYPE_ALIPAY(1,"支付宝"),
    TYPE_WXPAY(2,"微信"),
    TYPE_UNIONPAY(3,"银联"),
    TYPE_XMPAY(4,"小米支付"),
    ;

    private int status;
    private String desc;

    private  PayTypeEnum(int status,String desc){
        this.status = status;
        this.desc = desc;
    }
    public static String getDesc(int status){
        for(PayTypeEnum value : PayTypeEnum.values()){
            if(value.status == status){
                return value.getDesc();
            }
        }
        return null;
    }
    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
