package com.bonc.rrs.pay.entity.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.pay.entity.entity
 * @description:
 * @date 2022/1/27 9:51
 */
@Data
@TableName("worder_order_log_refund")
public class WorderOrderLogRefundEntity implements Serializable {
    /**
     * 工单号
     */
    private String worderNo;
    /**
     * 退款流水号
     */
        private String orderNoRefund;

    /**
     * 微信支付订单号 即支付流水
     */
    private String transactionId;

    /** 创建时间*/
    private Date createTime;
    /** 更新时间*/
    private Date updateTime;
    /**
     * 操作人id
     */
    private Long operatorId;

    private Integer status;

    /** 支付金额（单位：分）*/
    private BigDecimal payActualAmount;
}