package com.bonc.rrs.pay.entity.entity;

import com.alibaba.druid.sql.visitor.functions.Char;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@Data
public class PayOperLogEntity {
    /** 主键ID  */
    private Integer payLogId;
    /** 操作IP */
    private String operIp;
    /** 支付方式0：未知  1：支付宝 2：微信 */
    private Integer payTypeCode;
    /** 支付名称 未知/支付宝/微信 */
    private String payTypeName;
    /** 交易方式 in:收款 */
    private String tradeType;
    /** 操作时间 */
    private Date createTime;
    /** 备注 */
    private String remark;
    /** 操作状态 SUCCESS:成功 FAIL:失败 THROWING:异常 */
    private String operStatus;
    /** 标题  */
    private String title;
    /** 操作内容  */
    private String operContent;
}
