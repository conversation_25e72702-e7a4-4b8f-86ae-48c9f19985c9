package com.bonc.rrs.pay.manage;





import com.bonc.rrs.pay.manage.utils.SpringUtils;

import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 *  异步任务管理
 * <AUTHOR> 2020年3月26日
 * @description
 */
public class AsyncManager {
    /**
     * 操作延迟10毫秒
     */
    private final int OPERATE_DELAY_TIME = 10;
    /**
     * 异步操作任务调度线程池
     */
    private ScheduledExecutorService executor = SpringUtils.getBean("scheduledExecutorService");

    /**
     * 单例模式
     */
    private AsyncManager() {
    }

    private volatile static AsyncManager me = null;

    /**
     * 线程安全
     */
    public static AsyncManager instance() {
        if (me != null) {
            synchronized (AsyncManager.class) {
                if (me != null) {
                    return me;
                }
            }
        }
        return new AsyncManager();
    }

    /**
     * 执行任务
     *
     * @param task 任务
     */
    public void execute(TimerTask task) {
        executor.schedule(task, OPERATE_DELAY_TIME, TimeUnit.MILLISECONDS);
    }

    /**
     * 执行任务
     *
     * @param task 任务
     */
    public void execute(TimerTask task, int operateDelayTime) {
        executor.schedule(task, operateDelayTime, TimeUnit.MILLISECONDS);
    }
}
