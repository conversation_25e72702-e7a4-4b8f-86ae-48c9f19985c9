package com.bonc.rrs.pay.enums;

/**
 * 微信支付操作步骤
 * <AUTHOR>
 * @description
 * @see
 */
public enum PayOperProcessor {
    //生成二维码
    GENERATEERCODE("生成支付二维码"),
    //查询
    QUERY("支付查询"),
    //异步回调
    SYNCHRONOUNOTIFY("微信支付异步回调"),
    //异步回调
    ALIPAYCALLBACK("支付宝支付异步回调"),
    //轮询查询
    CYCLE_QUERY("轮询查询");

    private String desc;

    private PayOperProcessor(String desc){
        this.desc = desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
