package com.bonc.rrs.pay.enums;

/**
 * @ProjectName: rrs-ticket
 * @Package: com.bonc.rrs.pay.enums
 * @ClassName: PayResultEnum
 * @Author: admin
 * @Description: 支付结果枚举
 * @Date: 2020/6/3 13:53
 * @Version: 1.0
 */
public enum PayResultEnum {
    PAY_LOAD(1,"支付中"),
    PAY_SUCEESS(2,"支付成功"),
    PAY_FAIL(3,"支付失败"),
    PAY_CANCELL(4,"作废");

    private Integer status;

    private String desc;

    private PayResultEnum(Integer status, String desc){
        this.status = status;
        this.desc = desc;
    }
    public String getDesc(Integer status){
        for(PayResultEnum payResultEnum : PayResultEnum.values()){
            if(payResultEnum.status == status){
                return payResultEnum.desc;
            }
        }
        return null;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
