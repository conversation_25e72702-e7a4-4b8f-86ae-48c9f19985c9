package com.bonc.rrs.pay.aspect;

import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.pay.annotation.PayOperLog;
import com.bonc.rrs.pay.entity.entity.PayOperLogEntity;
import com.bonc.rrs.pay.enums.PayOperProcessor;
import com.bonc.rrs.pay.enums.PayOperResultCode;
import com.bonc.rrs.pay.manage.AsyncManager;
import com.bonc.rrs.pay.manage.factory.AsyncFactory;
import com.common.pay.common.utils.ServletUtils;
import com.common.pay.wxpay.util.CommonUtil;
import com.common.pay.wxpay.util.WXPayUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.regex.Pattern;

/**
 * 支付操作记录
 * <AUTHOR>
 * @description
 * @see
 */
@Aspect
@Component
public class PayOperLogAspect {
    private Logger logger = LoggerFactory.getLogger(PayOperLogAspect.class);

    private static Pattern NUMBER_PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");

    /**
     *配置织入点
     * */
    @Pointcut("@annotation(com.bonc.rrs.pay.annotation.PayOperLog)")
    public void logPointCut(){

    }
    /** 对返回的消息进行处理 */
    @AfterReturning(pointcut = "logPointCut()",returning = "jsonResult")
    public void doAfterOper(JoinPoint joinPoint, Object jsonResult){
        handleReturnMsg( joinPoint,  null ,jsonResult);
    }
    /** 异常处理 */
    @AfterThrowing(pointcut = " logPointCut()",throwing = "e")
    public void doAfterThrow(JoinPoint joinPoint, Exception e){
        handleReturnMsg( joinPoint, e,null);
    }
    /**
     *对返回的消息的处理
     * */
    void handleReturnMsg(JoinPoint joinPoint,Exception e,Object jsonResult){
        try {
            HttpServletRequest request =  ServletUtils.getRequest();
            PayOperLog payOperLog = getAnnotationLog(joinPoint);
            PayOperLogEntity payOperLogEntity = new PayOperLogEntity();
            /** 标题 */
            payOperLogEntity.setTitle(payOperLog.title().getDesc());
            if(e!=null){
                payOperLogEntity.setOperContent(e.getMessage());
                payOperLogEntity.setOperStatus(PayOperResultCode.THROWING.name());
            }else{
                returnMsgPostProcessor( payOperLogEntity, jsonResult,payOperLog);
            }
            /** 操作IP */
            payOperLogEntity.setOperIp(CommonUtil.getIp(request));
            /** 支付方式0：未知  1：支付宝 2：微信 */
            payOperLogEntity.setPayTypeCode(payOperLog.payTypeCode().getStatus());
            /** 支付名称 未知/支付宝/微信 */
            payOperLogEntity.setPayTypeName(payOperLog.payTypeName().getDesc());
            /** 交易方式 in:收款 */
            payOperLogEntity.setTradeType(payOperLog.tradeType().name());
            /** 备注 */
            AsyncManager.instance().execute(AsyncFactory.recordPayOperLog(payOperLogEntity));
        } catch (Exception ex) {
            logger.info("支付操作日志对支付返回的消息的处理");
            ex.printStackTrace();
        }
    }
    /**
     * 获取
     * */
    void returnMsgPostProcessor(PayOperLogEntity payOperLogEntity,Object jsonResult,PayOperLog payOperLog){
        JSONObject jsonObject = null;

        try {
            if(payOperLog.title().name().equalsIgnoreCase(PayOperProcessor.SYNCHRONOUNOTIFY.name())){
                jsonObject =  JSONObject.parseObject(JSONObject.toJSONString(WXPayUtil.xmlToMap(String.valueOf(jsonResult))));
            }else {
                jsonObject =     JSONObject.parseObject(JSONObject.toJSONString(jsonResult));
            }
            logger.info("标题：{},日志消息返回：{}",payOperLog.title().getDesc(),jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

        String operContent =  jsonObject.containsKey("msg")?String.valueOf(jsonObject.get("msg")):(jsonObject.containsKey("return_msg")?String.valueOf(jsonObject.get("return_msg")):jsonObject.toString());
        String codeStr = jsonObject.containsKey("code")?String.valueOf(jsonObject.get("code")):jsonObject.containsKey("return_code")?String.valueOf(jsonObject.get("return_code")):"";
        boolean flag =  isInteger(codeStr);
        payOperLogEntity.setOperStatus(flag?PayOperResultCode.getName(Integer.valueOf(codeStr)):codeStr);
        if(flag&&(codeStr.equals(String.valueOf(PayOperResultCode.REPEAT.getStatus())))){
            String data = jsonObject.containsKey("data")?String.format("内容:%s",JSONObject.toJSONString(jsonObject.get("data"))):"";
            operContent = operContent + data;
        }
        payOperLogEntity.setOperContent(operContent);
    }

    public static boolean isInteger(String str) {
        return NUMBER_PATTERN.matcher(str).matches();
    }
    /**
     * 是否存在注解，如果存在就获取
     */
    private PayOperLog getAnnotationLog(JoinPoint joinPoint) throws Exception
    {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method != null)
        {
            return method.getAnnotation(PayOperLog.class);
        }
        return null;
    }
}
