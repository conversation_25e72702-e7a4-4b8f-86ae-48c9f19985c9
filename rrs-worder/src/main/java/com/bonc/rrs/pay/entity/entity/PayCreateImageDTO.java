package com.bonc.rrs.pay.entity.entity;

import lombok.Data;

import java.util.Date;

/**
 * @ProjectName: lenmon-parent
 * @Package: com.bonc.rrs.pay.entity.entity
 * @ClassName: PayCreateImageDTO
 * @Author: admin
 * @Description:
 * @Date: 2020/8/26 17:11
 * @Version: 1.0
 */
@Data
public class PayCreateImageDTO {

    private Integer orderId;

    private String imgContent;

    private Integer intervals;

    private String  unit;

    private Integer enable;

    private Date createTime;
}
