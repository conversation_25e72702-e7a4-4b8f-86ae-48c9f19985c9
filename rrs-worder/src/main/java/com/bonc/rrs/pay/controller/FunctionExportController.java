/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.pay.controller;

import com.bonc.rrs.pay.service.FunctionExportService;
import com.youngking.lenmoncore.common.utils.R;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/9 14:16
 * @Version 1.0.0
 */
@RestController
@RequestMapping("/function/export")
public class FunctionExportController {

    @Autowired
    private FunctionExportService functionExportService;


    /**
     * 区域维护
     *
     * @param file
     * @return
     * @throws IOException
     */
    @RequestMapping("/efficiencyAreaRule")
    @ResponseBody
    public R efficiencyAreaRule(@RequestParam("file") MultipartFile file, String name) {
        try {
            //获取文件名
            String fileName = file.getOriginalFilename();
            if (StringUtils.isNotBlank(fileName)) {
                //校验上传文件格式是否为excel文件
                if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
                    return R.error().put("msg", "您上传文件不是excel文件！");
                } else {
                    return functionExportService.efficiencyAreaRuleExport(file, name);
                }
            } else {
                return R.error().put("msg", "您没有上传文件，请确认！");
            }
        } catch (Exception e) {
            return R.error().put("msg", "系统异常:" + e.getMessage());
        }
    }

    /**
     * 修改派单比例
     *
     * @param file
     * @param brandIds
     * @return
     */
    @RequestMapping("/updateDotDispatchRule")
    @ResponseBody
    public R updateRule(@RequestParam("file") MultipartFile file, String brandIds, String cycle) {
        try {
            //获取文件名
            String fileName = file.getOriginalFilename();
            if (StringUtils.isNotBlank(fileName)) {
                //校验上传文件格式是否为excel文件
                if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
                    return R.error().put("msg", "您上传文件不是excel文件！");
                } else {
                    return functionExportService.updateDotDispatchRule(file, brandIds, cycle);
                }
            } else {
                return R.error().put("msg", "您没有上传文件，请确认！");
            }
        } catch (Exception e) {
            return R.error().put("msg", "系统异常:" + e.getMessage());
        }
    }

    /**
     * 批量转单
     *
     * @param file
     * @param
     * @return
     */
    @RequestMapping("/updateDotInformation")
    @ResponseBody
    public R updateDotInformation(@RequestParam("file") MultipartFile file) {
        try {
            //获取文件名
            String fileName = file.getOriginalFilename();
            if (StringUtils.isNotBlank(fileName)) {
                //校验上传文件格式是否为excel文件
                if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
                    return R.error().put("msg", "您上传文件不是excel文件！");
                } else {
                    return functionExportService.updateDotInformation(file);
                }
            } else {
                return R.error().put("msg", "您没有上传文件，请确认！");
            }
        } catch (Exception e) {
            return R.error().put("msg", "系统异常:" + e.getMessage());
        }
    }


    /**
     * 修改增项信息
     * @param file
     * @return
     */
    @RequestMapping("/updateIncre")
    @ResponseBody
    public R updateIncre(@RequestParam("file") MultipartFile file)  {
        try {
            //获取文件名
            String fileName = file.getOriginalFilename();
            if (StringUtils.isNotBlank(fileName)) {
                //校验上传文件格式是否为excel文件
                if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
                    return R.error().put("msg", "您上传文件不是excel文件！");
                } else {
                    return functionExportService.updateIncre(file);
                }
            } else {
                return R.error().put("msg", "您没有上传文件，请确认！");
            }
        }catch (Exception e){
            return R.error().put("msg", "系统异常:"+e.getMessage());
        }
    }

    /**
     * 修改工单模板
     */
    @ResponseBody
    @RequestMapping("/changeTemplate")
    public R changeTemplate(@RequestParam("file") MultipartFile file) {
        //获取文件名
        String fileName = file.getOriginalFilename();
        if (!StringUtils.isNotBlank(fileName)) {
            return R.error().put("msg", "您没有上传文件，请确认！");
        }
        //校验上传文件格式是否为excel文件
        if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
            return R.error().put("msg", "您上传文件不是excel文件！");
        }

        return  functionExportService.changeTemplate(file);
    }

    /**
     * 回退至安装资料无误待车企确认
     */
    @ResponseBody
    @RequestMapping("/revertWorderStatus")
    public R revertWorderStatus(@RequestParam("file") MultipartFile file) {
        //获取文件名
        String fileName = file.getOriginalFilename();
        if (!StringUtils.isNotBlank(fileName)) {
            return R.error().put("msg", "您没有上传文件，请确认！");
        }
        //校验上传文件格式是否为excel文件
        if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
            return R.error().put("msg", "您上传文件不是excel文件！");
        }

        return functionExportService.revertWorderStatus(file);
    }

}

