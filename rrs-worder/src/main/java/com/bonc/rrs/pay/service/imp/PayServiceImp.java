package com.bonc.rrs.pay.service.imp;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayTradeCloseModel;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.bonc.rrs.InitializingBeanAfter;
import com.bonc.rrs.pay.annotation.PayOperLog;
import com.bonc.rrs.pay.dao.PayCreateImageMapper;
import com.bonc.rrs.pay.dao.WorderOrderLogMapper;
import com.bonc.rrs.pay.entity.entity.PayCreateImageDTO;
import com.bonc.rrs.pay.entity.request.CycleQueyRequest;
import com.bonc.rrs.pay.entity.request.GoPayRequestDTO;
import com.bonc.rrs.pay.entity.request.PayInfoRequest;
import com.bonc.rrs.pay.enums.*;
import com.bonc.rrs.pay.manage.AsyncManager;
import com.bonc.rrs.pay.manage.factory.AsyncFactory;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.pay.service.PayService;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dao.WorderTemplateDao;
import com.bonc.rrs.worder.entity.dto.WorderInfoDTO;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.BrandPo;
import com.bonc.rrs.workManager.dao.BizRegionMapper;
import com.common.pay.alipay.config.Configs;
import com.common.pay.alipay.config.Constants;
import com.common.pay.alipay.model.builder.AlipayTradeCancelRequestBuilder;
import com.common.pay.alipay.model.builder.AlipayTradePrecreateRequestBuilder;
import com.common.pay.alipay.model.builder.AlipayTradeQueryRequestBuilder;
import com.common.pay.alipay.model.result.AlipayNotifyParamResult;
import com.common.pay.common.constant.HttpStatus;
import com.common.pay.common.utils.ResponseResult;
import com.common.pay.wxpay.config.WXPayConfig;
import com.common.pay.wxpay.config.WxConfig;
import com.common.pay.wxpay.config.WxConfigImpI;
import com.common.pay.wxpay.enums.WxReturnStatus;
import com.common.pay.wxpay.sdk.WXPay;
import com.common.pay.wxpay.sdk.WXPayConstants;
import com.common.pay.wxpay.util.CommonUtil;
import com.common.pay.wxpay.util.WXPayUtil;
import com.common.pay.zxing.utils.GenerateQrCodeUtil;
import com.gexin.rp.sdk.template.style.AbstractNotifyStyle;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.template.PushStyle;
import com.youngking.lenmoncore.common.utils.*;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.redis.core.Cursor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 支付服务层
 *
 * <AUTHOR>
 * @description
 */
@Service
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
public class PayServiceImp implements PayService {

    @Resource
    private WorderInformationDao worderInformationDao;
    @Resource
    private BizRegionMapper bizRegionMapper;
    @Resource
    private WorderOrderLogMapper worderOrderLogMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private WorderTemplateDao worderTemplateDao;
    @Resource
    private PayCreateImageMapper payCreateImageMapper;

    @Autowired
    private PayService payService;
    private ExecutorService executorService = Executors.newFixedThreadPool(20);

    private Logger logger = LoggerFactory.getLogger(PayServiceImp.class);

    /**
     * 支付管理
     */
    @Override
    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public Object goPay(HttpServletRequest request, GoPayRequestDTO goPayRequest, SysUserEntity userEntity, Date createDate) {
        /** 金额验证 */
        if (goPayRequest.getPayActualAmount().compareTo(new BigDecimal("0.01")) < 0) {
            return ResponseResult.error("实际支付金额不能小于0.01");
        }
        if (goPayRequest.getPayDiscountAmount().compareTo(new BigDecimal(0)) < 0) {
            return ResponseResult.error("优惠金额不能小于0");
        }
        String time = worderOrderLogMapper.getNowTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //转为Date类型
        try {
            createDate = sdf.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Map<String, String> resultMap = new HashMap<>();
        /** 获取支付完成的订单 **/
        List<WorderOrderLogDTO> worderOrderLogDTO = worderOrderLogMapper.findOrderLogInfoList(goPayRequest.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        /** 判断支付完成的订单是否为空，为空就是没有 **/
        if (CollectionUtils.isEmpty(worderOrderLogDTO)) {
            /** 获取支付宝或者微信支付中的订单*/
            WorderOrderLogDTO worderOrderLog = worderOrderLogMapper.getOrderLogInfoListByStates(goPayRequest.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS, goPayRequest.getPayType());
            //首先根据支付类型去查询微信或者支付宝
            /** 如果为空就去创建订单*/
            if (worderOrderLog == null) {
                if (goPayRequest.getPayType() == PayTypeEnum.TYPE_ALIPAY.getStatus()) {
                    /** 支付宝支付 */
                    return ((PayService) AopContext.currentProxy()).aliPayment(goPayRequest, userEntity, createDate);
                } else if (goPayRequest.getPayType() == PayTypeEnum.TYPE_WXPAY.getStatus()) {
                    /** 微信支付 */
                    return ((PayService) AopContext.currentProxy()).wxPay(request, goPayRequest, userEntity, createDate);
                } else {
                    return ResponseResult.error("支付方式不存在");
                }
            } else {
                int num=worderOrderLog.getPayActualAmount().compareTo(goPayRequest.getPayActualAmount());
                if(num!=0){
                    if (goPayRequest.getPayType() == PayTypeEnum.TYPE_ALIPAY.getStatus()) {
                        /** 支付宝支付 */
                        worderOrderLogMapper.updateOrderStatusByWorderNo(worderOrderLog.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL,
                                Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS);
                        return ((PayService) AopContext.currentProxy()).aliPayment(goPayRequest, userEntity, createDate);
                    } else if (goPayRequest.getPayType() == PayTypeEnum.TYPE_WXPAY.getStatus()) {
                        /** 微信支付 */
                        worderOrderLogMapper.updateOrderStatusByWorderNo(worderOrderLog.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL,
                                Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS);
                        return ((PayService) AopContext.currentProxy()).wxPay(request, goPayRequest, userEntity, createDate);
                    } else {
                        return ResponseResult.error("支付方式不存在");
                    }
                }else{
                PayCreateImageDTO imgContentByOrderId = payCreateImageMapper.findImgContentByOrderId(worderOrderLog.getOrderLogId());
                SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                //系统当前时间
                Date nowTime = null;
                try {
                    nowTime = sd.parse(time);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                //二维码生成时间
                Date createTime = worderOrderLog.getCreateTime();
                //相减获取毫秒
                long sumTime = nowTime.getTime() - createTime.getTime();

                Integer timeExpire = Configs.getTimeExpire();
                long millisecond = timeExpire * 60 * 1000;

                //判断支付宝
                if (worderOrderLog.getPayType() == PayTypeEnum.TYPE_ALIPAY.getStatus()) {
                    resultMap.put("content", imgContentByOrderId.getImgContent());
                    resultMap.put("orderNo", worderOrderLog.getOrderNo());
                    //查询支付宝交易接口
                    AlipayTradeQueryResponse response = this.aliPayPayments(worderOrderLog.getOrderNo());
                    logger.info("获取支付宝查询订单信息:{}-------------------------------------/n", response.getBody());
                    //如果订单为空判断二维码是否超过三十分钟，超过三十分钟重新调用支付宝二维码接口，没有超过返回原来二维码
                    if (StringUtils.isBlank(response.getTradeStatus())) {
                        //超过30分钟
                        if (sumTime > millisecond) {
                            /** 更新状态支付数据 */
                            if (worderOrderLogMapper.updateOrderStatus(worderOrderLog.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL) < 1) {
                                logger.error("更新状态支付数据失败,工单编号：{}", worderOrderLog.getWorderNo());
                                throw new RuntimeException(String.format("更新状态支付数据失败,工单编号:%s", worderOrderLog.getWorderNo()));
                            }
                            //超过三十分钟就去重新创建一个订单
                            return ((PayService) AopContext.currentProxy()).aliPayment(goPayRequest, userEntity, createDate);
                        } else {
                            return ResponseResult.success(resultMap);
                        }
                        //交易创建，等待买家付款
                    } else if (AliPayTradeStatusEnum.WAIT_BUYER_PAY.name().equals(response.getTradeStatus())) {
                        if (sumTime >= millisecond) {
                            /** 更新状态支付数据 */
                            if (worderOrderLogMapper.updateOrderStatus(worderOrderLog.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL) < 1) {
                                logger.error("更新状态支付数据失败,工单编号：{}", worderOrderLog.getWorderNo());
                                throw new RuntimeException(String.format("更新状态支付数据失败,工单编号:%s", worderOrderLog.getWorderNo()));
                            }
                            //超过三十分钟就去重新创建一个订单
                            return ((PayService) AopContext.currentProxy()).aliPayment(goPayRequest, userEntity, createDate);
                        } else {
                            return ResponseResult.success(resultMap);
                        }
                        //未付款交易超时关闭，或支付完成后全额退款）
                    } else if (AliPayTradeStatusEnum.TRADE_CLOSED.name().equals(response.getTradeStatus())) {
                        if (worderOrderLogMapper.updateOrderStatus(worderOrderLog.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL) < 1) {
                            logger.error("更新状态支付数据失败,工单编号：{}", worderOrderLog.getWorderNo());
                            throw new RuntimeException(String.format("更新状态支付数据失败,工单编号:%s", worderOrderLog.getWorderNo()));
                        }
                        //重新创建一个订单
                        return ((PayService) AopContext.currentProxy()).aliPayment(goPayRequest, userEntity, createDate);
                        //交易支付成功
                    } else if (AliPayTradeStatusEnum.TRADE_SUCCESS.name().equals(response.getTradeStatus())) {
                        worderOrderLog.setTransactionId(response.getTradeNo());
                        doWorderNotifyInfoPayment(worderOrderLog);
                        AlipayNotifyParamResult param = new AlipayNotifyParamResult();
                        BeanUtils.copyProperties(response, param);
                        doWorderPaymentUserIdAndCompleteTime(param);
                        resultMap.put("code", "10000");
                        return ResponseResult.success(resultMap);
                    }
                    //判断微信流水是否存在
                } else if (worderOrderLog.getPayType() == PayTypeEnum.TYPE_WXPAY.getStatus()) {
                    resultMap.put("content", imgContentByOrderId.getImgContent());
                    resultMap.put("orderNo", worderOrderLog.getOrderNo());
                    //查询微信
                    String queryData = getQueryResqData(worderOrderLog.getOrderNo());
                    try {
                        Map<String, String> respMap = WXPayUtil.xmlToMap(queryData);
                        logger.info("获取微信查询订单信息:{}-------------------------------------/n", queryData);
                        //订单未支付
                        if (PayOperResultCode.NOTPAY.name().equals(respMap.get("trade_state"))) {
                            //超过30分钟
                            if (sumTime > millisecond) {
                                if (worderOrderLogMapper.updateOrderStatus(worderOrderLog.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL) < 1) {
                                    logger.error("更新状态支付数据失败,工单编号：{}", worderOrderLog.getWorderNo());
                                    throw new RuntimeException(String.format("更新状态支付数据失败,工单编号:%s", worderOrderLog.getWorderNo()));
                                }
                                //超过三十分钟就去重新创建一个订单
                                return ((PayService) AopContext.currentProxy()).wxPay(request, goPayRequest, userEntity, createDate);
                            } else {
                                //返回原来的二维码
                                return ResponseResult.success(resultMap);
                            }
                            //订单已支付
                        } else if (PayOperResultCode.SUCCESS.name().equals(respMap.get("trade_state"))) {
                            String transactionId = respMap.get("transaction_id");
                            worderOrderLog.setTransactionId(transactionId);
                            doWorderNotifyInfoPayment(worderOrderLog);
                            doWorderWxmentUserIdAndCompleteTime(respMap);
                            resultMap.put("code", "10000");
                            return ResponseResult.success(resultMap);
                            //订单已关闭
                        } else if (PayOperResultCode.CLOSED.name().equals(respMap.get("trade_state"))) {
                            if (worderOrderLogMapper.updateOrderStatus(worderOrderLog.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL) < 1) {
                                logger.error("更新状态支付数据失败,工单编号：{}", worderOrderLog.getWorderNo());
                                throw new RuntimeException(String.format("更新状态支付数据失败,工单编号:%s", worderOrderLog.getWorderNo()));
                            }
                            /** 微信支付 */
                            return ((PayService) AopContext.currentProxy()).wxPay(request, goPayRequest, userEntity, createDate);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
                return null;
            }
            }
        } else {
            resultMap.put("code", "10000");
            return ResponseResult.success(resultMap);
        }
    }

    @Override
    public void refundMoney() {
        try {
            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
            AlipayTradeRefundModel refundModel = new AlipayTradeRefundModel();
            refundModel.setOutTradeNo("c32669a476ca457eb189980f73a5662e");
            refundModel.setRefundAmount("1");
            request.setBizModel(refundModel);
            AlipayTradeRefundResponse refundResponse = InitializingBeanAfter.getAlipayClient().certificateExecute(request);
            logger.info("退款详情：{}", refundResponse.getBody());
//            AlipayTradeCancelRequest request = new AlipayTradeCancelRequest();
//            AlipayTradeCancelModel cancelModel = new AlipayTradeCancelModel();
//            cancelModel.setOutTradeNo("511f768a24f74104ab44c335ec237a9b");
//            request.setBizModel(cancelModel);
//            AlipayTradeCancelResponse response =  InitializingBeanAfter.getAlipayClient().certificateExecute(request);
//            logger.info("退款详情：{}",response.getBody());
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 支付宝支付生成二维码
     *
     * @param goPayRequest
     * @param userEntity
     * @return
     */
    @Override
    @PayOperLog(payTypeCode = PayTypeEnum.TYPE_ALIPAY, payTypeName = PayTypeEnum.TYPE_ALIPAY)
    public Object aliPay(GoPayRequestDTO goPayRequest, SysUserEntity userEntity, Date createTime) {

        /** 金额验证 */
        if (goPayRequest.getPayActualAmount().compareTo(new BigDecimal("0.01")) < 0) {
            return ResponseResult.error("实际支付金额不能小于0.01");
        }
        if (goPayRequest.getPayDiscountAmount().compareTo(new BigDecimal(0)) < 0) {
            return ResponseResult.error("优惠金额不能小于0");
        }

        /**  获取订单支付请情况 */
        List<WorderOrderLogDTO> worderOrderLogDTOS = worderOrderLogMapper.findOrderLogInfoList(goPayRequest.getWorderNo()
                , Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        if (worderOrderLogDTOS != null && worderOrderLogDTOS.size() > 0) {
            return ResponseResult.result(HttpStatus.NO_CONTENT, String.format("订单不可重复支付,支付流水号:%s", worderOrderLogDTOS.get(0).getOrderNo()));
        }

        /** 数据验证 */
        ResponseResult result = aliPayVolidata(goPayRequest);
        if (result != null) {
            logger.warn("工单信息验证不通过,请求信息：{},返回报文：{}", goPayRequest, result);
            return result;
        }
        /** 获取工单信息 */
        WorderInfoDTO worderInfoDTO = worderInformationDao.findWorderInfo(goPayRequest.getWorderNo());
        if (worderInfoDTO == null) {
            logger.warn("工单信息不存在,请求信息：{}", goPayRequest);
            throw new RuntimeException("工单信息不存在");
        }
        WorderOrderLogDTO worderOrderLogDTO = new WorderOrderLogDTO();
        /** 支付宝-预支付-生成二维码 */
        result = prepaid(goPayRequest, worderInfoDTO, worderOrderLogDTO);
        if (!"200".equals(String.valueOf(result.get(ResponseResult.CODE_TAG)))) {
            return result;
        }
        /** 收款人 */
        worderOrderLogDTO.setToUserId(userEntity.getUserId());
        worderOrderLogDTO.setCreateTime(createTime);
        /** 生成二维码成功（数据库处理） */
        /** 支付数据入库 */
        Object object = result.get("data");
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(object);
        respDataPostProccessor(worderOrderLogDTO, jsonObject.getString("content"));
        /** 记录当前的流水号 */
        redisUtils.set(String.format("ALIPAY:%s:%s", Constant.REDISPARAMS.ORDERNO, worderOrderLogDTO.getOrderNo()), userEntity.getUserId());
        return result;
    }

    @Override
    public Object aliPayment(GoPayRequestDTO goPayRequest,
                             SysUserEntity userEntity, Date createTime) {
        ResponseResult result = null;
        /** 获取工单信息 */
        WorderInfoDTO worderInfoDTO = worderInformationDao.findWorderInfo(goPayRequest.getWorderNo());
        WorderOrderLogDTO worderOrderLogDTO = new WorderOrderLogDTO();
        /** 支付宝-预支付-生成二维码 */
        result = prepaid(goPayRequest, worderInfoDTO, worderOrderLogDTO);
        if (!"200".equals(String.valueOf(result.get(ResponseResult.CODE_TAG)))) {
            return result;
        }
        /** 收款人 */
        worderOrderLogDTO.setToUserId(userEntity.getUserId());
        worderOrderLogDTO.setCreateTime(createTime);
        /** 生成二维码成功（数据库处理） */
        /** 支付数据入库 */
        Object object = result.get("data");
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(object);
        respDataPostProccessorPayment(worderOrderLogDTO, jsonObject.getString("content"));
        /** 记录当前的流水号 */
        redisUtils.set(String.format("ALIPAY:%s:%s", Constant.REDISPARAMS.ORDERNO, worderOrderLogDTO.getOrderNo()), userEntity.getUserId());
        return result;
    }

    /**
     * 阿里支付验证
     *
     * @param goPayRequest
     * @return
     */
    ResponseResult aliPayVolidata(GoPayRequestDTO goPayRequest) {
        String orderStatus = Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS + "," + Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS;
        String[] arr = orderStatus.split(",");
        WorderOrderLogDTO worderOrderLogDTO = worderOrderLogMapper.findOrderLogInfoListByStates(goPayRequest.getWorderNo(), Arrays.asList(arr));
        if (worderOrderLogDTO != null) {
            List<Object> list = new ArrayList<>();
            /** 对数据进行处理 */
            if (worderOrderLogDTO.getOrderStatus().equals(PayResultEnum.PAY_SUCEESS.getStatus())) {
                list.add(queryReturnInfo(worderOrderLogDTO.getOrderNo(), WxReturnStatus.SUCCESS.name()));
            } else {
                if (worderOrderLogDTO.getPayType().intValue() == 1) {
                    /**  */
//                    PayCreateImageDTO payCreateImageDTO = payCreateImageMapper.findImgContentByOrderId(worderOrderLogDTO.getOrderLogId());
//                    if(payCreateImageDTO.getIntervals() <= Configs.getHeartbeatDuration()){
//                        Map<String,String> resultMap = new HashMap<>();
//                        resultMap.put("content",payCreateImageDTO.getImgContent());
//                        resultMap.put("orderNo",worderOrderLogDTO.getOrderNo());
//                        return  ResponseResult.success(String.format("操作成功,支付流水:%s",worderOrderLogDTO.getOrderNo()),resultMap);
//                    }
                    /** 支付宝 */
                    ResponseResult responseResult = doOrdeVolidataWithAliPayQuery(list, worderOrderLogDTO);
                    if (responseResult != null) {
                        return responseResult;
                    }
                } else if (worderOrderLogDTO.getPayType().intValue() == 2) {
                    /** 微信 */
                    Map<String, String> map = orderQuery(worderOrderLogDTO.getOrderNo());
                    if (WxReturnStatus.SUCCESS.name().equals(map.get("return_code"))) {
                        list.add(map);
                    }
                } else {
                    list.add("支付类型错误");
                }

            }
            if (list.size() > 0) {
                logger.info("支付成功的订单，无法再次生成二维码,内容：{}", list);
                return ResponseResult.result(HttpStatus.NO_CONTENT, "支付成功的订单，无法再次生成二维码 ", list);
            }
        }
        return null;
    }

    /**
     * 支付宝的订单处理
     *
     * @param list
     * @param worderOrderLogDTO
     */
    ResponseResult doOrdeVolidataWithAliPayQuery(List<Object> list, WorderOrderLogDTO worderOrderLogDTO) {
        AlipayTradeQueryResponse alipayTradeQueryResponse = aliPayPayments(worderOrderLogDTO.getOrderNo());
        logger.info("订单信息查询：{}", alipayTradeQueryResponse.getBody());

        boolean flag = Constants.SUCCESS.equals(alipayTradeQueryResponse.getCode());
        if (!flag) {
            return null;
        }
        if (AliPayTradeStatusEnum.WAIT_BUYER_PAY.name().equals(alipayTradeQueryResponse.getTradeStatus())) {
            return null;
        }
        if (AliPayTradeStatusEnum.TRADE_SUCCESS.name().equals(alipayTradeQueryResponse.getTradeStatus())) {
            list.add(String.format("支付宝-订单查询,%s，详情信息：%s", AliPayTradeStatusEnum.getDescByName(alipayTradeQueryResponse.getTradeStatus())
                    , alipayTradeQueryResponse.getBody()));
        }
        logger.info("当前订单状态:{}", AliPayTradeStatusEnum.getDescByName(alipayTradeQueryResponse.getTradeStatus()));
        return null;
    }

    /**
     * 关闭订单撤销
     *
     * @param outTradeNo
     * @return
     */
    AlipayTradeCancelResponse aliPayCancelTrade(String outTradeNo) {
        try {
            AlipayTradeCancelRequest request = new AlipayTradeCancelRequest();
            AlipayTradeCancelRequestBuilder bizContent = new AlipayTradeCancelRequestBuilder();
            bizContent.setOutTradeNo(outTradeNo);
            request.setBizContent(bizContent.toJsonString());
            return InitializingBeanAfter.getAlipayClient().execute(request);
        } catch (AlipayApiException e) {
            logger.error("工单交易撤销信息异常,工单编号:{},返回信息:{}", outTradeNo);
            throw new RuntimeException("工单交易撤销信息异常");
        }
    }

    /**
     * 关闭订单交易
     *
     * @param outTradeNo
     * @return
     */
    AlipayTradeCloseResponse aliPayCloseTrade(String outTradeNo) {
        try {
            /** 关闭订单 */
            logger.info("工单交易开始进行关闭,工单编号:{}", outTradeNo);
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            AlipayTradeCloseModel bizContent = new AlipayTradeCloseModel();
            bizContent.setOutTradeNo(outTradeNo);
            request.setBizModel(bizContent);
            logger.info("工单交易开始进行关闭,请求信息:{}", bizContent);
            return InitializingBeanAfter.getAlipayClient().certificateExecute(request);
        } catch (AlipayApiException e) {
            logger.error("工单交易关闭信息异常,工单编号:{}", outTradeNo);
            throw new RuntimeException("工单交易关闭信息异常", e);
        }
    }


    /**
     * 支付宝-查询支付信息
     *
     * @param orderNO
     */
    AlipayTradeQueryResponse aliPayPayments(String orderNO) {
        try {
            AlipayTradeQueryRequest alipayTradeQueryRequest = new AlipayTradeQueryRequest();
            AlipayTradeQueryRequestBuilder alipayTradeQueryRequestBuilder = new AlipayTradeQueryRequestBuilder();
            alipayTradeQueryRequestBuilder.setOutTradeNo(orderNO);
            alipayTradeQueryRequest.setBizContent(alipayTradeQueryRequestBuilder.toJsonString());
            AlipayTradeQueryResponse alipayTradeQueryResponse = InitializingBeanAfter.getAlipayClient().certificateExecute(alipayTradeQueryRequest);
            return alipayTradeQueryResponse;
        } catch (AlipayApiException e) {
            logger.error("查询工单信息异常,工单编号:{}", orderNO);
            throw new RuntimeException("查询工单信息异常");
        }
    }

    /**
     * 支付宝退款接口
     *
     * @param outTradeNo
     * @param refundAmount
     * @return
     * @throws AlipayApiException
     */
    AlipayTradeRefundResponse aliRefundResponse(String outTradeNo, String refundAmount,String outRequestTradeNo) throws AlipayApiException {
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        AlipayTradeRefundModel refundModel = new AlipayTradeRefundModel();

        refundModel.setOutTradeNo(outTradeNo);
        refundModel.setRefundAmount(refundAmount);
        refundModel.setOutRequestNo(outRequestTradeNo);
        request.setBizModel(refundModel);
        AlipayTradeRefundResponse refundResponse = InitializingBeanAfter.getAlipayClient().certificateExecute(request);
        return refundResponse;
    }

    /**
     * 、
     * 支付宝退款接口查询
     *
     * @param outTradeNo
     * @return
     * @throws AlipayApiException
     */
    AlipayTradeFastpayRefundQueryResponse selectAliRefundResponse(String outTradeNo,String outRequestTradeNo) throws AlipayApiException {
        //String outRequestTradeNo = UUID.randomUUID().toString().trim().replaceAll("-", "");
        AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
        AlipayTradeRefundModel refundModel = new AlipayTradeRefundModel();
        refundModel.setOutTradeNo(outTradeNo);
        List<String> list=new ArrayList<>();
        list.add("refund_status");
        refundModel.setQueryOptions(list);
        refundModel.setOutRequestNo(outRequestTradeNo);
        request.setBizModel(refundModel);
        AlipayTradeFastpayRefundQueryResponse response = InitializingBeanAfter.getAlipayClient().certificateExecute(request);
        return response;
    }

    /**
     * 支付宝-预支付-生成二维码
     *
     * @param goPayRequest
     */
    ResponseResult prepaid(GoPayRequestDTO goPayRequest, WorderInfoDTO worderInfoDTO, WorderOrderLogDTO worderOrderLogDTO) {
        /** 判断是否工单是否以及支付 */
        try {
            /** 获取工单模板信息 */
            WorderTemplateDto worderTemplateDto = worderTemplateDao.findTemplateInfoById(worderInfoDTO.getTemplateId());
            /** 获取品牌信息 */
            BrandPo brandPo = worderInformationDao.getBrand(worderInfoDTO.getTemplateId());
            /** 生成商户交易号 */
            String outTradeNo = UUID.randomUUID().toString().trim().replaceAll("-", "");
            //String outTradeNo="aae7acbc4aeb49e1aacb1ccef292b9f0";
            /** 预支付对象 */
            AlipayTradePrecreateRequest preCreateRequest = new AlipayTradePrecreateRequest();
            /** 回调地址 */
            preCreateRequest.setNotifyUrl(Configs.getNotifyUrl());
            /** 组装支付内容 */
            preCreateRequest.setBizContent(constructPrepaidObj(goPayRequest, outTradeNo, worderTemplateDto));
            /** 获取qr_code */
            logger.info("preCreateRequest:{}-------------------------------------/n", preCreateRequest.getBizContent());
            AlipayTradePrecreateResponse precreateResponse = InitializingBeanAfter.getAlipayClient().certificateExecute(preCreateRequest);
            logger.info("获取qrcode:{}-------------------------------------/n", precreateResponse.getBody());
            /** 组装入库信息 */
            inserDataPostPorccessor(goPayRequest, worderOrderLogDTO, worderInfoDTO, outTradeNo, brandPo);
            /** 生成二维码 */
            return generateAliPrecreateErCode(precreateResponse);
        } catch (AlipayApiException e) {
            logger.error("支付宝预支付支付请求失败:{},{}", e.getErrCode(), e.getErrMsg());
            throw new RuntimeException("支付宝预支付支付请求失败");
        }
    }

    /**
     * 构造预支付的请求文本
     *
     * @param goPayRequest
     * @return
     */
    String constructPrepaidObj(GoPayRequestDTO goPayRequest, String outTradeNo, WorderTemplateDto worderTemplateDto) {

        AlipayTradePrecreateRequestBuilder alipayTrade = new AlipayTradePrecreateRequestBuilder();
        /** 商户交易号 */
        alipayTrade.setOutTradeNo(outTradeNo);
        /** 门店编号 */
//        alipayTrade.setStoreId("SH_001");
//        alipayTrade.setSellerId(Configs.getPid());
        /** 【订单总金额】=【打折金额】+【不可打折金额】 */
        alipayTrade.setTotalAmount(String.valueOf(goPayRequest.getPayActualAmount()));
        /** 【订单总金额】-【不可打折金额】  */
//        if(goPayRequest.getMaxNumber().compareTo(goPayRequest.getPayActualAmount()) > 0){
//            alipayTrade.setDiscountableAmount(String.valueOf(goPayRequest.getMaxNumber().subtract(goPayRequest.getPayActualAmount())));
//        }
        /** 订单标题（工单模版的名称） */
        String sub = "未知";
        if (worderTemplateDto != null) {
            sub = worderTemplateDto.getTemplateName();
        }
        alipayTrade.setSubject(sub);
        /** 订单支付的最后时间是10分钟 */
        alipayTrade.setTimeoutExpress(Configs.getTimeExpire() + "m");
        logger.info("支付信息：{}", alipayTrade);
//        alipayTrade.validate();
        return alipayTrade.toJsonString();
    }

    /**
     * 生成阿里支付二维码
     *
     * @param precreateResponse
     * @return
     */
    public ResponseResult generateAliPrecreateErCode(AlipayTradePrecreateResponse precreateResponse) {
        try {
            if (!Constants.SUCCESS.equals(precreateResponse.getCode())) {
                logger.error("获取code_url失败,返回消息:{}", JSONObject.toJSONString(precreateResponse));
                return ResponseResult.error(String.format("获取code_url失败,返回消息:%s", JSONObject.toJSONString(precreateResponse)));
            }
            /** 生成二维码,以流的方式进行传输 */
            String base64Content = GenerateQrCodeUtil.encodeQrcodeBase64(precreateResponse.getQrCode());
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("content", base64Content);
            resultMap.put("orderNo", precreateResponse.getOutTradeNo());
            return ResponseResult.success(String.format("操作成功,支付流水:%s", precreateResponse.getOutTradeNo()), resultMap);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    /**
     * 微信支付
     */
    @Override
    @PayOperLog(payTypeCode = PayTypeEnum.TYPE_WXPAY)
    public Object wxPay(HttpServletRequest request, GoPayRequestDTO goPayRequest, SysUserEntity userEntity, Date createTime) {
        /* *//** 金额验证 *//*
        if(goPayRequest.getPayActualAmount().compareTo(new BigDecimal("0.01")) < 0){
            return ResponseResult.error("实际支付金额不能小于0.01");
        }
        if(goPayRequest.getPayDiscountAmount().compareTo(new BigDecimal(0)) < 0){
            return ResponseResult.error("优惠金额不能小于0");
        }

        *//**  获取订单支付请情况 *//*
        List<WorderOrderLogDTO> worderOrderLogDTOS =  worderOrderLogMapper.findOrderLogInfoList(goPayRequest.getWorderNo()
                ,Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        if(worderOrderLogDTOS != null && worderOrderLogDTOS.size()  > 0){
            return ResponseResult.result(HttpStatus.NO_CONTENT,String.format("订单不可重复支付,支付流水号:%s",worderOrderLogDTOS.get(0).getOrderNo()));
        }*/
        /** 微信支付 */
        /**   对象的处理   */
        SortedMap<String, String> parameters = new TreeMap<String, String>();
        WorderOrderLogDTO worderOrderLogDTO = new WorderOrderLogDTO();
        /** 处理判断订单支付是否成功 *//*
        ResponseResult responseResult =   aliPayVolidata(goPayRequest);
        if(responseResult!=null){
            return responseResult;
        }*/
        ResponseResult responseResult = null;
        worderOrderLogDTO.setCreateTime(createTime);
        /** 获取预支付的返回文本 */
        String respData = getRespData(goPayRequest, parameters, worderOrderLogDTO, request);
        if (respData == null) {
            return ResponseResult.error("支付的返回文本错误");
        }
        /** 生成二维码 */
        responseResult = generateErCode(respData, worderOrderLogDTO.getOrderNo());
        /** 对返回的消息进行处理 */
        if (!String.valueOf(HttpStatus.SUCCESS).equals(String.valueOf(responseResult.get(ResponseResult.CODE_TAG)))) {
            return responseResult;
        }
        /** 收款人 */
        worderOrderLogDTO.setToUserId(userEntity.getUserId());
        /** 支付数据入库 */
        Object object = responseResult.get("data");
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(object);
        respDataPostProccessorPayment(worderOrderLogDTO, jsonObject.getString("content"));

        /** 记录当前的流水号 */
        redisUtils.set(String.format(Constant.REDISPARAMS.ORDERNO + ":%s", worderOrderLogDTO.getOrderNo()), userEntity.getUserId());
        return responseResult;
    }

    /**
     * 阿里支付 - 防止支付成功二次生成二维码
     *
     * @param worderNo
     * @return
     */
    public ResponseResult volidatePaySuccessToGenerateErcode(String worderNo) {
        String orderStatus = Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS + "," + Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS;
        String[] arr = orderStatus.split(",");
        WorderOrderLogDTO worderOrderLogDTO = worderOrderLogMapper.findOrderLogInfoListByStates(worderNo, Arrays.asList(arr));
        if (worderOrderLogDTO != null) {
            List<Map<String, String>> list = new ArrayList<>();
            /** 对数据进行处理 */
            if (worderOrderLogDTO.getOrderStatus().equals(PayResultEnum.PAY_SUCEESS.getStatus())) {
                list.add(queryReturnInfo(worderOrderLogDTO.getOrderNo(), WxReturnStatus.SUCCESS.name()));
            }
            if (list.size() > 0) {
                logger.info("支付成功的订单，无法再次生成二维码,内容：{}", list);
                return ResponseResult.result(HttpStatus.NO_CONTENT, "支付成功的订单，无法再次生成二维码 ", list);
            }
        }
        return null;
    }


    @Override
    public Object synchronouNotify(HttpServletRequest request) {
        try {
            InputStream inStream = request.getInputStream();
            ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inStream.read(buffer)) != -1) {
                outSteam.write(buffer, 0, len);
            }
            String resultxml = new String(outSteam.toByteArray(), "utf-8");
            Map<String, String> map = WXPayUtil.xmlToMap(resultxml);
            outSteam.close();
            inStream.close();
            logger.info("回调数据：{}", map);
            /** 对数据进行验签 */
            /*if(WXPayUtil.isSignatureValid(map,WxConfig.getApiKey())){*/
            /** 验签成功处理 */
            return returnInfo(afterVolidateSignSuccessPostProcessor(map), map);
            //}
            //return returnInfo(WxReturnStatus.FAIL.name(),map);
        } catch (Exception e) {
            throw new RuntimeException(String.format("回调处理异常，异常信息：%s", e.getMessage()));
        }
    }

    /**
     * 回调消息返回
     */
    public String returnInfo(String code, Map<String, String> respMap) {
        Map<String, String> map = new HashMap<>();
        if (WxReturnStatus.SUCCESS.name().equals(code)) {
            /** 删除redis中的数据 */
            String outTradeNo = respMap.get("out_trade_no");
            logger.info("删除redis中的key:{}", Constant.REDISPARAMS.ORDERNO + ":" + outTradeNo);
            redisUtils.delete(Constant.REDISPARAMS.ORDERNO + ":" + outTradeNo);
            map.put("return_code", WxReturnStatus.SUCCESS.name());
            map.put("return_msg", String.format("支付成功,支付流水:%s", outTradeNo));
        } else {
            map.put("return_code", WxReturnStatus.FAIL.name());
            map.put("return_msg", respMap.get("return_msg"));
        }
        try {
            return WXPayUtil.mapToXml(map);
        } catch (Exception e) {
            logger.error("xml转化出错");
            throw new RuntimeException("xml转化出错");
        }
    }

    public void doWorderWxmentUserIdAndCompleteTime(Map<String, String> map){
        String userId=null;
        String completeTime=null;
        if(StringUtils.isNotBlank(map.get("openid"))){
            userId=map.get("openid");
        }
        if(StringUtils.isNotBlank(map.get("time_end"))){
            SimpleDateFormat sdf =new SimpleDateFormat("yyyyMMddHHmmss");
            try {
                Date parse = sdf.parse(map.get("time_end"));
                SimpleDateFormat sdfs =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                completeTime = sdfs.format(parse);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        worderOrderLogMapper.updateOpenIdByOrderNo(map.get("out_trade_no"),userId,completeTime);
    }
    /**
     * 对验签成功之后的数据进行处理
     */
    public String afterVolidateSignSuccessPostProcessor(Map<String, String> map) {
        logger.info("打印微信支付的map{}=============================="+map.toString());
        if (WxReturnStatus.SUCCESS.name().equals(map.get("return_code"))) {
            logger.info("消息返回成功");
            /** 数据进行验证处理,判断订单的金额和订单编号是否相同 */
            //操作流水（商户订单号）
            String outTradeNo = map.get("out_trade_no");
            //订单总金额
            String totalFee = map.get("total_fee");
            WorderOrderLogDTO worderOrderLogDTO = worderOrderLogMapper.findConstantStatusOrderLogInfoByOrderNo(outTradeNo, Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
            if (worderOrderLogDTO != null) {
                logger.info("支付订单已经完成支付,支付流水：{}", outTradeNo);
                return WxReturnStatus.SUCCESS.name();
            }
            /**  获取订单的状态  */
            logger.info("map信息:{}", JSONObject.toJSONString(map));
            worderOrderLogDTO = worderOrderLogMapper.findOrderLogInfoByOrderNo(outTradeNo);
            if (worderOrderLogDTO == null) {
                logger.error("支付订单信息不存在,支付流水：{}", outTradeNo);
                throw new RuntimeException(String.format("支付订单信息不存在,支付流水：%s", outTradeNo));
            }
            logger.info("订单操作流水：" + outTradeNo + ",已处理");
            //TODO sql进行修改
            if (worderOrderLogMapper.updateOrderStatusByOrderNo(outTradeNo, Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS
                    , map.get("transaction_id"), new BigDecimal(totalFee).divide(new BigDecimal(100))) < 1) {
                logger.error("验签成功之后的数据进行处理失败");
                throw new RuntimeException("验签成功之后的数据进行处理失败");
            }
            doWorderWxmentUserIdAndCompleteTime(map);
            //把不是该流水号的状态改为4(作废)
            worderOrderLogMapper.updateStatusByOrderNo(outTradeNo, Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL, worderOrderLogDTO.getWorderNo());
            logger.info("返回消息体:{}", JSONObject.toJSONString(worderOrderLogDTO));
            /** 更新工单信息 */
            worderInformationDao.updateWorderInfoAmount(worderOrderLogDTO.getWorderNo(), worderOrderLogDTO.getPayDiscountAmount(), worderOrderLogDTO.getPayActualAmount());
            logger.info("工单更新成功");
            /** 调用acs收款数据 */
            AsyncManager.instance().execute(AsyncFactory.asyncBilling(outTradeNo, worderOrderLogDTO.getToUserId()), 1000);
            /** 消息推送 */
            pushMsg(String.valueOf(worderOrderLogDTO.getToUserId()), worderOrderLogDTO.getOrderNo(), worderOrderLogDTO.isPushState(), true);

            return WxReturnStatus.SUCCESS.name();
        }
        /** 消息返回失败处理 */
        msgCallback(Constant.REDISPARAMS.COUNT);
        return WxReturnStatus.FAIL.name();
    }

    /**
     * 消息回调失败处理
     */
    public void msgCallback(long count) {
        Cursor<String> cursor = redisUtils.scan(Constant.REDISPARAMS.ORDERNO + "*", count);
        String key = "";
        String orderNo = "";
        while (cursor.hasNext()) {
            /** 数据处理 */
            key = cursor.next();
            String[] orderNos = key.split(":");
            orderNo = orderNos[1];
            Map<String, String> map = orderQuery(orderNo);
            if (WxReturnStatus.SUCCESS.name().equals(map.get("return_code"))) {
                redisUtils.delete(key);
                if (cursor.getCursorId() != 0) {
                    msgCallback(cursor.getCursorId());
                }
            }
        }
    }

    /**
     * 消息推送处理
     */
    public void pushMsg(String userId, String orderNo, boolean pushState, boolean flag) {
        logger.info("进入消息推送,操作流水号：{}", orderNo);
        if (pushState) {
            logger.info("消息已推送获取CID,操作流水号：{}", orderNo);
            return;
        }
        logger.info("消息开始推送,操作流水号：{}", orderNo);
        String cid = redisUtils.get(userId + "-cid");
        logger.info("cid:{}", cid);
        if (!StringUtils.isNotBlank(cid)) {
            logger.error("cid不能为空,用户ID:{}", userId);
//           throw new RuntimeException(String.format("用户cid不能为空，用户ID:%s",userId));
            return;
        }
        AbstractNotifyStyle style = null;
        logger.info("消息推送，userId:{},orderNo:{},pushState:{},flag:{}", userId, orderNo, pushState, flag);
        String result = "error";
        if (flag) {
            style = PushStyle.paySuccess("收款成功");
            result = "success";
        } else {
            style = PushStyle.payFail();
            result = "failure";
        }
        /** 更新推送状态 */
        worderOrderLogMapper.updatePushState(orderNo, flag);
        UniPushUtils.pushToSingle(cid, style);
        UniPushUtils.pushToSingleTransmission(cid, result);
    }

    @Override
    public Object test() {
        //AsyncManager.instance().execute(AsyncFactory.asyncBilling("91eeb17ceb8044bc989d613801305a38",66L),1000);
        //pushMsg("66");
        //pushMsg("271","SHSSXQ2020-05-050004",false,true);
        ResponseResult responseResult = volidatePaySuccessToGenerateErcode("SHSSXQ2020-06-010003");
        System.out.println(responseResult);
        if (responseResult != null) {
            return responseResult;
        }
        System.out.println("未支付");
        return R.ok();
    }

    @Override
    public Object findPayInfo(PayInfoRequest payInfoRequest) {
        List<WorderOrderLogDTO> worderOrderLog = worderOrderLogMapper.findOrderLogInfoList(payInfoRequest.getWorderNo()
                , Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        if (worderOrderLog != null && worderOrderLog.size() > 0) {
            return R.ok().putList(worderOrderLog.get(0));
        }
        return R.error(301, "数据不存在");
    }

    @Override
    public Map<String, String> orderQuery(String outTradeNo) {
        String respData = getQueryResqData(outTradeNo);
        /** 数据的处理 */
        return orderQueryRespDataPostProcessor(respData, outTradeNo);
    }

    /**
     * 获取订单列表
     * 先从数据库中去查询，若支付成功则进行返回;
     * 失败则调用微信查询接口，若支付成功，则对数据库的数据进行处理;如果若未支付成功，判断是否关闭该订单
     */
    @Override
    public Object payCycleQuey(CycleQueyRequest cycleQueyRequest) {
        ResponseResult responseResult = ResponseResult.success(String.format("支付成功,支付流水:%s", cycleQueyRequest.getOrderNo()));

        if (cycleQueyRequest == null) {
            throw new RuntimeException("订单请求参数不能为空");
        }

        /** 获取订单信息  */
        WorderOrderLogDTO worderOrderLogDTO = worderOrderLogMapper.findOrderLogInfoByOrderNo(cycleQueyRequest.getOrderNo());

        if (worderOrderLogDTO == null) {
            throw new RuntimeException(String.format("支付流水不存在，流水号:%s", cycleQueyRequest.getOrderNo()));
        }

        if (worderOrderLogDTO.getOrderStatus() == PayResultEnum.PAY_SUCEESS.getStatus()) {
            return ResponseResult.success(String.format("支付成功,支付流水:%s", cycleQueyRequest.getOrderNo()));
        }

        if (worderOrderLogDTO.getPayType().intValue() == PayTypeEnum.TYPE_WXPAY.getStatus()) {
            /** 调用微信查询接口进行处理 */
            responseResult = wxCyclePay(worderOrderLogDTO, cycleQueyRequest);
        }
        if (worderOrderLogDTO.getPayType().intValue() == PayTypeEnum.TYPE_ALIPAY.getStatus()) {
            /** 调用支付宝查询接口进行处理 */
            responseResult = aliPayCyclePay(worderOrderLogDTO, cycleQueyRequest);
        }
        return responseResult;
    }

    /**
     * 微信轮询处理
     *
     * @param worderOrderLogDTO
     * @param cycleQueyRequest
     * @return
     */
    ResponseResult wxCyclePay(WorderOrderLogDTO worderOrderLogDTO, CycleQueyRequest cycleQueyRequest) {
        ResponseResult responseResult = ResponseResult.success(String.format("支付成功,支付流水:%s", cycleQueyRequest.getOrderNo()));
        ;
        if (worderOrderLogDTO.getOrderStatus() != PayResultEnum.PAY_SUCEESS.getStatus()) {
            String respData = getQueryResqData(cycleQueyRequest.getOrderNo());
            responseResult = doQueryOrder(respData, cycleQueyRequest.getOrderNo());
            if (responseResult.get(ResponseResult.CODE_TAG).equals(String.valueOf(HttpStatus.SUCCESS))) {
                return responseResult;
            }
        }
        /** 关闭订单接口 */
        /*if(cycleQueyRequest.isCloseOrderStatus() && worderOrderLogDTO.getOrderStatus() !=  PayResultEnum.PAY_SUCEESS.getStatus()){
            List<WorderOrderLogDTO> worderOrderLogDTOs = new ArrayList<>();
            worderOrderLogDTOs.add(worderOrderLogDTO);
            closeOrder(worderOrderLogDTOs);
            *//** 移除redis内容 *//*
            logger.info("删除redis中的key:{}",Constant.REDISPARAMS.ORDERNO+":"+cycleQueyRequest.getClass());
            redisUtils.delete(Constant.REDISPARAMS.ORDERNO+":"+cycleQueyRequest.getClass());
            return  ResponseResult.success(String.format("订单关闭成功,支付流水:%s",cycleQueyRequest.getOrderNo()));
        }*/
        return responseResult;
    }


    ResponseResult wxQueryPay(WorderOrderLogDTO worderOrderLogDTO, CycleQueyRequest cycleQueyRequest) {
        ResponseResult responseResult = ResponseResult.success(String.format("支付成功,支付流水:%s", cycleQueyRequest.getOrderNo()));
        ;
        if (worderOrderLogDTO.getOrderStatus() != PayResultEnum.PAY_SUCEESS.getStatus()) {
            String respData = getQueryResqData(cycleQueyRequest.getOrderNo());
            responseResult = doQueryOrder(respData, cycleQueyRequest.getOrderNo());
            if (responseResult.get(ResponseResult.CODE_TAG).equals(String.valueOf(HttpStatus.SUCCESS))) {
                return responseResult;
            }
        }

        /** 关闭订单接口 */
        if (cycleQueyRequest.isCloseOrderStatus() && worderOrderLogDTO.getOrderStatus() != PayResultEnum.PAY_SUCEESS.getStatus()) {
            List<WorderOrderLogDTO> worderOrderLogDTOs = new ArrayList<>();
            worderOrderLogDTOs.add(worderOrderLogDTO);
            closeOrder(worderOrderLogDTOs);
            /** 移除redis内容 */
            logger.info("删除redis中的key:{}", Constant.REDISPARAMS.ORDERNO + ":" + cycleQueyRequest.getClass());
            redisUtils.delete(Constant.REDISPARAMS.ORDERNO + ":" + cycleQueyRequest.getClass());
            return ResponseResult.success(String.format("订单关闭成功,支付流水:%s", cycleQueyRequest.getOrderNo()));
        }
        return responseResult;
    }


    /**
     * 支付宝轮询处理
     *
     * @param worderOrderLogDTO
     * @param cycleQueyRequest
     * @return
     */
    ResponseResult aliPayCyclePay(WorderOrderLogDTO worderOrderLogDTO, CycleQueyRequest cycleQueyRequest) {
        ResponseResult responseResult = ResponseResult.success(String.format("支付成功,支付流水:%s", cycleQueyRequest.getOrderNo()));
        AlipayTradeQueryResponse response = null;
        if (!PayResultEnum.PAY_SUCEESS.getStatus().equals(worderOrderLogDTO.getOrderStatus())) {
            response = this.aliPayPayments(worderOrderLogDTO.getOrderNo());
            if (response.isSuccess()) {
                if (AliPayTradeStatusEnum.TRADE_SUCCESS.name().equals(response.getTradeStatus())) {
                    /**  支付成功处理 */
                    AlipayNotifyParamResult param = new AlipayNotifyParamResult();
                    BeanUtils.copyProperties(response, param);
                    doWorderNotifyInfo(param);
                    /** 删除缓存 */
                    redisUtils.delete(String.format("ALIPAY:%s:%s", Constant.REDISPARAMS.ORDERNO, param.getOutTradeNo()));
                    return ResponseResult.success(String.format("%s,订单流水:%s", AliPayTradeStatusEnum.getDescByName(response.getTradeStatus())
                            , cycleQueyRequest.getOrderNo()));
                }
            } else {
                return ResponseResult.result(PayOperResultCode.NOTPAY.getStatus(), String.format("订单已预创建,%s,订单流水:%s", response.getSubMsg(), cycleQueyRequest.getOrderNo()));
            }
        }

        /** 关闭订单接口 */
        if (cycleQueyRequest.isCloseOrderStatus() && !PayResultEnum.PAY_SUCEESS.getStatus().equals(worderOrderLogDTO.getOrderStatus())) {
            aliPayCloseTrade(worderOrderLogDTO.getOrderNo());
            redisUtils.delete(String.format("ALIPAY:%s:%s", Constant.REDISPARAMS.ORDERNO, worderOrderLogDTO.getOrderNo()));
            return ResponseResult.success(String.format("订单关闭成功,支付流水:%s", worderOrderLogDTO.getOrderNo()));
        }
        if (response.isSuccess() && AliPayTradeStatusEnum.WAIT_BUYER_PAY.name().equals(response.getTradeStatus())) {
            return ResponseResult.result(Integer.valueOf(AliPayTradeStatusEnum.WAIT_BUYER_PAY.getCode()), String.format("%s,订单流水:%s", AliPayTradeStatusEnum.WAIT_BUYER_PAY.getDesc()
                    , cycleQueyRequest.getOrderNo()));
        }
        return responseResult;
    }


    AlipayTradeQueryResponse aliPayQuery(WorderOrderLogDTO worderOrderLogDTO, CycleQueyRequest cycleQueyRequest) {
        ResponseResult responseResult = ResponseResult.success(String.format("支付成功,支付流水:%s", cycleQueyRequest.getOrderNo()));
        AlipayTradeQueryResponse response = null;
        if (!PayResultEnum.PAY_SUCEESS.getStatus().equals(worderOrderLogDTO.getOrderStatus())) {
            response = this.aliPayPayments(worderOrderLogDTO.getOrderNo());
            if (response.isSuccess()) {
                if (AliPayTradeStatusEnum.TRADE_SUCCESS.name().equals(response.getTradeStatus())) {
                    /**  支付成功处理 */
                    AlipayNotifyParamResult param = new AlipayNotifyParamResult();
                    BeanUtils.copyProperties(response, param);
                    doWorderNotifyInfo(param);
                    /** 删除缓存 */
                    redisUtils.delete(String.format("ALIPAY:%s:%s", Constant.REDISPARAMS.ORDERNO, param.getOutTradeNo()));
                    return response;
                }
            } else {
                return response;
            }
        }

        /** 关闭订单接口 */
        if (cycleQueyRequest.isCloseOrderStatus() && !PayResultEnum.PAY_SUCEESS.getStatus().equals(worderOrderLogDTO.getOrderStatus())) {
            aliPayCloseTrade(worderOrderLogDTO.getOrderNo());
            redisUtils.delete(String.format("ALIPAY:%s:%s", Constant.REDISPARAMS.ORDERNO, worderOrderLogDTO.getOrderNo()));
            return response;
        }
        if (response.isSuccess() && AliPayTradeStatusEnum.WAIT_BUYER_PAY.name().equals(response.getTradeStatus())) {
            return response;
        }
        return response;
    }


    /**
     * 支付宝支付成功查询处理
     */

    void aliPayTradSuc(AlipayTradeQueryResponse response) {

    }

    @Override
    public String getSandboxSignKey() {
        try {
            SortedMap<String, String> params = new TreeMap<String, String>();
            //商户号

            params.put("mch_id", WxConfig.getMchId());
            //随机数
            params.put("nonce_str", WXPayUtil.generateNonceStr());
            //此处密钥使用正式环境的密钥，加密方式只能使用MD5
            params.put("sign", WXPayUtil.generateSignature(params, WxConfig.getApiKey(), WXPayConstants.SignType.MD5));

            /** 转化成你xml */
            String generateSignedXml = null;
            /** 转化成你xml */
            generateSignedXml = WXPayUtil.generateSignedXml(params, WxConfig.getApiKey());
            logger.info("微信支付预下单请求xml格式：：" + generateSignedXml);
            /** 获取请求返回消息 */
            String respData = CommonUtil.httpsRequest("https://api.mch.weixin.qq.com/sandboxnew/pay/getsignkey", "POST", generateSignedXml);
            Map<String, String> respMap = WXPayUtil.xmlToMap(respData);
            logger.info(String.format("获取请求返回消息,{}", respMap));
            return respData;
        } catch (Exception e) {
            logger.info("获取sandbox_signkey异常" + e.getMessage());
            return null;
        }
    }

    @Override
    public Object findWorderHadPay(String worderNo) {
        String orderStatus = Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS + "," + Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS;
        String[] arr = orderStatus.split(",");
        WorderOrderLogDTO worderOrderLog = worderOrderLogMapper.findOrderLogInfoListByStates(worderNo, Arrays.asList(arr));
        if (worderOrderLog != null) {
            /** 判断是否存在 */
            if (worderOrderLog.getOrderStatus().intValue() == Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS.intValue()) {
                return ResponseResult.success(String.format("支付成功,支付流水:%s", worderOrderLog.getOrderNo()));
            }

            if (worderOrderLog.getPayType().intValue() == IntegerEnum.ONE.getValue()) {
                AlipayTradeQueryResponse response = aliPayPayments(worderOrderLog.getOrderNo());
                if (response.isSuccess()) {
                    if (!AliPayTradeStatusEnum.WAIT_BUYER_PAY.name().equals(response.getTradeStatus()) && !AliPayTradeStatusEnum.TRADE_CLOSED.name().equals(response.getTradeStatus())) {
                        return ResponseResult.success(String.format("%s,订单流水:%s", AliPayTradeStatusEnum.getDescByName(response.getTradeStatus())
                                , worderOrderLog.getOrderNo()));
                    }
                } else {
                    return ResponseResult.result(PayOperResultCode.NOTPAY.getStatus(), String.format("订单已预创建,%s,订单流水:%s", response.getSubMsg(), worderOrderLog.getOrderNo()));
                }
            } else if (worderOrderLog.getPayType().intValue() == IntegerEnum.TWO.getValue()) {
                String respData = getQueryResqData(worderOrderLog.getOrderNo());
                ResponseResult responseResult = doQueryOrder(respData, worderOrderLog.getOrderNo());
                if (responseResult.get(ResponseResult.CODE_TAG).equals(String.valueOf(HttpStatus.SUCCESS))) {
                    return responseResult;
                }
            }

        }
        return ResponseResult.error();
    }

    /**
     * 阿里支付回调
     *
     * @param request
     * @return
     */
    @Override
    public String alipayCallback(HttpServletRequest request) {
        ((PayServiceImp) AopContext.currentProxy()).doAlipayCallback(request);
        return "success";
    }

    @Override
    public Object aliPayHadPay() {
        List<Integer> payTypes = new ArrayList<>();
        payTypes.add(1);
        List<String> orderStatus = new ArrayList<>();
        orderStatus.add("1");
        List<WorderOrderLogDTO> worderOrderLogDTOS = worderOrderLogMapper.findNoPayOrder(payTypes, orderStatus);
        if (CollectionUtils.isNotEmpty(worderOrderLogDTOS)) {
            worderOrderLogDTOS.forEach(e -> {
                AlipayTradeQueryResponse response = null;
                if (!PayResultEnum.PAY_SUCEESS.getStatus().equals(e.getOrderStatus())) {
                    response = this.aliPayPayments(e.getOrderNo());
                    if (response.isSuccess()) {
                        if (AliPayTradeStatusEnum.TRADE_SUCCESS.name().equals(response.getTradeStatus())) {
                            /**  支付成功处理 */
                            AlipayNotifyParamResult param = new AlipayNotifyParamResult();
                            BeanUtils.copyProperties(response, param);
                            doWorderNotifyInfo(param);
                            /** 删除缓存 */
                            redisUtils.delete(String.format("ALIPAY:%s:%s", Constant.REDISPARAMS.ORDERNO, param.getOutTradeNo()));
                        }
                    }
                }
            });
        }
        return ResponseResult.success();
    }

    @Override
    public Object payQueryOrderNo(CycleQueyRequest cycleQueyRequest) {

        ResponseResult responseResult = ResponseResult.success(String.format("支付成功,支付流水:%s", cycleQueyRequest.getOrderNo()));

        if (cycleQueyRequest == null) {
            throw new RuntimeException("订单请求参数不能为空");
        }

        /** 获取订单信息  */
        WorderOrderLogDTO worderOrderLogDTO = worderOrderLogMapper.findOrderLogInfoByOrderNo(cycleQueyRequest.getOrderNo());

        if (worderOrderLogDTO == null) {
            throw new RuntimeException(String.format("支付流水不存在，流水号:%s", cycleQueyRequest.getOrderNo()));
        }

        if (worderOrderLogDTO.getOrderStatus() == PayResultEnum.PAY_SUCEESS.getStatus()) {
            return ResponseResult.success(String.format("支付成功,支付流水:%s", cycleQueyRequest.getOrderNo()));
        }
        AlipayTradeQueryResponse response = null;
        if (worderOrderLogDTO.getPayType().intValue() == PayTypeEnum.TYPE_WXPAY.getStatus()) {
            /** 调用微信查询接口进行处理 */

            responseResult = wxCyclePay(worderOrderLogDTO, cycleQueyRequest);
        }
        if (worderOrderLogDTO.getPayType().intValue() == PayTypeEnum.TYPE_ALIPAY.getStatus()) {
            /** 调用支付宝查询接口进行处理 */
            response = aliPayQuery(worderOrderLogDTO, cycleQueyRequest);
        }
        return responseResult;
    }

    @Override
    public AlipayTradeQueryResponse aliPaySelect(String orderNO) {
        return this.aliPayPayments(orderNO);
    }

    @Override
    public String weChatSelect(String orderNO) {
        return this.getQueryResqData(orderNO);
    }

    @Override
    public Map<String, String> wxCloseOrder(List<WorderOrderLogDTO> worderOrderLogDTOS) {
        Map<String, String> respMap = new HashMap<>();
        if (worderOrderLogDTOS == null || worderOrderLogDTOS.size() < 1) {
            respMap.put("return_msg", "工单流水不存在");
        }
        SortedMap<String, String> map = new TreeMap();
        for (WorderOrderLogDTO worderOrderLogDTO : worderOrderLogDTOS) {
            /** 关闭之前的订单 */
            map.put("appid", WxConfig.getAppId());
            map.put("mch_id", WxConfig.getMchId());
            map.put("out_trade_no", worderOrderLogDTO.getOrderNo());
            map.put("nonce_str", WXPayUtil.generateNonceStr());
            try {
                String sign = WXPayUtil.generateSignature(map, WxConfig.getApiKey());
                map.put("sign", sign);
                String respData = CommonUtil.httpsRequest(WxConfig.getCloseOrder(), "POST", WXPayUtil.mapToXml(map));
                if (StringUtils.isNullOrEmpty(respData)) {
                    respMap.put("return_msg", "工单流水不存在");
                }
                /** 验签 */
                respMap = WXPayUtil.xmlToMap(respData);
            } catch (Exception ex) {
                logger.error("订单关闭异常，流水号：{}", worderOrderLogDTO.getOrderNo());
                throw new RuntimeException(String.format("订单关闭异常，流水号：%s,传参：%s", worderOrderLogDTO.getOrderNo(), JSONObject.toJSONString(map)));
            }
        }
        return respMap;
    }

    @Override
    public AlipayTradeCloseResponse aLiCloseOrder(String outTradeNo) {
        return this.aliPayCloseTrade(outTradeNo);
    }

    @Override
    public AlipayTradeCancelResponse aliRevoke(String outTradeNo) {
        return this.aliPayCancelTrade(outTradeNo);
    }

    @PayOperLog(title = PayOperProcessor.ALIPAYCALLBACK)
    public Object doAlipayCallback(HttpServletRequest request) {
        /** 将异步通知中收到的待验证所有参数都存放到map中 */
        Map<String, String> params = convertRequestParamsToMap(request);
        String paramsJson = JSONObject.toJSONString(params);
        logger.info("支付宝回调，{}", paramsJson);
        // 调用SDK验证签名
        try {

            boolean signVerified = AlipaySignature.rsaCertCheckV1(params, Configs.getAliPayPublicCertPath(),
                    Configs.getCharset(), Configs.getSignType());
            if (!signVerified) {
                logger.info("支付宝回调签名认证失败，signVerified=false, paramsJson:{}", paramsJson);
                throw new RuntimeException(String.format("支付宝回调签名认证失败，signVerified=false, paramsJson:%s", paramsJson));
            }
            //回调参数验证
            this.check(params);
            // 另起线程处理业务
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    AlipayNotifyParamResult notify = buildAlipayNotifyParam(params);
                    logger.info("支付宝响应参数{}=============================="+params.toString());
                    String trade_status = notify.getTradeStatus();
                    // 支付成功
                    if (AliPayTradeStatusEnum.TRADE_SUCCESS.name().equals(trade_status)
                            || AliPayTradeStatusEnum.TRADE_FINISHED.name().equals(trade_status)) {
                        /** 处理支付成功逻辑 */
                        doWorderNotifyInfo(notify);
                        /** 删除缓存 */
                        redisUtils.delete(String.format("ALIPAY:%s:%s", Constant.REDISPARAMS.ORDERNO, notify.getOutTradeNo()));
                    } else {
                        logger.error("没有处理支付宝回调业务，支付宝交易状态：{},params:{}", trade_status, paramsJson);
                        throw new RuntimeException(String.format("没有处理支付宝回调业务，支付宝交易状态:%s,params:{}:%s", trade_status, paramsJson));
                    }
                }
            });
            return ResponseResult.result(HttpStatus.SUCCESS, String.format("支付宝回调成功,%s", paramsJson));
        } catch (AlipayApiException e) {
            logger.error("支付宝回调签名认证失败,paramsJson:{},errorMsg:{}", paramsJson, e.getMessage());
            throw new RuntimeException(String.format("支付宝回调签名认证失败,paramsJson:%s,errorMsg:{}:%s", paramsJson, e.getMessage()));
        }
    }

    public void doWorderNotifyInfoPayment(WorderOrderLogDTO worderOrderLogDTO) {
        //WorderOrderLogDTO worderOrderLogDTO = worderOrderLogMapper.findConstantStatusOrderLogInfoByOrderNo(orderNo,Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        //TODO sql进行修改
        if (worderOrderLogMapper.updateOrderStatusByOrderNo(worderOrderLogDTO.getOrderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS
                , worderOrderLogDTO.getTransactionId(), new BigDecimal(1)) < 1) {
        } else {
            //支付中的工单状态改为作废
            worderOrderLogMapper.updateOrderStatusByWorderNo(worderOrderLogDTO.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL,
                    Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS);

            logger.info("返回消息体:{}", JSONObject.toJSONString(worderOrderLogDTO));
            /** 更新工单信息 */
            worderInformationDao.updateWorderInfoAmount(worderOrderLogDTO.getWorderNo(), worderOrderLogDTO.getPayDiscountAmount(), worderOrderLogDTO.getPayActualAmount());
            logger.info("工单更新成功");
            /** 调用acs收款数据 */
            AsyncManager.instance().execute(AsyncFactory.asyncBilling(worderOrderLogDTO.getOrderNo(), worderOrderLogDTO.getToUserId()), 1000);
            /** 消息推送 */
            pushMsg(String.valueOf(worderOrderLogDTO.getToUserId()), worderOrderLogDTO.getOrderNo(), worderOrderLogDTO.isPushState(), true);
        }

    }
    public void doWorderPaymentUserIdAndCompleteTime(AlipayNotifyParamResult param){
        String userId=null;
        String completeTime=null;
        if(StringUtils.isNotBlank(param.getBuyerId())){
            userId=param.getBuyerId();
        }else if(StringUtils.isNotBlank(param.getBody())){
            JSONObject jsonObjec= JSONObject.parseObject(param.getBody());
            Object queryResponse = jsonObjec.get("alipay_trade_query_response");
            if(queryResponse!=null){
                Map map = JSONObject.parseObject(queryResponse.toString(), Map.class);
                if(map!=null){
                    userId=map.get("buyer_user_id").toString();
                }
            }
        }
        if(param.getGmtPayment()!=null){
            userId=param.getBuyerId();
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            completeTime=sdf.format(param.getGmtPayment());
        }else if(StringUtils.isNotBlank(param.getBody())){
            JSONObject jsonObjec= JSONObject.parseObject(param.getBody());
            Object queryResponse = jsonObjec.get("alipay_trade_query_response");
            if(queryResponse!=null){
                Map map = JSONObject.parseObject(queryResponse.toString(), Map.class);
                if(map!=null){
                    userId=map.get("buyer_user_id").toString();
                    completeTime=map.get("send_pay_date").toString();
                }
            }
        }
        if(StringUtils.isNotBlank(userId)&&StringUtils.isNotBlank(completeTime)){
            worderOrderLogMapper.updateOpenIdByOrderNo(param.getOutTradeNo(),userId,completeTime);
        }
    }
    @Override
    public AlipayTradeRefundResponse aliRefund(String outTradeNo, String refundAmount,String outRequestTradeNo) throws AlipayApiException {
        return this.aliRefundResponse(outTradeNo, refundAmount,outRequestTradeNo);
    }

    @Override
    public Map<String, String> wxRefund(String outTradeNo, String amount) {
        return this.requestRefundsWx(outTradeNo, amount);
    }

    @Override
    public AlipayTradeFastpayRefundQueryResponse AliRefundSelect(String outTradeNo,String outRequestTradeNo) throws AlipayApiException {
        return this.selectAliRefundResponse(outTradeNo,outRequestTradeNo);
    }

    @Override
    public Map<String, String> WxRefundSelect(String outRefundNo) {
        return this.requestRefundsWxSelect(outRefundNo);
    }

    /**
     * 构建订单对象
     *
     * @param param
     */
    private void doWorderNotifyInfo(AlipayNotifyParamResult param) {
        WorderOrderLogDTO worderOrderLogDTO = worderOrderLogMapper.findConstantStatusOrderLogInfoByOrderNo(param.getOutTradeNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        if (worderOrderLogDTO != null) {
            logger.info("支付订单已经完成支付,支付流水：{}", param.getOutTradeNo());
            return;
        }
        /**  获取订单的状态  */
        logger.info("map信息:{}", JSONObject.toJSONString(param));
        worderOrderLogDTO = worderOrderLogMapper.findOrderLogInfoByOrderNo(param.getOutTradeNo());
        if (worderOrderLogDTO == null) {
            logger.error("支付订单信息不存在,支付流水：{}", param.getOutTradeNo());
            throw new RuntimeException(String.format("支付订单信息不存在,支付流水：%s", param.getOutTradeNo()));
        }
        //TODO sql进行修改
        if (worderOrderLogMapper.updateOrderStatusByOrderNo(param.getOutTradeNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS
                , param.getTradeNo(), param.getTotalAmount()) < 1) {
            logger.error("验签成功之后的数据进行处理失败");
            throw new RuntimeException("验签成功之后的数据进行处理失败");
        }
        doWorderPaymentUserIdAndCompleteTime(param);
        //支付中的工单状态改为作废
        worderOrderLogMapper.updateOrderStatusByWorderNo(worderOrderLogDTO.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL,
                Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS);
        logger.info("返回消息体:{}", JSONObject.toJSONString(worderOrderLogDTO));
        /** 更新工单信息 */
        worderInformationDao.updateWorderInfoAmount(worderOrderLogDTO.getWorderNo(), worderOrderLogDTO.getPayDiscountAmount(), worderOrderLogDTO.getPayActualAmount());
        logger.info("工单更新成功");
        /** 调用acs收款数据 */
        AsyncManager.instance().execute(AsyncFactory.asyncBilling(param.getOutTradeNo(), worderOrderLogDTO.getToUserId()), 1000);
        /** 消息推送 */
        pushMsg(String.valueOf(worderOrderLogDTO.getToUserId()), worderOrderLogDTO.getOrderNo(), worderOrderLogDTO.isPushState(), true);
    }


    private AlipayNotifyParamResult buildAlipayNotifyParam(Map<String, String> params) {
        String json = JSONObject.toJSONString(params);
        return JSONObject.parseObject(json, AlipayNotifyParamResult.class);
    }

    private void check(Map<String, String> params) throws AlipayApiException {
        String outTradeNo = params.get("out_trade_no");

        // 1、商户需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号，
        WorderOrderLogDTO worderOrderInfo = worderOrderLogMapper.findOrderLogInfoByOrderNo(outTradeNo);
        if (worderOrderInfo == null) {
            throw new AlipayApiException("out_trade_no错误");
        }

        // 2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额），
        long total_amount = new BigDecimal(params.get("total_amount")).longValue();
        if (total_amount != worderOrderInfo.getPayActualAmount().longValue()) {
            throw new AlipayApiException("error total_amount");
        }

        // 3、验证app_id是否为该商户本身。
        if (!params.get("app_id").equals(Configs.getAppid())) {
            throw new AlipayApiException("app_id不一致");
        }
    }

    /**
     * 将request中的参数转换成Map
     *
     * @param request
     * @return
     */
    private static Map<String, String> convertRequestParamsToMap(HttpServletRequest request) {

        Map<String, String> retMap = new HashMap<String, String>();

        Set<Map.Entry<String, String[]>> entrySet = request.getParameterMap().entrySet();

        for (Map.Entry<String, String[]> entry : entrySet) {
            String name = entry.getKey();
            String[] values = entry.getValue();
            int valLen = values.length;

            if (valLen == 1) {
                retMap.put(name, values[0]);
            } else if (valLen > 1) {
                StringBuilder sb = new StringBuilder();
                for (String val : values) {
                    sb.append(",").append(val);
                }
                retMap.put(name, sb.toString().substring(1));
            } else {
                retMap.put(name, "");
            }
        }

        return retMap;
    }

    /**
     * 对微信支付查询的数据进行处理
     */
    public ResponseResult doQueryOrder(String respData, String outTradeNo) {
        try {
            Map<String, String> respMap = WXPayUtil.xmlToMap(respData);
            /** 对数据进行验签 */
            if (!WXPayUtil.isSignatureValid(respMap, WxConfig.getApiKey())) {
                /** 验签失败处理 */
                logger.error("数据验签失败,支付流水:{}", outTradeNo);
                throw new RuntimeException(String.format("数据验签失败,,支付流水:%s", outTradeNo));
            }
            /** 验签成功处理 */
            String respStr = orderQueryVolidateSignSuccess(respMap);
            return doResult(outTradeNo, respStr);
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 返回信息的处理
     */
    public ResponseResult doResult(String outTradeNo, String code) {
        if (WxReturnStatus.SUCCESS.name().equals(code)) {
            return ResponseResult.success(String.format("支付成功,订单流水:%s", outTradeNo));
        } else if (PayOperResultCode.containName(code)) {
            return ResponseResult.result(PayOperResultCode.getStatus(code), String.format("订单%s,订单流水:%s", PayOperResultCode.getDescByName(code), outTradeNo));
        }
        return ResponseResult.error(String.format("支付失败,订单流水:%s", outTradeNo));
    }


    /**
     * 对微信支付查询的数据进行处理
     */
    public Map<String, String> orderQueryRespDataPostProcessor(String respData, String outTradeNo) {
        try {
            Map<String, String> respMap = WXPayUtil.xmlToMap(respData);
            /** 获取支付流水 */
            WorderOrderLogDTO worderOrderLogDTO = worderOrderLogMapper.findOrderLogInfoByOrderNo(outTradeNo);
            if (worderOrderLogDTO == null) {
                throw new RuntimeException(String.format("支付流水不存在，流水号:%s", outTradeNo));
            }
            /** return_code 、result_code、trade_state */
            /** 对数据进行验签 */
            if (WXPayUtil.isSignatureValid(respMap, WxConfig.getApiKey())) {
                /** 验签成功处理 */
                String respStr = orderQueryVolidateSignSuccess(respMap);
                //
                if (WxReturnStatus.SUCCESS.name().equals(respStr)) {
                    /** 移除redis内容 */
                    logger.info("删除redis中的key:{}", Constant.REDISPARAMS.ORDERNO + ":" + outTradeNo);
                    redisUtils.delete(Constant.REDISPARAMS.ORDERNO + ":" + outTradeNo);
                }
                return queryReturnInfo(outTradeNo, respStr);
            }
            return queryReturnInfo(outTradeNo, WxReturnStatus.FAIL.name());
        } catch (Exception e) {
            logger.error("数据解析失败,支付流水:{}", outTradeNo);
            throw new RuntimeException(String.format("数据解析失败,,支付流水:%s", outTradeNo));
        }
    }

    /**
     * 返回信息的处理
     */
    public Map<String, String> queryReturnInfo(String outTradeNo, String code) {
        Map<String, String> map = new HashMap<>();
        if (WxReturnStatus.SUCCESS.name().equals(code)) {
            map.put("return_code", WxReturnStatus.SUCCESS.name());
            map.put("return_msg", String.format("订单操作流水:%s,当前订单的交易状态:%s(%s)"
                    , outTradeNo, code, PayOperResultCode.getDescByName(code)));
        } else {
            map.put("return_code", code);
            map.put("return_msg", String.format("订单操作流水:%s,当前订单的交易状态:%s(%s)"
                    , outTradeNo, code, PayOperResultCode.getDescByName(code)));
        }
        return map;
    }

    /**
     * 查询数据的验签的处理
     */
    String orderQueryVolidateSignSuccess(Map<String, String> respMap) {
        boolean returnCodeFlag = respMap.containsKey("return_code") && WxReturnStatus.SUCCESS.name().equals(respMap.get("return_code"));
        boolean resultCodeFlag = respMap.containsKey("result_code") && WxReturnStatus.SUCCESS.name().equals(respMap.get("result_code"));
        boolean tradeStateFlag = respMap.containsKey("trade_state") && WxReturnStatus.SUCCESS.name().equals(respMap.get("trade_state"));
        if (returnCodeFlag && resultCodeFlag) {
            if (tradeStateFlag) {
                /** 交易成功 */
                return afterVolidateSignSuccessPostProcessor(respMap);
            } else {
                /** 交易其他处理 */
                return respMap.get("trade_state");
            }
        }
        return WxReturnStatus.FAIL.name();
    }

    /**
     * 获取查询请求返回的报文
     */
    public String getQueryResqData(String outTradeNo) {
        String respData = "";
        try {
            SortedMap<String, String> map = new TreeMap<String, String>();
            map.put("appid", WxConfig.getAppId());
            map.put("mch_id", WxConfig.getMchId());
            map.put("out_trade_no", outTradeNo);
            map.put("nonce_str", WXPayUtil.generateNonceStr());
            //默认 MD5
            String sign = WXPayUtil.generateSignature(map, WxConfig.getApiKey());
            map.put("sign", sign);
            logger.info("请求查询微信接口的xml{}---------------"+WXPayUtil.generateSignedXml(map, WxConfig.getApiKey()));
            return CommonUtil.httpsRequest(WxConfig.getOrderQueryUrl(), "POST"
                    , WXPayUtil.generateSignedXml(map, WxConfig.getApiKey()));
        } catch (Exception e) {
            logger.error("获取查询请求返回的报文出错,支付流水:{}", outTradeNo);
            throw new RuntimeException(String.format("获取查询请求返回的报文出错,支付流水:%s", outTradeNo));
        }
    }

    /**
     * 微信退款接口
     * @param outTradeNo
     * @param amount
     * @return
     */
    public Map<String, String> requestRefundsWx(String outTradeNo, String amount) {
        try {
            SortedMap<String, String> map = new TreeMap<String, String>();
            map.put("appid", WxConfig.getAppId());
            map.put("mch_id", WxConfig.getMchId());
            map.put("out_trade_no", outTradeNo);
            //map.put("nonce_str", WXPayUtil.generateNonceStr());
            map.put("refund_fee", amount);
            map.put("total_fee",amount);
            //map.put("refundAmount","1");
            String outRefundNo = UUID.randomUUID().toString().trim().replaceAll("-", "");
            map.put("out_refund_no", outRefundNo);
            logger.info("路径--------{}"+WxConfig.getOrderRefundsUrl());
            logger.info("key--------{}"+WxConfig.getApiKey());
            WxConfigImpI wxConfigImpI=new WxConfigImpI();
            WXPay wxPay=new WXPay(wxConfigImpI,false,false);
            Map<String, String> refund = wxPay.refund(map);
            return  refund;
        } catch (Exception e) {
            logger.error("获取查询请求返回的报文出错,支付流水:{}", outTradeNo);
            throw new RuntimeException(String.format("获取查询请求返回的报文出错,支付流水:%s", outTradeNo));
        }
    }
    public Map<String, String> requestRefundsWxSelect(String outRefundNo){
        try {
            SortedMap<String, String> map = new TreeMap<String, String>();
            map.put("appid", WxConfig.getAppId());
            map.put("mch_id", WxConfig.getMchId());
            map.put("nonce_str", WXPayUtil.generateNonceStr());
            map.put("out_refund_no", outRefundNo);
            //默认 MD5
            String sign = WXPayUtil.generateSignature(map, WxConfig.getApiKey());
            WxConfigImpI wxConfigImpI=new WxConfigImpI();
            WXPay wxPay=new WXPay(wxConfigImpI,false,false);
            Map<String, String> refund = wxPay.refundQuery(map);
            return refund;
        } catch (Exception e) {
            logger.error("获取查询请求返回的报文出错,支付流水:{}", outRefundNo);
            throw new RuntimeException(String.format("获取查询请求返回的报文出错,支付流水:%s", outRefundNo));
        }
    }
    /**
     * 对返回的消息进行处理
     */
    public void respDataPostProccessor(WorderOrderLogDTO worderOrderLogDTO, String imgContent) {
        /**  获取订单支付请情况 */
        List<WorderOrderLogDTO> worderOrderLogDTOS = worderOrderLogMapper.findOrderLogInfoList(worderOrderLogDTO.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS);
        if (worderOrderLogDTOS != null && worderOrderLogDTOS.size() > 0) {
            /** 更新状态支付数据 */
            if (worderOrderLogMapper.updateOrderStatus(worderOrderLogDTO.getWorderNo(), Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_CANCEL) < 1) {
                logger.error("更新状态支付数据失败,工单编号：{}", worderOrderLogDTO.getWorderNo());
                throw new RuntimeException(String.format("更新状态支付数据失败,工单编号:%s", worderOrderLogDTO.getWorderNo()));
            }
            /** 微信关闭订单 */
            if (worderOrderLogDTO.getPayType().intValue() == 2) {
                closeOrder(worderOrderLogDTOS);
            } else if (worderOrderLogDTO.getPayType().intValue() == 1) {
                worderOrderLogDTOS.forEach(e -> {
                    AlipayTradeCloseResponse alipayTradeCloseResponse = aliPayCloseTrade(e.getOrderNo());
                    logger.info("关闭订单返回信息:{}", alipayTradeCloseResponse.getBody());
                });
            }
        }


        /** 新增信息 */
        if (worderOrderLogMapper.insertOrderLogInfo(worderOrderLogDTO) < 1) {
            logger.error("插入信息失败,工单编号:{}", worderOrderLogDTO.getWorderNo());
            throw new RuntimeException(String.format("新增支付信息失败,工单编号:%s", worderOrderLogDTO.getWorderNo()));
        }
        PayCreateImageDTO payCreateImageDTO = new PayCreateImageDTO();
        payCreateImageDTO.setIntervals(Configs.getTimeExpire());
        payCreateImageDTO.setOrderId(worderOrderLogDTO.getOrderLogId());
        payCreateImageDTO.setCreateTime(worderOrderLogDTO.getCreateTime());
        payCreateImageDTO.setImgContent(imgContent);
        payCreateImageDTO.setUnit("m");
        payCreateImageDTO.setEnable(1);
        if (payCreateImageMapper.insertInfo(payCreateImageDTO) < 1) {
            logger.error("插入信息失败,工单编号:{}", worderOrderLogDTO.getWorderNo());
            throw new RuntimeException(String.format("新增支付信息失败,工单编号:%s", worderOrderLogDTO.getWorderNo()));
        }
    }

    /**
     * 对返回的消息进行处理
     */
    public void respDataPostProccessorPayment(WorderOrderLogDTO worderOrderLogDTO, String imgContent) {
        /** 新增信息 */
        if (worderOrderLogMapper.insertOrderLogInfo(worderOrderLogDTO) < 1) {
            logger.error("插入信息失败,工单编号:{}", worderOrderLogDTO.getWorderNo());
            throw new RuntimeException(String.format("新增支付信息失败,工单编号:%s", worderOrderLogDTO.getWorderNo()));
        }
        PayCreateImageDTO payCreateImageDTO = new PayCreateImageDTO();
        payCreateImageDTO.setIntervals(Configs.getTimeExpire());
        payCreateImageDTO.setOrderId(worderOrderLogDTO.getOrderLogId());
        payCreateImageDTO.setCreateTime(worderOrderLogDTO.getCreateTime());
        payCreateImageDTO.setImgContent(imgContent);
        payCreateImageDTO.setUnit("m");
        payCreateImageDTO.setEnable(1);
        if (payCreateImageMapper.insertInfo(payCreateImageDTO) < 1) {
            logger.error("插入信息失败,工单编号:{}", worderOrderLogDTO.getWorderNo());
            throw new RuntimeException(String.format("新增支付信息失败,工单编号:%s", worderOrderLogDTO.getWorderNo()));
        }
    }

    /**
     * 关闭之前的支付订单
     */
    public void closeOrder(List<WorderOrderLogDTO> worderOrderLogDTOS) {
        if (worderOrderLogDTOS == null || worderOrderLogDTOS.size() < 1) {
            return;
        }
        SortedMap<String, String> map = new TreeMap();
        /** 关闭之前的订单 */
        worderOrderLogDTOS.forEach(e -> {
            /** 关闭之前的订单 */
            map.put("appid", WxConfig.getAppId());
            map.put("mch_id", WxConfig.getMchId());
            map.put("out_trade_no", e.getOrderNo());
            map.put("nonce_str", WXPayUtil.generateNonceStr());
            try {
                String sign = WXPayUtil.generateSignature(map, WxConfig.getApiKey());
                map.put("sign", sign);
                String respData = CommonUtil.httpsRequest(WxConfig.getCloseOrder(), "POST", WXPayUtil.mapToXml(map));
                if (StringUtils.isNullOrEmpty(respData)) {
                    return;
                }
                /** 验签 */
                Map<String, String> respMap = WXPayUtil.xmlToMap(respData);
                if (!WXPayUtil.isSignatureValid(respMap, WxConfig.getApiKey())) {
                    logger.error("订单关闭验签异常，流水号：{}，返回数据:{}", e.getOrderNo(), JSONObject.toJSONString(respMap));
                    throw new RuntimeException(String.format("订单关闭验签异常，流水号：%s，传参：%s，返回文本:%s", e.getOrderNo(), JSONObject.toJSONString(map), JSONObject.toJSONString(respMap)));
                }
                if (!respMap.containsKey("return_code") || !WxReturnStatus.SUCCESS.name().equalsIgnoreCase(String.valueOf(respMap.get("return_code")))) {
                    logger.error("订单关闭异常，流水号：{},传参：{},返回文本:{}", e.getOrderNo(), JSONObject.toJSONString(map), JSONObject.toJSONString(respMap));
                    throw new RuntimeException(String.format("订单关闭异常，流水号：%s,传参：%s,返回文本:%s", e.getOrderNo(), JSONObject.toJSONString(map), JSONObject.toJSONString(respMap)));
                }
                logger.info("订单关闭成功，流水号：{}", e.getOrderNo());
            } catch (Exception ex) {
                logger.error("订单关闭异常，流水号：{}", e.getOrderNo());
                throw new RuntimeException(String.format("订单关闭异常，流水号：%s,传参：%s", e.getOrderNo(), JSONObject.toJSONString(map)));
            }
        });
    }

    /**
     * 生成二维码
     *
     * @param respData
     */
    public ResponseResult generateErCode(String respData, String orderNo) {
        try {
            Map<String, String> map = WXPayUtil.xmlToMap(respData);
            boolean flag = map.containsKey("return_code") && map.containsKey("result_code") && map.containsKey("code_url")
                    && WxReturnStatus.SUCCESS.name().equals(map.get("return_code")) && WxReturnStatus.SUCCESS.name().equals(map.get("result_code"));
            if (!flag) {
                logger.error("获取code_url失败,返回消息:{}", JSONObject.toJSONString(map));
                return ResponseResult.error(String.format("获取code_url失败,返回消息:%s", map));
            }
            /** 对返回的消息进行处理 */
            String codeUrl = map.get("code_url");
            /** 生成二维码,以流的方式进行传输 */
            String base64Content = GenerateQrCodeUtil.encodeQrcodeBase64(codeUrl);
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("content", base64Content);
            resultMap.put("orderNo", orderNo);
            return ResponseResult.success(String.format("操作成功,支付流水:%s", orderNo), resultMap);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    /**
     * 微信支付获取code_url
     */
    public String getRespData(GoPayRequestDTO goPayRequest, SortedMap<String, String> parameters
            , WorderOrderLogDTO worderOrderLogDTO, HttpServletRequest request) {
        try {
            /** 微信支付的对象的处理 wxUnifiedOrderRequestParam */
            ResponseResult responseResult = constructWxObj(goPayRequest, parameters, worderOrderLogDTO, request);
            if (responseResult != null) {
                logger.error("微信支付的对象的处理失败");
                return null;
            }
            String generateSignedXml = null;
            /** 转化成你xml */
            generateSignedXml = WXPayUtil.generateSignedXml(parameters, WxConfig.getApiKey());
            logger.info("微信支付预下单请求xml格式：：" + generateSignedXml);
            /** 获取请求返回消息 */
            String respData = CommonUtil.httpsRequest(WxConfig.getUnifiedOrderUrl(), "POST", generateSignedXml);
            logger.info("返回消息体:" + respData);
            return respData;
        } catch (Exception e) {
            logger.error("微信支付获取code_url失败" + e.getMessage());
            throw new RuntimeException("微信支付获取code_url失败");
        }
    }


    /**
     * 封装成微信对象
     **/
    public ResponseResult constructWxObj(GoPayRequestDTO goPayRequest, SortedMap<String, String> parameters
            , WorderOrderLogDTO worderOrderLogDTO, HttpServletRequest request) {
        if (goPayRequest == null || (parameters == null && parameters.size() > 0)) {
            logger.error("封装成微信对象不能为空");
            return ResponseResult.error("封装成微信对象不能为空");
        }
        /** 获取工单信息 */
        WorderInfoDTO worderInfoDTO = worderInformationDao.findWorderInfo(goPayRequest.getWorderNo());
        if (worderInfoDTO == null) {
            logger.info("工单信息不存在,工单编号:{}", goPayRequest.getWorderNo());
            return ResponseResult.error(String.format("工单信息不存在,工单编号:%s", goPayRequest.getWorderNo()));
        }
        /** 获取品牌信息 */
        BrandPo brandPo = worderInformationDao.getBrand(worderInfoDTO.getTemplateId());

        /** 生成商户交易号 */
        String outTradeNo = UUID.randomUUID().toString().trim().replaceAll("-", "");
        /**  处理支付数据 */
        payDataPostPorccessor(goPayRequest, request, parameters, worderInfoDTO, outTradeNo, brandPo, worderOrderLogDTO.getCreateTime());
        /** 处理入库的消息 */
        inserDataPostPorccessor(goPayRequest, worderOrderLogDTO, worderInfoDTO, outTradeNo, brandPo);
        return null;
    }

    /**
     * 处理支付对象数据
     */
    public void payDataPostPorccessor(GoPayRequestDTO goPayRequest, HttpServletRequest request, SortedMap<String, String> parameters
            , WorderInfoDTO worderInfoDTO, String outTradeNo, BrandPo brandPo, Date createTime) {
        /** 服务号应用ID */
        parameters.put("appid", WxConfig.getAppId());
        /** 商品描述 */
        parameters.put("body", "商品收款-品牌:" + brandPo.getBrandName());
        /** 商户号ID */
        parameters.put("mch_id", WxConfig.getMchId());
        /** 随机字符串 */
        parameters.put("nonce_str", WXPayUtil.generateNonceStr());
        /** 通知地址	 */
        parameters.put("notify_url", WxConfig.getNotifyUrl());
        /** 商户订单号 */
        parameters.put("out_trade_no", outTradeNo);
        /** 商品ID */
        parameters.put("product_id", worderInfoDTO.getWorderNo());
        /** 签名类型 */
        parameters.put("sign_type", WxConfig.getSginType());
        /** 终端IP */
        String spbillCreateIp = StringUtils.isNotBlank(CommonUtil.getIp(request)) ? CommonUtil.getIp(request).split("\\,")[0].trim() : "127.0.0.1";
        parameters.put("spbill_create_ip", spbillCreateIp);
        if ("test".equalsIgnoreCase(WxConfig.getPrefix())) {
            /** 支付金额 */
            parameters.put("total_fee", "301");
        } else {
            /** 支付金额 */
            parameters.put("total_fee", String.valueOf(goPayRequest.getPayActualAmount().multiply(new BigDecimal(100)).intValue()));
        }
        //
        logger.info("应实际支付金额(分):{},测试支付金额(分):{}", String.valueOf(goPayRequest.getPayActualAmount().multiply(new BigDecimal(100)).intValue()), 301);
        /** 过期时间 */
        try {
            long time = createTime.getTime();
            parameters.put("time_start", new SimpleDateFormat("yyyyMMddHHmmss").format(time));
            parameters.put("time_expire", new SimpleDateFormat("yyyyMMddHHmmss").format(time + (Configs.getTimeExpire() * 60 * 1000)));
            /** 交易类型 */
            parameters.put("trade_type", WxConfig.getTradeType());
            parameters.put("sign", WXPayUtil.generateSignature(parameters, WxConfig.getApiKey(), WXPayConstants.SignType.MD5));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理入库的对象数据
     */
    public void inserDataPostPorccessor(GoPayRequestDTO goPayRequest, WorderOrderLogDTO worderOrderLogDTO
            , WorderInfoDTO worderInfoDTO, String outTradeNo, BrandPo brandPo) {
        /** 订单编号 即支付流水 */
        worderOrderLogDTO.setOrderNo(outTradeNo);
        /** 工单编号（对应工单表的worder_no） */
        worderOrderLogDTO.setWorderNo(worderInfoDTO.getWorderNo());
        /** 实际支付金额（单位：分） */
        worderOrderLogDTO.setPayActualAmount(goPayRequest.getPayActualAmount());
        /** 订单状态（0:未知 1:支付中 2:支付成功 3:支付失败 4:作废） */
        worderOrderLogDTO.setOrderStatus(Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENTS);
        /** 品牌名称*/
        worderOrderLogDTO.setBrandName(brandPo.getBrandName());
        /** 支付方式 1：支付宝  2：微信 */
        worderOrderLogDTO.setPayType(goPayRequest.getPayType());
        /** 优惠金额（单位：分） */
        worderOrderLogDTO.setPayDiscountAmount(goPayRequest.getMaxNumber().subtract(goPayRequest.getPayActualAmount()));
        /** 应付金额（单位：分） */
        worderOrderLogDTO.setPayAbleAmuount(goPayRequest.getMaxNumber());
        /** 支付区域id */
        worderOrderLogDTO.setPayAreaId(worderInfoDTO.getAreaId());
        /** 支付区域名称 */
        /** 对区域的处理 */
        worderOrderLogDTO.setPayAreaName(bizRegionMapper.findParentName((worderInfoDTO.getAreaId())));
    }

    public static void main(String[] args) {
        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            Date parse = sdf.parse("20220224171239");
            SimpleDateFormat sdfs =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = sdfs.format(parse);
            System.out.println(format);
        } catch (ParseException e) {
            e.printStackTrace();
        }

    }
}
