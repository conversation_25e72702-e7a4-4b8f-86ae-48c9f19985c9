
package com.bonc.rrs.pay.entity.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/8 16:17
 * @Version 1.0.0
 */

@Data
@NoArgsConstructor
@ToString
public class InvoiceBusinessTypeDetailDto {

    @ExcelProperty(value  = "NEWS工单号")
    private String worderNo;

    @ExcelProperty(value  = "email")
    private String email;

    @ExcelProperty(value  = "建筑服务发生地")
    private String jzfwfsd;

    @ExcelProperty(value  = "发生地详细地址")
    private String fsdxxdz;

    @ExcelProperty(value  = "建筑项目名称")
    private String jzxmmc;


    @ExcelProperty(value  = "跨地市标志")
    private String kdsbz;


    @ExcelProperty(value  = "土地增值税项目编号")
    private String tdzzsxmbh;

}