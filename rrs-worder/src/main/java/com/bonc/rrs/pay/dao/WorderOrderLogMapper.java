package com.bonc.rrs.pay.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bonc.rrs.pay.entity.entity.LocalPayDataStatisticEntity;
import com.bonc.rrs.pay.entity.entity.WorderOrderLogEntity;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worderapp.entity.WorderInformation;
import com.youngking.lenmoncore.common.utils.PageUtils;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 订单的支付结果处理
 * <AUTHOR>
 * @description
 */
@Mapper
public interface WorderOrderLogMapper extends BaseMapper<WorderOrderLogEntity> {
    /** 获取所有的列表 */
    List<WorderOrderLogDTO> findOrderLogInfo();
    /** 新增 */
    int insertOrderLogInfo(WorderOrderLogDTO worderOrderLogDTO);
    /** 获取支付成功的订单 */
    List<WorderOrderLogDTO> findConstantStatusOrderLogInfo(@Param("orderStatus") Integer orderStatus,@Param("worderNos") List<String> worderNos);
    /** 更新记账状态 */
    int updateBookkeepingStatus(@Param("orderNo") String orderNo,@Param("bookkeepingStatus") Integer bookkeepingStatus);
    /** 获取支付情况的订单 */
    List<WorderOrderLogDTO> findOrderLogInfoList(@Param("worderNo") String worderNo,@Param("orderStatus") Integer orderStatus);
    /** 更新订单状态 */
    int updateOrderStatus(@Param("worderNo") String worderNo,@Param("orderStatus") Integer orderStatus);
    /** 获取支付情况的订单 */
    WorderOrderLogDTO findConstantStatusOrderLogInfoByOrderNo(@Param("orderNo") String orderNo,@Param("orderStatus") Integer orderStatus);
    /** 更新订单的状态 */
    int updateOrderStatusByOrderNo(@Param("orderNo") String orderNo,@Param("orderStatus") Integer orderStatus
            ,@Param("transactionId") String transactionId,@Param("payActualAmount") BigDecimal payActualAmount);
    /**根据orderNo修改支付时间和买家id*/
    int updateOpenIdByOrderNo(@Param("orderNo") String orderNo, @Param("userId") String userID, @Param("completeTime") String completeTime);
    /** 更新支付流水的申请状态 */
    int updateApplyStatus(@Param("orderNo") String orderNo,@Param("apply") boolean apply);
    /** 获取支付情况的订单 */
    WorderOrderLogDTO findOrderLogInfoByOrderNo(@Param("orderNo") String orderNo);
    /** 更新推送状态 */
    int updatePushState(@Param("orderNo") String orderNo,@Param("pushState") boolean pushState);

    WorderOrderLogDTO findOrderLogInfoListByStates(@Param("worderNo") String worderNo,@Param("orderStatus") List<String> orderStatus);

    List<WorderOrderLogDTO> findOrderLogInfoListsByStates(@Param("worderNo") String worderNo,@Param("orderStatus") List<String> orderStatus);

    List<WorderOrderLogDTO> findNoPayOrder(@Param("payTypes") List<Integer> payTypes,@Param("orderStatus") List<String> orderStatus);

    LocalPayDataStatisticEntity  findOrderLogRecordCounts(@Param("startTime") String startTime, @Param("endTime") String endTime
            , @Param("orderStatus") Integer orderStatus, @Param("payType") Integer payType);

    List<WorderOrderLogDTO>  findOrderLogRecords(@Param("startTime") String startTime, @Param("endTime") String endTime
            , @Param("orderStatus") Integer orderStatus,@Param("payType") Integer payType);

    /** 获取没有开票的数据的工单编号 */
    Set<String> findWorderNoWithNoBilled(@Param("bookkeepingStatus") Integer bookkeepingStatus, @Param("apply") Integer apply
            , @Param("billingStatus") List<Integer> billingStatus);
    /**
     *
     */
    /** 根据工单编号更新订单状态支付中改为作废 */
    int updateOrderStatusByWorderNo(@Param("worderNo") String worderNo,@Param("orderStatus") Integer orderStatus,@Param("orderStatus1") Integer orderStatus1);
    /** 查询状态为支付中的数据并且区分微信支付宝 */
    List<WorderOrderLogDTO> getWxOrAliOrderLogInfoList(@Param("worderNo") String worderNo,@Param("orderStatus") Integer orderStatus,@Param("payType") Integer payType);
    /**获取微信或者支付宝支付的状态 */
    WorderOrderLogDTO getOrderLogInfoListByStates(@Param("worderNo") String worderNo,@Param("orderStatus") Integer orderStatus,@Param("payType") Integer payType);
    int updateStatusByOrderNo(@Param("orderNo") String orderNo,@Param("orderStatus") Integer orderStatus,@Param("worderNo") String worderNo);
    /**获取系统时间*/
    String getNowTime();
    /**根据工单号查询 是否已经支付 */
    WorderOrderLogDTO getByWorderNoAndStatus(@Param("worderNo") String worderNo,@Param("orderStatus") Integer orderStatus);
    /**支付流水查询列表**/
    IPage<WorderOrderLogEntity> queryList(IPage<WorderOrderLogEntity> pageNotI,@Param("p") Map<String, Object> params);

    /**
     * 根据id查询
     * @param orderLogId
     * @return
     */
    WorderOrderLogDTO getById(@Param("orderLogId") Integer orderLogId);

    /**
     * 根据id去修改
     * @param orderLogId
     * @return
     */
    Integer updateById(@Param("orderLogId") Integer orderLogId);



    /**根据流水查询工单*/
    String isExsitWorderLog(@Param("transactionId") String transactionId);

    /**根据车企订单号查询是否存在增项支付信息*/
    String isExsitPayInfo(@Param("worderNO") String worderNO);

}
