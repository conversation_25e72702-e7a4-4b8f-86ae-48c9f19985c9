package com.bonc.rrs.pay.enums;

/**
 * @ProjectName: lenmon-parent
 * @Package: com.bonc.rrs.pay.enums
 * @ClassName: AliPayResultEnum
 * @Author: admin
 * @Description: 阿里支付返回代码 枚举
 * @Date: 2020/8/25 10:55
 * @Version: 1.0
 */
public enum AliPayResultEnum {

    SUCCESS("10000","成功"),
    PAYING("10003","用户支付中"),
    FAILED("40004","失败"),
    ERROR("20000","系统异常");

    private String code;
    private String desc;

    private  AliPayResultEnum(String code,String desc){
        this.code = code;
        this.desc = desc;
    }
    public static String getDesc(String code){
        for(AliPayResultEnum value : AliPayResultEnum.values()){
            if(value.code == code){
                return value.getDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }


    public void setDesc(String desc) {
        this.desc = desc;
    }
}
