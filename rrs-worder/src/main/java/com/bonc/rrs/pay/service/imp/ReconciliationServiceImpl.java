package com.bonc.rrs.pay.service.imp;

import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayDataDataserviceBillDownloadurlQueryModel;
import com.alipay.api.request.AlipayDataDataserviceBillDownloadurlQueryRequest;
import com.alipay.api.response.AlipayDataDataserviceBillDownloadurlQueryResponse;
import com.bonc.rrs.InitializingBeanAfter;
import com.bonc.rrs.pay.dao.PayDayEndDetailsMapper;
import com.bonc.rrs.pay.dao.PayDayEndStatisticsMapper;
import com.bonc.rrs.pay.dao.WorderOrderLogMapper;
import com.bonc.rrs.pay.entity.entity.LocalPayDataStatisticEntity;
import com.bonc.rrs.pay.entity.entity.PayDayEndDetailsEntity;
import com.bonc.rrs.pay.entity.entity.PayDayEndStatisticsEntity;
import com.bonc.rrs.pay.enums.PayTypeEnum;
import com.bonc.rrs.pay.enums.ReconciliationStatus;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.pay.service.ReconciliationService;
import com.common.pay.alipay.config.Configs;
import com.common.pay.alipay.utils.BillToolUtils;
import com.common.pay.common.utils.ResponseResult;
import com.common.pay.wxpay.config.WxConfig;
import com.common.pay.wxpay.sdk.WXPayConstants;
import com.common.pay.wxpay.util.CommonUtil;
import com.common.pay.wxpay.util.WXPayUtil;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalTime;
import java.util.*;

/**
 * @ProjectName: rrs-ticket
 * @Package: com.bonc.rrs.pay.service.imp
 * @ClassName: ReconciliationServiceImpl
 * @Author: admin
 * @Description: 对账业务实现
 * @Date: 2020/6/24 10:53
 * @Version: 1.0
 */
@Service
public class ReconciliationServiceImpl implements ReconciliationService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private final String dateFormat = "yyyy-MM-dd HH:mm:ss";
    private final String dateDayFormat = "yyyy-MM-dd";
    private final String dateFormatLimitDay = "yyyyMMdd";
    @Resource
    private PayDayEndStatisticsMapper payDayEndStatisticsMapper;
    @Resource
    private PayDayEndDetailsMapper payDayEndDetailsMapper;
    @Resource
    private WorderOrderLogMapper worderOrderLogMapper;


    private final long timeLong =  -1L;

    @Override
    @Transactional(rollbackFor = {Exception.class,RuntimeException.class})
    public Object reconciliation() {
        /**微信的账单处理 */
        wxBillResult();
        return ResponseResult.success();
    }

    @Override
    @Transactional(rollbackFor = {RuntimeException.class,Exception.class})
    public Object test() {
        aliBillResult();
        return ResponseResult.success();
    }

    @Override
    public Object alipayReconciliation() {
        aliBillResult();
        return ResponseResult.success();
    }

    /**
     * 支付宝对账
     */
    public void aliBillResult(){
        try {
            /** 获取昨天的开始时间 */
            String startTime = DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateFormat);
            /** 获取昨天的结束时间 */
            String endTime = DateUtils.getCurrentNeedTime(timeLong, LocalTime.MAX,dateFormat);

            AlipayDataDataserviceBillDownloadurlQueryRequest   request = new AlipayDataDataserviceBillDownloadurlQueryRequest ();
            AlipayDataDataserviceBillDownloadurlQueryModel model = new AlipayDataDataserviceBillDownloadurlQueryModel();
            model.setBillType("trade");
            model.setBillDate(DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateDayFormat));
            request.setBizModel(model);
            AlipayDataDataserviceBillDownloadurlQueryResponse response = InitializingBeanAfter.getAlipayClient().certificateExecute(request);
            logger.debug("获取账单信息：{}",response.getBody());
            doAliBillResult( response, startTime, endTime);
        } catch (AlipayApiException e) {
            logger.error("支付宝获取账单异常");
            throw new RuntimeException("支付宝获取账单异常",e);
        }
    }


    public void doAliBillResult( AlipayDataDataserviceBillDownloadurlQueryResponse response,String startTime,String endTime){
        /** 初始化统计数据 */
        PayDayEndStatisticsEntity payDayEndStatistics = new PayDayEndStatisticsEntity();
        /** 获取前一天时间，插入结算日期 */
        payDayEndStatistics.setDayEndDate(DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateFormatLimitDay));
        payDayEndStatistics.setPayType(PayTypeEnum.TYPE_ALIPAY.getStatus());
        if(!response.isSuccess()) {
            /** 如果账单不存在 */
            if("isp.bill_not_exist".equals(response.getSubCode())){
                payDayEndStatistics.setRemark(String.format("当前状态:%s,状态描述:%s",response.getSubCode(),response.getSubMsg()));
                aliRecordBillNotExist(payDayEndStatistics);
                return;
            }
            logger.error("返回数据解析错误或者数据错误，LastlineContent:{}", response.getBody());
            throw new RuntimeException(String.format("返回数据解析错误或者数据错误,LastlineContent:%s", response.getBody()));
        }

        String filePath = Configs.getBillDownloadPath() + "/fund_bill_"+payDayEndStatistics.getDayEndDate()+".csv.zip";
        BillToolUtils.downLoadAliPayBill(response.getBillDownloadUrl(),filePath);
        /** 解析表单信息 */
        BillToolUtils.zipDecompressing(filePath,Configs.getBillUnzipPath());
        Map<String,List<List<String>>> collect = new HashMap<>();
        BillToolUtils.parse(collect,Configs.getBillUnzipPath());
        /** 删除文件 */
        BillToolUtils.deleteFile(Configs.getBillUnzipPath());
        BillToolUtils.deleteFile(Configs.getBillDownloadPath());
        List<PayDayEndDetailsEntity> payDayEndDetails = new ArrayList<>();
        if(!doReconciliation( payDayEndStatistics, collect, startTime, endTime)){
            /** 处理对账的详情数据 */
            aliPaySuccessResultPostProcessor(payDayEndStatistics,   payDayEndDetails ,collect, startTime, endTime);
            /** 详情数据的添加   */
            if(payDayEndDetails.size() > 0){
                doPayDayEndDetails(payDayEndDetails);
            }
        }
    }

    /**
     * 支付宝账单不存在
     * @param payDayEndStatistics
     */
    void  aliRecordBillNotExist(PayDayEndStatisticsEntity payDayEndStatistics){
        payDayEndStatistics.setReconciliationStatus(ReconciliationStatus.WARN.name());
        /** 记录当前的数据的金额 */
        if(payDayEndStatisticsMapper.insertPayDayEndStatisticsInfo(payDayEndStatistics) < 1){
            throw new RuntimeException(String.format("记录当前的数据的金额失败，记录的数据:%s",payDayEndStatistics));
        }

    }
    /**
     * 阿里支付详情的处理
     * @param payDayEndStatistics
     * @param collect
     * @param startTime
     * @param endTime
     */
    void aliPaySuccessResultPostProcessor(PayDayEndStatisticsEntity payDayEndStatistics,  List<PayDayEndDetailsEntity> payDayEndDetails ,Map<String,List<List<String>>> collect,String startTime,String endTime){
        if(!collect.containsKey("details")){
            return;
        }
        List<List<String>> details = collect.get("details");
        if(CollectionUtils.isEmpty(details) && details.size() > 1){
            return;
        }
        /** 删除标题栏 */
        details.remove(0);
        /** 获取支付流水 */
        List<String> succOrderInfo = new ArrayList<>();
        List<WorderOrderLogDTO> worderOrderLogs
                = worderOrderLogMapper.findOrderLogRecords(startTime,endTime,Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS,PayTypeEnum.TYPE_ALIPAY.getStatus());
        if(worderOrderLogs!=null && worderOrderLogs.size() > 0){
            for(WorderOrderLogDTO worderOrderLog : worderOrderLogs){
                if(StringUtils.isNotBlank(worderOrderLog.getTransactionId())){
                    String transactionId = worderOrderLog.getTransactionId();
                    succOrderInfo.add(transactionId);
                }
            }
        }

        Iterator<List<String>> iterator =  details.iterator();

        while(iterator.hasNext()){

            List<String> lineContent = iterator.next();


            String transactionId = lineContent.get(0);

            if(succOrderInfo.contains(transactionId)){
                /** 包含进行删除 */
                iterator.remove();
            }else{
                //TODO 微信中支付的数据，但是本地支付中或者不存在的数据记录
                /**  组成日结对账错误数据详情对象，添加到列表  */
                PayDayEndDetailsEntity payDayEndDetailsEntity = new PayDayEndDetailsEntity();
                aliPayPostPayDayEndDetailsEntity( payDayEndDetailsEntity,lineContent,  payDayEndStatistics);
                payDayEndDetails.add(payDayEndDetailsEntity);
            }
        }
    }

    /**组成阿里支付详情对象
     *
     * @param payDayEndDetailsEntity
     * @param lineContent
     * @param payDayEndStatistics
     */
    public void aliPayPostPayDayEndDetailsEntity(PayDayEndDetailsEntity payDayEndDetailsEntity, List<String> lineContent, PayDayEndStatisticsEntity payDayEndStatistics){
        logger.info("数据：{}，长度：{}",lineContent,lineContent.size());
        /** 日结日期 */
        payDayEndDetailsEntity.setDayEndDate(payDayEndStatistics.getDayEndDate());
        /** ﻿交易时间 */
        payDayEndDetailsEntity.setTradeTime(lineContent.get(5));
        /** 支付宝交易号 */
        payDayEndDetailsEntity.setTransactionId(lineContent.get(0));
        /** 商户订单号 */
        payDayEndDetailsEntity.setOrderNo(lineContent.get(1));
        /** 交易类型 */
        payDayEndDetailsEntity.setTradeType("0");
        /** 交易状态 */
        payDayEndDetailsEntity.setTradeStatus("");
        /** 手续费 */
        payDayEndDetailsEntity.setHandlingFee(new BigDecimal(lineContent.get(22)));
        /** 费率  */
        payDayEndDetailsEntity.setRate("");
        /** 交易金额 */
        payDayEndDetailsEntity.setTotalAcount(new BigDecimal(lineContent.get(11)));
        /** 备注 */
        payDayEndDetailsEntity.setRemark("");
        logger.info("详情信息：{}",payDayEndDetailsEntity);
    }
    /**
     * 支付宝总金额比较
     * @param payDayEndStatistics
     * @param collect
     * @param startTime
     * @param endTime
     * @return
     */
    boolean  doReconciliation(PayDayEndStatisticsEntity payDayEndStatistics, Map<String,List<List<String>>> collect,String startTime,String endTime){
        boolean flag = true;
        if(collect.containsKey("gather")){
            List<List<String>> gather = collect.get("gather");
            doGatherBill(payDayEndStatistics,gather);
        }
//        /**  手续费总金额 */
//        payDayEndStatistics.setHandlingFee(new BigDecimal(LastlineContents[4]));
        /** 获取本地支付记录数 */
        LocalPayDataStatisticEntity LocalPayDataStatisticEntity = worderOrderLogMapper.findOrderLogRecordCounts(startTime,endTime,Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS, PayTypeEnum.TYPE_ALIPAY.getStatus());

        if(LocalPayDataStatisticEntity!=null){
            /** 本地总交易单数 */
            payDayEndStatistics.setLocalBillNum(LocalPayDataStatisticEntity.getCounts());
            /** 本地账总交易额 */
            payDayEndStatistics.setLocalBillToatalAmount( LocalPayDataStatisticEntity.getPayActualTotalAmount()!=null
                    ?LocalPayDataStatisticEntity.getPayActualTotalAmount():new BigDecimal("0"));
            flag = payDayEndStatistics.getLocalBillNum()!=null && payDayEndStatistics.getLocalBillToatalAmount()!=null
                    && payDayEndStatistics.getLocalBillNum().equals(payDayEndStatistics.getReconciliationBillNum())
                    && payDayEndStatistics.getLocalBillToatalAmount().compareTo(payDayEndStatistics.getReconciliationBillToatalAmount()) == 0;
            logger.info("数据单数的总数以及总金额的对比:localBillNum:{},localBillToatalAmount:{},reconciliationBillNum:{},reconciliationBillToatalAmount:{},返回结果:{}"
                    ,payDayEndStatistics.getLocalBillNum(),payDayEndStatistics.getLocalBillToatalAmount()
                    ,payDayEndStatistics.getReconciliationBillNum(),payDayEndStatistics.getReconciliationBillToatalAmount(),flag);
        }
        payDayEndStatistics.setReconciliationStatus(flag? ReconciliationStatus.SUCEESS.name():ReconciliationStatus.WARN.name());
        /** 记录当前的数据的金额 */
        if(payDayEndStatisticsMapper.insertPayDayEndStatisticsInfo(payDayEndStatistics) < 1){
            throw new RuntimeException(String.format("记录当前的数据的金额失败，记录的数据:%s",payDayEndStatistics));
        }
        return flag;
    }

    /**
     * 汇总账单的处理
     * @param gather
     */
    void doGatherBill(PayDayEndStatisticsEntity payDayEndStatistics,List<List<String>> gather){
        if(CollectionUtils.isEmpty(gather)){
            return;
        }
        List<String>  total =   gather.get( gather.size()-1);
        if(!total.get(0).contains("合计")){
            return;
        }
        /** 交易订单总笔数 */
        payDayEndStatistics.setReconciliationBillNum(Integer.valueOf(StringUtils.isEmpty(total.get(2))?"0":total.get(2).trim()));
        /** 总交易额 */
        payDayEndStatistics.setReconciliationBillToatalAmount(new BigDecimal(StringUtils.isEmpty(total.get(4))?"0":total.get(4).trim()));
    }

    /**
     * 汇总账单的处理
     * @param details
     */
    void doDetailsBill(List<List<String>> details){

        if(CollectionUtils.isEmpty(details)){
            return;
        }
        for(int i = details.size()-1; i>=0 ;i--){
        }
    }
    /**
     * 微信的账单处理
     */
   public void wxBillResult(){
       try {
           /**   对象的处理   */
           SortedMap<String,String> parameters = new TreeMap<String,String>();
           /** 组建请求对象 */
           payDataPostPorccessor( parameters);
           /** 转化成你xml */
           String  generateSignedXml = WXPayUtil.generateSignedXml(parameters, WxConfig.getApiKey());
           logger.info("微信支付预下单请求xml格式：："+generateSignedXml);
           /** 获取请求返回消息 */
           List<String> lineContents = CommonUtil.getLineNumberReader(WxConfig.getDownloadBillUrl(), "POST", generateSignedXml);
            /** 数据流的处理 */
           doResult(lineContents);
       } catch (Exception e) {
           logger.error("异常信息:{}",e.getMessage());
           throw new RuntimeException(String.format("异常信息:%s",e.getMessage()));
       }
   }

    /**
     * 处理数据流
     * @param lineContents
     */
    void doResult(List<String> lineContents) throws ParseException {
        /** 初始化统计数据 */
        PayDayEndStatisticsEntity payDayEndStatistics = new PayDayEndStatisticsEntity();
        /** 获取前一天时间，插入结算日期 */
        payDayEndStatistics.setDayEndDate(DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateFormatLimitDay));
        /** 获取昨天的开始时间 */
        String startTime = DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateFormat);
        /** 获取昨天的结束时间 */
        String endTime = DateUtils.getCurrentNeedTime(timeLong, LocalTime.MAX,dateFormat);
        logger.info("日期,结账日期:{},昨天的开始时间:{},昨天的结束时间:{}",payDayEndStatistics.getDayEndDate(),startTime,endTime);
        /** 创建日结对账错误数据详情对象 */
        List<PayDayEndDetailsEntity> payDayEndDetails = new ArrayList<>();
        /** 判断返回结果是xml还是对账数据 */
        String LastlineContent =  isXml(lineContents);
        if(Constant.RETURNCODE_NULL.equals(LastlineContent)){
            logger.warn("数据不存在");
            return;
        }
        if(null == LastlineContent){
            /** 返回的数据是xml */
            xmlResultPostProcessor(lineContents);
            return;
        }
        /** 处理返回成功是对账的数据 */
        if(!doSuccessResultLastData(LastlineContent,payDayEndStatistics,startTime,endTime)){
            /** 处理对账的详情数据 */
            successResultPostProcessor(lineContents, payDayEndDetails ,payDayEndStatistics,startTime,endTime);
            /** 详情数据的添加   */
            if(payDayEndDetails.size() > 0){
                doPayDayEndDetails(payDayEndDetails);
            }
        }
    }

    void doPayDayEndDetails(List<PayDayEndDetailsEntity> payDayEndDetails){
        if(payDayEndDetails!=null && payDayEndDetails.size() > 0){
            payDayEndDetailsMapper.insertPayDayEndDetaisInfo(payDayEndDetails);
        }
    }

    /**
     * 返回结果的最后一行的数据进行处理
     * @param LastlineContent
     * @param payDayEndStatistics
     * @return 返回true则记录详情 false则不处理
     */
    boolean doSuccessResultLastData(String LastlineContent , PayDayEndStatisticsEntity payDayEndStatistics,String startTime,String endTime) throws ParseException {
        boolean flag = true;
        String [] LastlineContents = LastlineContent.replaceAll("\\`","").split("\\,");
        /** 处理本地的数据 */
        if(LastlineContents==null || LastlineContents.length != 7){
            logger.error("返回数据解析错误或者数据错误，LastlineContent:{}",LastlineContent);
            throw new RuntimeException(String.format("返回数据解析错误或者数据错误,LastlineContent:%s",LastlineContent));
        }
        /** 总交易单数 */
        payDayEndStatistics.setReconciliationBillNum(Integer.valueOf(LastlineContents[0]));
        /** 总交易额 */
        payDayEndStatistics.setReconciliationBillToatalAmount(new BigDecimal(LastlineContents[5]));
        /**  手续费总金额 */
        payDayEndStatistics.setHandlingFee(new BigDecimal(LastlineContents[4]));
        /** 获取本地支付记录数 */
        LocalPayDataStatisticEntity LocalPayDataStatisticEntity = worderOrderLogMapper.findOrderLogRecordCounts(startTime,endTime,Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS, PayTypeEnum.TYPE_WXPAY.getStatus());

        if(LocalPayDataStatisticEntity!=null){
            /** 本地总交易单数 */
            payDayEndStatistics.setLocalBillNum(LocalPayDataStatisticEntity.getCounts());
            /** 本地账总交易额 */
            payDayEndStatistics.setLocalBillToatalAmount( LocalPayDataStatisticEntity.getPayActualTotalAmount());
            flag = payDayEndStatistics.getLocalBillNum()!=null && payDayEndStatistics.getLocalBillToatalAmount()!=null
                            && payDayEndStatistics.getLocalBillNum().equals(payDayEndStatistics.getReconciliationBillNum())
                            && payDayEndStatistics.getLocalBillToatalAmount().compareTo(payDayEndStatistics.getReconciliationBillToatalAmount()) == 0;
            logger.info("数据单数的总数以及总金额的对比:localBillNum:{},localBillToatalAmount:{},reconciliationBillNum:{},reconciliationBillToatalAmount:{},返回结果:{}"
                    ,payDayEndStatistics.getLocalBillNum(),payDayEndStatistics.getLocalBillToatalAmount()
                    ,payDayEndStatistics.getReconciliationBillNum(),payDayEndStatistics.getReconciliationBillToatalAmount(),flag);
        }
        payDayEndStatistics.setReconciliationStatus(flag? ReconciliationStatus.SUCEESS.name():ReconciliationStatus.WARN.name());
        /** 记录当前的数据的金额 */
        if(payDayEndStatisticsMapper.insertPayDayEndStatisticsInfo(payDayEndStatistics) < 1){
            throw new RuntimeException(String.format("记录当前的数据的金额失败，记录的数据:%s",payDayEndStatistics));
        }
        return flag;
    }

    /**
     *  返回成功的处理对账的详情数据(移除第一行和最后一行数据)的处理
     *  未出现
     * @param lineContents
     */
    void successResultPostProcessor(List<String> lineContents, List<PayDayEndDetailsEntity> payDayEndDetails , PayDayEndStatisticsEntity payDayEndStatistics,String startTime,String endTime){
        /** 获取当前返回成功的数据 */
        List<String> succOrderInfo = new ArrayList<>();
        List<WorderOrderLogDTO> worderOrderLogs
                = worderOrderLogMapper.findOrderLogRecords(startTime,endTime,Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS,PayTypeEnum.TYPE_WXPAY.getStatus());
        if(worderOrderLogs!=null && worderOrderLogs.size() > 0){
            for(WorderOrderLogDTO worderOrderLog : worderOrderLogs){
                if(StringUtils.isNotBlank(worderOrderLog.getTransactionId())){
                    String transactionId = worderOrderLog.getTransactionId();
                    succOrderInfo.add(transactionId);
                }
            }
        }
        Iterator<String> iterator =  lineContents.iterator();
        while(iterator.hasNext()){

            String lineContent = iterator.next();

            String [] lineContentArr = lineContent.replaceAll("\\`","").split("\\,");

            String transactionId = lineContentArr[5];

            if(succOrderInfo.contains(transactionId)){
                /** 包含进行删除 */
                iterator.remove();
            }else{
                //TODO 微信中支付的数据，但是本地支付中或者不存在的数据记录
                /**  组成日结对账错误数据详情对象，添加到列表  */
                PayDayEndDetailsEntity payDayEndDetailsEntity = new PayDayEndDetailsEntity();
                postPayDayEndDetailsEntity(payDayEndDetailsEntity,lineContentArr,payDayEndStatistics);
                payDayEndDetails.add(payDayEndDetailsEntity);
            }
        }
        /** 判断是否还存在的订单不在本地系统的数据的支付成功 */
        if(lineContents.size() > 0){
            /** 获取为支付完成的数据 */
            //TODO 获取为本地支付未记录完成的数据，进行处理（暂不处理）


        }
    }


    /**
     * 组成详情对象
     * @param payDayEndDetailsEntity
     * @param lineContentArr
     * @param payDayEndStatistics
     */
    public void postPayDayEndDetailsEntity(PayDayEndDetailsEntity payDayEndDetailsEntity, String [] lineContentArr, PayDayEndStatisticsEntity payDayEndStatistics){
        /** 日结日期 */
        payDayEndDetailsEntity.setDayEndDate(payDayEndStatistics.getDayEndDate());
        /** ﻿交易时间 */
        payDayEndDetailsEntity.setTradeTime(lineContentArr[0]);
        /** 微信订单号 */
        payDayEndDetailsEntity.setTransactionId(lineContentArr[5]);
        /** 商户订单号 */
        payDayEndDetailsEntity.setOrderNo(lineContentArr[6]);
        /** 交易类型 */
        payDayEndDetailsEntity.setTradeType(lineContentArr[8]);
        /** 交易状态 */
        payDayEndDetailsEntity.setTradeStatus(lineContentArr[9]);
        /** 手续费 */
        payDayEndDetailsEntity.setHandlingFee(new BigDecimal(lineContentArr[16]));
        /** 费率  */
        payDayEndDetailsEntity.setRate(lineContentArr[17]);
        /** 交易状态 */
        payDayEndDetailsEntity.setTotalAcount(new BigDecimal(lineContentArr[18]));
        /** 备注 */
        payDayEndDetailsEntity.setRemark("");
    }
    /**
     * 判断返回结果是否是xml,如果返回的是xml则表示当前日期没有账单数据
     * @param lineContents
     * @return
     */
    String isXml(List<String> lineContents){
        if(lineContents!=null && lineContents.size()>0){
            String LastlineContent = lineContents.get(lineContents.size()-1);
            if(LastlineContent.contains("</xml>")){
                return null;
            }
            /** 移除第一行和最后一行数据 */
            lineContents.remove(0);
            /** 删除最后一行 */
            lineContents.remove(lineContents.size()-1);
            /** 删除最后倒数第二行 */
            lineContents.remove(lineContents.size()-1);
            return LastlineContent;
        }
        return Constant.RETURNCODE_NULL;
    }
    /**
     * 处理XML数据转换后的Map数据
     * */
    void  xmlResultPostProcessor(List<String> lineContents){
        try {
            StringBuffer respData = new StringBuffer();
            lineContents.forEach(e->{
                respData.append(e);
            });
            Map<String,String> respMap =  WXPayUtil.xmlToMap(respData.toString());
            doXmlResultPost( respMap);
        } catch (Exception e) {
           throw new RuntimeException(String.format("处理对账没有成功的返回结果数据失败,异常信息:%s",e.getMessage()));
        }
    }


    void doXmlResultPost(Map<String,String> respMap){
        String code =  respMap.get("return_code");
        if(ReconciliationStatus.FAIL.name().equals(code)){
            PayDayEndStatisticsEntity payDayEndStatisticsEntity = new PayDayEndStatisticsEntity();
            if("20002".equals(respMap.get("error_code"))){
                payDayEndStatisticsEntity.setRemark(String.format("当前状态:%s(注:%s)",respMap.get("return_msg")
                        ,"NO Bill Exist : 账单不存在,Bill Creating 账单未生成"));
                payDayEndStatisticsEntity.setDayEndDate(DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateFormatLimitDay));
                payDayEndStatisticsEntity.setReconciliationStatus(ReconciliationStatus.WARN.name());
            }else if("20001".equals(respMap.get("error_code"))){
                payDayEndStatisticsEntity.setRemark(respMap.get("return_msg"));
                payDayEndStatisticsEntity.setDayEndDate(DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateFormatLimitDay));
                payDayEndStatisticsEntity.setReconciliationStatus(ReconciliationStatus.FAIL.name());
            }else if("20007".equals(respMap.get("error_code"))){
                payDayEndStatisticsEntity.setRemark(respMap.get("return_msg"));
                payDayEndStatisticsEntity.setDayEndDate(DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateFormatLimitDay));
                payDayEndStatisticsEntity.setReconciliationStatus(ReconciliationStatus.FAIL.name());
            }else {
                //TODO 处理XML数据转换后的respMap 其他类型的数据
                throw new RuntimeException("其他的类型返回信息不做处理");
            }
            payDayEndStatisticsMapper.insertPayDayEndStatisticsInfo(payDayEndStatisticsEntity);
        }

    }

    /**
     * 获取文件的总行数
     * */
   boolean getLastLineNum(List<String> lineContents){
       if(lineContents!=null && lineContents.size()>0){
           String lineContent = lineContents.get(lineContents.size());
       }
       return false;
   }

    /**
     * 处理支付对象数据
     * */
    public void payDataPostPorccessor( SortedMap<String,String> parameters){
        /** 服务号应用ID */
        parameters.put("appid",WxConfig.getAppId());
        /** 对账日期 */
        parameters.put("bill_date", DateUtils.getCurrentNeedTime(timeLong, LocalTime.MIN,dateFormatLimitDay));
        /** 账单类型 */
        parameters.put("bill_type",WxConfig.getDownloadBillType());
        /** 商户号ID */
        parameters.put("mch_id", WxConfig.getMchId());
        /** 随机字符串 */
        parameters.put("nonce_str", WXPayUtil.generateNonceStr());
        /** 签名类型 */
        parameters.put("sign_type",WxConfig.getSginType());
        try {
            parameters.put("sign",WXPayUtil.generateSignature(parameters , WxConfig.getApiKey(), WXPayConstants.SignType.MD5));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
