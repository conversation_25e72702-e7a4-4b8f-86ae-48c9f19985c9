package com.bonc.rrs.pay.controller;

import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.pay.annotation.PayOperLog;
import com.bonc.rrs.pay.enums.PayOperProcessor;
import com.bonc.rrs.pay.manage.AsyncManager;
import com.bonc.rrs.pay.manage.factory.AsyncFactory;
import com.bonc.rrs.pay.service.PayService;
import com.common.pay.wxpay.util.WXPayUtil;
import com.google.gson.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@RestController
@RequestMapping("/payCallback")
public class PayCallbackCtroller {
    @Autowired
    private PayService payService;

    /**
     * 返回消息处理
     * */
    @RequestMapping("/synchronouNotify")
    @PayOperLog(title= PayOperProcessor.SYNCHRONOUNOTIFY)
    public Object synchronouNotify(HttpServletRequest request){
        return payService.synchronouNotify(request);
    }

    @GetMapping("/test")
    public Object test(){
        payService.test();
        return null;
    }

    /**
     * 支付包回调接口
     * @param request
     * @return
     */
    @RequestMapping("/alipayCallback")
    @ResponseBody
    public Object alipayCallback(HttpServletRequest request){
        return payService.alipayCallback(request);
    }

    /**
     * 支付包回调接口
     * @return
     */
    @RequestMapping("/sendPay")
    @ResponseBody
    public void sendPay(HttpServletRequest request){
        String orderNo = request.getParameter("orderNo");
        Long userId = Long.valueOf(request.getParameter("userId"));
        /** 调用acs收款数据 */
        AsyncManager.instance().execute(AsyncFactory.asyncBilling(orderNo, userId), 1000);
    }
}
