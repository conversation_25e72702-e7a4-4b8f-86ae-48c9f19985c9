package com.bonc.rrs.pay.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.bonc.rrs.pay.annotation.PayOperLog;
import com.bonc.rrs.pay.entity.entity.WorderOrderLogEntity;
import com.bonc.rrs.pay.entity.request.CycleQueyRequest;
import com.bonc.rrs.pay.entity.request.GoPayRequestDTO;
import com.bonc.rrs.pay.entity.request.PayInfoRequest;
import com.bonc.rrs.pay.entity.request.RefundRequest;
import com.bonc.rrs.pay.enums.PayOperProcessor;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.pay.service.PayService;
import com.bonc.rrs.pay.service.WorderOrderLogService;
import com.bonc.rrs.payment.service.LockService;
import com.youngking.renrenwithactiviti.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description
 * @see
 */
@RestController
@RequestMapping("/pay")
@CrossOrigin
public class PayController  extends AbstractController {
    @Autowired
    private PayService payService;

    @Autowired
    private LockService lockService;

    @Autowired
    private WorderOrderLogService worderOrderLogService;
    /**
     * 调用支付接口生成二维码
     * */
    @PostMapping("/goPay")
    public Object goPay(HttpServletRequest request, @RequestBody GoPayRequestDTO goPayRequest){
        return lockService.qrCode(request,goPayRequest,getUser());
    }

    @GetMapping("/test")
    public Object test(){
        // return payService.test();
        return    payService.getSandboxSignKey();
//        payService.refundMoney();
//        return null;
    }



    /**
     * 获取支付的信息
     * */
    @PostMapping("/findPayInfo")
    @PayOperLog(title= PayOperProcessor.QUERY)
    public Object findPayInfo(@RequestBody PayInfoRequest payInfoRequest){
        return payService.findPayInfo(payInfoRequest);
    }

    /**
     * 查询订单情况接口
     * @param cycleQueyRequest
     * @return
     */
    @PostMapping("/payCycleQuey")
    @PayOperLog(title= PayOperProcessor.CYCLE_QUERY)
    public Object  payCycleQuey(@RequestBody CycleQueyRequest cycleQueyRequest){
        return payService.payCycleQuey(cycleQueyRequest);
    }
}
