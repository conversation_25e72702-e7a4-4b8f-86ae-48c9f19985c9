package com.bonc.rrs.pay.enums;

import com.youngking.lenmoncore.common.utils.StringUtils;

/**
 * <AUTHOR>
 * @description
 * @see
 */
public enum PayOperResultCode {

    SUCCESS(0,"支付成功"),
    REFUND(2,"转入退款"),
    NOTPAY(3,"未支付"),
    CLOSED(4,"已关闭"),
    REVOKED(5,"已撤销"),
    USERPAYING(6,"用户支付中"),
    PAYERROR(7,"支付失败"),
    THROWING(10,"抛出异常"),
    REPEAT(204,"订单已支付,重复生成二维码");
    private int status;
    private String desc;

    private  PayOperResultCode(int status,String desc){
        this.status = status;
        this.desc = desc;
    }
    public static boolean containName(String name){
        for(PayOperResultCode value : PayOperResultCode.values()){
            if(value.name().equalsIgnoreCase(name)){
                return true;
            }
        }
        return false;
    }
    public static int getStatus(String name){
        for(PayOperResultCode value : PayOperResultCode.values()){
            if(value.name().equalsIgnoreCase(name)){
                return value.getStatus();
            }
        }
        return -1;
    }
    public static String getDesc(int status){
        for(PayOperResultCode value : PayOperResultCode.values()){
            if(value.getStatus() == status){
                return value.getDesc();
            }
        }
        return null;
    }
    public static String getDescByName(String name){
        if(StringUtils.isEmpty(name)){
            return null;
        }
        for(PayOperResultCode value : PayOperResultCode.values()){
            if(value.name().equals(name)){
                return value.getDesc();
            }
        }
        return null;
    }
    public static String getName(int status){
        if(status==0 || status == 200){
            return SUCCESS.name();
        }
        for(PayOperResultCode value : PayOperResultCode.values()){
            if(value.getStatus() == status){
                return value.name();
            }
        }
        return "FAIL";
    }
    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
