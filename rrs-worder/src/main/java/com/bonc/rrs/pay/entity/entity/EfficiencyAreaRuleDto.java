
package com.bonc.rrs.pay.entity.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/9 14:23
 * @Version 1.0.0
 */

@Data
@NoArgsConstructor
@ToString
public class EfficiencyAreaRuleDto {
    @ExcelProperty(value  = "品牌名称")
    private String brandName;

    @ExcelProperty(value  = "品牌id")
    private String brandId;

    @ExcelProperty(value  = "区域名称")
    private String execrule;


    @ExcelProperty(value  = "地区编码")
    private String regcode;


    @ExcelProperty(value  = "地区名称")
    private String regName;

}