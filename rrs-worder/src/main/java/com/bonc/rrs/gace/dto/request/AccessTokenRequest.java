package com.bonc.rrs.gace.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
public class AccessTokenRequest extends RequestDto{
    private String user = "12341111234";
    private String apptoken;
    @JsonProperty("loginType")
    private String loginType = "2";
    private String language = "zh_CN";
}