package com.bonc.rrs.gace.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class AuditStatusRequest extends RequestDto {
    private String billno;

    @JsonIgnore
    public String getUrlParams() {
        return "?billno=" + billno;
    }
}