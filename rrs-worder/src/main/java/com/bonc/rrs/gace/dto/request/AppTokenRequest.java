package com.bonc.rrs.gace.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
public class AppTokenRequest extends RequestDto{
    @JsonProperty("appId")
    private String appId = "DMJ";
    @JsonProperty("appSecret")
    private String appSecret = "DMJ@202410150001123a";
    @JsonProperty("language")
    private String language = "zh-CN";
}