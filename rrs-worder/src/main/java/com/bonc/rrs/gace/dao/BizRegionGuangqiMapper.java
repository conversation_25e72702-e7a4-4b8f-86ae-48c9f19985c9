package com.bonc.rrs.gace.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.gace.entity.BizRegionGuangqi;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广汽区域表数据库操作Mapper
 * <AUTHOR>
 * @description 针对表【biz_region_guangqi(广汽区域表)】的数据库操作Mapper
 */
@Mapper
public interface BizRegionGuangqiMapper extends BaseMapper<BizRegionGuangqi> {

    /**
     * 批量插入
     * @param list 广汽区域数据列表
     * @return 插入条数
     */
    int insertBatch(@Param("list") List<BizRegionGuangqi> list);

    /**
     * 根据位置名称标识查询
     * @param positionNameIdentifier 位置名称标识
     * @return 广汽区域信息
     */
    BizRegionGuangqi selectByPositionNameIdentifier(@Param("positionNameIdentifier") String positionNameIdentifier);

    /**
     * 清空表数据
     * @return 删除条数
     */
    int truncateTable();

    /**
     * 根据位置名称标识更新记录（保护已有的region_id）
     * @param entity 广汽区域数据
     * @return 更新条数
     */
    int updateByPositionNameIdentifier(@Param("entity") BizRegionGuangqi entity);

    /**
     * 批量更新或插入（存在则更新，不存在则插入）
     * @param list 广汽区域数据列表
     * @return 处理条数
     */
    int insertOrUpdateBatch(@Param("list") List<BizRegionGuangqi> list);
}
