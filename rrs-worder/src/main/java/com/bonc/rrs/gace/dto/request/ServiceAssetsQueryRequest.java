package com.bonc.rrs.gace.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 服务商设备台账查询请求
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ServiceAssetsQueryRequest extends RequestDto {
    
    /**
     * 设备编码
     */
    private String number;
    
    /**
     * 资产管理状态.状态编码
     */
    private String asset_status_id_number;
    
    /**
     * 专业归类.专业归类编码
     */
    private String mgmt_class_number;
    
    /**
     * 位置.位置名称/标识，即设备存在的仓库位置，不同服务商对应标识不一致，需联系系统管理员获取
     */
    private String location_id_number;
    
    /**
     * 分页参数，分页数量
     */
    private Integer pageSize;
    
    /**
     * 分页参数，查询页码
     */
    private Integer pageNo;
}
