/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 车企回款天数表
 *
 */
@Data
@TableName("vendor_receivable_days")
@ApiModel(value = "车企回款天数表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceivableDaysEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private int id;
    /**
     * 厂商id
     */
    private int vendorId;

    private int days;

    private Date createTime;

}
