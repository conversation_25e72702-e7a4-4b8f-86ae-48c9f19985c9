package com.bonc.rrs.vendorbalancemanage.controller;

import com.bonc.rrs.vendorbalancemanage.entity.VendorInvoiceEntity;
import com.bonc.rrs.vendorbalancemanage.service.VendorInvoiceService;
import com.bonc.rrs.vendorbalancemanage.vo.VendorInvoiceVO;
import com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import com.youngking.lenmoncore.common.annotation.SysLog;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ASUS on 2020/1/14.
 * 原来的工单开票回款逻辑，弃用
 */
@RestController
@RequestMapping("/vendorInvoice/")
@Api(tags = {"工单开票相关接口"})
public class VendorInvoiceController extends AbstractController {

    private final VendorInvoiceService vendorInvoiceService;
    private final WorderInformationAccountService worderInformationAccountService;

    public VendorInvoiceController(VendorInvoiceService vendorInvoiceService, WorderInformationAccountService worderInformationAccountService) {
        this.vendorInvoiceService = vendorInvoiceService;
        this.worderInformationAccountService = worderInformationAccountService;
    }

    @PostMapping("/queryVendorInvoiceList")
    @ApiOperation(value = "已开发票列表", notes = "已开发票列表")
    public R queryVendorInvoiceList(@RequestBody VendorInvoiceVO vendorInvoiceVO){
        PageUtils page = vendorInvoiceService.queryVendorInvoiceList(vendorInvoiceVO);
        return R.ok().put("page", page);
    }

    @PostMapping("/queryWorderInfoListByInvoiceId")
    @ApiOperation(value = "已开发票工单列表", notes = "已开发票工单列表")
    public R queryWorderInfoListByInvoiceId(@RequestBody @Validated(value = VendorInvoiceVO.Query.class) VendorInvoiceVO vendorInvoiceVO){
        PageUtils page = vendorInvoiceService.queryWorderInfoListByInvoiceId(vendorInvoiceVO);
        return R.ok().put("page", page);
    }

    @PostMapping("/updateReceivableFeeById")
    @ApiOperation(value = "保存回款金额", notes = "保存回款金额")
    @SysLog("保存回款金额")
    public R updateReceivableFeeById(@RequestBody @Validated(value = VendorInvoiceVO.Save.class) VendorInvoiceVO vendorInvoiceVO){
        VendorInvoiceEntity invoiceEntity = new VendorInvoiceEntity();
        //已回款金额
        BigDecimal receivableFee = new BigDecimal(vendorInvoiceVO.getReceivableFee().toString());
        //本次回款金额
        BigDecimal newReceivableFee = new BigDecimal(vendorInvoiceVO.getNewReceivableFee().toString());
        //未回款金额
        BigDecimal noReceivableFee = new BigDecimal(vendorInvoiceVO.getNoReceivableFee().toString());
        //未支付余额  = 已回款金额 - 已支付余额    或者 = 未支付余额+本次回款金额
        BigDecimal noPaymentFee = new BigDecimal(vendorInvoiceVO.getNoPaymentFee().toString());

//        BigDecimal validateFee = vendorInvoiceVO.getInvoiceFee().subtract(receivableFee.add(newReceivableFee));
//        if(validateFee.intValue() < 0)
//            return R.error(500,"车企结算金额不能大于未支付余额");

        noReceivableFee = noReceivableFee.subtract(newReceivableFee);
        if(noReceivableFee.intValue() < 0)
            return R.error(500,"回款金额不能大于发票余额");
        invoiceEntity.setId(vendorInvoiceVO.getId());
        //已回款金额
        invoiceEntity.setReceivableFee(receivableFee.add(newReceivableFee));
        //未回款金额
        invoiceEntity.setNoReceivableFee(noReceivableFee);
        //未支付余额
        invoiceEntity.setNoPaymentFee(noPaymentFee.add(newReceivableFee));
        vendorInvoiceService.updateById(invoiceEntity);
        return R.ok().put("invoiceEntity",invoiceEntity);
    }

    @PostMapping("/worderConfirmPayment")
    @ApiOperation(value = "工单确认回款", notes = "工单确认回款")
    @Transactional
    @SysLog("工单确认回款")
    public R worderConfirmPayment(@RequestBody @Validated(value = VendorInvoiceVO.Confirm.class) VendorInvoiceVO vendorInvoiceVO){
        VendorInvoiceEntity invoiceEntity = new VendorInvoiceEntity();
        //车企结算金额
        BigDecimal vendorBalanceFee = new BigDecimal(vendorInvoiceVO.getVendorBalanceFee().toString());
        //未支付余额
        BigDecimal noPaymentFee = new BigDecimal(vendorInvoiceVO.getNoPaymentFee().toString());
        //已支付余额
        BigDecimal paymentFee = new BigDecimal(vendorInvoiceVO.getPaymentFee().toString());
        invoiceEntity.setId(vendorInvoiceVO.getId());
        //未支付余额 = 未支付余额 - 车企结算金额
        noPaymentFee = noPaymentFee.subtract(vendorBalanceFee);
        if(noPaymentFee.intValue() < 0)
            return R.error(500,"车企结算金额不能大于未支付余额");
        invoiceEntity.setNoPaymentFee(noPaymentFee);
        invoiceEntity.setPaymentFee(paymentFee.add(vendorBalanceFee));
        List<WorderInformationAccountEntity> list = new ArrayList<WorderInformationAccountEntity>();
        for (Integer id: vendorInvoiceVO.getIds()) {
            WorderInformationAccountEntity worderInformationAccountEntity = new WorderInformationAccountEntity();
            worderInformationAccountEntity.setWorderId(id);
            worderInformationAccountEntity = worderInformationAccountService.getById(worderInformationAccountEntity);
            if(worderInformationAccountEntity.getWorderSetStatus() == 4)
                return R.error(500,"已回款工单不能重复回款");
            worderInformationAccountEntity.setWorderSetStatus(4);
            worderInformationAccountEntity.setWorderSetStatusValue("车企已回款");
            list.add(worderInformationAccountEntity);
        }
        //发票 已支付/未支付金额更新
        vendorInvoiceService.updateById(invoiceEntity);
        //工单批量确认回款
        worderInformationAccountService.updateBatchById(list);
        return R.ok().put("invoiceEntity",invoiceEntity);
    }

    @PostMapping("/getReceivableInvoiceList")
    @ApiOperation(value = "可回款发票列表", notes = "可回款发票列表")
    public R getReceivableInvoiceList(@RequestBody @Validated(value = VendorInvoiceVO.AutoQuery.class) VendorInvoiceVO vendorInvoiceVO){
        return vendorInvoiceService.getReceivableInvoiceList(vendorInvoiceVO);
    }

    @PostMapping("/confirmAutoReceivable")
    @ApiOperation(value = "自动回款确认", notes = "自动回款确认")
    @SysLog("自动回款确认")
    public R confirmAutoReceivable(@RequestBody @Validated(value = VendorInvoiceVO.AutoReceivable.class) VendorInvoiceVO vendorInvoiceVO){
        return vendorInvoiceService.confirmAutoReceivable(vendorInvoiceVO);
    }
}
