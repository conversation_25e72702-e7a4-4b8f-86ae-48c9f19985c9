/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.vendorbalancemanage.entity.VendorInvoiceEntity;
import com.bonc.rrs.vendorbalancemanage.vo.VendorInvoiceVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 */
@Mapper
public interface VendorInvoiceDao extends BaseMapper<VendorInvoiceEntity> {


    List queryVendorInvoicList(VendorInvoiceVO vendorInvoiceVO);

    Integer queryVendorInvoicTotalCount(VendorInvoiceVO vendorInvoiceVO);

    List queryWorderInfoListByInvoiceId(VendorInvoiceVO vendorInvoiceVO);

    Integer queryWorderInfoCountByInvoiceId(VendorInvoiceVO vendorInvoiceVO);

    List getReceivableInvoiceList(VendorInvoiceVO vendorInvoiceVO);

    Integer updateWorderSetStatusByInvoiceId(@Param("id") Integer id,@Param("typeId") Integer typeId);


}
