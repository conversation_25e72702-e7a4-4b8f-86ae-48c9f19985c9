/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.vendorbalancemanage.entity.VendorInvoiceEntity;
import com.bonc.rrs.vendorbalancemanage.vo.VendorInvoiceVO;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;

/**
 *
 */
public interface VendorInvoiceService extends IService<VendorInvoiceEntity> {

    public PageUtils queryVendorInvoiceList(VendorInvoiceVO vendorInvoiceVO);

    PageUtils queryWorderInfoListByInvoiceId(VendorInvoiceVO vendorInvoiceVO);

    public R getReceivableInvoiceList(VendorInvoiceVO vendorInvoiceVO);

    R confirmAutoReceivable(VendorInvoiceVO vendorInvoiceVO);
}
