/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.vo;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
public class ReceivableDaysVO {


    public interface Insert {
    }

    public interface Update {
    }
    /**
     * id
     */
    @NotNull(message = "id不能为空" , groups = Update.class)
    private Integer id;
    /**
     * 厂商id
     */
    @NotNull(message = "厂商不能为空",groups = {Update.class, Insert.class})
    private Integer companyId;

    @NotNull(message = "回款天数不能为空", groups = {Update.class, Insert.class})
    @Min(value = 1,message = "回款天数必须是数字,且最小天数为1", groups = {Update.class, Insert.class})
    private Integer days;

    private Date createTime;

    //当前页
    private Integer curPage;

    //每页记录数
    private Integer limit;

    //分页起始页
    private Integer page;

    private List<Integer> ids;


}
