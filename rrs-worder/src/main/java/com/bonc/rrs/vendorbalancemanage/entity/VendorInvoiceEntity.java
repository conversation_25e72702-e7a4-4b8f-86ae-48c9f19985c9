/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 */
@Data
@TableName("vendor_invoice")
@ApiModel(value = "车企发票表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VendorInvoiceEntity {


    /**
     * id
     */
    private Integer id;


    /**
     * 车企id
     */
    private Integer vendorId;

    //发票金额(含税)
    private BigDecimal invoiceFee;

    //税额
    private BigDecimal tax;

    //不含税金额
    private BigDecimal noTaxFee;

    //已回款金额
    private BigDecimal receivableFee;

    //本次回款金额
    private BigDecimal newReceivableFee;

    //未回款金额
    private BigDecimal noReceivableFee;

    //已支付余额 +=车企结算金额
    private BigDecimal paymentFee;

    //未支付余额  = 已回款金额 - 已支付余额
    private BigDecimal noPaymentFee;

    private Date createTime;

    //调整尾差的工单ID
    private Integer adjustedWorderId;

    //调整尾差描述
    private String adjustedDesc;

    //开票状态。0:等待开票 1:已开票 2:已记账 3:开票失败 4:记账失败
    private Integer status;

    /**
     * 发票单号，调用开票系统接口的唯一标识
     */
    private String invoiceOrderNo;
    //金税查询开票反馈信息
    private String rn;
    private String flag;
    private String kprq;
    private String notaxamount;
    private String taxamount;
    private String totalamount;
    private String drawer;
    private String dmgs;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 发票号码
     */
    private String invoiceNo;

    @TableField(exist = false)
    private String companyNo;

    @TableField(exist = false)
    private String companyName;
}
