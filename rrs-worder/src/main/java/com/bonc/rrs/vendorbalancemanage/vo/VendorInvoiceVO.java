/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Data
public class VendorInvoiceVO {

    public interface Confirm {
    }

    public interface Save {
    }

    public interface Query {
    }

    public interface AutoQuery {
    }

    public interface AutoReceivable{

    }

    @NotNull(message = "发票id不能为空",groups ={Confirm.class,Save.class,Query.class} )
    private Integer id;

    //发票代码
    private String invoiceCode;

    //发票号码
    private String invoiceNo;


    //车企id
    @NotNull(message = "车企id不能为空",groups = AutoQuery.class)
    private Integer companyId;

    //发票金额
    @NotNull(message = "发票金额不能为空",groups =Save.class)
    @Min(value = 0,message = "发票金额必须是数字,且最小金额为0",groups =Save.class)
    private BigDecimal invoiceFee;

    //已回款金额
    @NotNull(message = "回款金额不能为空",groups =Save.class)
    @Min(value = 0,message = "回款金额必须是数字,且最小金额为0",groups =Save.class)
    private BigDecimal receivableFee;

    //本次回款金额
    @NotNull(message = "本次回款金额不能为空",groups =Save.class)
    @Min(value = 0,message = "本次回款金额必须是数字,且最小金额为0",groups ={Save.class,AutoQuery.class,AutoReceivable.class})
    private BigDecimal newReceivableFee;

    //未回款金额
    @NotNull(message = "未回款金额不能为空",groups =Save.class)
    @Min(value = 0,message = "未回款金额必须是数字,且最小金额为0",groups =Save.class)
    private BigDecimal noReceivableFee;

    //已支付余额
    @NotNull(message = "已支付余额不能为空",groups ={Save.class,Confirm.class})
    @Min(value = 0,message = "已支付余额必须是数字,且最小金额为0",groups =Save.class)
    private BigDecimal paymentFee;

    //未支付余额
    @NotNull(message = "未支付余额不能为空",groups ={Save.class,Confirm.class})
    @Min(value = 0,message = "未支付余额必须是数字,且最小金额为0",groups =Save.class)
    private BigDecimal noPaymentFee;

    //车企结算金额
    @NotNull(message = "车企结算金额不能为空",groups = Confirm.class)
    @Min(value = 0,message = "车企结算金额必须是数字,且最小金额为0",groups = Confirm.class)
    private BigDecimal vendorBalanceFee;

//    //工单结算状态 3:已开票 4：车企已回款
//    private Integer worderSetStatus;
//
//    //工单结算状态 3:已开票 4：车企已回款
//    private String worderSetStatusValue;

    //当前页
    private Integer curPage;

    //每页记录数
    private Integer limit;

    //分页起始页
    private Integer page;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    //工单id  list
    @NotNull(message = "工单id不能为空",groups = Confirm.class)
    private List<Integer> ids;

    //发票 list
    @NotNull(message = "发票list不能为空",groups = AutoReceivable.class)
    private List<Map> invoiceList;


}
