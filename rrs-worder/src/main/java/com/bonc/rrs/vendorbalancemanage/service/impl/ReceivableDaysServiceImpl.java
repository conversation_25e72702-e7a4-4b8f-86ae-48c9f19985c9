package com.bonc.rrs.vendorbalancemanage.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.vendorbalancemanage.dao.ReceivableDaysDao;
import com.bonc.rrs.vendorbalancemanage.entity.ReceivableDaysEntity;
import com.bonc.rrs.vendorbalancemanage.service.ReceivableDaysService;
import com.bonc.rrs.vendorbalancemanage.vo.ReceivableDaysVO;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ASUS on 2020/1/14.
 */
@Service("receivableDaysService")
public class ReceivableDaysServiceImpl extends ServiceImpl<ReceivableDaysDao, ReceivableDaysEntity> implements ReceivableDaysService {

    @Override
    public PageUtils queryReceivableDaysList(ReceivableDaysVO receivableDaysVO){
        if(StringUtils.isEmpty(receivableDaysVO.getCurPage()) || StringUtils.isEmpty(receivableDaysVO.getLimit())){
            receivableDaysVO.setCurPage(1);
            receivableDaysVO.setLimit(10);
        }
        receivableDaysVO.setPage((receivableDaysVO.getCurPage() - 1)*receivableDaysVO.getLimit());
        List list = baseMapper.queryReceivableDaysList(receivableDaysVO);
        return new PageUtils(list,baseMapper.queryReceivableDaysTotalCount(receivableDaysVO),
                receivableDaysVO.getCurPage(),receivableDaysVO.getLimit());
    }

    @Override
    public List companyInfoList(){
        return baseMapper.companyInfoList();
    }
}
