package com.bonc.rrs.vendorbalancemanage.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.vendorbalancemanage.dao.VendorInvoiceDao;
import com.bonc.rrs.vendorbalancemanage.entity.VendorInvoiceEntity;
import com.bonc.rrs.vendorbalancemanage.service.VendorInvoiceService;
import com.bonc.rrs.vendorbalancemanage.vo.VendorInvoiceVO;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by ASUS on 2020/1/14.
 */
@Service("vendorInvoiceService")
public class VendorInvoiceServiceImpl extends ServiceImpl<VendorInvoiceDao, VendorInvoiceEntity> implements VendorInvoiceService {


    @Override
    public PageUtils queryVendorInvoiceList(VendorInvoiceVO vendorInvoiceVO) {
        if(StringUtils.isEmpty(vendorInvoiceVO.getCurPage()) || StringUtils.isEmpty(vendorInvoiceVO.getLimit())){
            vendorInvoiceVO.setCurPage(1);
            vendorInvoiceVO.setLimit(10);
        }
        vendorInvoiceVO.setPage((vendorInvoiceVO.getCurPage() - 1) * vendorInvoiceVO.getLimit());
        return new PageUtils(baseMapper.queryVendorInvoicList(vendorInvoiceVO),
                baseMapper.queryVendorInvoicTotalCount(vendorInvoiceVO),
                vendorInvoiceVO.getCurPage(),vendorInvoiceVO.getLimit());
    }

    @Override
    public PageUtils queryWorderInfoListByInvoiceId(VendorInvoiceVO vendorInvoiceVO) {
        if(StringUtils.isEmpty(vendorInvoiceVO.getCurPage()) || StringUtils.isEmpty(vendorInvoiceVO.getLimit())){
            vendorInvoiceVO.setCurPage(1);
            vendorInvoiceVO.setLimit(10);
        }
        vendorInvoiceVO.setPage((vendorInvoiceVO.getCurPage() - 1) * vendorInvoiceVO.getLimit());
        return new PageUtils(baseMapper.queryWorderInfoListByInvoiceId(vendorInvoiceVO),
                baseMapper.queryWorderInfoCountByInvoiceId(vendorInvoiceVO),
                vendorInvoiceVO.getCurPage(),vendorInvoiceVO.getLimit());
    }

    @Override
    public R getReceivableInvoiceList(VendorInvoiceVO vendorInvoiceVO){
        List<Map> receivableList = baseMapper.getReceivableInvoiceList(vendorInvoiceVO);
        List invoiceList = new ArrayList();
        if(receivableList != null && receivableList.size() > 0){
            BigDecimal newReceivableFee = vendorInvoiceVO.getNewReceivableFee();
            for (Map map:receivableList) {
                invoiceList.add(map);
                BigDecimal noReceivableFee = new BigDecimal(map.get("noReceivableFee") + "");
                newReceivableFee = newReceivableFee.subtract(noReceivableFee);
                if(newReceivableFee.intValue() <=0)
                    return R.ok().put("invoiceList",invoiceList);
            }
            return R.ok().put("invoiceList",invoiceList);
        }
        return R.error(6666,"无数据");
    }

    @Override
    public R confirmAutoReceivable(VendorInvoiceVO vendorInvoiceVO){
        BigDecimal newReceivableFee = vendorInvoiceVO.getNewReceivableFee();
        for (Map map:vendorInvoiceVO.getInvoiceList()) {
            if(newReceivableFee.intValue() <=0)
                return R.ok();
            JSONObject json = new JSONObject(map);
            BigDecimal noReceivableFee = new BigDecimal(json.getString("noReceivableFee"));
            newReceivableFee = newReceivableFee.subtract(noReceivableFee);
            VendorInvoiceEntity invoiceEntity = baseMapper.selectById(json.getInteger("id"));
            if(newReceivableFee.intValue() >=0) {
                invoiceEntity.setNoReceivableFee(new BigDecimal("0"));
                invoiceEntity.setNoPaymentFee(new BigDecimal("0"));
                invoiceEntity.setPaymentFee(json.getBigDecimal("invoiceFee"));
                invoiceEntity.setReceivableFee(json.getBigDecimal("invoiceFee"));
                baseMapper.updateById(invoiceEntity);
                baseMapper.updateWorderSetStatusByInvoiceId(invoiceEntity.getId(),1);
            }else {
                newReceivableFee = noReceivableFee.add(newReceivableFee);
                //未回款
                invoiceEntity.setNoReceivableFee(noReceivableFee.subtract(newReceivableFee));
                //已回款
                invoiceEntity.setReceivableFee(invoiceEntity.getReceivableFee().add(newReceivableFee));
                //未支付金额
                BigDecimal noPayFee = invoiceEntity.getNoPaymentFee().add(newReceivableFee);
                //当前已支付金额
                BigDecimal payFee = invoiceEntity.getPaymentFee();
                JSONArray worderArr = json.getJSONArray("worderList");
                a: for (int i = 0 ; i <  worderArr.size(); i++){
                    JSONObject object = worderArr.getJSONObject(i);
                    if(noPayFee.intValue() < object.getBigDecimal("companyBalanceFee").intValue())
                        break a;
                    payFee = payFee.add(object.getBigDecimal("companyBalanceFee"));
                    noPayFee = noPayFee.subtract(object.getBigDecimal("companyBalanceFee"));
                    baseMapper.updateWorderSetStatusByInvoiceId(object.getInteger("worderId"),2);
                }
                //未支付
                invoiceEntity.setNoPaymentFee(noPayFee);
                //已支付
                invoiceEntity.setPaymentFee(payFee);
                baseMapper.updateById(invoiceEntity);
                return R.ok();
            }
        }
        return R.ok();
    }

}
