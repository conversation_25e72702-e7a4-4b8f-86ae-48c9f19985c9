/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.vendorbalancemanage.entity.ReceivableDaysEntity;
import com.bonc.rrs.vendorbalancemanage.vo.ReceivableDaysVO;
import com.youngking.lenmoncore.common.utils.PageUtils;

import java.util.List;

/**
 * 车企回款天数表
 *
 */
public interface ReceivableDaysService extends IService<ReceivableDaysEntity> {

    PageUtils queryReceivableDaysList(ReceivableDaysVO receivableDaysVO);

    List companyInfoList();
}
