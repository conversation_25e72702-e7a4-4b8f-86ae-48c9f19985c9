/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.vendorbalancemanage.entity.ReceivableDaysEntity;
import com.bonc.rrs.vendorbalancemanage.vo.ReceivableDaysVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 验证码
 *
 */
@Mapper
public interface ReceivableDaysDao extends BaseMapper<ReceivableDaysEntity> {


    List queryReceivableDaysList(ReceivableDaysVO receivableDaysVO);

    Integer queryReceivableDaysTotalCount(ReceivableDaysVO receivableDaysVO);

    List companyInfoList();

}
