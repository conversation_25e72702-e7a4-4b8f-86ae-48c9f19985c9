/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.vendorbalancemanage.controller;

import com.bonc.rrs.vendorbalancemanage.entity.ReceivableDaysEntity;
import com.bonc.rrs.vendorbalancemanage.service.ReceivableDaysService;
import com.bonc.rrs.vendorbalancemanage.vo.ReceivableDaysVO;
import com.youngking.lenmoncore.common.annotation.SysLog;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 车企回款天数表
 *
 */
@RestController
@RequestMapping("/receivableDays")
@Api(tags = {"车企回款天数相关接口"})
public class ReceivableDaysController  extends AbstractController {
    private final ReceivableDaysService receivableDaysService;

    public ReceivableDaysController(ReceivableDaysService receivableDaysService) {
        this.receivableDaysService = receivableDaysService;
    }

    @PostMapping("/list")
    @ApiOperation(value = "车企回款天数列表", notes = "车企回款天数列表")
    public R queryReceivableDaysList(@RequestBody ReceivableDaysVO receivableDaysVO){
        PageUtils page = receivableDaysService.queryReceivableDaysList(receivableDaysVO);
        return R.ok().put("page", page);
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增车企回款天数", notes = "新增车企回款天数")
    @Transactional
    @SysLog("新增车企回款天数")
    public R saveReceivableDays(@Validated(value = ReceivableDaysVO.Insert.class) @RequestBody ReceivableDaysVO receivableDaysVO){
        ReceivableDaysEntity receivableDaysEntity = new ReceivableDaysEntity();
        receivableDaysEntity.setCreateTime(new Date());
        receivableDaysEntity.setDays(receivableDaysVO.getDays());
        receivableDaysEntity.setVendorId(receivableDaysVO.getCompanyId());
        receivableDaysService.save(receivableDaysEntity);
        return R.ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改车企回款天数", notes = "修改车企回款天数")
    @Transactional
    @SysLog("修改车企回款天数")
    public R updateReceivableDays( @Validated(value = ReceivableDaysVO.Update.class) @RequestBody ReceivableDaysVO receivableDaysVO){
        ReceivableDaysEntity receivableDaysEntity = new ReceivableDaysEntity();
        receivableDaysEntity.setDays(receivableDaysVO.getDays());
        receivableDaysEntity.setId(receivableDaysVO.getId());
        receivableDaysEntity.setVendorId(receivableDaysVO.getCompanyId());
        receivableDaysService.updateById(receivableDaysEntity);
        return R.ok();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除车企回款天数", notes = "删除车企回款天数")
    @Transactional
    @SysLog("删除车企回款天数")
    public R deleteReceivableDays(@RequestBody ReceivableDaysVO receivableDaysVO){
        receivableDaysService.removeByIds(receivableDaysVO.getIds());
        return R.ok();
    }

    @GetMapping("/companyInfoList")
    //@RequiresPermissions("worder:companyinformation:list")
    @ApiOperation(value = "获取车企列表", notes = "获取列表")
    public R companyInfoList() {
        return R.ok().putList(receivableDaysService.companyInfoList());
    }

}
