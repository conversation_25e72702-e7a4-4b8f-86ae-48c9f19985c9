package com.bonc.rrs.worderinformationaccount.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 查询结算工单的数据对象
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(15)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)
public class WorderInformationAccountExcelVO implements Serializable {
    @ExcelProperty(value = "工单号", index = 0)
    @ColumnWidth(25)
    private String worderNo;

    @ExcelProperty(value = "激励Id", index = 1)
    @ColumnWidth(25)
    private String stimulateId;

    @ExcelIgnore
    private Integer worderInvoiceType;

    @ExcelProperty(value = "工单开票类型", index = 2)
    @ColumnWidth(20)
    private String worderInvoiceTypeName;

    @ExcelProperty(value = "车企", index = 3)
    @ColumnWidth(25)
    private String companyBrand;

    @ExcelProperty(value = "车企订单号", index = 4)
    @ColumnWidth(20)
    private String companyOrderNumber;

    @ExcelProperty(value = "工单类型", index = 5)
    @ColumnWidth(20)
    private String worderTypeName;

    @ExcelProperty(value = "工单完成日期", index = 6)
    @ColumnWidth(20)
    private Date worderFinishTime;

    @ExcelProperty(value = "结算状态", index = 7)
    @ColumnWidth(20)
    private String worderStatusValue;

    @ExcelProperty(value = "车企工单费用(不含税)", index = 8)
    @ColumnWidth(20)
    private BigDecimal companyBalanceFee;

    @ExcelProperty(value = "车企含税金额", index = 9)
    @ColumnWidth(20)
    private BigDecimal companyBalanceSum;

    @ExcelProperty(value = "车企税额", index = 10)
    @ColumnWidth(20)
    private BigDecimal companyBalanceFeetax;

    @ExcelProperty(value = "车企税点", index = 11)
    @ColumnWidth(20)
    private String taxRate;

    @ExcelProperty(value = "服务网点", index = 12)
    @ColumnWidth(35)
    private String dotName;

    @ExcelProperty(value = "网点含税金额", index = 13)
    @ColumnWidth(20)
    private BigDecimal dotBalanceFeeSum;

    @ExcelProperty(value = "网点不含税金额", index = 14)
    @ColumnWidth(20)
    private BigDecimal dotBalanceFee;

    @ExcelProperty(value = "网点税额", index = 15)
    @ColumnWidth(20)
    private String dotBalanceFeeTax;

    @ExcelProperty(value = "是否开票：Y/N", index = 16)
    @ColumnWidth(20)
    private String release;

}
