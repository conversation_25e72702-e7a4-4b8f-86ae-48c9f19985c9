package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by liqingchao on 2020/4/25.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("balance_cvp_bill_detail")
public class CvpBillDetailEntity  implements Serializable {
    private Integer id;
    private Integer billId;
    private Integer worderId;
    private Integer increId;
    private Integer stimulateId;

}
