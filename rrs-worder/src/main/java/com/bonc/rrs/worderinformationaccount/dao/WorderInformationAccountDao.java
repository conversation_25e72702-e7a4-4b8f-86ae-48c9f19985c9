package com.bonc.rrs.worderinformationaccount.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bonc.rrs.worder.dto.dto.ManagerAreaBrandDto;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worderinformationaccount.dto.PublishDetail;
import com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity;
import com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO;
import com.bonc.rrs.worderinformationaccount.vo.WorderFlowOrdersVO;
import com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountExcelVO;
import com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO;
import com.youngking.lenmoncore.common.utils.Query;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 工单主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:59
 */
@Mapper
public interface WorderInformationAccountDao extends BaseMapper<WorderInformationAccountEntity> {

    List<Map> queryWorderIdList(WorderInformationAccountVO worderInformation);

    List<Map> queryWorderInformationNotAccountList(WorderInformationAccountVO worderInformation);

    IPage<Map> queryWorderInformationAllFlowOrdersList(Page<Map> pageNotI, @Param("p") Map<String, Object> params);

    List<WorderInformationAccountExcelVO> queryWorderInformationNotAccountExcel(WorderInformationAccountVO worderInformation);

    Integer queryWorderInformationAllFlowOrdersCount(Map<String, Object> params);

    Integer queryWorderInformationNotAccountCount(WorderInformationAccountVO worderInformation);

    Map<String,Object> getCompanyById(Integer companyId);

    List<Map<String,Object>> getInvoiceCustomerByInvoiceId(Integer invoiceId);

    List<Map> listWorderByIds(List<Integer> worderIdList);

    Map getWorderAndDotByWorderId(Integer worderId);

    void updateSetStatusByInvoiceIds(List<Integer> idList);

    List<Map> getWorderAndDotByInvoiceId(Integer invoiceId);

    List<Map> listCompanyInvoiceByInvoiceId(Integer invoiceId);

    List<Map> queryVendorInvoiceUnfinished();

    List queryTotalAduitList(ReceivableFeeAduitVO receivableFeeAduit);

    Integer queryTotalAduitCount(ReceivableFeeAduitVO receivableFeeAduit);

    List queryWorderInfoAduitList(ReceivableFeeAduitVO receivableFeeAduit);

    List queryBalanceAdwanceMoneyWorderInfoAduitList(ReceivableFeeAduitVO receivableFeeAduit);

    List queryStimulateInfoAduitList(ReceivableFeeAduitVO receivableFeeAduit);

    Integer queryBalanceAdwanceMoneyWorderInfoAduitCount(ReceivableFeeAduitVO receivableFeeAduit);

    Integer queryStimulateInfoAduitCount(ReceivableFeeAduitVO receivableFeeAduit);

    Integer queryWorderInfoAduitCount(ReceivableFeeAduitVO receivableFeeAduit);

    List querydotIncreBalanceAduitList(ReceivableFeeAduitVO receivableFeeAduit);

    Integer querydotIncreBalanceAduitCount(ReceivableFeeAduitVO receivableFeeAduit);

    List queryWorderStimulateList(ReceivableFeeAduitVO receivableFeeAduit);

    Integer queryWorderStimulateCount(ReceivableFeeAduitVO receivableFeeAduit);

    List getDotInfoList();

    Map<String,Object> queryUserInvoice(Integer worderId);

    List<Map> getDotAllInfoByDotIds(@Param("dotIds") Collection<Integer> dotIds);

    Map getOneDotAllInfoByDotId(@Param("dotId") Integer dotId);

    List<Map> getAllDotList();

    List<Map> queryWorderByIds(List<Integer> worderIds);

    List<Map> queryWorderByStimulateIds(List<Integer> stimulateIds);

    List<Map> queryWorderAndStimulateForPublish();

    List<Map> queryIncreWorderByIds(List<Integer> increIds);

    List<Map> queryCheckedWorderAndStimulateForPublish(PublishDetail pd);

    void updateWorderChildToPublishByIds(@Param("worderChildIdList")List<Integer> worderChildIdList, @Param("status")Integer status, @Param("statusValue") String statusValue);
    void updateStimulateStatusToPublishByIds(List<Integer> stimulateIds);
    void updateStimulateStatusToAccountByIds(List<Integer> stimulateIds);

    List<Map> queryChildWorderByBalanceIds(@Param("balanceIds") List<Integer> balanceIds);

    List<Map> queryChildStimulateWorderByBalanceIds(@Param("balanceIds") List<Integer> balanceIds);

    Integer getDotId(Long userId);
}
