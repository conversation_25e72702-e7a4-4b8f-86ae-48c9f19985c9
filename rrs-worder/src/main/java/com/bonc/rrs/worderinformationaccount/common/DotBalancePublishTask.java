package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liqingchao on 2020/4/27.
 * 自动发布结算的定时任务
 */
@Component("dotBalancePublishTask")
public class DotBalancePublishTask implements ITask{
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    private static final Logger log = LoggerFactory.getLogger(DotBalancePublishTask.class);
    @Override
    public void run(String params) {
//        log.info("定时获取 WorderBalanceScheduling 开始");
//        worderBalanceScheduling.worderPublishScheduling();
//        log.info("定时获取 WorderBalanceScheduling 结束");
        log.info("定时获取 balancePublishScheduling 开始");
        worderBalanceScheduling.balancePublishScheduling();
        log.info("定时获取 balancePublishScheduling 结束");
    }
}
