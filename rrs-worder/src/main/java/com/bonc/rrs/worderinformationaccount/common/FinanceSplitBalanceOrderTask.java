package com.bonc.rrs.worderinformationaccount.common;

import com.bonc.rrs.balanceprocess.service.CompanyInvoiceService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liqingchao on 2020/7/11.
 * 自动厂商开票的定时任务
 */
@Log4j2
@Component("financeSplitBalanceOrderTask")
public class FinanceSplitBalanceOrderTask implements ITask {
    @Autowired
    private CompanyInvoiceService companyInvoiceService;
    @Override
    public void run(String params) {
        log.info("定时获取 financeSplitBalanceOrderTask 开始");
        companyInvoiceService.financeSplitBalanceOrder();
        log.info("定时获取 financeSplitBalanceOrderTask 结束");
    }
}
