package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/4/23.
 */
@Data
@TableName("balance_cvp_detail_record")
public class BalanceCvpDetailEntity  implements Serializable {
    @TableId
    private Integer id;
    private Integer recId;
    private Integer worderId;
    private Integer increId;
    private Integer stimulateId;


    protected Date accountCreateTime;
    protected String add1;
    protected String add2;
    protected String add3;
    protected String add4;
    protected String add5;
    protected String belnrzg;
    protected String bktxt;
    protected String branchCode;
    protected String bubrs;
    protected Date budat;
    protected String bukrszg;
    protected String buyerCity;
    protected String buyerMobile;
    protected String buyerName;
    protected String buyerRecAddr;
    protected String buyerRecMobile;
    protected String buyerReceiver;
    protected String city;
    protected BigDecimal costValue;
    protected Date deliveryTime;
    protected BigDecimal dmbtr;
    protected BigDecimal dmbtr1;
    protected BigDecimal dmbtr2;
    protected String expressCode;
    protected String gjahrzg;
    protected BigDecimal invoiceTaxRate;
    protected String itemCode;
    protected String kunnr;
    protected String lifnr;
    protected String name;
    protected BigDecimal orderAmount;
    protected String orderCode;
    protected Date orderCreateTime;
    protected String orderId;
    protected Date orderPayTime;
    protected String payNo;
    protected String productName;
    protected Long productQuantity;
    protected String remark;
    protected String rowIdzg;
    protected BigDecimal settleAmount;
    protected String sgtxt;
    protected Date signTime;
    protected String sourceType;
    protected String supplierAddr;
    protected String supplierBankAccount;
    protected String supplierBankBranchName;
    protected String supplierInvCode;
    protected String supplierKjt;
    protected String supplierName;
    protected String supplierVcode;
    protected String waers;
    protected String xblnr;
    protected String xref3;
    protected String ywdh;
    protected String ywms;
    protected String ywmszg;

    /**品牌商确认*/
    protected String brandConfirm;
    /**品牌商回访*/
    protected String brandVisit;

    protected Date buyDate;
    /**开箱不合格费 N*/
    protected BigDecimal compensateAmt;
    /**公里数 N*/
    protected BigDecimal distanceNum;
    /**工单创建时间*/

    protected Date docCreatedDate;
    /**工单号 N*/
    protected String docNo;
    /**工单类型 N*/
    protected String docType;
    /**其它奖惩费 N*/
    protected BigDecimal examineAmt;
    protected String examineReason;
    /**故障现象*/
    protected String faultAppreance;

    protected String faultObject;
    protected String faultReason;

    protected Date finishDate;
    /**材料费    N*/
    protected BigDecimal materialAmt;
    /**网点结单*/
    protected String nodeFinish;
    protected BigDecimal operationAmt;
    /**订单类型    N*/
    protected String orderType;
    protected String proSeries;
    /**产品型号*/
    protected String productVersion;
    protected String reSourceType;
    protected String rebktxt;
    protected String rebubrs;

    protected Date rebudat;
    protected String recity;
    protected BigDecimal redmbtr;
    protected BigDecimal redmbtr1;
    protected BigDecimal redmbtr2;
    protected String rekunnr;
    protected String relifnr;
    @TableField("re_name")
    protected String rename;
    /**维修人员*/
    protected String repairWorker;
    protected String resgtxt;
    protected String rewaers;
    protected String rexblnr;
    protected String rexref3;
    protected String reywdh;
    protected String reywms;
    /**服务措施*/
    protected String serviceStep;
    /**实际服务方式*/
    protected String serviceStyle;
    /**服务方式费 N*/
    protected BigDecimal serviceStyleAmt;
    /**实际服务类型*/
    protected String serviceType;
    /**超长补贴 N*/
    protected BigDecimal subsidyAmt;
    /**单条费用合计 N*/
    protected BigDecimal totalAmt;
    /**交通费 N*/
    protected BigDecimal trafficAmt;
    /**拉送费 N*/
    protected BigDecimal transportAmt;
    protected String userAddr;
    /**用户要求*/
    protected String userAsk;
    /**用户名称*/
    protected String userName;
    /**用户手机号*/
    protected String userPhone;
    /**回访时间*/

    protected Date visitDate;
    /** 回访备注*/
    protected String visitRemark;
    /** 回访结果*/
    protected String visitResult;

}
