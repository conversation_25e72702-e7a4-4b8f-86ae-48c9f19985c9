package com.bonc.rrs.worderinformationaccount.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.worderinformationaccount.dao.InvoiceBusinessTypeDetailDao;
import com.bonc.rrs.worderinformationaccount.entity.InvoiceBusinessTypeDetailEntity;
import com.bonc.rrs.worderinformationaccount.service.InvoiceBusinessTypeDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@AllArgsConstructor
public class InvoiceBusinessTypeDetailServiceImpl extends ServiceImpl<InvoiceBusinessTypeDetailDao, InvoiceBusinessTypeDetailEntity> implements InvoiceBusinessTypeDetailService {

    final InvoiceBusinessTypeDetailDao invoiceBusinessTypeDetailDao;

    @Override
    public void deleteByInvoiceId(Integer invoiceId) {
        invoiceBusinessTypeDetailDao.deleteByInvoiceId(invoiceId);
    }
}
