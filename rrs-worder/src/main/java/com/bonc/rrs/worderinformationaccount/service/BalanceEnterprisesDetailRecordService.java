package com.bonc.rrs.worderinformationaccount.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.worderinformationaccount.entity.BalanceEnterprisesDetailRecordEntity;

import java.util.List;

/**
 * Created by liqingchao on 2020/4/23.
 */
public interface BalanceEnterprisesDetailRecordService extends IService<BalanceEnterprisesDetailRecordEntity> {



    List<BalanceEnterprisesDetailRecordEntity> listSumByInvoiceId(List<Integer> invoiceIdList);

}
