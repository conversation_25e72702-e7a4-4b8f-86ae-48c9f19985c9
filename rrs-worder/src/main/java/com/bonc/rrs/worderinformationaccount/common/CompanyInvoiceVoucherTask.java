package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by tang<PERSON>heng on 2022/12/20
 * 开票单归档定时任务
 */
@Component("companyInvoiceVoucherTask")
public class CompanyInvoiceVoucherTask implements ITask {
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    private static final Logger log = LoggerFactory.getLogger(CompanyInvoiceVoucherTask.class);
    @Override
    public void run(String params) {
        log.info("定时获取 companyInvoiceVoucherTask 开始");
        worderBalanceScheduling.companyInvoiceVoucherScheduling();
        log.info("定时获取 companyInvoiceVoucherTask 结束");
    }
}
