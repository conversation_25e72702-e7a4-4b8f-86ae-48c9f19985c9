package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liqingchao on 2020/7/11.
 * 自动厂商开票的定时任务
 */
@Component("companyInvoiceTask")
public class CompanyInvoiceTask implements ITask {
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    private static final Logger log = LoggerFactory.getLogger(DotBalancePublishTask.class);
    @Override
    public void run(String params) {
        log.info("定时获取 companyInvoiceScheduling 开始");
        worderBalanceScheduling.companyInvoiceScheduling();
        log.info("定时获取 companyInvoiceScheduling 结束");
    }
}
