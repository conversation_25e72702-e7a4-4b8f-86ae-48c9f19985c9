package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 推送增项的定时任务
 */
@Component("pushIncreTask")
public class PushIncreTask implements ITask {
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    private static final Logger log = LoggerFactory.getLogger(PushIncreTask.class);

    @Override
    public void run(String params) {
        log.info("定时获取 pushIncreScheduling 开始");
        worderBalanceScheduling.pushIncreScheduling();
        log.info("定时获取 pushIncreScheduling 结束");
    }
}
