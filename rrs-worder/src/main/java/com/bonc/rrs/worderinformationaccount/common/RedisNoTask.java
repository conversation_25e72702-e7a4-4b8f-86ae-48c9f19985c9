/**
 * Copyright (C), 2024,
 */
package com.bonc.rrs.worderinformationaccount.common;

import com.bonc.rrs.sequence.service.SequenceService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/11 10:17
 * @Version 1.0.0
 */

@Component("redisNoTask")
public class RedisNoTask implements ITask {
    @Autowired
    private SequenceService sequenceService;

    private static final Logger log = LoggerFactory.getLogger(RedisNoTask.class);

    @Override
    public void run(String params) {
        log.info("定时获取 redisNoTask 开始");
        sequenceService.updateSequence();
        log.info("定时获取 redisNoTask 结束");
    }
}