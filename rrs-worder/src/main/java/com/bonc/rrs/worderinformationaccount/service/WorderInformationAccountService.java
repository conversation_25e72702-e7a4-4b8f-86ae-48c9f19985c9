package com.bonc.rrs.worderinformationaccount.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.balanceprocess.entity.CompanyInvoiceEntity;
import com.bonc.rrs.balanceprocess.entity.WorderChildInformationEntity;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.resp.RespSearchOrder;
import com.bonc.rrs.invoice.enterprises.util.InvoiceResults;
import com.bonc.rrs.worderinformationaccount.dto.PublishDetail;
import com.bonc.rrs.worderinformationaccount.entity.CvpBillEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderAuditInfoEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO;
import com.bonc.rrs.worderinformationaccount.vo.WorderFlowOrdersVO;
import com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * 工单主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:59
 */
public interface WorderInformationAccountService extends IService<WorderInformationAccountEntity> {

    List queryWorderIdList(WorderInformationAccountVO worderInformation);

    /**
     * 查询待结算工单列表
     * @param worderInformation
     * @return
     */
    List queryWorderNotAccountList(WorderInformationAccountVO worderInformation);

    PageUtils queryWorderInformationNotAccountList(WorderInformationAccountVO worderInformation);

    List queryWorderByCompanyOrderNumbers(WorderInformationAccountVO worderInformation);

    void pushAcsAccountStimulate(List<WorderChildInformationEntity> worderChildInformationList, List<WorderPmStimulateEntity> stimulateList);

    void pushFinanceAccountStimulate(List<WorderChildInformationEntity> worderChildInformationList, List<WorderPmStimulateEntity> stimulateList);

    void queryWorderAcsAccount();

    void updateWorderFinanceAccount(CompanyInvoiceEntity companyInvoiceEntity, RespSearchOrder respSearchOrder);

    List<WorderInformationAccountEntity> queryIncreReceivablesAcsAccount();

    List<WorderInformationAccountEntity> queryIncreReceivablesFinanceAccount();

    void receivablesAcsAccountSuccess(WorderInformationAccountEntity worderInformationAccountEntity);

    void receivablesFinanceAccountSuccess(WorderInformationAccountEntity worderInformationAccountEntity);

    void queryIncreAcsAccount();

    void queryIncreFinanceAccount();

    void queryStimulateAcsAccount();

    void queryStimulateFinanceAccount();

    void pushCvpBill(PublishDetail pd);

    void queryCvpBill(CvpBillEntity bill);

    void insertVendorInvoice(WorderInformationAccountVO worderInformation);

    InvoiceResults queryVendorInvoiceFinished(String rowId,String companyInvoiceNo);

    R querySurplusLimit();

    PageUtils queryWorderInfoAduitList(ReceivableFeeAduitVO receivableFeeAduitVO);

    R receivableWorderAduit(ReceivableFeeAduitVO receivableFeeAduitVO);

    R worderInfoAduit(WorderAuditInfoEntity auditInfoEntity);

    R worderReCommitAduit(ReceivableFeeAduitVO auditInfoEntity);

    List getDotInfoList();

    void pushAcsAccount(CompanyInvoiceEntity v, InvoiceResults invoiceResults, String orderNo, SimpleDateFormat df);

    void pushAcsAccount(CompanyInvoiceEntity v, List<InvoiceResults> invoiceResults, String orderNo, SimpleDateFormat df);
    void pushComapanyBalanceAcsAccount(CompanyInvoiceEntity companyInvoice, List<InvoiceResults> invoiceResults, String invoiceOrderNo, SimpleDateFormat df, Map<Integer,List<WorderChildInformationEntity>> childOrderMap);


    void pushAcsAccountIncreReceivables(Integer worderId);

    void pushFinanceAccountIncreReceivables(Integer worderId);

    boolean queryAcsAccountIncreReceivables(Integer worderId);

    void pushAcsAccountIncre(Integer worderId);

    void queryAllAcsAccount();

//    void dotBalancePublish(PublishDetail pd);

    PageUtils queryWorderInformationAllFlowOrdersList(Map<String, Object> params);
}

