package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/4/23.
 */
@Data
@TableName("balance_cvp_examine_record")
public class BalanceCvpExamineEntity  implements Serializable {
    @TableId
    private Integer id;
    private Integer recId;
    private Integer stimulateId;


    protected String add1;
    protected String add2;
    protected String add3;
    protected String add4;
    protected String add5;
    /**审核员*/
    protected String auditor;
    /**兑现金额*/
    protected BigDecimal cashedAmt;
    /**兑现时间*/
    protected Date cashedDate;
    /**是否兑现*/
    protected String cashedFlag;
    /**创建时间*/
    protected Date createdTime;
    /**创建人*/
    protected String creator;
    /**正负激励分值上岗证扣分*/
    protected BigDecimal deducePoints;
    /**工单号*/
    protected String docNo;
    /**工贸名称*/
    protected String entityName;
    /**正负激励金额*/
    protected BigDecimal examineAmt;
    /**正负激励生成时间*/
    
    protected Date examineDate;
    /**正负激励清单编号*/
    protected String examineNo;
    /**正负激励原因*/
    protected String examineReason;
    /**激励备注*/
    protected String examineRemark;
    /**激励类型描述*/
    protected String examineTypeDesc;
    /**订单类型*/
    protected String orderType;
    /**结算月份*/
    protected String period;
    /**产品系列号*/
    protected String proSeriesCode;
    /**产品系列名称*/
    protected String proSeriesName;
    /**产品大类编码*/
    protected String productCode;
    /**产品大类名称*/
    protected String productName;
    /**产品型号*/
    protected String productVersion;
    /**结算扣款详述*/
    protected String reduceDesc;
    /**备注*/
    protected String remark;
    /**工单费用合计*/
    protected BigDecimal totalAmt;
    /**跳闸大类*/
    protected String tripClass;

}
