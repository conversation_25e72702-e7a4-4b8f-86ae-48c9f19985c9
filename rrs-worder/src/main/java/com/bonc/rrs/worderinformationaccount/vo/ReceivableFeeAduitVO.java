package com.bonc.rrs.worderinformationaccount.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by ASUS on 2020/3/30.
 */
@Data
public class ReceivableFeeAduitVO implements Serializable {

    public interface Query {
    }

    public interface Aduit {
    }
    public interface ReSubmit{

    }

    //网点
    private Integer dotId;
    //品牌ID
    private Integer brandId;

    //日日顺税点
    private BigDecimal taxRate;

    //结算类型 0:全部 33：网点工单费用 34：网点增项费用 35：奖惩金额
    @NotNull(message = "结算类型不能为空",groups = {Query.class,Aduit.class,ReSubmit.class})
    private Integer balanceType;

    @NotNull(message = "结算类型描述不能为空",groups = {Aduit.class})
    private String balanceTypeValue;

    /**
     * 是否审核  0 ：重新提交审核  1 ：待审核
     */
    @NotNull(message = "审核状态不能为空",groups = Query.class)
    private Integer isAduit;
    @NotNull(message = "id不能为空",groups = {Aduit.class,ReSubmit.class})
    private Integer id;

    private String userId;

//    @NotNull(message = "结算金额不能为空", groups = ReSubmit.class)
    private BigDecimal dotIncreBalanceFee;

    //厂商结算首次审核/厂商结算二次审核
    @NotEmpty(message = "操作不能为空",groups = {Aduit.class})
    private String operation;

    //审核状态(1:通过、0:不通过)
    @NotNull(message = "审核状态id不能为空",groups = {Aduit.class})
    private Integer auditStatus;

    //不通过原因S
    private String refusalCause;

    //当前页
    private Integer curPage;

    //每页记录数
    private Integer limit;

    //分页起始页
    private Integer page;

    // 开票单号
    private String companyInvoiceNo;

    //回款时间开始
    private String receivableStartTime;

    //回款时间结束
    private String receivableEndTime;
}
