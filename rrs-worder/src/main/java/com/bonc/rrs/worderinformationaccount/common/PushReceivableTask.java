package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 传输回款审核单1216定时任务
 */
@Component("pushReceivableTask")
public class PushReceivableTask  implements ITask {
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    private static final Logger log = LoggerFactory.getLogger(PushReceivableTask.class);

    @Override
    public void run(String params) {
        log.info("定时获取 pushReceivableScheduling 开始");
        worderBalanceScheduling.pushReceivableScheduling();
        log.info("定时获取 pushReceivableScheduling 结束");
    }
}
