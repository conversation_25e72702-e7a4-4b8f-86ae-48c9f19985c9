package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Created by liqingchao on 2020/4/23.
 */
@Data
@TableName("balance_enterprises_header_record")
public class BalanceEnterprisesHeaderRecordEntity {
    @TableId
    private Integer id;
    private Date pushTime;
    private String orderNo;           //XSDDM单据号
    /**
     * 开票单号
     */
    private String companyInvoiceNo;
    private String invoiceId;
    private String orderType;
    private Integer invoiceType;    //发票类型 0-开票 1-红冲 2-被红冲
    private String origInvoiceCode;
    private Integer orderId;//订单号
    private String company = "0RK0";  //DMGS单据公司	0RK0
    private String customerCode;      //KHDM客户编码
    private String customerHeading;   //KHSWDJH客户税号 增值税专用发票
    private String customerName;      //KHMC客户名称 增值税专用发票、普通发票、地税发票、电子发票
    private String customerMobile;    //KHSJ 客户手机 电子发票需要
    private String customerAddTel;    //KHDZ 客户地址电话 增值税专用发票pr
    private String bankNo;            //KHYH 开户银行账号 增值税专用发票
    private String remark;            //BZ 备注
    private String payee;             //SKR	收款人 商户
    private String reviewer;          //FHR	复核人 空
    private String orderDate;         //KPRQ 单据日期 传单据的时间点
    private String invoiceTypes;      //FPZL 发票种类(0专用发票 1普通发票 3电子发票)
    private String writeDates;        //XRRQ 写入日期	传单据的时间点
    private String writeTime;         //XRSJ 写入日期	传单据的时间点
    private String requisition;       //TZDH 红字通知单号
    private String scarletInvoiceCode;//CHFPH 红冲发票号
    private String invoiceCode;       //发票号
    private String viewUrl;           //电子发票地址
    private String relatedCode;       //查询返回的红字发票号
    @TableField(value = "sapin")
    private String SAPIn;             //是否向sap记账
    private String invoiceBranch;     //收款发票比较，1总账，2平台收款 3社区店/驿站收款 4创客收款
    private Date redBackTime;     //发票红冲时间
    private String customerAddress;//客户公司地址
    private String customerTel;//客户公司电话
    private String customerBank;//客户开户行
    private String customerBankNo;//客户开户行账号
    private Integer inRuiHong; //是否是到瑞红开票
    private String drawer; //开票人
    private Date updateTime;//更新时间

    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 规格型号
     */
    private String goodsModel;
    /**
     * 单位
     */
    private String goodsUnit;

    /**
     * 特殊业务类型(字典business_type)
     */
    private String businessType;

}
