package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by liqingchao on 2020/6/18.
 */
@Data
@TableName("balance_acs_accounting_status")
public class BalanceAcsAccountingStatusEntity implements Serializable {
    @TableId(type = IdType.INPUT)
    private String rowId;
    private String flag;
    private String error;
    private String accountFlag;
    private String accountCode;
    private String accountDesc;
    private String accountDate;
}
