package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 执行财务中台(查询合并单、增项、激励单流程状态接口)定时任务
 */
@Component("queryFinanceTask")
public class QueryFinanceTask implements ITask {

    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;

    private static final Logger log = LoggerFactory.getLogger(QueryFinanceTask.class);

    @Override
    public void run(String params) {
        log.info("定时获取 queryFinanceTaskScheduling 开始");
        worderBalanceScheduling.queryFinanceTaskScheduling();
        log.info("定时获取 queryFinanceTaskScheduling 结束");
    }
}
