package com.bonc.rrs.worderinformationaccount.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worderinformationaccount.entity.BalanceEnterprisesDetailRecordEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Created by liqingchao on 2020/4/23.
 */
@Mapper
public interface BalanceEnterprisesDetailRecordDao extends BaseMapper<BalanceEnterprisesDetailRecordEntity>{

    List<BalanceEnterprisesDetailRecordEntity> listByInvoiceId(List<Integer> invoiceIdList);


    List<BalanceEnterprisesDetailRecordEntity> listSumByInvoiceId(List<Integer> invoiceIdList);



}
