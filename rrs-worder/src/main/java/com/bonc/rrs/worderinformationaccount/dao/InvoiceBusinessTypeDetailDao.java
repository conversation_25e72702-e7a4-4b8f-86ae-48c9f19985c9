package com.bonc.rrs.worderinformationaccount.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worderinformationaccount.entity.InvoiceBusinessTypeDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface InvoiceBusinessTypeDetailDao extends BaseMapper<InvoiceBusinessTypeDetailEntity>{

    InvoiceBusinessTypeDetailEntity queryByHeaderId(@Param("enterprisesHeaderId") Integer enterprisesHeaderId);

    InvoiceBusinessTypeDetailEntity queryByInvoiceId(@Param("invoiceId") Integer invoiceId);

    void deleteByInvoiceId(@Param("invoiceId") Integer invoiceId);
}
