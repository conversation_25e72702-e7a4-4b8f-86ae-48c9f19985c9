package com.bonc.rrs.worderinformationaccount.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class WorderFlowOrdersVO implements Serializable {

    //当前页
    private Integer curPage;

    //每页记录数
    private Integer limit;

    //分页起始页
    private Integer page;

    private List<String> area;

    private List<String> areaId;

    private String areaList;

    /**
     * 工单号
     */
    private String worderNo;

    /**
     * 车企订单号
     */
    private String companyOrderNumber;

    /**
     * 网点id
     */
    private String dotId;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 商户通状态
     */
    private String status;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 结单开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startFinishTime;

    /**
     * 结单结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endFinishTime;

}