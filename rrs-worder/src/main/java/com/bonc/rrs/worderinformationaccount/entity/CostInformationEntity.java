package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by liqingchao on 2020/4/1.
 */
@Data
@TableName("balance_cost_information")
public class CostInformationEntity implements Serializable {
    @TableId
    private Integer id;
    private Integer worderId;//  工单ID，工单结算使用
    private Integer increId;//  工单ID，增项结算使用
    private Integer stimulateId;//  激励ID，激励结算使用
    private Integer balanceId;
    private Integer dotId;//网点ID
    private String rowId;//ACS账单主键
    private String orderNo;// 订单号，对应company_invoice表的invoice_order_no字段
    private String payNo;//支付单号
    private String sourceType;
    private String bktxt;
    private String ywms;
    private String waers;
    private String kunnr;
    private String lifnr;
    private String budat;// 过账日期
    private String jylx;//交易类型
    private String xblnr;//发票代码
    private String name;
    private String city;
    private BigDecimal dmbtr;// 应付金额
    private BigDecimal dmbtr1;// 手续费
    private BigDecimal dmbtr2;// 税金
    private BigDecimal dmbtr3;// 提现金额
    private String xref3;//行项目单据号
    private String sgtxt;//行项目文本
    private Integer billId;//对账单ID
}
