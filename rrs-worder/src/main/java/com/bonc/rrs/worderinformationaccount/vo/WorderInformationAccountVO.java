package com.bonc.rrs.worderinformationaccount.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 查询结算工单的数据对象
 */
@Data
public class WorderInformationAccountVO implements Serializable {

    public interface Insert {
    }

    public interface Query {
    }

    public interface QueryNotAccount {
    }

    public interface Update {
    }

    public interface Aduit {
    }
    /**
     * 厂商id
     */
    @NotNull(message = "厂商id不能为空",groups = QueryNotAccount.class)
    private Integer companyId;

    //日日顺税点
    private BigDecimal taxRate;

    // 车企订单号
    private String companyOrderNumber;
    // 车企订单号逗号分割加括号
    private String companyOrderNumbers;


    /**
     * 工单类型编号
     */
    private Integer worderTypeId;

    /**
     * 工单主状态  0 ：结算中/勘测结单  3 ：结算中  5 勘测结单
     */
    @NotNull(message = "工单主状态不能为空",groups = Query.class)
    private Integer worderMainStatus;

    /**
     * 是否审核  0 ：重新提交审核  1 ：待审核
     */
    @NotNull(message = "审核状态不能为空",groups = Query.class)
    private Integer isAduit;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    //结算类型 0:全部 1：工单费用 2：增项费用 3：奖惩金额
    private Integer balanceType;
    //结算类型 工单费用 /增项费用 /奖惩金额
    private String balanceTypeValue;

    //审核类型 首次审核/二次审核  1/2
    private Integer aduitType;

    //操作
    private String operation;

    //关联id
    private Integer fromId;

    //当前页
    private Integer curPage;

    //每页记录数
    private Integer limit;

    //分页起始页
    private Integer page;

    //发票金额
//    @NotNull(message = "发票金额不能为空",groups = {Insert.class})
    private BigDecimal invoiceFee;

    //工单list 集合
    @NotNull(message = "工单list不能为空",groups = {Insert.class})
    private List<Map> list;
}
