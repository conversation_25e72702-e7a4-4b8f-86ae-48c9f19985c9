package com.bonc.rrs.worderinformationaccount.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bonc.rrs.balanceprocess.entity.*;
import com.bonc.rrs.balanceprocess.service.*;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.ReqSearchOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.resp.RespSearchOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.transferApprovalForm.resp.RespTransferApprovalForm;
import com.bonc.rrs.invoice.enterprises.util.FinanceBusiness;
import com.bonc.rrs.invoice.enterprises.util.InvoiceBusiness;
import com.bonc.rrs.invoice.enterprises.util.InvoiceResults;
import com.bonc.rrs.invoice.enterprises.util.SearchOrderDetailInvoiceList;
import com.bonc.rrs.pay.entity.entity.WorderOrderLogEntity;
import com.bonc.rrs.pay.manage.utils.SpringUtils;
import com.bonc.rrs.pay.service.WorderOrderLogService;
import com.bonc.rrs.util.hscapi.bladesellerbusiness.getsellerinvoice.RespData;
import com.bonc.rrs.util.hscapi.bladesellerbusiness.getsellerinvoice.RespSellerInvoiceDetail;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.impl.WorderAuditRecordServiceImpl;
import com.bonc.rrs.worderinformationaccount.dao.CvpBillDao;
import com.bonc.rrs.worderinformationaccount.dao.WorderInformationAccountDao;
import com.bonc.rrs.worderinformationaccount.dto.PublishDetail;
import com.bonc.rrs.worderinformationaccount.entity.*;
import com.bonc.rrs.worderinformationaccount.service.BalanceEnterprisesDetailRecordService;
import com.bonc.rrs.worderinformationaccount.service.CompanyInvoiceQueryResultService;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import com.bonc.rrs.worderinformationaccount.service.WorderPmStimulateService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by ASUS on 2020/2/9.
 */
@Component("worderBalanceScheduling")
//@EnableScheduling
public class WorderBalanceScheduling {

    @Autowired(required = false)
    private WorderInformationAccountService worderInformationAccountService;
    @Autowired(required = false)
    private WorderInformationAccountDao worderInformationAccountDao;
    //    @Autowired
//    private VendorInvoiceService vendorInvoiceService;
    @Autowired
    private WorderPmStimulateService worderPmStimulateService;

    @Autowired
    private FinanceBusiness financeBusiness;
    @Autowired(required = false)
    private CvpBillDao cvpBillDao;
    @Autowired(required = false)
    private BalancePublishService balancePublishService;
    @Autowired(required = false)
    private CompanyInvoiceService companyInvoiceService;
    @Autowired(required = false)
    private CompanyInvoiceQueryResultService companyInvoiceQueryResultService;
    @Autowired(required = false)
    private BalanceEnterprisesDetailRecordService balanceEnterprisesDetailRecordService;
    @Autowired(required = false)
    private InvoiceBusiness invoiceBusiness;
    @Autowired
    private WorderChildInformationService worderChildInformationService;

    @Autowired
    private WorderInformationService worderInformationService;

    @Autowired
    private WorderOrderLogService worderOrderLogService;

    @Autowired
    private CompanyReceivableService companyReceivableService;

    @Autowired
    private CompanyReceivableRecordService companyReceivableRecordService;

    private static final Logger log = LoggerFactory.getLogger(WorderBalanceScheduling.class);

    /**
     * 推送ACS记账的定时任务
     */
//    @Scheduled(cron="${sync.worderInvoice}")
    public void pushAcsScheduling() {
//        log.info("定时获取 vendorInvoiceStatusScheduling 开始");
//        vendorInvoiceStatusScheduling();
//        log.info("定时获取 vendorInvoiceStatusScheduling 结束");

        log.info("定时获取 companyInvoiceStatusScheduling 开始");
        companyInvoiceStatusScheduling();
        log.info("定时获取 companyInvoiceStatusScheduling 结束");

        log.info("定时获取 dotStimulateAccountScheduling 开始");
        dotStimulateAccountScheduling();
        log.info("定时获取 dotStimulateAccountScheduling 开始");

        log.info("定时获取 queryAcsAccountScheduling 开始");
        queryAcsAccountScheduling();
        log.info("定时获取 queryAcsAccountScheduling 结束");
    }


    /**
     * 自动发布结算的定时任务
     */
//    @Scheduled(cron="${sync.worderBalance}")
    public void dotBalancePublishScheduling() {
        log.info("定时获取 WorderBalanceScheduling 开始");
        worderPublishScheduling();
        log.info("定时获取 WorderBalanceScheduling 结束");
    }


    /**
     * 查询商户通开票状态的定时任务
     */
//    @Scheduled(cron="${sync.balancePublish}")
    public void queryCvpBillScheduling() {
        log.info("定时获取 cvpBillStatusScheduling 开始");
        cvpBillStatusScheduling();
        log.info("定时获取 cvpBillStatusScheduling 结束");
    }

    public void companyInvoiceStatusScheduling() {
        List<CompanyInvoiceEntity> companyInvoiceEntityList = companyInvoiceService.list(
                new QueryWrapper<CompanyInvoiceEntity>().eq("status", 5));
        if (CollectionUtils.isEmpty(companyInvoiceEntityList)) {
            return;
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        for (CompanyInvoiceEntity v : companyInvoiceEntityList) {
            String companyInvoiceNo = v.getCompanyInvoiceNo();
            // 根据开票单号查询所有开票信息
            QueryWrapper<BalanceEnterprisesDetailRecordEntity> queryWrapper =
                    new QueryWrapper<BalanceEnterprisesDetailRecordEntity>().eq("company_invoice_no", companyInvoiceNo);
            List<BalanceEnterprisesDetailRecordEntity> list = balanceEnterprisesDetailRecordService.list(queryWrapper);
            String invoiceOrderNo = v.getInvoiceOrderNo();
            try {
                List<InvoiceResults> invoiceResultList = new ArrayList<>();
                Boolean flag = true;
                for (int i = 0, len = list.size(); i < len; i++) {
                    BalanceEnterprisesDetailRecordEntity entity = list.get(i);
                    QueryWrapper<CompanyInvoiceQueryResultEntity> query = new QueryWrapper<CompanyInvoiceQueryResultEntity>().eq("invoice_id", entity.getInvoiceId());
                    CompanyInvoiceQueryResultEntity companyInvoiceQueryResultEntity = companyInvoiceQueryResultService.getOne(query);
                    /** 当这条发票号查到结果并且为成功时，不调用金税再次查询 **/
                    if (companyInvoiceQueryResultEntity != null) {
                        if (companyInvoiceQueryResultEntity.getFlag() != null) {

                            continue;
                        }
                    }
                    // 根据每条开票号查询是否开票成功
                    //InvoiceResults invoiceResults = worderInformationAccountService.queryVendorInvoiceFinished(entity.getInvoiceId());
                    //调用新的开票结果查询
                    String resp = invoiceBusiness.getSellerInvoice(entity.getInvoiceId());
                    JSONObject resultJson = JSON.parseObject(resp);
                    if (!resultJson.containsKey("code") || !InvoiceBusiness.SUCCESS.equals(resultJson.getInteger("code"))) {
                        flag = false;
                        continue;
                    }
                    List<RespData> respDatas = JSON.parseArray(resultJson.getJSONArray("data").toJSONString(), RespData.class);
                    RespData respData = respDatas.get(0);
                    if (!"2".equals(respData.getMakeInvoiceState())) {
                        flag = false;
                        continue;
                    }

                    //封装返回结果
                    InvoiceResults invoiceResult = new InvoiceResults();
                    invoiceResult.setINVOICECODE(respData.getInvoiceCode() + respData.getInvoiceNo());
                    invoiceResult.setRN("1.0");
                    invoiceResult.setFLAG("1");
                    invoiceResult.setKPRQ(respData.getInvoiceDate());
                    invoiceResult.setDRAWER(respData.getDrawer());
                    invoiceResult.setDMGS(entity.getCompany());
                    List<RespSellerInvoiceDetail> sellerInvoiceDetailList = respData.getSellerInvoiceDetailList();
                    if (!sellerInvoiceDetailList.isEmpty()) {
                        RespSellerInvoiceDetail respSellerInvoiceDetail = sellerInvoiceDetailList.get(0);
                        invoiceResult.setNOTAXAMOUNT(respSellerInvoiceDetail.getGoodSum());
                        invoiceResult.setTAXAMOUNT(respSellerInvoiceDetail.getGoodTax());
                        invoiceResult.setTOTALAMOUNT(respSellerInvoiceDetail.getGoodSumTax());
                    }
                    invoiceResultList.add(invoiceResult);
                    //保存
                    CompanyInvoiceQueryResultEntity companyInvoiceQueryResult = new CompanyInvoiceQueryResultEntity();
                    companyInvoiceQueryResult.setInvoiceCode(invoiceResult.getINVOICECODE());
                    companyInvoiceQueryResult.setRn(invoiceResult.getRN());
                    companyInvoiceQueryResult.setFlag(invoiceResult.getFLAG());
                    companyInvoiceQueryResult.setKprq(respData.getInvoiceDate());
                    companyInvoiceQueryResult.setNotaxamount(invoiceResult.getNOTAXAMOUNT());
                    companyInvoiceQueryResult.setTaxamount(invoiceResult.getTAXAMOUNT());
                    companyInvoiceQueryResult.setTotalamount(invoiceResult.getTOTALAMOUNT());
                    companyInvoiceQueryResult.setDrawer(invoiceResult.getDRAWER());
                    companyInvoiceQueryResult.setDmgs(invoiceResult.getDMGS());
                    companyInvoiceQueryResult.setCompanyInvoiceNo(companyInvoiceNo);
                    companyInvoiceQueryResult.setInvoiceId(entity.getInvoiceId());
                    /** 保存开票结果 **/
                    companyInvoiceQueryResultService.saveOrUpdate(companyInvoiceQueryResult);
                }


                if (flag && invoiceResultList.size() > IntegerEnum.ZERO.getValue()) {
                    //根据开票单拆分结算子订单
                    Map<Integer,List<WorderChildInformationEntity>> childOrderMap = worderChildInformationService.splitBalanceOrder(v.getId());
                    //更新开票单关联激励结算子工单状态
                    worderChildInformationService.updateChildStimulateWorderStatus(v.getId(),3,"已开票");

                    /** 更新开票状态 记账中 **/
                    CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
                    companyInvoiceEntity.setStatus(6);
                    companyInvoiceEntity.setId(v.getId());
                    companyInvoiceService.updateById(companyInvoiceEntity);
                    try {
                        worderInformationAccountService.pushComapanyBalanceAcsAccount(v, invoiceResultList, invoiceOrderNo, df,childOrderMap);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                log.error("调用金税查询开票状态接口失败，发票ID:" + invoiceOrderNo + "\n", e);
                e.printStackTrace();
            }
        }
    }

    public void queryFinanceTaskScheduling() {
        List<CompanyInvoiceEntity> companyInvoiceEntityList = companyInvoiceService.list(
                new QueryWrapper<CompanyInvoiceEntity>().in("status", 4,5,6));
        //查询开票中状态改为查询
        if (CollectionUtils.isNotEmpty(companyInvoiceEntityList)) {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            for (CompanyInvoiceEntity v : companyInvoiceEntityList) {
                String companyInvoiceNo = v.getCompanyInvoiceNo();
                // 根据开票单号查询所有开票信息
                QueryWrapper<BalanceEnterprisesDetailRecordEntity> queryWrapper =
                        new QueryWrapper<BalanceEnterprisesDetailRecordEntity>().eq("company_invoice_no", companyInvoiceNo);
                List<BalanceEnterprisesDetailRecordEntity> list = balanceEnterprisesDetailRecordService.list(queryWrapper);
                String invoiceOrderNo = v.getInvoiceOrderNo();
                try {
                    List<InvoiceResults> invoiceResultList = new ArrayList<>();
                    Integer flag = 1;

                    List<Integer> successIdList = new ArrayList<>();
                    BalanceEnterprisesDetailRecordEntity entity = list.get(0);
                    //调用新的开票结果查询
                    ReqSearchOrder reqSearchOrder = new ReqSearchOrder();
                    reqSearchOrder.setOrderNo(entity.getCompanyInvoiceNo());
                    reqSearchOrder.setOrderType("10");
                    JSONObject rsp = financeBusiness.companySearchOrder(reqSearchOrder);
                    if (!rsp.containsKey("code") || !InvoiceBusiness.SUCCESS.equals(rsp.getInteger("code"))) {
                        flag = 0;
//                       continue;
                    }
                    RespSearchOrder respSearchOrder = new RespSearchOrder();
                    if (rsp!=null){
                        respSearchOrder = rsp.toJavaObject(RespSearchOrder.class);
                    }
                    CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
                    if (respSearchOrder.getData().getFinancialProcess().equals("开票中")){
                        companyInvoiceEntity.setStatus(5);
                        companyInvoiceEntity.setId(v.getId());
                        companyInvoiceService.updateById(companyInvoiceEntity);
                        continue;
                    }
                    //封装返回结果
                    InvoiceResults invoiceResult = new InvoiceResults();
                    invoiceResult.setINVOICECODE(respSearchOrder.getData().getInvoiceList().get(0).getInvoiceCode());
                    invoiceResult.setRN("1.0");
                    invoiceResult.setFLAG(flag.toString());//1成功 0失败
                    invoiceResult.setKPRQ(respSearchOrder.getData().getInvoiceList().get(0).getInvoiceDate());
                    invoiceResult.setDMGS(entity.getCompany());
                    invoiceResult.setDRAWER(respSearchOrder.getData().getInvoiceList().get(0).getDrawer());//开票人
                    List<SearchOrderDetailInvoiceList> searchOrderDetailInvoiceListList = respSearchOrder.getData().getInvoiceList();
                    if (!searchOrderDetailInvoiceListList.isEmpty()) {
                        SearchOrderDetailInvoiceList searchOrderDetailInvoiceList = searchOrderDetailInvoiceListList.get(0);
                        invoiceResult.setNOTAXAMOUNT(searchOrderDetailInvoiceList.getInvoiceAmount());
                        invoiceResult.setTAXAMOUNT(searchOrderDetailInvoiceList.getSumTax());
                        invoiceResult.setTOTALAMOUNT(searchOrderDetailInvoiceList.getSumPrice());
                    }
                    invoiceResultList.add(invoiceResult);
                    //保存
                    CompanyInvoiceQueryResultEntity companyInvoiceQueryResult = new CompanyInvoiceQueryResultEntity();
                    companyInvoiceQueryResult.setInvoiceCode(invoiceResult.getINVOICECODE());
                    companyInvoiceQueryResult.setRn(invoiceResult.getRN());
                    companyInvoiceQueryResult.setFlag(invoiceResult.getFLAG());
                    companyInvoiceQueryResult.setKprq(respSearchOrder.getData().getInvoiceList().get(0).getInvoiceDate());
                    companyInvoiceQueryResult.setNotaxamount(invoiceResult.getNOTAXAMOUNT());
                    companyInvoiceQueryResult.setTaxamount(invoiceResult.getTAXAMOUNT());
                    companyInvoiceQueryResult.setTotalamount(invoiceResult.getTOTALAMOUNT());
                    companyInvoiceQueryResult.setDrawer(invoiceResult.getDRAWER());
                    companyInvoiceQueryResult.setDmgs(invoiceResult.getDMGS());
                    companyInvoiceQueryResult.setCompanyInvoiceNo(companyInvoiceNo);
                    companyInvoiceQueryResult.setInvoiceId(entity.getInvoiceId());
                    /** 保存开票结果 **/
                    companyInvoiceQueryResultService.saveOrUpdate(companyInvoiceQueryResult);
                    if (flag==1 && invoiceResultList.size() > IntegerEnum.ZERO.getValue()) {
                        //更新开票单关联激励结算子工单状态
                        worderChildInformationService.updateChildStimulateWorderStatus(v.getId(),3,"已开票");
                        /** 根据接口返回值更新开票状态 **/
                        //1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:开票中 6:记账中 7:暂估完成 9:已记收 -1:第一次审核不通过 -2:第二次审核不通过 20:已推送5A 8:财务审核中

                        if (respSearchOrder.getData().getFinancialProcess().equals("已开票")){
                            companyInvoiceEntity.setStatus(6);
                        }else if (respSearchOrder.getData().getFinancialProcess().equals("记收暂估完成")){
                            //工单结算的财务中台记账业务
                            worderInformationAccountService.updateWorderFinanceAccount(v,respSearchOrder);
                            companyInvoiceEntity.setStatus(7);
                        }
                        companyInvoiceEntity.setId(v.getId());
                        companyInvoiceService.updateById(companyInvoiceEntity);
                    }
                } catch (Exception e) {
                    log.error("调用财务中台查询开票状态接口失败，发票ID:" + invoiceOrderNo + "\n", e);
                    e.printStackTrace();
                }
            }
        }


        //查询工单结算的财务中台记账是否成功
//        worderInformationAccountService.queryWorderFinanceAccount();
        //查询增项计收的ACS记账是否成功
        worderInformationAccountService.queryIncreFinanceAccount();
//        //查询增项收款的ACS记账是否成功
//        List<WorderInformationAccountEntity> entityList = worderInformationAccountService.queryIncreReceivablesFinanceAccount();
//        for (WorderInformationAccountEntity worderInformationAccountEntity : entityList) {
//            //增项收款的ACS记账成功后的处理：更新工单增项结算状态和用户开票
//            try {
//                worderInformationAccountService.receivablesFinanceAccountSuccess(worderInformationAccountEntity);
//            } catch (Exception e) {
//                log.error("调用用户开票程序失败：", e);
//                e.printStackTrace();
//            }
//        }
        //查询激励的ACS记账是否成功
        worderInformationAccountService.queryStimulateFinanceAccount();
    }
    /**
     * ACS记账是否成功定时查询
     */
    public void queryAcsAccountScheduling() {
        //查询工单结算的ACS记账是否成功
        worderInformationAccountService.queryWorderAcsAccount();
        //查询增项计收的ACS记账是否成功
        worderInformationAccountService.queryIncreAcsAccount();
        //查询增项收款的ACS记账是否成功
        List<WorderInformationAccountEntity> entityList = worderInformationAccountService.queryIncreReceivablesAcsAccount();
        for (WorderInformationAccountEntity worderInformationAccountEntity : entityList) {
            //增项收款的ACS记账成功后的处理：更新工单增项结算状态和用户开票
            try {
                worderInformationAccountService.receivablesAcsAccountSuccess(worderInformationAccountEntity);
            } catch (Exception e) {
                log.error("调用用户开票程序失败：", e);
                e.printStackTrace();
            }
        }
        //查询激励的ACS记账是否成功
        worderInformationAccountService.queryStimulateAcsAccount();
    }


    /**
     * 传输回款审核单1216接口
     */
    public void pushReceivableScheduling(){
        List<CompanyReceivableEntity> receivableEntityList = companyReceivableService.list(
                new QueryWrapper<CompanyReceivableEntity>().eq("status",4));
        for (CompanyReceivableEntity companyReceivableEntity : receivableEntityList) {
            WorderAuditRecordServiceImpl worderAuditRecordService = new WorderAuditRecordServiceImpl();
            RespTransferApprovalForm respTransferApprovalForm = worderAuditRecordService.transferApprovalForm(companyReceivableEntity);
            if (!FinanceBusiness.getSUCCESS().equals(respTransferApprovalForm.getCode())) {
                log.error(respTransferApprovalForm.getMsg());
                CompanyReceivableRecordEtity companyReceivableRecordEtity = companyReceivableRecordService.getOne(
                        new QueryWrapper<CompanyReceivableRecordEtity>().eq("company_receivable_no",companyReceivableEntity.getCompanyReceivableNo()));
                CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceService.getById(companyReceivableRecordEtity.getInvoiceId());
                companyInvoiceEntity.setCavState(-3);
                companyInvoiceService.updateById(companyInvoiceEntity);
            }else{
                CompanyReceivableRecordEtity companyReceivableRecordEtity = companyReceivableRecordService.getOne(
                        new QueryWrapper<CompanyReceivableRecordEtity>().eq("company_receivable_no",companyReceivableEntity.getCompanyReceivableNo()));
                CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceService.getById(companyReceivableRecordEtity.getInvoiceId());

                String[] balanceIdArr = null;
                String balanceIds = companyReceivableEntity.getBalanceIds();
                if (StringUtils.isNotBlank(balanceIds)) {
                    balanceIdArr = balanceIds.split(",");
                }

                // 平账中处理
                if (companyInvoiceEntity.getCavState() == 3) {
                    worderAuditRecordService.updateBalanceAdvanceMoneyCompanyInvoiceCavState(companyInvoiceEntity, balanceIdArr,companyReceivableEntity);
                    worderAuditRecordService.updateCompanyReceivableCapitalpoolByCompanyReceivableRecord(companyInvoiceEntity.getCompanyInvoiceNo(), "pushReceivableTask");
                } else {

                    companyInvoiceEntity.setCavState(2);
                    companyInvoiceService.updateById(companyInvoiceEntity);
                    //修改子订单状态


                    worderInformationAccountDao.update(null,
                            new UpdateWrapper<WorderInformationAccountEntity>()
                                    .in("id", balanceIdArr)
                                    .set("worder_set_status", 4)
                                    .set("worder_set_status_value", "车企已回款"));

                    if (companyReceivableEntity.getType()==1){
                        worderInformationAccountDao.update(null,
                                new UpdateWrapper<WorderInformationAccountEntity>()
                                        .in("id", balanceIdArr)
                                        .set("type", 1));
                        companyInvoiceEntity.setType(1);
                        companyInvoiceService.updateById(companyInvoiceEntity);
                    }
                }


//                if (balanceIdArr != null) {
//                    worderAuditRecordService.receivableSucessUpdateWorderInformation(balanceIdArr);
//                }
            }
        }

    }


    public void pushIncreScheduling() {
        log.info("========================推送增项定时任务开始========================");
        List<WorderInformationEntity> worderInformationEntityList = worderInformationService.list(
                new QueryWrapper<WorderInformationEntity>().in("ticket_status", 1,2).eq("worder_Incre_status",0));
        for (WorderInformationEntity worderInformationEntity : worderInformationEntityList) {
            try{
                WorderOrderLogEntity worderOrderLogEntity = worderOrderLogService.getOne(
                        new QueryWrapper<WorderOrderLogEntity>().eq("order_status", 2).eq("apply",1).eq("worder_no",worderInformationEntity.getWorderNo()));
                if (worderOrderLogEntity!=null) {
                    WorderInformationAccountService worderInformationAccountService =  (WorderInformationAccountService) SpringUtils.getBean("worderInformationAccountService");
                    worderInformationAccountService.pushFinanceAccountIncreReceivables(worderInformationEntity.getWorderId());
                }
            }catch (Exception e){
                log.error("error:", e);
                log.info("========================推送增项定时任务异常========================",e);
            }

        }
        log.info("========================推送增项定时任务结束========================");
    }

    /**
     * 自动发布商户通对账单的定时任务
     * 工单结算流程：待结算 -> 金税开票 -> 开票完成 -> ACS记账 -> 记账成功 ->已开票 -> 已回款 -> 三次审批通过 -> 商户通开票 -> 已发布 -> 商户通开票完成 -> 已结算
     * 增项结算流程：用户增项结算 -> ACS记账收款 -> 收款记账成功 -> 瑞宏电子票开票 -> 开票完成 -> ACS记账收入和暂估成本 -> 待计算 -> 记账成功 -> 待结算 -> 三次审批通过 -> 商户通开票 -> 已发布 -> 商户通开票完成 -> 已结算
     * 网点激励结算流程：待结算 -> 三次审批通过 -> -> ACS记账 -> 记账成功 -> 商户通开票 -> 已发布 -> 商户通开票完成 -> 已结算
     */
    public void worderPublishScheduling() {
        List<Map> list = worderInformationAccountDao.queryWorderAndStimulateForPublish();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        balanceSplitPublish(list);
    }

    /**
     * 发布选中的结算
     *
     * @param pd
     */
    public void checkedBalancePublish(PublishDetail pd) {
        List<Map> list = worderInformationAccountDao.queryCheckedWorderAndStimulateForPublish(pd);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        balanceSplitPublish(list);
    }

    /**
     * 按照网点和品牌划分结算数据，相同网点和品牌的合并发布对账单
     *
     * @param list 待发布的工单、激励
     */
    private void balanceSplitPublish(List<Map> list) {
        //按照网点和品牌划分，相同网点和品牌的合并发布对账单
        Map<Integer, Map<Integer, PublishDetail>> pdMap = new HashMap<>();
        for (Map m : list) {
            Integer dotId = getIntegerValue(m, "dot_id");
            Integer dataId = getIntegerValue(m, "data_id");
            Integer type = getIntegerValue(m, "data_type");
            Integer brandId = getIntegerValue(m, "brand_id");
            if (pdMap.get(dotId) == null) {
                PublishDetail publishDetail = new PublishDetail();
                Map<Integer, PublishDetail> map = new HashMap<>();
                map.put(brandId, publishDetail);
                pdMap.put(dotId, map);
            } else if (pdMap.get(dotId).get(brandId) == null) {
                pdMap.get(dotId).put(brandId, new PublishDetail());
            }
            PublishDetail pd = pdMap.get(dotId).get(brandId);
            switch (type.intValue()) {
                case 1:
                    pd.getWorderIds().add(dataId);
                    break;
                case 2:
                    pd.getIncreIds().add(dataId);
                    break;
                case 3:
                    pd.getStimulateIds().add(dataId);
                    break;
                default:
            }
        }
        for (Map<Integer, PublishDetail> map : pdMap.values()) {
            for (PublishDetail publishDetail : map.values()) {
                try {
                    worderInformationAccountService.pushCvpBill(publishDetail);
                } catch (Exception e) {
                    log.error("推送商户通对账单失败：", e);
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 查询商户通对账单状态的定时任务
     */
    public void cvpBillStatusScheduling() {
        List<CvpBillEntity> billList = cvpBillDao.selectList(new QueryWrapper<CvpBillEntity>().ne("status_flag", "10"));
        if (CollectionUtils.isNotEmpty(billList)) {
            for (CvpBillEntity bill : billList) {
                try {
                    worderInformationAccountService.queryCvpBill(bill);
                } catch (Exception e) {
                    log.error("查询商户通对账单失败：", e);
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 网点激励推送ACS自动任务
     */
    public void dotStimulateAccountScheduling() {
        List<WorderPmStimulateEntity> stimulateList = worderPmStimulateService.queryDotStimulateForBalance();
        if (CollectionUtils.isEmpty(stimulateList)) {
            return;
        }

        List<WorderChildInformationEntity> worderChildInformationList = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>().in("stimulateId", stimulateList.stream().map(WorderPmStimulateEntity::getId).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(worderChildInformationList)) {
            return;
        }
        log.info("---------网点激励推送ACS---------");
        worderInformationAccountService.pushAcsAccountStimulate(worderChildInformationList, stimulateList);
    }

    private String getStringValue(Map worderMap, Object key) {
        Object value = worderMap.get(key);
        return (value == null) ? "" : value.toString();
    }

    private Integer getIntegerValue(Map worderMap, Object key) {
        Object value = worderMap.get(key);
        if (null == value) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof Boolean) {
            boolean b = ((Boolean) value).booleanValue();
            if (b) {
                return 1;
            } else {
                return 0;
            }
        }
        return Integer.valueOf(value.toString());
    }

    /**
     * 查询待发布的发布单系信息，自动推送推送对账单到商户通
     */
    public void balancePublishScheduling() {
        List<BalancePublishEntity> balancePublishEntityList = balancePublishService.list(new QueryWrapper<BalancePublishEntity>().eq("status", 4));
        for (BalancePublishEntity balancePublishEntity : balancePublishEntityList) {
            try {
                balancePublishService.pushCvpBill(balancePublishEntity);
            } catch (Exception e) {
                log.error("自动发布对账单到商户通失败，发布单ID：{}", balancePublishEntity.getId(), e);
            }
        }
    }

    /**
     * 查询待开票的厂商发票单，自动调用金税开票
     */
    public void companyInvoiceScheduling() {
        List<CompanyInvoiceEntity> list = companyInvoiceService.list(new QueryWrapper<>(new CompanyInvoiceEntity().setStatus(4)));
        for (CompanyInvoiceEntity entity : list) {
            try {
                companyInvoiceService.pushEnterprisesInvoice(entity);
            } catch (Exception e) {
                log.error("自动推送税票云开票失败，开票单ID：" + entity.getId(), e);
                e.printStackTrace();
            }
        }
    }

    /**
     * 开票单归档定时任务
     */
    public void companyInvoiceVoucherScheduling() {
        try {
            companyInvoiceService.invoiceVoucherArchive();
        } catch (Exception e) {
            log.error("开票单归档失败");
            e.printStackTrace();
        }
    }
}
