package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/1/20.
 * 网点查询结算工单的数据对象
 */
@Data
@TableName("worder_information")
@ApiModel(value = "工单主表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorderInformationAccountEntity implements Serializable {

    public interface  ReSubmit  {
    }

    //ID
    @TableId
    @NotNull(message = "worderId不能为空", groups = ReSubmit.class)
    private Integer worderId;
    //工单类型编号
    private Integer worderTypeId;
    //工单编号
    private String worderNo;
    //厂商结算总金额(不含税)
    @NotNull(message = "厂商结算金额不能为空", groups = ReSubmit.class)
    private BigDecimal companyBalanceFee;
    //厂商结算总金额(含税)
    private BigDecimal companyBalanceFeeSum;
    //厂商结算税额
    private BigDecimal companyBalanceFeeTax;
    //网点增项结算总金额(不含税)
    private BigDecimal dotIncreBalanceFee;
    //网点增项结算总金额(含税)
    private BigDecimal dotIncreBalanceFeeSum;
    //网点增项结算税额
    private BigDecimal dotIncreBalanceFeeTax;
    //网点工单结算总金额(不含税)
    private BigDecimal dotBalanceFee;
    //网点工单结算总金额(含税)
    private BigDecimal dotBalanceFeeSum;
    //网点工单结算税额
    private BigDecimal dotBalanceFeeTax;
    //发票ID
    private Integer invoiceId;
    private Integer worderStatus;
    private Integer worderExecStatus;
    //工单结算状态（数据字典worder_set_status）
    @NotNull(message = "工单结算状态不能为空", groups = ReSubmit.class)
    private Integer worderSetStatus;
    //工单结算状态冗余
    @NotNull(message = "工单结算状态描述不能为空", groups = ReSubmit.class)
    private String worderSetStatusValue;
    //工单增项结算状态（数据字典worder_Incre_status）
    private Integer worderIncreStatus;
    //工单增项结算状态冗余
    private String worderIncreStatusValue;
    //工单激励状态（数据字典worder_exci_status）
    private Integer worderExciStatus;
    //工单激励状态冗余
    private String worderExciStatusValue;
    //工单结算发布时间
    private Date worderPublishTime;
    //增项结算发布时间
    private Date worderIncrePublishTime;

    // 车企
    @TableField(exist = false)
    private String companyName;
    // 工单类型
    @TableField(exist = false)
    private String worderTypeName;
    // 税点
    @TableField(exist = false)
    private BigDecimal taxRate;
    // 工单完成日期
    @TableField(exist = false)
    private String worderPublishTimeValue;

    private String worderFinishTime;

    @TableField(exist = false)
    private Integer waitAccountId;
    @TableField(exist = false)
    private Integer worderInvoiceType;
    /**
     * 车企结算申请开票状态 0： 待结算  1：待开票
     */
    @TableField(exist = false)
    private Integer companyAccountStatus;

    @TableField(exist = false)
    private String companyAccountStatusValue;
}
