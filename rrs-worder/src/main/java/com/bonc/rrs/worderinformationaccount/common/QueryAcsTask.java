package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liqingchao on 2020/4/27.
 * 查询ACS记账的定时任务
 */
@Component("queryAcsTask")
public class QueryAcsTask implements ITask{
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    private static final Logger log = LoggerFactory.getLogger(QueryAcsTask.class);
    @Override
    public void run(String params) {
        log.info("定时获取 queryAcsAccountScheduling 开始");
        worderBalanceScheduling.queryAcsAccountScheduling();
        log.info("定时获取 queryAcsAccountScheduling 结束");
    }
}
