package com.bonc.rrs.worderinformationaccount.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worder.entity.StimulateFileEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:59
 */
@Mapper
public interface WorderPmStimulateDao extends BaseMapper<WorderPmStimulateEntity> {

    List<WorderPmStimulateEntity> selectNotBalanceStimulateOnBalanceWorderByCompanyId(Integer companyId);

    List<WorderPmStimulateEntity> queryDotStimulateForBalance();

    List<WorderPmStimulateEntity> listStimulateCompanyNotAccount();

    WorderPmStimulateEntity selectByStimulateId(Integer stimulateId);
    //修改激励表 激励单状态为发布成功，发布状态清空
    Integer updatePublishStatus(Integer id);
    Integer updatePmStatus(@Param("id") Integer id,@Param("status") Integer status);

    void addStimulateFile(StimulateFileEntity stimulateFileEntity);

    List<StimulateFileEntity> findStimulateFileByStimulateId(Integer stimulateId);

}
