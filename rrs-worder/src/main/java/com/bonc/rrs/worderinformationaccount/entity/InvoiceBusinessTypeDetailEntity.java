package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 发票特殊业务类型详细信息表
 */
@Data
@TableName("invoice_business_type_detail")
public class InvoiceBusinessTypeDetailEntity {
    @TableId
    private Integer id;
    private Integer enterprisesHeaderId;
    private Integer invoiceId;

    /**
     * 建筑服务发生地
     */
    private String jzfwfsd;
    /**
     * 发生地详细地址
     */
    private String fsdxxdz;
    /**
     * 建筑项目名称
     */
    private String jzxmmc;
    /**
     * 跨地市标志(Y 是 N 否)
     */
    private String kdsbz;
    /**
     * 土地增值税项目编号
     */
    private String tdzzsxmbh;


}
