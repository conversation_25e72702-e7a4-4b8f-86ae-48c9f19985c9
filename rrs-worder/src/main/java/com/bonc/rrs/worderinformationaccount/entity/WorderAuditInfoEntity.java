package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by liqingchao on 2020/1/20.
 * 网点查询结算工单的数据对象
 */
@Data
@TableName("worder_audit_info")
@ApiModel(value = "工单结算审核记录表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorderAuditInfoEntity implements Serializable {
    //ID
    @TableId
    private Integer id;

    //关联主键id(工单id/激励id等)
    @NotNull(message = "关联主键id不能为空")
    private Integer fromId;
    //厂商结算首次审核/厂商结算二次审核
    @NotEmpty(message = "操作不能为空")
    private String operation;
    //结算类型 厂商结算费用/网点增项费用/网点工单结算费用/奖惩费用
    @NotEmpty(message = "结算类型不能为空")
    private String balanceType;
    //审核人
    private String userId;
    //审核时间
    private Date auditTime;
    //审核状态(1:通过、0:不通过)
    @NotNull(message = "审核状态id不能为空")
    private Integer auditStatus;
    //不通过原因S
    private String refusalCause;
}
