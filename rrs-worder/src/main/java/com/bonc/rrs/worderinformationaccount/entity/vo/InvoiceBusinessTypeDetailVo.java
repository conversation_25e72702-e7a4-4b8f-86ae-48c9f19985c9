package com.bonc.rrs.worderinformationaccount.entity.vo;

import lombok.Data;

/**
 * 发票特殊业务类型详细信息
 */
@Data
public class InvoiceBusinessTypeDetailVo {
    /**
     * 建筑服务发生地
     */
    private String jzfwfsd;
    /**
     * 发生地详细地址
     */
    private String fsdxxdz;
    /**
     * 建筑项目名称
     */
    private String jzxmmc;
    /**
     * 跨地市标志(Y 是 N 否)
     */
    private String kdsbz;
    /**
     * 土地增值税项目编号
     */
    private String tdzzsxmbh;


}
