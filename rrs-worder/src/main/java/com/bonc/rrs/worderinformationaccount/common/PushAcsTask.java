package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liqingchao on 2020/4/27.
 * 推送ACS记账的定时任务
 */
@Component("pushAcsTask")
public class PushAcsTask implements ITask {
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    private static final Logger log = LoggerFactory.getLogger(PushAcsTask.class);

    @Override
    public void run(String params) {
        log.info("定时获取 companyInvoiceStatusScheduling 开始");
        worderBalanceScheduling.companyInvoiceStatusScheduling();
        log.info("定时获取 companyInvoiceStatusScheduling 结束");
    }

}
