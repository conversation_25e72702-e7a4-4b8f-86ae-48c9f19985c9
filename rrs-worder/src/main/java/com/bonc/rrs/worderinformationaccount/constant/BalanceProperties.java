package com.bonc.rrs.worderinformationaccount.constant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by liqing<PERSON><PERSON> on 2020/3/8.
 * 对接开票、记账系统使用的常量
 */

@Component
@ConfigurationProperties(prefix="balance")
@Data
public class BalanceProperties {
    /**
     * 结算金额计算的舍入模式，使用BigDecimal的舍入模式 6：四舍六入五成双（银行家舍入法） 4：四舍五入 1：截取
     */
    public int ROUND_MODE = 6;
    /**
     * 日日顺公司代码
     */
    public String RRS_COMPANY_CODE;
    /**
     * 日日顺公司名称
     */
    public String RRS_COMPANY_NAME;
    public String RRS_COMPANY_TAX_NO;// 91310117MA1J31BJ0W #日日顺公司税号
    public String RRS_COMPANY_MOBILE;// 021-******** #日日顺税号
    public String RRS_COMPANY_ADDRESS;// 上海市普陀区真北路958号21幢6层 #日日顺公司地址
    public String RRS_COMPANY_BANK;// 中国建设银行股份有限公司上海番禺路支行 #日日顺公司开户行
    public String RRS_COMPANY_BANK_NO;// 31050174400000000657 #日日顺公司银行账号
    /**
     * 日日顺公司收款账号
     */
    public Map<String, String> RRS_COMPANY_ACCOUNT_MAP;
    /**
     * 单位
     */
    public String PRODUCT_UNIT;
    /**
     * 日日顺对厂商收款的税率
     */
    public BigDecimal RRS_COMPANY_TAX_RATE;
    /**
     * 日日顺对用户收款的税率
     */
    public BigDecimal RRS_USER_TAX_RATE;
    /**
     * 产品分类税码 --金税接口使用
     */
    public String PRODUCT_TAX_CODE;
    /**
     * 发票种类(0专用发票 1普通发票 3电子发票) --金税接口使用
     */
    public String INVOICE_TYPE = "0";
    /**
     * 日日顺收款人 --金税接口使用
     */
    public String PAYEE;
    /**
     * 日日顺复核人 --金税接口使用
     */
    public String REVIEWER;
    /**
     * 商品代码 --金税接口使用
     */
    public String GOODS_NO;
    /**
     * 商品名称 --金税接口使用
     */
    public String GOODS_NAME;
    /**
     * 商品型号 --金税接口使用
     */
    public String GOODS_MODEL;
    /**
     * 结算员工号 --商户通接口使用
     */
    public String BALANCE_STAFF_CODE;
    /**
     * 结算员姓名 --商户通接口使用
     */
    public String BALANCE_STAFF_NAME;
    /**
     * 预算体编码 --商户通接口使用
     */
    public String BUDGET_CODE;
    /**
     * 预算体名称 --商户通接口使用
     */
    public String BUDGET_NAME;
    /**
     * 费用类型代码 --商户通接口使用
     */
    public String FEE_TYPE_CODE;
    /**
     * 费用类型名称 --商户通接口使用
     */
    public String FEE_TYPE_NAME;
    /**
     * 网点工单结算是否乘以网点对应星级的结算系数
     */
    public boolean DOT_WORDER_USE_BALANCE_RATE;
    /**
     * 网点增项结算是否乘以网点对应星级的结算系数
     */
    public boolean DOT_INCRE_USE_BALANCE_RATE;

    /**
     * 开票含税最大限额
     */
    public BigDecimal INVOICE_LIMIT_PRICE;
    /**
     * 开票不含税最大限额
     */
    public BigDecimal NO_TAX_INVOICE_LIMIT_PRICE;


}
