package com.bonc.rrs.worderinformationaccount.common;

import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liqingchao on 2020/4/27.
 * 查询商户通开票状态的定时任务
 */
@Component("queryCvpBillTask")
public class QueryCvpBillTask implements ITask{
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    private static final Logger log = LoggerFactory.getLogger(QueryCvpBillTask.class);

    @Override
    public void run(String params) {
        log.info("定时获取 cvpBillStatusScheduling 开始");
        worderBalanceScheduling.cvpBillStatusScheduling();
        log.info("定时获取 cvpBillStatusScheduling 结束");
    }
}
