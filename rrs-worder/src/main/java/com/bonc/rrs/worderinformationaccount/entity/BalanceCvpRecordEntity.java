package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/4/23.
 */
@Data
@TableName("balance_cvp_record")
public class BalanceCvpRecordEntity  implements Serializable {
    @TableId
    private Integer id;
    private Date pushTime;
    /**对账单应付总金额 N*/
    protected BigDecimal acAmt;
    protected String add1;
    protected String add2;
    protected String add3;
    protected String add4;
    protected String add5;
    /**对账单应结总金额 N*/
    protected BigDecimal apAmt;
    /**预算体编码 N*/
    protected String bdEntityCode;
    /**预算体名称 N*/
    protected String bdEntityName;
    /**结算开始日期 N*/
    protected Date billBeginDate;
    /** 结算结束日期 N*/
    protected Date billEndDate;
    /** 对账单（结算单）号 N*/
    protected String billNo;
    /** 凭证抬头文本 N*/
    protected String bktxt;
    /** 公司代码 N*/
    protected String bubrs;
    /** 凭证过帐日期 N*/
    protected Date budat;
    /**业务模式 N*/
    protected String businesstype;
    /**城市 N*/
    protected String city;
    /**结算日期 N*/
    protected Date createdTime;
    /**对账单明细行数 N*/
    protected Long detailCount;
    /**应收/应付总额 N*/
    protected BigDecimal dmbtr;
    /**手续费金额  N*/
    protected BigDecimal dmbtr1;
    /**手税金  N*/
    protected BigDecimal dmbtr2;
    /**费用类型（结算单类型）编码*/
    protected String feeTypeCode;
    /**费用类型（结算单类型）名称*/
    protected String feeTypeName;
    /**客户编号 N*/
    protected String kunnr;
    /**供应商编码 N*/
    protected String lifnr;
    protected String name;
    /**业务系统编码 N*/
    protected String originApp;
    /**对所属账期（结算年月）YYYYMM N*/
    protected String periodChar;
    /**对账单索赔金额 没有的话填0 N*/
    protected BigDecimal reduceAmt;
    /**对账单激励金额 没有的话填0 N*/
    protected BigDecimal rewardAmt;
    /**项目文本 N*/
    protected String sgtxt;
    protected String sourceType;
    /** 工贸编码*/
    protected String stEntityCode;
    /** 工贸名称*/
    protected String stEntityName;
    /** 结算员工号*/
    protected String stUserCode;
    /** 结算员姓名*/
    protected String stUserName;
    /** 供应商（服务商、网点）V码 N*/
    protected String vendorCode;
    /** 供应商名称 N*/
    protected String vendorName;
    /** 货币码  N*/
    protected String waers;
    /**参考凭证编号 N*/
    protected String xblnr;
    /**行项目参考码3—业务订单号 N*/
    protected String xref3;
    /**业务单据号 N*/
    protected String ywdh;
    /**业业务模式 N*/
    protected String ywms;
    /**是否签合同*/
    protected String bBargain;
    /**收款银行名称 N*/
    protected String cAccountBank;
    /**出账银行名称 N*/
    protected String cAccountBankOut;
    /**出账公司编码 N*/
    protected String cAccountCode;
    /**出账公司名称 N*/
    protected String cAccountName;
    /**收款单位账号 N*/
    protected String cAccountNo;
    /**出账公司账户*/
    protected String cAccountNoOut;
    /**客户编码*/
    protected String cCusCode;
    /**客户名称*/
    protected String cCusName;
    /**业务描述*/
    protected String cDescriptionOn;
    /**费用项目编码*/
    protected String cFeeItemCode;
    /**费用项目名称*/
    protected String cFeeItemName;
    /**合同号*/
    protected String cHtCode;
    /** 发票选项（发票先到、发票后到、无需发票） N*/
    protected String cInvoiceOption;
    /** 发票类型（正常发票、无需发票） N*/
    protected String cInvoiceType;
    /**付款方式 传X即可 N*/
    protected String cPayType;
    /**付款类型 正常付款 N*/
    protected String cPayTypeCode;
    /**工作项目编码*/
    protected String cProCode;
    /**原因*/
    protected String cReason;
    /**（内销/外销）N*/
    protected String cSaleType;
    /**供应商类型*/
    protected String cSsupType;
    /**申请人工号*/
    protected String cUserCode;
    /**申请人姓名*/
    protected String cUserName;
    /**WBS要素*/
    protected String cWbsCode;
    /**计划付款日期*/
    protected Date dPayable;
    /**付款金额 N*/
    protected BigDecimal iApplied;

}
