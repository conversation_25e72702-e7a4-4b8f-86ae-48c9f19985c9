package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/4/23.
 */
@Data
@TableName("balance_enterprises_detail_record")
public class BalanceEnterprisesDetailRecordEntity {
    @TableId
    private Integer id;
    private Integer headerId;
    private String invoiceId;
    /**
     * 开票单号
     */
    private String companyInvoiceNo;
    private String orderType;
    private String orderNo;                                 //单据号XSDDM
    private String company;                                 //单据公司DMGS
    private int detailsQuenc;                            //明细序号（行项目)MXXH
    private String typesNo;                                 //FLBM 分类编码
    private String goodsNo;                                 //产品代码CPDM
    private String goodsName;                               //产品名称CPMC
    private String model;                                   //型号XH
    private String storeId;                                 //产品单位CPDW
    private BigDecimal goodsNum;                                //数量
    private BigDecimal taxPrice;                                //含税单价HSDJ
    private BigDecimal taxAmount;                               //含税金额HSJE
    private BigDecimal noTaxPrice;                              //不含税单价XXDJ
    private BigDecimal noTaxAmount;                             //不含税金额BHSJE
    private BigDecimal texForehead;                             //税额SE
    private BigDecimal taxRate;                                 //税率(17,13,11,6,3,0)SL
    private BigDecimal discountNotTex;                          //折扣不含税金额(这一栏可以没有)zbhsje
    private BigDecimal discountTax;                             //折扣税额(这一栏可以没有)zse
    private Date createTime;                              //创建时间
    private Date updateTime;                              //修改时间
    private Date deleteTime;                              //删除时间
    private String zeroTaxRateFlag;//零税率标识 1:免税; 2:不征税; 3:普通零税率。税率为0的情况下如果不传默认为3
    //是否删除
    private Integer isDelete;

    @TableField(exist = false)
    private BigDecimal invoiceNoTaxFeeSum;
    @TableField(exist = false)
    private BigDecimal invoiceTaxFeeSum;
    @TableField(exist = false)
    private BigDecimal invoiceTaxSum;


}
