/**
 * Copyright (C), 2024,
 */
package com.bonc.rrs.worderinformationaccount.common;

import com.bonc.rrs.workManager.service.AutoSendService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/11 15:00
 * @Version 1.0.0
 */

@Component("reSendWorderTask")
public class ReSendWorderTask implements ITask {
    @Autowired
    AutoSendService autoSendService;
    private static final Logger log = LoggerFactory.getLogger(ReSendWorderTask.class);
    @Override
    public void run(String params) {
        log.info("重新派单 reSendWorderTask 开始");
        autoSendService.reSendWorder(params);
        log.info("重新派单 reSendWorderTask 结束");
    }
}