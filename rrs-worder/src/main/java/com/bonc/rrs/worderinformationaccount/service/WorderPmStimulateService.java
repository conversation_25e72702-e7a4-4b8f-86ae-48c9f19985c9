package com.bonc.rrs.worderinformationaccount.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;

import java.util.List;

/**
 * Created by liqingchao on 2020/4/27.
 */
public interface WorderPmStimulateService extends IService<WorderPmStimulateEntity> {
    List<WorderPmStimulateEntity> queryDotStimulateForBalance();

    List<WorderPmStimulateEntity> selectNotBalanceStimulateOnBalanceWorderByCompanyId(Integer companyId);
}
