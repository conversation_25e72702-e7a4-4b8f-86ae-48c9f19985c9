package com.bonc.rrs.worderinformationaccount.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.worderinformationaccount.entity.CostInformationEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Created by liqingchao on 2020/4/1.
 */
public interface CostInformationService extends IService<CostInformationEntity> {

    Boolean saveOrUpdateBathchByRowId(List<CostInformationEntity> entityList);


    void saveOrUpdateBathch(List<CostInformationEntity> entityList);

    CostInformationEntity getCostByWorderId(Integer childId);

    CostInformationEntity getCostByIncreId(Integer childId);

    CostInformationEntity getCostByStimulateId(Integer childId);
}
