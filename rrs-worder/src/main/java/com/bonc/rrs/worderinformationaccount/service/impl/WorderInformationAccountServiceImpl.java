package com.bonc.rrs.worderinformationaccount.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.balanceprocess.dao.CompanyInvoiceDao;
import com.bonc.rrs.balanceprocess.entity.AdvanceMoneyInfoEntity;
import com.bonc.rrs.balanceprocess.entity.BalancePublishEntity;
import com.bonc.rrs.balanceprocess.entity.CompanyInvoiceEntity;
import com.bonc.rrs.balanceprocess.entity.WorderChildInformationEntity;
import com.bonc.rrs.balanceprocess.service.AdvanceMoneyInfoService;
import com.bonc.rrs.balanceprocess.service.BalancePublishService;
import com.bonc.rrs.balanceprocess.service.CompanyInvoiceService;
import com.bonc.rrs.balanceprocess.service.WorderChildInformationService;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeEntity;
import com.bonc.rrs.branchbalance.service.BranchBalanceService;
import com.bonc.rrs.invoice.acs.AcsInterfaceUtil;
import com.bonc.rrs.invoice.acs.dto.FncCollectionInformation;
import com.bonc.rrs.invoice.dot.hcsp.DotHcspService;
import com.bonc.rrs.invoice.dot.hcsp.HcspResultInfo;
import com.bonc.rrs.invoice.dot.hcsp.IfLgSheetDetailCkDTO;
import com.bonc.rrs.invoice.dot.hcsp.IfLjSheetIn;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.FncInvoiceDetails;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.FncInvoiceHeaders;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.ReqInsertOrUpdate;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.ReqSearchOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.resp.RespSearchOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.selectSettlementStatus.req.ReqSelectSettlementStatus;
import com.bonc.rrs.invoice.enterprises.finance.business.selectSettlementStatus.resp.RespSelectSettlementStatus;
import com.bonc.rrs.invoice.enterprises.finance.business.selectSettlementStatus.resp.SelectSettlementStatusData;
import com.bonc.rrs.invoice.enterprises.util.*;
import com.bonc.rrs.pay.dao.WorderOrderLogMapper;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.sparesettlement.config.TicketConfigs;
import com.bonc.rrs.sparesettlement.dao.BillingOrderRecodeMapper;
import com.bonc.rrs.sparesettlement.dao.BillingRecodeMapper;
import com.bonc.rrs.sparesettlement.dao.InvoiceRecodeMapper;
import com.bonc.rrs.sparesettlement.model.entity.*;
import com.bonc.rrs.sparesettlement.model.request.BookkeepingRequest;
import com.bonc.rrs.sparesettlement.service.InvoiceAPIService;
import com.bonc.rrs.util.DataUtil;
import com.bonc.rrs.util.hscapi.bladesellerbusiness.setxwsqzzsheadvo.ReqXwsqZzsItemNewList;
import com.bonc.rrs.util.hscapi.bladesellerbusiness.setxwsqzzsheadvo.ReqXwsqZzsParams;
import com.bonc.rrs.vendorbalancemanage.entity.VendorInvoiceEntity;
import com.bonc.rrs.worder.dao.BizRegionDao;
import com.bonc.rrs.worder.dao.CompanyInformationDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dto.dto.ManagerAreaBrandDto;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.po.BrandPo;
import com.bonc.rrs.worder.service.CompanyInformationService;
import com.bonc.rrs.worder.service.DotInformationService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.utils.UserRoleUtils;
import com.bonc.rrs.worderinformationaccount.constant.BalanceProperties;
import com.bonc.rrs.worderinformationaccount.dao.*;
import com.bonc.rrs.worderinformationaccount.dto.PublishDetail;
import com.bonc.rrs.worderinformationaccount.entity.*;
import com.bonc.rrs.worderinformationaccount.entity.vo.InvoiceBusinessTypeDetailVo;
import com.bonc.rrs.worderinformationaccount.service.*;
import com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO;
import com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO;
import com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity;
import com.bonc.rrs.worderinvoice.service.WorderWaitAccountService;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.*;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysRoleEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysRoleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Service("worderInformationAccountService")
public class WorderInformationAccountServiceImpl extends ServiceImpl<WorderInformationAccountDao, WorderInformationAccountEntity> implements WorderInformationAccountService {

//    @Autowired
//    private VendorInvoiceService vendorInvoiceService;
    @Autowired
    private CompanyInvoiceService companyInvoiceService;
    @Autowired
    private DotInformationService dotInformationService;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private EnterprisesService enterprisesService; //金税开票服务
    @Autowired
    private AcsInterfaceUtil acsInterfaceUtil; //ACS记账服务
    @Autowired
    private DotHcspService dotHcspService; //商户通服务
    @Autowired
    private CompanyInformationDao companyInformationDao;
    @Autowired
    private CompanyInvoiceDao companyInvoiceDao;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired(required = false)
    private WorderAuditInfoDao worderAuditInfoDao;
    @Autowired
    private WorderPmStimulateService worderPmStimulateService;
    @Autowired
    private CompanyInformationService companyInformationService;
    @Autowired
    private CostInformationService costInformationService;
    @Autowired
    private CvpInvoiceService cvpInvoiceService;
    @Autowired
    private BranchBalanceService branchBalanceService;
    @Resource
    private BizRegionDao bizRegionDao;
    @Autowired
    private BalanceAcsPushRecordService balanceAcsPushRecordService;
    @Autowired
    private BalanceEnterprisesDetailRecordService balanceEnterprisesDetailRecordService;
    @Autowired
    private BalanceEnterprisesHeaderRecordService balanceEnterprisesHeaderRecordService;
    @Autowired
    private BalanceCvpRecordService balanceCvpRecordService;
    @Autowired
    private BalanceCvpDetailService balanceCvpDetailService;
    @Autowired
    private BalanceCvpExamineService balanceCvpExamineService;
    @Autowired
    private CvpBillDetailService cvpBillDetailService;
    @Autowired(required = false)
    private CvpBillDao cvpBillDao;
    @Autowired
    private InvoiceAPIService invoiceAPIService;
    @Autowired
    private BalanceProperties balanceProperties;
    @Autowired
    private FinanceBusiness financeBusiness;
    @Autowired(required = false)
    private WorkMsgDao workMsgDao;
    @Autowired
    private BalanceAcsAccountingStatusService balanceAcsAccountingStatusService;
    @Autowired
    private BalancePublishService balancePublishService;
    @Autowired(required = false)
    private CompanyInvoiceQueryResultService companyInvoiceQueryResultService;
    @Autowired
    private WorderChildInformationService worderChildInformationService;
    @Autowired
    private WorderInformationDao worderInformationDao;
    @Autowired
    private WorderPmStimulateDao worderPmStimulateDao;

    @Autowired
    private WorderOrderLogMapper worderOrderLogMapper;

    @Autowired
    private WorderInformationAccountDao worderInformationAccountDao;

    @Autowired
    private WorderWaitAccountService worderWaitAccountService;

    private final String pattern = "yyyy-MM-dd HH:mm:ss";

    @Resource
    private BillingOrderRecodeMapper billingOrderRecodeMapper;

    @Resource
    private BillingRecodeMapper billingRecodeMapper;

    @Resource
    private InvoiceRecodeMapper invoiceRecodeMapper;

    @Autowired
    private AdvanceMoneyInfoService advanceMoneyInfoService;
    @Autowired
    private InvoiceBusinessTypeDetailDao invoiceBusinessTypeDetailDao;

    /**
     * 来源系统
     */
    public static final String PSY_KJFWWOS = "PSY_KJFWWOS";

    private static final Logger log = LoggerFactory.getLogger(WorderInformationAccountServiceImpl.class);

    @Override
    public List queryWorderIdList(WorderInformationAccountVO worderInformation) {
        return baseMapper.queryWorderIdList(worderInformation);
    }


    @Override
    public List queryWorderNotAccountList(WorderInformationAccountVO worderInformation){
        worderInformation.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE);
        List list = baseMapper.queryWorderInformationNotAccountList(worderInformation);
        return list;
    }

    @Override
    public PageUtils queryWorderInformationNotAccountList(WorderInformationAccountVO worderInformation){
        if(StringUtils.isEmpty(worderInformation.getCurPage()) || StringUtils.isEmpty(worderInformation.getLimit())){
            worderInformation.setCurPage(1);
            worderInformation.setLimit(10);
        }
        //worderInformation.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE);
        worderInformation.setPage((worderInformation.getCurPage() - 1)*worderInformation.getLimit());
        List list = baseMapper.queryWorderInformationNotAccountList(worderInformation);
//        list.stream().filter(item -> null != item).map(item -> {
//            Map params = new HashMap();
//            Map params2 = (Map) item;
//            params.put("worderNo", params2.get("worderNo"));
//            params.put("worderStatus", Constant.INSTALL);
//            params.put("worderExecStatus", Constant.INSTALL_END);
//            List<OperationRecord> operationRecords = workMsgDao.selectWorderOperateListByMap(params);
//            if (operationRecords.size() > 0 && null != operationRecords.get(0)) {
//                params2.put("worderFinishTime", operationRecords.get(0).getCreateTime());
//            }
//            return params2;
//        }).collect(Collectors.toList());

        return new PageUtils(list,baseMapper.queryWorderInformationNotAccountCount(worderInformation),
                worderInformation.getCurPage(),worderInformation.getLimit());
    }

    /**
     * 获取用户
     *
     * @return
     */
    public SysUserEntity getUser() {
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }

    private void cockpitDataHandleCondition(Map<String, Object> params) {
        Long userId = getUser().getUserId();
        //获取用户角色信息
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("brandIds"))){
            String brandIds = Objects.toString(params.get("brandIds"));
            String[] brandIdArr = brandIds.split(",");
            params.put("brandIds", Arrays.asList(brandIdArr));
        }
        //添加每个客服只能看到自己创建的工单
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))){
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            String [] areas= regionLabelList.split(",");
            List<String> areaId = new ArrayList<>();
            List<String> area = new ArrayList<>();
            for (int i = 0;i<areas.length;i++){
                QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                String id = areas[i];
                query.eq("id",id);
                BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                if (bizRegionEntity.getType()==1){
                    //省
                    area.add(bizRegionEntity.getId().toString());
                }else if (bizRegionEntity.getType()==2){
                    //市
                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("pid",bizRegionEntity.getId());
                    List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                    for (int j = 0; j < bizRegionEntities.size(); j++) {
                        areaId.add(bizRegionEntities.get(j).getId().toString());
                    }
                }else if (bizRegionEntity.getType()==3) {
                    //县
                    areaId.add(bizRegionEntity.getId().toString());
                }
            }

            if (area!=null && area.size()>0){
                params.put("areas",area);
            }
            if (areaId!=null && areaId.size()>0){
                params.put("areaIds",areaId);
            }
        }

        if (UserRoleUtils.isAdmin(list)) {
        } else if (UserRoleUtils.isCustomerService(list) || UserRoleUtils.isCompanyManager(list)) {//客服或者厂商管理员
            params.put("createBy", userId);
        } else if (UserRoleUtils.isPM(list)) {//项目经理
            params.put("pmId", userId);
        } else if (UserRoleUtils.isBranch(list)) {//网点
            params.put("dotId", getDotId(userId));
        } else if (UserRoleUtils.isService(list)) {//服务兵
            params.put("serviceId", userId);
        } else {
            params.put("userId", userId);
        }
    }

    /**
     * 查询网点id
     *
     * @param userId
     * @return
     */
    public Integer getDotId(Long userId) {
        return baseMapper.getDotId(userId);
    }

    @Override
    public PageUtils queryWorderInformationAllFlowOrdersList(Map<String, Object> params) {
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("areaList"))) {
            String regionLabelList = Objects.toString(params.get("areaList"));
            regionLabelList = regionLabelList.replace("[","");
            regionLabelList = regionLabelList.replace("]","");
            if (StringUtils.isNotBlank(regionLabelList)){
                String [] areas= regionLabelList.split(",");
                List<String> areaId = new ArrayList<>();
                List<String> area = new ArrayList<>();
                for (int i = 0; i < areas.length; i++) {
                    QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                    String id = areas[i];
                    query.eq("id", id);
                    BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                    if (bizRegionEntity.getType() == 1) {
                        //省
                        area.add(bizRegionEntity.getId().toString());
                    } else if (bizRegionEntity.getType() == 2) {
                        //市
                        QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("pid", bizRegionEntity.getId());
                        List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                        for (int j = 0; j < bizRegionEntities.size(); j++) {
                            areaId.add(bizRegionEntities.get(j).getId().toString());
                        }
                    } else if (bizRegionEntity.getType() == 3) {
                        //县
                        areaId.add(bizRegionEntity.getId().toString());
                    }
                }
                if (area!=null && area.size()>0){
                    params.put("areas",area);
                }
                if (areaId!=null && areaId.size()>0){
                    params.put("areaIds",areaId);
                }
            }
        }
        Long userId = getUser().getUserId();
        //获取用户角色信息
        List<SysRoleEntity> userList = sysRoleService.getByUserId(userId);
        if (UserRoleUtils.isAdmin(userList)) {

        } else if (UserRoleUtils.isCustomerService(userList) || UserRoleUtils.isCompanyManager(userList)) {//客服或者厂商管理员
            params.put("createBy", userId);
        } else if (UserRoleUtils.isPM(userList)) {//项目经理
            params.put("pmId", userId);
        } else if (UserRoleUtils.isBranch(userList)) {//网点
            params.put("dotId", getDotId(userId));
        } else if (UserRoleUtils.isService(userList)) {//服务兵
            params.put("serviceId", userId);
        } else {
            SysUserEntity user = getUser();
            List<ManagerAreaBrandDto> userAreaBrand = worderInformationDao.getUserAreaBrand(user.getUserId());
            if (!org.springframework.util.CollectionUtils.isEmpty(userAreaBrand)) {
                List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
                List<Integer> brandIds = userAreaBrand.stream().filter(o -> o.getBrandId() != null).map(ManagerAreaBrandDto::getBrandId).collect(Collectors.toList());
                params.put("userId", user.getUserId());
                if (!org.springframework.util.CollectionUtils.isEmpty(areaIds)) {
                    params.put("areaFlag", 1);
                    if (!org.springframework.util.CollectionUtils.isEmpty(brandIds)) {
                        params.put("brandFlag", 1);
                    }
                } else if (!org.springframework.util.CollectionUtils.isEmpty(brandIds)) {
                    params.put("brandFlag", 1);
                }
            }
        }
        IPage<Map> page = baseMapper.queryWorderInformationAllFlowOrdersList(new Query<Map>().getPageNotI(params),params);
        PageUtils pageUtils = new PageUtils(page);
        return pageUtils;
    }


    @Override
    public List queryWorderByCompanyOrderNumbers(WorderInformationAccountVO worderInformation) {
        return baseMapper.queryWorderInformationNotAccountList(worderInformation);
    }


    /**
     * 工单结算推送ACS记账
     * @param v
     * @param invoiceResults
     * @param orderNo
     * @param df
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void pushAcsAccount(CompanyInvoiceEntity v, InvoiceResults invoiceResults, String orderNo, SimpleDateFormat df){
        //更新厂商发票表
        String invoiceCode = invoiceResults.getINVOICECODE();
        //推送ACS记账
        CompanyInformationEntity company = companyInformationService.getByCompanyId(v.getCompanyId());
        String companyNo = company.getCompanyNo();
        String companyName = company.getCompanyName();
        Date date = new Date();
        String ds = df.format(date);
                    /*----------------------  收入记账 start ----------------------*/
        FncCollectionInformation fncCollectionInformation = new FncCollectionInformation();
        fncCollectionInformation.setRowId(orderNo);
        fncCollectionInformation.setOrderNo(orderNo);
        fncCollectionInformation.setPayNo(orderNo);
        fncCollectionInformation.setOrderCount(1);
        fncCollectionInformation.setSource("ZLW");
        fncCollectionInformation.setSourceMode("10");
        fncCollectionInformation.setSourceType("ST_ZLW");
        fncCollectionInformation.setOrderType("Z");
        fncCollectionInformation.setOrderDate(date);
        fncCollectionInformation.setOrderAmount(v.getInvoiceFee());
        fncCollectionInformation.setPayType("电汇");
        fncCollectionInformation.setYwms("C18");
        fncCollectionInformation.setJylx("6");
        fncCollectionInformation.setBukrs(balanceProperties.RRS_COMPANY_CODE);
        fncCollectionInformation.setBudat(ds);
        fncCollectionInformation.setBktxt(companyName+"收入");
        fncCollectionInformation.setXblnr(invoiceCode);
        fncCollectionInformation.setWaers("CNY");
        fncCollectionInformation.setKunnr(companyNo);
        fncCollectionInformation.setIfOnce("N");
        fncCollectionInformation.setDmbtr(v.getInvoiceFee());
        fncCollectionInformation.setDmbtr2(v.getTax());
        fncCollectionInformation.setXref3(orderNo);
        fncCollectionInformation.setSgtxt("ST_ZLW+"+companyNo+"+"+orderNo);
        //推送记录
        List<BalanceAcsPushRecordEntity> recordList = new ArrayList<>();
        BalanceAcsPushRecordEntity record = new BalanceAcsPushRecordEntity();
        BeanUtils.copyProperties(fncCollectionInformation, record);
        if(CollectionUtils.isNotEmpty(fncCollectionInformation.getOdsAccountingInfoDetailList())) {
            record.setOdsAccountingInfoDetail(JSON.toJSONString(fncCollectionInformation.getOdsAccountingInfoDetailList()));
        }
        recordList.add(record);
                    /*----------------------  收入记账 end ----------------------*/
        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<FncCollectionInformation>();
        fncCollectionInformationList.add(fncCollectionInformation);
                    /*----------------------  暂记成本记账 start ----------------------*/
        //暂记成本-工单
        List<Map> worderList = baseMapper.getWorderAndDotByInvoiceId(v.getId());
        List<Integer> worderIdList = new ArrayList<>();
        for (Map map : worderList) {
            Integer worderId = getIntegerValue(map, "worder_id");
            worderIdList.add(worderId);
        }
        List<WorderPmStimulateEntity> stimulateList = new ArrayList<>();
        List<CostInformationEntity> entityList = new ArrayList<>();
        int orderCount = worderList.size();
        int i = 0;
        int j = orderCount;
        for (Map map : worderList) {
            String index = (++i)+"";
            while (index.length()<3){
                index = "0"+index;
            }
            String xref3 = orderNo+"-"+index;
            Integer worderId = getIntegerValue(map, "worder_id");
            String worderName = getStringValue(map, "worder_name");
            String customerCode = getStringValue(map, "customer_code");
            String dotVCode = getStringValue(map, "v_code");
            Integer dotId = getIntegerValue(map, "dot_id");
            String dotName = getStringValue(map, "dot_name");
            String dotCity = getDotCity(map);
            BigDecimal amount = new BigDecimal(getStringValue(map, "dot_balance_fee_sum"));
            BigDecimal tax = new BigDecimal(getStringValue(map, "dot_balance_fee_tax"));
            String rowId = rowId(worderId, 0);
            FncCollectionInformation fncInformation = new FncCollectionInformation();
            fncInformation.setRowId(rowId);
            fncInformation.setOrderNo(orderNo);
            fncInformation.setPayNo(orderNo);
            fncInformation.setOrderCount(orderCount);
            fncInformation.setSource("ZLW");
            fncInformation.setSourceMode("10");
            fncInformation.setSourceType("ST_ZLW");
            fncInformation.setOrderType("Z");
            fncInformation.setOrderDate(date);
            fncInformation.setOrderAmount(amount);
            fncInformation.setPayType("电汇");
            fncInformation.setYwms("C15");
            fncInformation.setJylx("6");
            fncInformation.setBukrs(balanceProperties.RRS_COMPANY_CODE);
            fncInformation.setBudat(ds);
            fncInformation.setBktxt(worderName+"工单成本");
//            fncInformation.setXblnr(invoiceCode);
            fncInformation.setWaers("CNY");
            fncInformation.setKunnr(companyNo);
            fncInformation.setLifnr(dotVCode);
            fncInformation.setIfOnce("N");
            fncInformation.setName(dotName);
            fncInformation.setCity(dotCity);
            fncInformation.setDmbtr(amount);
            fncInformation.setDmbtr1(BigDecimal.ZERO);
            fncInformation.setDmbtr2(BigDecimal.ZERO);
//            fncInformation.setDmbtr3(BigDecimal.ZERO);
            fncInformation.setXref3(xref3);
            fncInformation.setSgtxt("ST_ZLW+"+companyNo+"+"+xref3+"+"+dotVCode);
            fncCollectionInformationList.add(fncInformation);
            //推送记录
            BalanceAcsPushRecordEntity record2 = new BalanceAcsPushRecordEntity();
            BeanUtils.copyProperties(fncInformation, record2);
            if(CollectionUtils.isNotEmpty(fncInformation.getOdsAccountingInfoDetailList())) {
                record2.setOdsAccountingInfoDetail(JSON.toJSONString(fncInformation.getOdsAccountingInfoDetailList()));
            }
            recordList.add(record2);
            //成本信息记录
            CostInformationEntity entity = new CostInformationEntity();
            BeanUtils.copyProperties(fncInformation, entity);
            entity.setDotId(dotId);
            entity.setWorderId(worderId);
            entityList.add(entity);

            //关联的网点激励
            List<WorderPmStimulateEntity> stimulateList1 = worderPmStimulateService.list(
                    new QueryWrapper<WorderPmStimulateEntity>()
                            .eq("stimulate_type", 10)
                            .eq("status", 19)
                            .eq("is_delete", 0)
                            .eq("worder_id", worderId));
            if(CollectionUtils.isNotEmpty(stimulateList1)){
                for (WorderPmStimulateEntity worderPmStimulateEntity : stimulateList1) {
                    String index2 = (++j)+"";
                    while (index2.length()<3){
                        index2 = "0"+index2;
                    }
                    Integer stimulateId = worderPmStimulateEntity.getId();
                    String rowId2 = rowId(stimulateId, 2);
                    String xref32 = orderNo+"-"+index2;
                    String sgtxt2 = "ST_ZLW+" + companyNo + "+" + xref32 + "+" + dotVCode;
                    /*----------------------  激励记账请求参数 start ----------------------*/
                    FncCollectionInformation fncInformation2 = new FncCollectionInformation();
                    fncInformation2.setRowId(rowId2);
                    fncInformation2.setOrderNo(orderNo);
                    fncInformation2.setPayNo(orderNo);
                    fncInformation2.setOrderCount(1);
                    fncInformation2.setSource("ZLW");
                    fncInformation2.setSourceMode("10");
                    fncInformation2.setSourceType("ST_ZLW");
                    fncInformation2.setOrderType("Z");
                    fncInformation2.setOrderDate(date);
                    fncInformation2.setPayType("电汇");
                    fncInformation2.setJylx("6");
                    fncInformation2.setBukrs(balanceProperties.RRS_COMPANY_CODE);
                    fncInformation2.setBudat(ds);
//                    fncInformation2.setXblnr(invoiceCode);
                    fncInformation2.setWaers("CNY");
                    fncInformation2.setKunnr(customerCode);
                    fncInformation2.setLifnr(dotVCode);
                    fncInformation2.setIfOnce("N");
                    fncInformation2.setName(dotName);
                    fncInformation2.setCity(dotCity);
                    fncInformation2.setDmbtr1(BigDecimal.ZERO);
                    fncInformation2.setDmbtr2(BigDecimal.ZERO);
//                   fncInformation.setDmbtr3(BigDecimal.ZERO);
                    fncInformation2.setXref3(xref32);
                    fncInformation2.setSgtxt(sgtxt2);

                    BigDecimal amount2 = worderPmStimulateEntity.getPriceTax();
                    if(amount2.compareTo(BigDecimal.ZERO)>=0) {
                        fncInformation2.setYwms("C86");
                        fncInformation2.setBktxt("桩联网正激励-"+stimulateId);
                        fncInformation2.setOrderAmount(amount2);
                        fncInformation2.setDmbtr(amount2);
                    }else {
                        amount2 = amount2.negate();
                        fncInformation2.setYwms("C27");
                        fncInformation2.setBktxt("桩联网负激励-"+stimulateId);
                        fncInformation2.setOrderAmount(amount2);
                        fncInformation2.setDmbtr(amount2);
                    }

                    fncCollectionInformationList.add(fncInformation2);
                    /*----------------------  激励记账请求参数 end ----------------------*/
                    //推送记录
                    BalanceAcsPushRecordEntity record3 = new BalanceAcsPushRecordEntity();
                    BeanUtils.copyProperties(fncInformation2, record3);
                    if(CollectionUtils.isNotEmpty(fncInformation2.getOdsAccountingInfoDetailList())) {
                        record3.setOdsAccountingInfoDetail(JSON.toJSONString(fncInformation2.getOdsAccountingInfoDetailList()));
                    }
                    recordList.add(record3);
                    //记录成本信息
                    CostInformationEntity cost = new CostInformationEntity();
                    BeanUtils.copyProperties(fncInformation2, cost);
                    cost.setDotId(dotId);
                    cost.setStimulateId(worderPmStimulateEntity.getId());
                    entityList.add(cost);
                    //激励状态更新
                    worderPmStimulateEntity.setStatus(21);
                    stimulateList.add(worderPmStimulateEntity);
                }
            }
        }
        /*----------------------  暂记成本记账 end ----------------------*/
        try {
            //车企发票信息更新
            v.setInvoiceCode(invoiceResults.getINVOICECODE());
            v.setRn(invoiceResults.getRN());
            v.setFlag(invoiceResults.getFLAG());
            v.setKprq(invoiceResults.getKPRQ());
            v.setNotaxamount(invoiceResults.getNOTAXAMOUNT());
            v.setTaxamount(invoiceResults.getTAXAMOUNT());
            v.setTotalamount(invoiceResults.getTOTALAMOUNT());
            v.setDrawer(invoiceResults.getDRAWER());
            v.setDmgs(invoiceResults.getDMGS());
            v.setStatus(6);
            companyInvoiceService.updateById(v);
            if(CollectionUtils.isNotEmpty(stimulateList)){
                worderPmStimulateService.updateBatchById(stimulateList);
            }
            balanceAcsPushRecordService.saveBatch(recordList);
            costInformationService.saveOrUpdateBathchByRowId(entityList);
            List<FncCollectionInformation> reList = acsInterfaceUtil.acsAccount(fncCollectionInformationList);
        } catch (Exception e){
//            log.error("推送ACS记账数据失败，发票ID:"+orderNo+"\n", e);
            e.printStackTrace();
            throw new RRException("推送ACS记账数据失败，发票ID:"+orderNo+"\n",e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void pushAcsAccount(CompanyInvoiceEntity v, List<InvoiceResults> invoiceResults, String orderNo, SimpleDateFormat df){
        //更新厂商发票表
        String invoiceCode = invoiceResults.get(IntegerEnum.ZERO.getValue()).getINVOICECODE();
        if(invoiceResults.size() > IntegerEnum.ONE.getValue()){
            invoiceCode = invoiceCode + "~";
        }
        //推送ACS记账
        CompanyInformationEntity company = companyInformationService.getByCompanyId(v.getCompanyId());
        String companyNo = company.getCompanyNo();
        String companyName = company.getCompanyName();
        Date date = new Date();
        String ds = df.format(date);
        /*----------------------  收入记账 start ----------------------*/
        FncCollectionInformation fncCollectionInformation = new FncCollectionInformation();
        fncCollectionInformation.setRowId(orderNo);
        fncCollectionInformation.setOrderNo(orderNo);
        fncCollectionInformation.setPayNo(orderNo);
        fncCollectionInformation.setOrderCount(1);
        fncCollectionInformation.setSource("ZLW");
        fncCollectionInformation.setSourceMode("10");
        fncCollectionInformation.setSourceType("ST_ZLW");
        fncCollectionInformation.setOrderType("Z");
        fncCollectionInformation.setOrderDate(date);
        fncCollectionInformation.setOrderAmount(v.getInvoiceFee());
        fncCollectionInformation.setPayType("电汇");
        fncCollectionInformation.setYwms("C18");
        fncCollectionInformation.setJylx("6");
        fncCollectionInformation.setBukrs(balanceProperties.RRS_COMPANY_CODE);
        fncCollectionInformation.setBudat(ds);
        fncCollectionInformation.setBktxt(companyName+"收入");
        fncCollectionInformation.setXblnr(invoiceCode);
        fncCollectionInformation.setWaers("CNY");
        fncCollectionInformation.setKunnr(companyNo);
        fncCollectionInformation.setIfOnce("N");
        fncCollectionInformation.setDmbtr(v.getInvoiceFee());
        fncCollectionInformation.setDmbtr2(v.getTax());
        fncCollectionInformation.setXref3(orderNo);
        fncCollectionInformation.setSgtxt("ST_ZLW+"+companyNo+"+"+orderNo);
        //推送记录
        List<BalanceAcsPushRecordEntity> recordList = new ArrayList<>();
        BalanceAcsPushRecordEntity record = new BalanceAcsPushRecordEntity();
        BeanUtils.copyProperties(fncCollectionInformation, record);
        if(CollectionUtils.isNotEmpty(fncCollectionInformation.getOdsAccountingInfoDetailList())) {
            record.setOdsAccountingInfoDetail(JSON.toJSONString(fncCollectionInformation.getOdsAccountingInfoDetailList()));
        }
        recordList.add(record);
        /*----------------------  收入记账 end ----------------------*/
        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<FncCollectionInformation>();
        fncCollectionInformationList.add(fncCollectionInformation);
        /*----------------------  暂记成本记账 start ----------------------*/
        //暂记成本-工单
        List<Map> worderList = baseMapper.getWorderAndDotByInvoiceId(v.getId());
        List<Integer> worderIdList = new ArrayList<>();
        for (Map map : worderList) {
            Integer worderId = getIntegerValue(map, "worder_id");
            worderIdList.add(worderId);
        }
        List<WorderPmStimulateEntity> stimulateList = new ArrayList<>();
        List<CostInformationEntity> entityList = new ArrayList<>();
        int orderCount = worderList.size();
        int i = 0;
        int j = orderCount;
        for (Map map : worderList) {
            String index = (++i)+"";
            while (index.length()<3){
                index = "0"+index;
            }
            String xref3 = orderNo+"-"+index;
            Integer worderId = getIntegerValue(map, "worder_id");
            String worderName = getStringValue(map, "worder_name");
            String customerCode = getStringValue(map, "customer_code");
            String dotVCode = getStringValue(map, "v_code");
            Integer dotId = getIntegerValue(map, "dot_id");
            String dotName = getStringValue(map, "dot_name");
            String dotCity = getDotCity(map);
            BigDecimal amount = new BigDecimal(getStringValue(map, "dot_balance_fee_sum"));
            BigDecimal tax = new BigDecimal(getStringValue(map, "dot_balance_fee_tax"));
            String rowId = rowId(worderId, 0);
            FncCollectionInformation fncInformation = new FncCollectionInformation();
            fncInformation.setRowId(rowId);
            fncInformation.setOrderNo(orderNo);
            fncInformation.setPayNo(orderNo);
            fncInformation.setOrderCount(orderCount);
            fncInformation.setSource("ZLW");
            fncInformation.setSourceMode("10");
            fncInformation.setSourceType("ST_ZLW");
            fncInformation.setOrderType("Z");
            fncInformation.setOrderDate(date);
            fncInformation.setOrderAmount(amount);
            fncInformation.setPayType("电汇");
            fncInformation.setYwms("C15");
            fncInformation.setJylx("6");
            fncInformation.setBukrs(balanceProperties.RRS_COMPANY_CODE);
            fncInformation.setBudat(ds);
            fncInformation.setBktxt(worderName+"工单成本");
//            fncInformation.setXblnr(invoiceCode);
            fncInformation.setWaers("CNY");
            fncInformation.setKunnr(companyNo);
            fncInformation.setLifnr(dotVCode);
            fncInformation.setIfOnce("N");
            fncInformation.setName(dotName);
            fncInformation.setCity(dotCity);
            fncInformation.setDmbtr(amount);
            fncInformation.setDmbtr1(BigDecimal.ZERO);
            fncInformation.setDmbtr2(BigDecimal.ZERO);
//            fncInformation.setDmbtr3(BigDecimal.ZERO);
            fncInformation.setXref3(xref3);
            fncInformation.setSgtxt("ST_ZLW+"+companyNo+"+"+xref3+"+"+dotVCode);
            fncCollectionInformationList.add(fncInformation);
            //推送记录
            BalanceAcsPushRecordEntity record2 = new BalanceAcsPushRecordEntity();
            BeanUtils.copyProperties(fncInformation, record2);
            if(CollectionUtils.isNotEmpty(fncInformation.getOdsAccountingInfoDetailList())) {
                record2.setOdsAccountingInfoDetail(JSON.toJSONString(fncInformation.getOdsAccountingInfoDetailList()));
            }
            recordList.add(record2);
            //成本信息记录
            CostInformationEntity entity = new CostInformationEntity();
            BeanUtils.copyProperties(fncInformation, entity);
            entity.setDotId(dotId);
            entity.setWorderId(worderId);
            entityList.add(entity);

            //关联的网点激励
            List<WorderPmStimulateEntity> stimulateList1 = worderPmStimulateService.list(
                    new QueryWrapper<WorderPmStimulateEntity>()
                            .eq("stimulate_type", 10)
                            .eq("status", 19)
                            .eq("is_delete", 0)
                            .eq("worder_id", worderId));
            if(CollectionUtils.isNotEmpty(stimulateList1)){
                for (WorderPmStimulateEntity worderPmStimulateEntity : stimulateList1) {
                    String index2 = (++j)+"";
                    while (index2.length()<3){
                        index2 = "0"+index2;
                    }
                    Integer stimulateId = worderPmStimulateEntity.getId();
                    String rowId2 = rowId(stimulateId, 2);
                    String xref32 = orderNo+"-"+index2;
                    String sgtxt2 = "ST_ZLW+" + companyNo + "+" + xref32 + "+" + dotVCode;
                    /*----------------------  激励记账请求参数 start ----------------------*/
                    FncCollectionInformation fncInformation2 = new FncCollectionInformation();
                    fncInformation2.setRowId(rowId2);
                    fncInformation2.setOrderNo(orderNo);
                    fncInformation2.setPayNo(orderNo);
                    fncInformation2.setOrderCount(1);
                    fncInformation2.setSource("ZLW");
                    fncInformation2.setSourceMode("10");
                    fncInformation2.setSourceType("ST_ZLW");
                    fncInformation2.setOrderType("Z");
                    fncInformation2.setOrderDate(date);
                    fncInformation2.setPayType("电汇");
                    fncInformation2.setJylx("6");
                    fncInformation2.setBukrs(balanceProperties.RRS_COMPANY_CODE);
                    fncInformation2.setBudat(ds);
//                    fncInformation2.setXblnr(invoiceCode);
                    fncInformation2.setWaers("CNY");
                    fncInformation2.setKunnr(customerCode);
                    fncInformation2.setLifnr(dotVCode);
                    fncInformation2.setIfOnce("N");
                    fncInformation2.setName(dotName);
                    fncInformation2.setCity(dotCity);
                    fncInformation2.setDmbtr1(BigDecimal.ZERO);
                    fncInformation2.setDmbtr2(BigDecimal.ZERO);
//                   fncInformation.setDmbtr3(BigDecimal.ZERO);
                    fncInformation2.setXref3(xref32);
                    fncInformation2.setSgtxt(sgtxt2);

                    BigDecimal amount2 = worderPmStimulateEntity.getPriceTax();
                    if(amount2.compareTo(BigDecimal.ZERO)>=0) {
                        fncInformation2.setYwms("C86");
                        fncInformation2.setBktxt("桩联网正激励-"+stimulateId);
                        fncInformation2.setOrderAmount(amount2);
                        fncInformation2.setDmbtr(amount2);
                    }else {
                        amount2 = amount2.negate();
                        fncInformation2.setYwms("C27");
                        fncInformation2.setBktxt("桩联网负激励-"+stimulateId);
                        fncInformation2.setOrderAmount(amount2);
                        fncInformation2.setDmbtr(amount2);
                    }

                    fncCollectionInformationList.add(fncInformation2);
                    /*----------------------  激励记账请求参数 end ----------------------*/
                    //推送记录
                    BalanceAcsPushRecordEntity record3 = new BalanceAcsPushRecordEntity();
                    BeanUtils.copyProperties(fncInformation2, record3);
                    if(CollectionUtils.isNotEmpty(fncInformation2.getOdsAccountingInfoDetailList())) {
                        record3.setOdsAccountingInfoDetail(JSON.toJSONString(fncInformation2.getOdsAccountingInfoDetailList()));
                    }
                    recordList.add(record3);
                    //记录成本信息
                    CostInformationEntity cost = new CostInformationEntity();
                    BeanUtils.copyProperties(fncInformation2, cost);
                    cost.setDotId(dotId);
                    cost.setStimulateId(worderPmStimulateEntity.getId());
                    entityList.add(cost);
                    //激励状态更新
                    worderPmStimulateEntity.setStatus(21);
                    stimulateList.add(worderPmStimulateEntity);
                }
            }
        }
        /*----------------------  暂记成本记账 end ----------------------*/
        try {
            // 保存开票结果
            List<CompanyInvoiceQueryResultEntity> companyInvoiceQueryResultEntities = new ArrayList<>();
            invoiceResults.forEach(item -> {
                CompanyInvoiceQueryResultEntity companyInvoiceQueryResultEntity = new CompanyInvoiceQueryResultEntity();
                companyInvoiceQueryResultEntity.setInvoiceCode(item.getINVOICECODE());
                companyInvoiceQueryResultEntity.setRn(item.getRN());
                companyInvoiceQueryResultEntity.setFlag(item.getFLAG());
                companyInvoiceQueryResultEntity.setKprq(item.getKPRQ());
                companyInvoiceQueryResultEntity.setNotaxamount(item.getNOTAXAMOUNT());
                companyInvoiceQueryResultEntity.setTaxamount(item.getTAXAMOUNT());
                companyInvoiceQueryResultEntity.setTotalamount(item.getTOTALAMOUNT());
                companyInvoiceQueryResultEntity.setDrawer(item.getDRAWER());
                companyInvoiceQueryResultEntity.setDmgs(item.getDMGS());
                companyInvoiceQueryResultEntity.setCompanyInvoiceNo(v.getCompanyInvoiceNo());
                companyInvoiceQueryResultEntities.add(companyInvoiceQueryResultEntity);
            });
            companyInvoiceQueryResultService.saveBatch(companyInvoiceQueryResultEntities);

            // 更新开票状态
            CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
            companyInvoiceEntity.setStatus(6);
            companyInvoiceEntity.setId(v.getId());
            companyInvoiceService.updateById(companyInvoiceEntity);

            if(CollectionUtils.isNotEmpty(stimulateList)){
                worderPmStimulateService.updateBatchById(stimulateList);
            }
            balanceAcsPushRecordService.saveBatch(recordList);
            costInformationService.saveOrUpdateBathchByRowId(entityList);
            log.info("总共需要推送" + fncCollectionInformationList.size() + "条");
            for (int k = 0, len = fncCollectionInformationList.size(); k < len; k = k + 199) {
                List<FncCollectionInformation> lst = new ArrayList();
                if(k + 199 > len){
                    lst = fncCollectionInformationList.subList(k, len);
                }else{
                    lst = fncCollectionInformationList.subList(k, k + 199);
                }
                log.info("推送" + lst.size() + "条");
                acsInterfaceUtil.acsAccount(lst);
            }
        } catch (Exception e){
//            log.error("推送ACS记账数据失败，发票ID:"+orderNo+"\n", e);
            e.printStackTrace();
            throw new RRException("推送ACS记账数据失败，发票ID:"+orderNo+"\n",e);
        }
    }

    @Override
    public void pushComapanyBalanceAcsAccount(CompanyInvoiceEntity companyInvoice, List<InvoiceResults> invoiceResults,
                                              String invoiceOrderNo, SimpleDateFormat df, Map<Integer,List<WorderChildInformationEntity>> childOrderMap) {
        Integer invoiceId = companyInvoice.getId();
        List<Integer> invoiceIdList = new ArrayList<>();
        invoiceIdList.add(invoiceId);
        List<BalanceEnterprisesDetailRecordEntity> balanceEnterprisesDetailRecords = balanceEnterprisesDetailRecordService.listSumByInvoiceId(invoiceIdList);
        BalanceEnterprisesDetailRecordEntity balanceEnterprisesDetailRecord = balanceEnterprisesDetailRecords.get(IntegerEnum.ZERO.getValue());
        BigDecimal invoiceTaxFeeSum = balanceEnterprisesDetailRecord.getInvoiceTaxFeeSum();
        BigDecimal invoiceTaxSum = balanceEnterprisesDetailRecord.getInvoiceTaxSum();

        //更新厂商发票表
        String invoiceCode = invoiceResults.get(IntegerEnum.ZERO.getValue()).getINVOICECODE();
        if(invoiceResults.size() > IntegerEnum.ONE.getValue()){
            invoiceCode = invoiceCode + "~";
        }
        //推送ACS记账
        CompanyInformationEntity company = companyInformationService.getByCompanyId(companyInvoice.getCompanyId());
        String companyNo = company.getCompanyNo();
        String companyName = company.getCompanyName();
        String companyEightCode = company.getCompanyCode();
        Date date = new Date();
        String ds = df.format(date);
        /*----------------------  收入记账 start ----------------------*/
        FncCollectionInformation income = new FncCollectionInformation();
        income.setRowId(invoiceOrderNo);
        income.setOrderNo(invoiceOrderNo);
        income.setPayNo(invoiceOrderNo);
        income.setOrderCount(1);
        income.setSource("ZLW");
        income.setSourceMode("10");
        income.setSourceType("ST_ZLW");
        income.setOrderType("Z");
        income.setOrderDate(date);
        income.setOrderAmount(invoiceTaxFeeSum);
        income.setPayType("发票");
        income.setYwms("C18");
        income.setJylx("6");
        income.setBukrs(balanceProperties.RRS_COMPANY_CODE);
        income.setBudat(ds);
        income.setBktxt(companyName+"收入");
        income.setXblnr(invoiceCode);
        income.setWaers("CNY");
        income.setKunnr(companyNo);
        income.setIfOnce("N");
        income.setDmbtr(invoiceTaxFeeSum);
        income.setDmbtr2(invoiceTaxSum);
        income.setXref3(invoiceOrderNo);
        income.setSgtxt("ST_ZLW+"+companyNo+"+"+invoiceOrderNo);
        //推送记录
        List<BalanceAcsPushRecordEntity> recordList = new ArrayList<>();
        BalanceAcsPushRecordEntity record = new BalanceAcsPushRecordEntity();
        BeanUtils.copyProperties(income, record);
        if(CollectionUtils.isNotEmpty(income.getOdsAccountingInfoDetailList())) {
            record.setOdsAccountingInfoDetail(JSON.toJSONString(income.getOdsAccountingInfoDetailList()));
        }
        recordList.add(record);
        /*----------------------  收入记账 end ----------------------*/
        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<>();
        fncCollectionInformationList.add(income);
        /*----------------------  暂记成本记账 start ----------------------*/
        //暂记成本-工单
        List<Map> worderList = baseMapper.listCompanyInvoiceByInvoiceId(companyInvoice.getId());
        //统计结算单的数量
        Collection<List<WorderChildInformationEntity>> values = childOrderMap.values();
        int orderCount = values.stream().map(items -> items.stream()
                .filter(item -> item.getDotBalanceFeeSum().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList())
        ).collect(Collectors.toList()).stream().mapToInt(List::size).sum();

        List<CostInformationEntity> entityList = new ArrayList<>();

        AtomicInteger i = new AtomicInteger();
        for (Map map : worderList) {
            Integer worderInvoiceType = getIntegerValue(map, "worder_invoice_type");
            // 激励
            if(worderInvoiceType.intValue() == IntegerEnum.ONE.getValue()){
                continue;
            }
            Integer worderId = getIntegerValue(map, "worder_id");
            String worderName = getStringValue(map, "worder_name");
            Integer dotId = getIntegerValue(map, "dot_id");
            String dotName = getStringValue(map, "dot_name");
            String dotVCode = getStringValue(map, "v_code");
            String dotCity = getDotCity(map);
            //BigDecimal amount = new BigDecimal(getStringValue(map, "dot_balance_fee_sum"));

            //获取工单的结算子订单
            List<WorderChildInformationEntity> worderChildInformationEntities = childOrderMap.get(worderId);
            worderChildInformationEntities.forEach(worderChildInformationEntity -> {
                //如果成本金额为0，不推送
                if(worderChildInformationEntity.getDotBalanceFeeSum().compareTo(BigDecimal.ZERO) <= 0){
                    return;
                }

                String xref3 = invoiceOrderNo + "-" + (i.incrementAndGet());
                String rowId = rowId(worderChildInformationEntity.getId(), 0);

                FncCollectionInformation fncInformation = new FncCollectionInformation();
                fncInformation.setRowId(rowId);
                fncInformation.setOrderNo(invoiceOrderNo);
                fncInformation.setPayNo(invoiceOrderNo);
                fncInformation.setOrderCount(orderCount);
                fncInformation.setSource("ZLW");
                fncInformation.setSourceMode("10");
                fncInformation.setSourceType("ST_ZLW");
                fncInformation.setOrderType("Z");
                fncInformation.setOrderDate(date);
                fncInformation.setOrderAmount(worderChildInformationEntity.getDotBalanceFeeSum());
                fncInformation.setPayType("电汇");
                fncInformation.setYwms("C15");
                fncInformation.setJylx("6");
                fncInformation.setBukrs(balanceProperties.RRS_COMPANY_CODE);
                fncInformation.setBudat(ds);
                fncInformation.setBktxt(worderName + "工单成本");
                fncInformation.setWaers("CNY");
                fncInformation.setKunnr(companyNo);
                fncInformation.setLifnr(dotVCode);
                fncInformation.setIfOnce("N");
                fncInformation.setName(dotName);
                fncInformation.setCity(dotCity);
                fncInformation.setDmbtr(worderChildInformationEntity.getDotBalanceFee());
                fncInformation.setDmbtr1(BigDecimal.ZERO);
                fncInformation.setDmbtr2(BigDecimal.ZERO);
                fncInformation.setXref3(xref3);
                fncInformation.setSgtxt("ST_ZLW+" + companyNo + "+" + xref3 + "+" + dotVCode);
                fncCollectionInformationList.add(fncInformation);

                //推送记录
                BalanceAcsPushRecordEntity record2 = new BalanceAcsPushRecordEntity();
                BeanUtils.copyProperties(fncInformation, record2);
                if (CollectionUtils.isNotEmpty(fncInformation.getOdsAccountingInfoDetailList())) {
                    record2.setOdsAccountingInfoDetail(JSON.toJSONString(fncInformation.getOdsAccountingInfoDetailList()));
                }
                recordList.add(record2);
                //成本信息记录
                CostInformationEntity entity = new CostInformationEntity();
                BeanUtils.copyProperties(fncInformation, entity);
                entity.setDotId(dotId);
                entity.setWorderId(worderId);
                entity.setBalanceId(worderChildInformationEntity.getId());
                entityList.add(entity);
            });

        }
        //保存推送信息
        balanceAcsPushRecordService.saveBatch(recordList);
        //保存成本信息
        costInformationService.saveOrUpdateBathchByRowId(entityList);
        /*----------------------  暂记成本记账 end ----------------------*/
        try {
            /** 推送ACS记账 **/
            log.info("总共需要推送" + fncCollectionInformationList.size() + "条");
            for (int k = 0, len = fncCollectionInformationList.size(); k < len; k = k + 199) {
                List<FncCollectionInformation> lst;
                if(k + 199 > len){
                    lst = fncCollectionInformationList.subList(k, len);
                }else{
                    lst = fncCollectionInformationList.subList(k, k + 199);
                }
                log.info("推送" + lst.size() + "条");
                acsInterfaceUtil.acsAccount(lst);
            }
        } catch (Exception e){
            log.error("推送ACS记账数据失败，发票ID:"+invoiceOrderNo+"\n", e);
            e.printStackTrace();
            throw new RRException("推送ACS记账数据失败，发票ID:"+invoiceOrderNo+"\n",e);
        }
    }

    /**
     * 增项结算推送ACS记账收款
     * @param worderId
     */
    @Override
    @Transactional
    public void pushAcsAccountIncreReceivables(Integer worderId){
        //计算并保存网点增项结算金额
        try{
            branchBalanceService.caculateOneDotIncreBalanceFee(worderId);
        }catch (Exception e){
            e.printStackTrace();
        }
        //查询开票信息（支付方式、支付流水单号、城市、发票代码、客户名、客户手机号）
        Map<String, Object> invoiceMap = baseMapper.queryUserInvoice(worderId);
        if(null == invoiceMap){
            SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            //todo  需要
            invoiceAPIService.generateBillingRecord(worderId, user.getUserId(), true);
            invoiceMap = baseMapper.queryUserInvoice(worderId);
        }
        //支付方式（1：支付宝 2：微信 ）
        Integer payTypeCode = getIntegerValue(invoiceMap, "pay_type_code");
        String jylx = payTypeCode+"";
        //支付方式（1：支付宝 2：微信 ）
        String payType = getStringValue(invoiceMap, "pay_type");
        //支付流水单号
        String payNo = getStringValue(invoiceMap, "transaction_id");
        //城市
        String city = getStringValue(invoiceMap, "area_name");
        //发票代码
        String invoiceCode = getStringValue(invoiceMap, "invoice_code");
        //客户名
        String username = getStringValue(invoiceMap, "user_name");
        //客户手机号
        String tel = getStringValue(invoiceMap, "user_phone");
        //网点ID
        Integer dotId = getIntegerValue(invoiceMap, "dot_id");
        //网点V码
        String vcode = getStringValue(invoiceMap, "v_code");
        //发票金额
        BigDecimal amount = new BigDecimal(getStringValue(invoiceMap, "order_items_amount"));
        //税率
        BigDecimal taxRate = new BigDecimal(getStringValue(invoiceMap, "order_items_tax_rate"));
        //取舍方式
        int roundMode = balanceProperties.ROUND_MODE;
        //计算税金
        BigDecimal tax = amount.multiply(taxRate).setScale(2, roundMode);
        //手续费率
        BigDecimal commissionRate = this.getCommissionRate(payTypeCode);
        //计算手续费
        BigDecimal commission = amount.multiply(commissionRate).setScale(2, roundMode);
        //计算提现金额
        BigDecimal cashout = amount.subtract(commission);
        Date date = new Date();
        String ds = new SimpleDateFormat("yyyy-MM-dd").format(date);
        String lifnr = null;
        switch (payTypeCode.intValue()){
            case 1: lifnr = "V9011291"; break;
            case 2: lifnr = "V9013147"; break;
            default: ;
        }
        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<>();
        /*---------------------- 收款 start ----------------------*/
        String rowId1 = rowId(worderId, 3);
        String orderNo = increOrderNo(worderId);
//        String orderNo = "ST_ZZSA" + rowId1;
        String sourceType = "ST_ZZSA";
        String kunnr = "A8B";
        String sgtxt = sourceType+"+"+rowId1+"+"+kunnr+"+"+vcode;
        FncCollectionInformation fncCollectionInformation1 = new FncCollectionInformation();
        fncCollectionInformation1.setRowId(rowId1);
        fncCollectionInformation1.setOrderNo(orderNo);
        fncCollectionInformation1.setPayNo(payNo);
        fncCollectionInformation1.setOrderCount(1);
        fncCollectionInformation1.setSource("ZLW");
        fncCollectionInformation1.setSourceMode("10");
        fncCollectionInformation1.setSourceType(sourceType);
        fncCollectionInformation1.setOrderType("Z");
        fncCollectionInformation1.setOrderDate(date);
        fncCollectionInformation1.setOrderAmount(amount);
        fncCollectionInformation1.setPayType(payType);
        fncCollectionInformation1.setYwms("C16");
        fncCollectionInformation1.setJylx(jylx);
        fncCollectionInformation1.setBukrs(balanceProperties.RRS_COMPANY_CODE);
        fncCollectionInformation1.setBudat(ds);
        fncCollectionInformation1.setBktxt("桩联网收款");
//        fncCollectionInformation1.setXblnr(invoiceCode);
        fncCollectionInformation1.setWaers("CNY");
        fncCollectionInformation1.setKunnr(kunnr);
        fncCollectionInformation1.setIfOnce("Y");
        fncCollectionInformation1.setName(tel);
        fncCollectionInformation1.setCity(city);
        fncCollectionInformation1.setDmbtr(amount);
        fncCollectionInformation1.setDmbtr1(commission);
        fncCollectionInformation1.setDmbtr3(cashout);
        fncCollectionInformation1.setAttribute2(balanceProperties.RRS_COMPANY_ACCOUNT_MAP.get(jylx));
        fncCollectionInformation1.setXref3(orderNo);
        fncCollectionInformation1.setSgtxt(sgtxt);
        fncCollectionInformation1.setLifnr(lifnr);
        fncCollectionInformationList.add(fncCollectionInformation1);
        /*----------------------  收款 end ----------------------*/
        try {
            //推送记录
            BalanceAcsPushRecordEntity record1 = new BalanceAcsPushRecordEntity();
            BeanUtils.copyProperties(fncCollectionInformation1, record1);
            if(CollectionUtils.isNotEmpty(fncCollectionInformation1.getOdsAccountingInfoDetailList())) {
                record1.setOdsAccountingInfoDetail(JSON.toJSONString(fncCollectionInformation1.getOdsAccountingInfoDetailList()));
            }
            balanceAcsPushRecordService.save(record1);
            //更新工单增项结算状态
            this.update(
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .eq("worder_id", worderId)
                            .set("worder_incre_status",9)
                            .set("worder_incre_status_value","收款记账中"));
            //推送记账数据
            List<FncCollectionInformation> reList = acsInterfaceUtil.acsAccount(fncCollectionInformationList);
            if(CollectionUtils.isEmpty(reList)){
                throw new RRException("推送ACS记账收款数据失败，orderNo:"+orderNo);
            }
        } catch (Exception e){
//            log.error("推送ACS记账数据失败，发票ID:"+orderNo+"\n", e);
            e.printStackTrace();
            throw new RRException("推送ACS记账收款数据失败，orderNo:"+orderNo,e);
        }
    }


    /**
     * 增项结算推送ACS记账收款
     * @param worderId
     */
    @Override
    @Transactional
    public void pushFinanceAccountIncreReceivables(Integer worderId){
        //计算并保存网点增项结算金额
//        try{
//            branchBalanceService.caculateOneDotIncreBalanceFee(worderId);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
        //查询开票信息（支付方式、支付流水单号、城市、发票代码、客户名、客户手机号）
        Map<String, Object> invoiceMap = baseMapper.queryUserInvoice(worderId);
        if(null == invoiceMap){
            SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            //todo  需要
            invoiceAPIService.generateBillingRecord(worderId, user.getUserId(), true);
            invoiceMap = baseMapper.queryUserInvoice(worderId);
        }
        WorderInfoEntity worderInfoEntity = worderInformationDao.getByWorderId(worderId);
        CompanyInformationEntity companyInformationEntity = companyInformationDao.getByCompanyId(worderInfoEntity.getCompanyId());
        DotInformationEntity dotInformationEntity = companyInvoiceDao.queryDotByDotId(worderInfoEntity.getDotId());
        //支付方式（1：支付宝 2：微信 ）
        Integer payTypeCode = getIntegerValue(invoiceMap, "pay_type_code");
        String jylx = payTypeCode+"";
        //支付方式（1：支付宝 2：微信 ）
        String payType = getStringValue(invoiceMap, "pay_type");
        //支付流水单号
        String payNo = getStringValue(invoiceMap, "transaction_id");
        //城市
        String city = getStringValue(invoiceMap, "area_name");
        //发票代码
        String invoiceCode = getStringValue(invoiceMap, "invoice_code");
        //客户名
        String username = getStringValue(invoiceMap, "user_name");
        //客户手机号
        String tel = getStringValue(invoiceMap, "user_phone");
        //网点ID
        Integer dotId = getIntegerValue(invoiceMap, "dot_id");
        //网点V码
        String vcode = getStringValue(invoiceMap, "v_code");
        //发票金额
        BigDecimal amount = new BigDecimal(getStringValue(invoiceMap, "order_items_amount"));
        //税率
        BigDecimal taxRate = new BigDecimal(getStringValue(invoiceMap, "order_items_tax_rate"));
//        Map taxMap = worderInformationAccountDao.getOneDotAllInfoByDotId(worderInfoEntity.getDotId());
//        BigDecimal dotTaxRate = new BigDecimal(getStringValue(taxMap, "dotTaxPoint").replaceAll("%", "").trim());
        String taxStr = (taxRate.multiply(BigDecimal.valueOf(100)).setScale(0)).toString();
        //取舍方式
        int roundMode = balanceProperties.ROUND_MODE;
        //计算税金
        BigDecimal tax = amount.multiply(taxRate).setScale(2, roundMode);
        //手续费率
        BigDecimal commissionRate = this.getCommissionRate(payTypeCode);
        //计算手续费
        BigDecimal commission = amount.multiply(commissionRate).setScale(2, roundMode);
        //计算提现金额
        BigDecimal cashout = amount.subtract(commission);
        Date date = new Date();
        String ds = new SimpleDateFormat("yyyy-MM-dd").format(date);
        String lifnr = null;
        String lifnrName = null;
        switch (payTypeCode.intValue()){
            case 1: lifnr = "V9011291"; lifnrName= "支付宝（中国）网络技术有限公司"; break;
            case 2: lifnr = "V9013147"; lifnrName= "财付通支付科技有限公司"; break;
            case 3: lifnr = "V9141541"; lifnrName= "银联商务股份有限公司"; break;
            case 4: lifnr = "V9044830"; lifnrName= "小米科技有限责任公司"; break;
            default: ;
        }
        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<>();
        /*---------------------- 收款 start ----------------------*/
        String rowId1 = rowId(worderId, 3);
        String orderNo = increOrderNo(worderId);
//        String orderNo = "ST_ZZSA" + rowId1;
        String sourceType = "ST_ZZSA";
        String kunnr = "A8B";
        String sgtxt = sourceType+"+"+rowId1+"+"+kunnr+"+"+vcode;
        FncCollectionInformation fncCollectionInformation1 = new FncCollectionInformation();
        fncCollectionInformation1.setRowId(rowId1);
        fncCollectionInformation1.setOrderNo(orderNo);
        fncCollectionInformation1.setPayNo(payNo);
        fncCollectionInformation1.setOrderCount(1);
        fncCollectionInformation1.setSource("ZLW");
        fncCollectionInformation1.setSourceMode("10");
        fncCollectionInformation1.setSourceType(sourceType);
        fncCollectionInformation1.setOrderType("Z");
        fncCollectionInformation1.setOrderDate(date);
        fncCollectionInformation1.setOrderAmount(amount);
        fncCollectionInformation1.setPayType(payType);
        fncCollectionInformation1.setYwms("C16");
        fncCollectionInformation1.setJylx(jylx);
        fncCollectionInformation1.setBukrs(balanceProperties.RRS_COMPANY_CODE);
        fncCollectionInformation1.setBudat(ds);
        fncCollectionInformation1.setBktxt("桩联网收款");
//        fncCollectionInformation1.setXblnr(invoiceCode);
        fncCollectionInformation1.setWaers("CNY");
        fncCollectionInformation1.setKunnr(kunnr);
        fncCollectionInformation1.setIfOnce("Y");
        fncCollectionInformation1.setName(tel);
        fncCollectionInformation1.setCity(city);
        fncCollectionInformation1.setDmbtr(amount);
        fncCollectionInformation1.setDmbtr1(commission);
        fncCollectionInformation1.setDmbtr3(cashout);
        fncCollectionInformation1.setAttribute2(balanceProperties.RRS_COMPANY_ACCOUNT_MAP.get(jylx));
        fncCollectionInformation1.setXref3(orderNo);
        fncCollectionInformation1.setSgtxt(sgtxt);
        fncCollectionInformation1.setLifnr(lifnr);
        fncCollectionInformationList.add(fncCollectionInformation1);
        /*----------------------  收款 end ----------------------*/


        //计算并保存网点增项结算金额
        WorderBalanceFeeEntity worderBalanceFeeEntity = branchBalanceService.queryOneWorderForBalance(worderId);
//        if(0 != worderBalanceFeeEntity.getWorderIncreStatus()){
//            log.error("工单状态为{}，不能推送增项收入和成本到ACS", worderBalanceFeeEntity.getWorderIncreStatus());
//            return;
//        }
        //查询开票信息（支付方式、支付流水单号、城市、发票代码、客户名、客户手机号）
        /*---------------------- 收入 start ----------------------*/
        String rowId2 = rowId(worderId, 4);
        FncCollectionInformation fncCollectionInformation2 = new FncCollectionInformation();
        fncCollectionInformation2.setRowId(rowId2);
        fncCollectionInformation2.setOrderNo(orderNo);
        fncCollectionInformation2.setPayNo(payNo);
        fncCollectionInformation2.setOrderCount(1);
        fncCollectionInformation2.setSource("ZLW");
        fncCollectionInformation2.setSourceMode("10");
        fncCollectionInformation2.setSourceType(sourceType);
        fncCollectionInformation2.setOrderType("Z");
        fncCollectionInformation2.setOrderDate(date);
        fncCollectionInformation2.setOrderAmount(amount);
        fncCollectionInformation2.setPayType(payType);
        fncCollectionInformation2.setYwms("F06");
        fncCollectionInformation2.setJylx(payTypeCode+"");
        fncCollectionInformation2.setBukrs(balanceProperties.RRS_COMPANY_CODE);
        fncCollectionInformation2.setBudat(ds);
        fncCollectionInformation2.setBktxt("桩联网收入");
        fncCollectionInformation2.setXblnr(invoiceCode);
        fncCollectionInformation2.setWaers("CNY");
        fncCollectionInformation2.setKunnr(kunnr);
        fncCollectionInformation2.setIfOnce("Y");
        fncCollectionInformation2.setName(tel);
        fncCollectionInformation2.setCity(city);
        fncCollectionInformation2.setDmbtr(amount);
        fncCollectionInformation2.setDmbtr1(BigDecimal.ZERO);
        fncCollectionInformation2.setDmbtr2(tax);
        fncCollectionInformation2.setXref3(orderNo);
        fncCollectionInformation2.setSgtxt(sgtxt);
        fncCollectionInformation2.setLifnr(lifnr);
        fncCollectionInformationList.add(fncCollectionInformation2);

        //推送记录
        List<BalanceAcsPushRecordEntity> recordList = new ArrayList<>();
        BalanceAcsPushRecordEntity record2 = new BalanceAcsPushRecordEntity();
        BeanUtils.copyProperties(fncCollectionInformation2, record2);
        if(CollectionUtils.isNotEmpty(fncCollectionInformation2.getOdsAccountingInfoDetailList())) {
            record2.setOdsAccountingInfoDetail(JSON.toJSONString(fncCollectionInformation2.getOdsAccountingInfoDetailList()));
        }
        recordList.add(record2);
        /*----------------------  收入 end ----------------------*/

        List<CostInformationEntity> entityList = new ArrayList<>();
        if(worderBalanceFeeEntity.getDotIncreBalanceFeeSum().compareTo(BigDecimal.ZERO)>0) {
            String rowId3 = rowId(worderId, 5);
            FncCollectionInformation fncCollectionInformation3 = new FncCollectionInformation();
            fncCollectionInformation3.setRowId(rowId3);
            fncCollectionInformation3.setOrderNo(orderNo);
            fncCollectionInformation3.setPayNo(payNo);
            fncCollectionInformation3.setOrderCount(1);
            fncCollectionInformation3.setSource("ZLW");
            fncCollectionInformation3.setSourceMode("10");
            fncCollectionInformation3.setSourceType(sourceType);
            fncCollectionInformation3.setOrderType("Z");
            fncCollectionInformation3.setOrderDate(date);
            fncCollectionInformation3.setOrderAmount(amount);
            fncCollectionInformation3.setPayType(payType);
            fncCollectionInformation3.setYwms("F58");
            fncCollectionInformation3.setJylx(payTypeCode + "");
            fncCollectionInformation3.setBukrs(balanceProperties.RRS_COMPANY_CODE);
            fncCollectionInformation3.setBudat(ds);
            fncCollectionInformation3.setBktxt("桩联网暂估货款成本");
            fncCollectionInformation3.setWaers("CNY");
            fncCollectionInformation3.setKunnr(kunnr);
            fncCollectionInformation3.setIfOnce("Y");
            fncCollectionInformation3.setName(tel);
            fncCollectionInformation3.setCity(city);
            fncCollectionInformation3.setDmbtr(worderBalanceFeeEntity.getDotIncreBalanceFee());
            fncCollectionInformation3.setDmbtr1(BigDecimal.ZERO);
            fncCollectionInformation3.setXref3(orderNo);
            fncCollectionInformation3.setSgtxt(sgtxt);
            fncCollectionInformation3.setLifnr(lifnr);
            fncCollectionInformationList.add(fncCollectionInformation3);
            //推送记录
            BalanceAcsPushRecordEntity record3 = new BalanceAcsPushRecordEntity();
            BeanUtils.copyProperties(fncCollectionInformation3, record3);
            if (CollectionUtils.isNotEmpty(fncCollectionInformation3.getOdsAccountingInfoDetailList())) {
                record3.setOdsAccountingInfoDetail(JSON.toJSONString(fncCollectionInformation3.getOdsAccountingInfoDetailList()));
            }
            recordList.add(record3);
            //成本信息记录
            CostInformationEntity entity = new CostInformationEntity();
            BeanUtils.copyProperties(fncCollectionInformation3, entity);
            entity.setDotId(dotId);
            entity.setIncreId(worderId);
            entityList.add(entity);
        }
        /*----------------------  暂估成本 end ----------------------*/

        //保存推送记录
        if(CollectionUtils.isNotEmpty(recordList)){
            balanceAcsPushRecordService.saveBatch(recordList);
        }
        //保存成本信息
        if(CollectionUtils.isNotEmpty(entityList)){
            costInformationService.saveOrUpdateBathchByRowId(entityList);
        }


        /*----------------------    开票   ----------------------*/
        log.info("执行开票操作");
        /** 通过工单编号获取开票订单的客户信息 */
        BillingOrderRecordDTO billingOrderRecord =   billingOrderRecodeMapper.findBillingOrderRecordByoOrderNo(worderInfoEntity.getWorderNo());
        if(billingOrderRecord == null){
            log.error("客户还没有申请开票,工单编号:{}",worderInfoEntity.getWorderNo());
            throw new RuntimeException("客户还没有申请开票");
        }
        /** 获取开票的记录 */
        BillingRecodeDTO billingRecord = billingRecodeMapper.findBillingRecordBySerialNo(billingOrderRecord.getSerialNo());
        if(billingRecord==null){
            log.error("发票记录不存在");
        }
        /** 处理开票请求报文 */
        BillingDTO billing = new BillingDTO();


        /*----- 记账成功用户已经申请，直接进行开票组装对象 -----*/
        String postTime = new SimpleDateFormat(pattern)
                .format(new Date(System.currentTimeMillis()));
        /** 操作流水号。传入重复的操作流水号则认为是重复操作 */
        billing.setSerialNo(billingRecord.getSerialNo());
        /** 请求发送时间。格式为yyyy-MM-dd HH:mm:ss。 */
        billing.setPostTime(postTime);

        /** 瑞宏开票回调 **/
        Map<String,String> map = new HashMap<String,String>();
        map.put("callbackUrl", TicketConfigs.getCallbackUrl());

        /** 获取invoice信息 */
        InvoiceDTO invoiceDTO = invoiceRecodeMapper.findInvoiceRecodeWithItemsBySerialNo(billingRecord.getSerialNo());
        if(invoiceDTO == null){
            log.error("获取invoice信息不存在，操作流水号：{}",billingRecord.getSerialNo());
        }
        log.info("invoice信息为 {}",JSON.toJSONString(invoiceDTO));
        invoiceDTO.getItems().get(0).setQuantity(TicketConfigs.getNums());
        invoiceDTO.getItems().get(0).setUom(TicketConfigs.getUnit());
        invoiceDTO.getItems().get(0).setPrice(invoiceDTO.getItems().get(0).getAmount());
        /** 插入发票订单信息 */
        billing.setOrder(billingOrderRecord);
        /** 发票信息 */
        billing.setInvoice(invoiceDTO);
        /** 自定义参数是 */
        billing.setDynamicParams(map);
        /** 返回消息 */
        List<NoticesDTO> notices = new ArrayList<>();
        NoticesDTO notice = new NoticesDTO();
        notice.setType("email");
        notice.setValue(billingOrderRecord.getEmail());
        notices.add(notice);
        billing.setNotices(notices);
        /*--------------  组装对象end  -------------*/
        List<FncInvoiceHeaders> fncInvoiceHeadersList = new ArrayList<>();

        /** 开票请求处理 */
        String respData = "";
        try {
            BillingOrderRecordDTO order = billing.getOrder();
            //获取发票信息
            InvoiceDTO invoice = billing.getInvoice();
            //业务参数对象
            ReqXwsqZzsParams reqXwsqZzsParams = new ReqXwsqZzsParams()
                    .setXsddm(order.getOrderNo())
                    .setDmgs(balanceProperties.RRS_COMPANY_CODE)
                    .setFpzl("3")
                    .setSourceSystem(PSY_KJFWWOS)
                    .setKhmc(invoice.getCustomerName())
                    .setKhswdjh(invoice.getCustomerCode())
                    .setReceiver(order.getEmail());
            //明细信息
            List<ReqXwsqZzsItemNewList> reqXwsqZzsItemNewList = new LinkedList<>();
            List<ItemsDTO> items = invoice.getItems();
            if (items != null && !items.isEmpty()) {
                List<FncInvoiceDetails> fncInvoiceDetailsList = new ArrayList<>();
                for (int i = 0; i < items.size(); i++) {
                    ItemsDTO item = items.get(i);
                    //含税金额
                    BigDecimal hsje = item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO;
                    //获取税率
                    BigDecimal sl = item.getTaxRate() != null ? item.getTaxRate() : BigDecimal.ZERO;
                    //获取不含税金额
                    BigDecimal bhsje = hsje.divide(BigDecimal.ONE.add(sl), 2, RoundingMode.HALF_EVEN);
                    //获取税额
                    BigDecimal se = hsje.subtract(bhsje);

                    BigDecimal hsdj = hsje.divide(new BigDecimal(items.size()),6);

                    BigDecimal bhsdj = bhsje.divide(new BigDecimal(items.size()),6);

                    FncInvoiceDetails fncInvoiceDetails = new FncInvoiceDetails();
                    fncInvoiceDetails.setTaxRate(taxStr);
                    fncInvoiceDetails.setNoTaxAmount(bhsje.toString());
                    fncInvoiceDetails.setTexForehead(se.toString());
                    fncInvoiceDetails.setTaxAmount(hsje.toString());
                    fncInvoiceDetails.setStoreId(item.getUom());
                    fncInvoiceDetails.setGoodsName(item.getName());
                    fncInvoiceDetails.setGoodsNum(item.getQuantity());
                    fncInvoiceDetails.setDetailsQuenc(i+"");
                    fncInvoiceDetails.setOrderType("Z");//Z - 增项蓝票
                    fncInvoiceDetails.setCompany(balanceProperties.RRS_COMPANY_CODE);
                    fncInvoiceDetails.setOrderNo(worderInfoEntity.getWorderNo());
                    fncInvoiceDetails.setOtherOrderId(billingOrderRecord.getOrderId());
                    fncInvoiceDetails.setTaxPrice(hsdj.toString());
                    fncInvoiceDetails.setNoTaxPrice(bhsdj.toString());
                    fncInvoiceDetails.setTypesNo("3050200000000000000");
                    fncInvoiceDetails.setStoreId("EA");
                    fncInvoiceDetailsList.add(fncInvoiceDetails);
                }
                FncInvoiceHeaders fncInvoiceHeaders = new FncInvoiceHeaders();
                fncInvoiceHeaders.setOrderNo(worderInfoEntity.getWorderNo());
                fncInvoiceHeaders.setOtherOrderId(billingOrderRecord.getOrderId());
                fncInvoiceHeaders.setInvoiceTypes(org.apache.commons.lang3.StringUtils.equals(invoiceDTO.getFinanceInvoiceType(), "81") ? "0" : "3");
                if (org.apache.commons.lang3.StringUtils.equals(fncInvoiceHeaders.getInvoiceTypes(), "0")) {
                    fncInvoiceHeaders.setCustomerMobile("0");
                    fncInvoiceHeaders.setCustomerAddress("0");
                    fncInvoiceHeaders.setCustomerBank("0");
                    fncInvoiceHeaders.setCustomerBankNo("0");
                    fncInvoiceHeaders.setRemark(worderInfoEntity.getWorderNo());
                }
                fncInvoiceHeaders.setCompany(balanceProperties.RRS_COMPANY_CODE);
                fncInvoiceHeaders.setOrderDate(ds);
                fncInvoiceHeaders.setNoticeType("");
                fncInvoiceHeaders.setNoticeValue("");
                fncInvoiceHeaders.setOrderType("Z");
                if (invoiceDTO.getInvoiceType().equals("2")){
                    fncInvoiceHeaders.setBusinessType("1");
                    fncInvoiceHeaders.setCustomerName(invoiceDTO.getCustomerName());
                    fncInvoiceHeaders.setCustomerHeading(invoiceDTO.getCustomerCode());
                    fncInvoiceHeaders.setNoticeValue(billingOrderRecord.getEmail());
                }else{
                    fncInvoiceHeaders.setBusinessType("0");
                    fncInvoiceHeaders.setCustomerName(invoiceDTO.getCustomerName());
                    fncInvoiceHeaders.setNoticeValue(billingOrderRecord.getEmail());
                }
                fncInvoiceHeaders.setSpecificInvoiceType("03");
                if (invoiceDTO.getInvoiceId() != null) {
                    InvoiceBusinessTypeDetailEntity invoiceBusinessTypeDetailEntity = invoiceBusinessTypeDetailDao.queryByInvoiceId(invoiceDTO.getInvoiceId());
                    log.info("增项内容为 {} 发票id为 {}",JSON.toJSONString(invoiceBusinessTypeDetailEntity),invoiceDTO.getInvoiceId());
                    if (invoiceBusinessTypeDetailEntity != null) {
                        InvoiceBusinessTypeDetailVo invoiceBusinessTypeDetailVo = DataUtil.copyBean(invoiceBusinessTypeDetailEntity, InvoiceBusinessTypeDetailVo.class);
                        JSONObject businessTypeJson = new JSONObject();
                        businessTypeJson.put("jzfw", JSON.parseObject(JSON.toJSONString(invoiceBusinessTypeDetailVo)));
                        fncInvoiceHeaders.setSpecificBusiness(businessTypeJson.toJSONString());
                    }
                }

                fncInvoiceHeaders.setFncInvoiceDetailsList(fncInvoiceDetailsList);
                fncInvoiceHeadersList.add(fncInvoiceHeaders);
            }
        } catch (Exception e) {
            log.error("开票失败，原因"+e.getCause()+","+e.getMessage());
        }
        /** 对开票后的数据进行处理 */
        if(billingRecodeMapper.updateBillingRecodeInfo(billing.getPostTime(),2,billing.getSerialNo()) < 1){
            throw new RRException("开票信息更新失败");
        }
        List<String> list = Collections.singletonList(billing.getInvoice().getItems().get(0).getCode());
        if(worderInformationDao.updateMoreVoteCountingStatus(list,2) < 1){
            throw  new RRException("工单的开票状态更新失败");
        }
        List<BillingRecodeDTO> billingRecodes = billingRecodeMapper.getBillingRecordByWorderNo(worderInfoEntity.getWorderNo());
        if (billingRecodes == null || billingRecodes.isEmpty()) {
            log.error("===================单据号：{}=未查询到开票信息！！==================", worderInfoEntity.getWorderNo());
            return;
        }
        InvoceResultLog invoceResultLog = new InvoceResultLog();
        invoceResultLog.setOrderNo(billing.getOrder().getOrderNo());
        invoceResultLog.setSerialNo(billing.getSerialNo());
        invoceResultLog.setResultMsg(respData.getBytes());
        invoiceRecodeMapper.insertInvoceResultLog(invoceResultLog);
        /*----------------------  开票end  ----------------------*/
        try {
            //推送记录
            BalanceAcsPushRecordEntity record1 = new BalanceAcsPushRecordEntity();
            BeanUtils.copyProperties(fncCollectionInformation1, record1);
            if(CollectionUtils.isNotEmpty(fncCollectionInformation1.getOdsAccountingInfoDetailList())) {
                record1.setOdsAccountingInfoDetail(JSON.toJSONString(fncCollectionInformation1.getOdsAccountingInfoDetailList()));
            }
            balanceAcsPushRecordService.save(record1);
            //更新工单增项结算状态
            this.update(
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .eq("worder_id", worderId)
                            .set("worder_incre_status",9)
                            .set("worder_incre_status_value","收入成本记账中")
                            .set("ticket_status",2));
            //推送记账数据
//            List<FncCollectionInformation> reList = acsInterfaceUtil.acsAccount(fncCollectionInformationList);


            String invoiceType = "";

            if (StringUtils.isNotBlank(invoiceDTO.getFinanceInvoiceType())) {
                invoiceType = invoiceDTO.getFinanceInvoiceType();
            } else if ("1".equals(invoiceDTO.getInvoiceType())) {
                invoiceType = "82";
            } else if ("2".equals(invoiceDTO.getInvoiceType())) {
                invoiceType = "81";
            }

            ReqInsertOrUpdate reqInsertOrUpdate = new ReqInsertOrUpdate();
            reqInsertOrUpdate.setOrderNo(worderInfoEntity.getWorderNo());
            reqInsertOrUpdate.setOrderType("20");
            reqInsertOrUpdate.setInvoiceType(invoiceType);

            reqInsertOrUpdate.setServiceFee(commission.toString());
            WorderOrderLogDTO worderOrderLogEntity = worderOrderLogMapper.getByWorderNoAndStatus(worderInfoEntity.getWorderNo(),2);
            //1：支付宝 2：微信
            if (worderOrderLogEntity.getPayType()==1){
                reqInsertOrUpdate.setPayForm("alipay");
            }else if (worderOrderLogEntity.getPayType()==2){
                reqInsertOrUpdate.setPayForm("wxpay");
            }else if(worderOrderLogEntity.getPayType()==3){
                reqInsertOrUpdate.setPayForm("unionpay");
            }
            else if(worderOrderLogEntity.getPayType()==4){
                reqInsertOrUpdate.setPayForm("xmpay");
            }
            reqInsertOrUpdate.setPaySn(worderOrderLogEntity.getTransactionId());

            reqInsertOrUpdate.setFncInvoiceHeadersList(fncInvoiceHeadersList);

            BrandPo brand = worderInformationDao.getBrandByWorderNo(worderInfoEntity.getWorderNo());

            reqInsertOrUpdate.setBrandName(brand.getBrandName());
            reqInsertOrUpdate.setBrandCode(brand.getBrandId());


            reqInsertOrUpdate.setCustomerName(invoiceDTO.getCustomerName());
            reqInsertOrUpdate.setCustomer88Code("A8B");

            reqInsertOrUpdate.setAmountTaxIncluded(fncInvoiceHeadersList.get(0).getFncInvoiceDetailsList().get(0).getTaxAmount());
            reqInsertOrUpdate.setAmountTaxExcluded(fncInvoiceHeadersList.get(0).getFncInvoiceDetailsList().get(0).getNoTaxAmount());
            reqInsertOrUpdate.setOrderCount("1");
            reqInsertOrUpdate.setTaxAmount(fncInvoiceHeadersList.get(0).getFncInvoiceDetailsList().get(0).getTexForehead());
            reqInsertOrUpdate.setTaxRate(taxStr);
            reqInsertOrUpdate.setInvoicingMethodName("先收款后开票");
            reqInsertOrUpdate.setCompanyCode(balanceProperties.RRS_COMPANY_CODE);
            reqInsertOrUpdate.setOrderCreateTime(DateUtils.format(worderInfoEntity.getCreateTime(), DateUtils.DATE_TIME_PATTERN));


            reqInsertOrUpdate.setBranchCode(dotInformationEntity.getBranchCode());
            reqInsertOrUpdate.setBranchName(dotInformationEntity.getBranchName());
            reqInsertOrUpdate.setCity(city);
            reqInsertOrUpdate.setSupplierName(dotInformationEntity.getDotName());
            reqInsertOrUpdate.setSupplierVcode(dotInformationEntity.getVCode());
            reqInsertOrUpdate.setCostAmountTaxExcluded(worderInfoEntity.getDotIncreBalanceFee().toString());
            reqInsertOrUpdate.setCostAmountTaxIncluded(worderInfoEntity.getDotIncreBalanceFeeSum().toString());
            JSONObject rsp = financeBusiness.companyInsertOrUpdate(reqInsertOrUpdate);
            if (!rsp.containsKey("code") || !InvoiceBusiness.SUCCESS.equals(rsp.getInteger("code"))) {
                //财务中台响应出错
                throw new Exception("推送财务中台出错");
            }
        } catch (Exception e){
            e.printStackTrace();
            throw new RRException("推送财务中台记账收款数据失败，orderNo:"+orderNo,e);
        }
    }

    /**
     * 查询增项结算推送ACS记账收款是否记账成功
     * @param worderId
     */
    @Override
    public boolean queryAcsAccountIncreReceivables(Integer worderId){
        String rowId = rowId(worderId, 3);
        try {
            List<String> resList = acsInterfaceUtil.queryAccountingStatus(new ArrayList<String>(){{add(rowId);}});
            if(resList.contains(rowId)){
                //更新工单增项结算状态
                this.update(
                        new UpdateWrapper<WorderInformationAccountEntity>()
                                .eq("worder_id", worderId)
                                .set("worder_incre_status",10)
                                .set("worder_incre_status_value","收款已记账"));
                return true;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用ACS查询SAP记账状态接口失败",e);
            throw new RRException("调用ACS查询SAP记账状态接口失败",e);
        }
    }

    /**
     * 增项结算推送ACS记账收入和暂估成本
     * @param worderId
     */
    @Override
    @Transactional
    public void pushAcsAccountIncre(Integer worderId){
        //计算并保存网点增项结算金额
        WorderBalanceFeeEntity worderBalanceFeeEntity = branchBalanceService.queryOneWorderForBalance(worderId);
        if(10 != worderBalanceFeeEntity.getWorderIncreStatus()){
            log.error("工单状态为{}，不能推送增项收入和成本到ACS", worderBalanceFeeEntity.getWorderIncreStatus());
            return;
        }
        //查询开票信息（支付方式、支付流水单号、城市、发票代码、客户名、客户手机号）
        Map<String, Object> invoiceMap = baseMapper.queryUserInvoice(worderId);
        //支付方式（1：支付宝 2：微信 ）
        Integer payTypeCode = getIntegerValue(invoiceMap, "pay_type_code");
        //支付方式（1：支付宝 2：微信 ）
        String payType = getStringValue(invoiceMap, "pay_type");
        //支付流水单号
        String payNo = getStringValue(invoiceMap, "transaction_id");
        //城市
        String city = getStringValue(invoiceMap, "area_name");
        //发票代码
        String invoiceCode = getStringValue(invoiceMap, "invoice_code");
        //客户名
        String username = getStringValue(invoiceMap, "user_name");
        //客户手机号
        String tel = getStringValue(invoiceMap, "user_phone");
        //网点ID
        Integer dotId = getIntegerValue(invoiceMap, "dot_id");
        //网点V码
        String vcode = getStringValue(invoiceMap, "v_code");
        //发票金额
        BigDecimal amount = new BigDecimal(getStringValue(invoiceMap, "order_items_amount"));
        //税率
        BigDecimal taxRate = new BigDecimal(getStringValue(invoiceMap, "order_items_tax_rate"));
        //取舍方式
        int roundMode = balanceProperties.ROUND_MODE;
        //计算税金
        BigDecimal tax = amount.divide(taxRate.add(BigDecimal.ONE),10,roundMode).multiply(taxRate).setScale(2, roundMode);
        //手续费率
        BigDecimal commissionRate = this.getCommissionRate(payTypeCode);
        //计算手续费
        BigDecimal commission = amount.multiply(commissionRate).setScale(2, roundMode);
        //计算提现金额
        BigDecimal cashout = amount.subtract(commission);
        Date date = new Date();
        String ds = new SimpleDateFormat("yyyy-MM-dd").format(date);
        String lifnr = null;
        switch (payTypeCode.intValue()){
            case 1: lifnr = "V9011291"; break;
            case 2: lifnr = "V9013147"; break;
            default: ;
        }
        String rowId1 = rowId(worderId, 3);
        String orderNo = increOrderNo(worderId);
//        String orderNo = "ZZSA" + rowId1;
        String sourceType = "ST_ZZSA";
        String kunnr = "A8B";
        String sgtxt = sourceType+"+"+rowId1+"+"+kunnr+"+"+vcode;
        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<>();
        /*---------------------- 收入 start ----------------------*/
        String rowId2 = rowId(worderId, 4);
        FncCollectionInformation fncCollectionInformation2 = new FncCollectionInformation();
        fncCollectionInformation2.setRowId(rowId2);
        fncCollectionInformation2.setOrderNo(orderNo);
        fncCollectionInformation2.setPayNo(payNo);
        fncCollectionInformation2.setOrderCount(1);
        fncCollectionInformation2.setSource("ZLW");
        fncCollectionInformation2.setSourceMode("10");
        fncCollectionInformation2.setSourceType(sourceType);
        fncCollectionInformation2.setOrderType("Z");
        fncCollectionInformation2.setOrderDate(date);
        fncCollectionInformation2.setOrderAmount(amount);
        fncCollectionInformation2.setPayType(payType);
        fncCollectionInformation2.setYwms("F06");
        fncCollectionInformation2.setJylx(payTypeCode+"");
        fncCollectionInformation2.setBukrs(balanceProperties.RRS_COMPANY_CODE);
        fncCollectionInformation2.setBudat(ds);
        fncCollectionInformation2.setBktxt("桩联网收入");
        fncCollectionInformation2.setXblnr(invoiceCode);
        fncCollectionInformation2.setWaers("CNY");
        fncCollectionInformation2.setKunnr(kunnr);
        fncCollectionInformation2.setIfOnce("Y");
        fncCollectionInformation2.setName(tel);
        fncCollectionInformation2.setCity(city);
        fncCollectionInformation2.setDmbtr(amount);
        fncCollectionInformation2.setDmbtr1(BigDecimal.ZERO);
        fncCollectionInformation2.setDmbtr2(tax);
        fncCollectionInformation2.setXref3(orderNo);
        fncCollectionInformation2.setSgtxt(sgtxt);
        fncCollectionInformation2.setLifnr(lifnr);
        fncCollectionInformationList.add(fncCollectionInformation2);

        //推送记录
        List<BalanceAcsPushRecordEntity> recordList = new ArrayList<>();
        BalanceAcsPushRecordEntity record2 = new BalanceAcsPushRecordEntity();
        BeanUtils.copyProperties(fncCollectionInformation2, record2);
        if(CollectionUtils.isNotEmpty(fncCollectionInformation2.getOdsAccountingInfoDetailList())) {
            record2.setOdsAccountingInfoDetail(JSON.toJSONString(fncCollectionInformation2.getOdsAccountingInfoDetailList()));
        }
        recordList.add(record2);
        /*----------------------  收入 end ----------------------*/
        List<CostInformationEntity> entityList = new ArrayList<>();
        if(worderBalanceFeeEntity.getDotIncreBalanceFeeSum().compareTo(BigDecimal.ZERO)>0) {
            String rowId3 = rowId(worderId, 5);
            FncCollectionInformation fncCollectionInformation3 = new FncCollectionInformation();
            fncCollectionInformation3.setRowId(rowId3);
            fncCollectionInformation3.setOrderNo(orderNo);
            fncCollectionInformation3.setPayNo(payNo);
            fncCollectionInformation3.setOrderCount(1);
            fncCollectionInformation3.setSource("ZLW");
            fncCollectionInformation3.setSourceMode("10");
            fncCollectionInformation3.setSourceType(sourceType);
            fncCollectionInformation3.setOrderType("Z");
            fncCollectionInformation3.setOrderDate(date);
            fncCollectionInformation3.setOrderAmount(amount);
            fncCollectionInformation3.setPayType(payType);
            fncCollectionInformation3.setYwms("F07");
            fncCollectionInformation3.setJylx(payTypeCode + "");
            fncCollectionInformation3.setBukrs(balanceProperties.RRS_COMPANY_CODE);
            fncCollectionInformation3.setBudat(ds);
            fncCollectionInformation3.setBktxt("桩联网暂估成本");
//        fncCollectionInformation3.setXblnr(invoiceCode);
            fncCollectionInformation3.setWaers("CNY");
            fncCollectionInformation3.setKunnr(kunnr);
            fncCollectionInformation3.setIfOnce("Y");
            fncCollectionInformation3.setName(tel);
            fncCollectionInformation3.setCity(city);
            fncCollectionInformation3.setDmbtr(worderBalanceFeeEntity.getDotIncreBalanceFee());
            fncCollectionInformation3.setDmbtr1(BigDecimal.ZERO);
//        fncCollectionInformation3.setXref3(xref3);
            fncCollectionInformation3.setXref3(orderNo);
            fncCollectionInformation3.setSgtxt(sgtxt);
            fncCollectionInformation3.setLifnr(vcode);
            fncCollectionInformationList.add(fncCollectionInformation3);
            //推送记录
            BalanceAcsPushRecordEntity record3 = new BalanceAcsPushRecordEntity();
            BeanUtils.copyProperties(fncCollectionInformation3, record3);
            if (CollectionUtils.isNotEmpty(fncCollectionInformation3.getOdsAccountingInfoDetailList())) {
                record3.setOdsAccountingInfoDetail(JSON.toJSONString(fncCollectionInformation3.getOdsAccountingInfoDetailList()));
            }
            recordList.add(record3);
            //成本信息记录
            CostInformationEntity entity = new CostInformationEntity();
            BeanUtils.copyProperties(fncCollectionInformation3, entity);
            entity.setDotId(dotId);
            entity.setIncreId(worderId);
            entityList.add(entity);
        }
        /*----------------------  暂估成本 end ----------------------*/

        try {
            //保存推送记录
            if(CollectionUtils.isNotEmpty(recordList)){
                balanceAcsPushRecordService.saveBatch(recordList);
            }
            //保存成本信息
            if(CollectionUtils.isNotEmpty(entityList)){
                costInformationService.saveOrUpdateBathchByRowId(entityList);
            }
            //更新工单增项结算状态
            this.update(
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .eq("worder_id", worderId)
                            .set("worder_incre_status",11)
                            .set("worder_incre_status_value","收入成本记账中"));
            //推送记账数据
            List<FncCollectionInformation> reList = acsInterfaceUtil.acsAccount(fncCollectionInformationList);
        } catch (Exception e){
//            log.error("推送ACS记账数据失败，发票ID:"+orderNo+"\n", e);
            e.printStackTrace();
            throw new RRException("推送ACS记账收入和暂估成本数据失败，发票ID:"+orderNo+"\n",e);
        }
    }


//    @Override
//    @Transactional
//    public void dotBalancePublish(PublishDetail pd) {
//        this.pushCvpBill(pd);
//    }

    /**
     * 激励推送ACS记账
     * @param stimulateList
     */
    @Override
    @Transactional
    public void pushAcsAccountStimulate(List<WorderChildInformationEntity> worderChildInformationList, List<WorderPmStimulateEntity> stimulateList){
        Map<Integer, Object[]> worderCostMap = new HashMap<>();
        Set<Integer> dotIdSet = new HashSet<>();

        Map<Integer, List<WorderPmStimulateEntity>> stimulateMap = stimulateList.stream().collect(Collectors.groupingBy(WorderPmStimulateEntity::getId, Collectors.toList()));

        for (WorderChildInformationEntity worderChildInformationEntity : worderChildInformationList) {
            Integer worderId = worderChildInformationEntity.getWorderId();
            if(!worderCostMap.containsKey(worderId)){
                WorderPmStimulateEntity worderPmStimulateEntity = stimulateMap.get(worderChildInformationEntity.getStimulateId()).get(0);

                String orderNo = "ZLW" + String.format("%06d", worderId) + String.format("%06d", worderChildInformationEntity.getId());
                String jylx = "6";
                Integer dotId = worderPmStimulateEntity.getDotId();
                dotIdSet.add(dotId);
                Object[] costInfo = {orderNo, null, dotId, null, jylx, null}; //OrderNo、companyNo、dotId、网点信息、交易类型、收入发票代码
                worderCostMap.put(worderId, costInfo);
            }
        }

        //查询相关的网点信息
        List<Map> dotInfoList = baseMapper.getDotAllInfoByDotIds(dotIdSet);
        Map<Integer, Map> dotIdInfoMap = new HashMap<>();
        for (Map map : dotInfoList) {
            Integer dotId = getIntegerValue(map, "dot_id");
            dotIdInfoMap.put(dotId, map);
        }
        //worderCostMap中记录关联的网点信息
        for (Map.Entry<Integer, Object[]> entry : worderCostMap.entrySet()) {
            Object[] value = entry.getValue();
            Integer dotId = (Integer)value[2];
            value[3] = dotIdInfoMap.get(dotId);
        }

        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<FncCollectionInformation>();
        List<CostInformationEntity> costList = new ArrayList<>();
        List<BalanceAcsPushRecordEntity> recordList = new ArrayList<>();
        Date date = new Date();
        String ds = new SimpleDateFormat("yyyy-MM-dd").format(date);
        for (WorderChildInformationEntity worderChildInformationEntity : worderChildInformationList) {
            WorderPmStimulateEntity worderPmStimulateEntity = stimulateMap.get(worderChildInformationEntity.getStimulateId()).get(0);

            Integer worderId = worderChildInformationEntity.getWorderId();
            Integer balancdChildId = worderChildInformationEntity.getId();
            Object[] costInfo = worderCostMap.get(worderId);//OrderNo、companyNo、dotId、网点信息、交易类型、收入发票代码
            String orderNo = (String)costInfo[0];
            Integer dotId = (Integer)costInfo[2];
            String jylx = (String)costInfo[4];
            String xblnr = (String)costInfo[5];
            Map dotInfoMap = (Map)costInfo[3];
            String customerCode = getStringValue(dotInfoMap, "customer_code");
            String dotVCode = getStringValue(dotInfoMap, "v_code");
            String dotName = getStringValue(dotInfoMap, "dot_name");
            String dotCity = getDotCity(dotInfoMap);
            String xref3 = orderNo;
            String sgtxt = "ST_ZLW+" + xref3 + "+" + dotVCode;
            String rowId = rowId(balancdChildId, 2);
            /*----------------------  激励记账请求参数 start ----------------------*/
            FncCollectionInformation fncInformation = new FncCollectionInformation();
            fncInformation.setRowId(rowId);
            fncInformation.setOrderNo(orderNo);
            fncInformation.setPayNo(orderNo);
            fncInformation.setOrderCount(1);
            fncInformation.setSource("ZLW");
            fncInformation.setSourceMode("10");
            fncInformation.setSourceType("ST_ZLW");
            fncInformation.setOrderType("Z");
            fncInformation.setOrderDate(date);
            fncInformation.setPayType("电汇");
            fncInformation.setJylx(jylx);
            fncInformation.setBukrs(balanceProperties.RRS_COMPANY_CODE);
            fncInformation.setBudat(ds);
            fncInformation.setXblnr(xblnr);
            fncInformation.setWaers("CNY");
            fncInformation.setKunnr(null);
            fncInformation.setLifnr(dotVCode);
            fncInformation.setIfOnce("N");
            fncInformation.setName(dotName);
            fncInformation.setCity(dotCity);
            fncInformation.setDmbtr1(BigDecimal.ZERO);
            fncInformation.setDmbtr2(BigDecimal.ZERO);
//            fncInformation.setDmbtr3(BigDecimal.ZERO);
            fncInformation.setXref3(xref3);
            fncInformation.setSgtxt(sgtxt);

            BigDecimal amount = worderChildInformationEntity.getDotBalanceFeeSum();
            if(amount.compareTo(BigDecimal.ZERO)>=0) {
                fncInformation.setYwms("C86");
                fncInformation.setBktxt("桩联网正激励");
                fncInformation.setOrderAmount(amount);
                fncInformation.setDmbtr(amount);
            }else {
                amount = amount.abs();
                fncInformation.setYwms("C27");
                fncInformation.setBktxt("桩联网负激励");
                fncInformation.setOrderAmount(amount);
                fncInformation.setDmbtr(amount);
            }

            fncCollectionInformationList.add(fncInformation);
            /*----------------------  激励记账请求参数 end ----------------------*/
            //推送记录
            BalanceAcsPushRecordEntity record = new BalanceAcsPushRecordEntity();
            BeanUtils.copyProperties(fncInformation, record);
            if(CollectionUtils.isNotEmpty(fncInformation.getOdsAccountingInfoDetailList())) {
                record.setOdsAccountingInfoDetail(JSON.toJSONString(fncInformation.getOdsAccountingInfoDetailList()));
            }
            recordList.add(record);
            //记录成本信息
            CostInformationEntity cost = new CostInformationEntity();
            BeanUtils.copyProperties(fncInformation, cost);
            //成本信息表保存金额为真实金额
            cost.setDmbtr(worderChildInformationEntity.getDotBalanceFeeSum());
            cost.setDotId(dotId);
            cost.setStimulateId(worderPmStimulateEntity.getId());
            cost.setBalanceId(worderChildInformationEntity.getId());
            costList.add(cost);
            //激励状态更新
            worderChildInformationEntity.setBalanceSetStatus(21);
            worderChildInformationEntity.setBalanceSetStatusValue("等待记账");
        }
        for (WorderPmStimulateEntity worderPmStimulateEntity : stimulateList) {
            worderPmStimulateEntity.setStatus(21);
        }
        try {
            //更新结算子订单状态
            worderChildInformationService.updateBatchById(worderChildInformationList);
            //更新激励
            worderPmStimulateService.updateBatchById(stimulateList);
            //保存推送记录
            balanceAcsPushRecordService.saveBatch(recordList);
            //保存成本信息
            costInformationService.saveOrUpdateBathchByRowId(costList);
            //推送ACS记账
            List<FncCollectionInformation> reList = acsInterfaceUtil.acsAccount(fncCollectionInformationList);
        } catch (Exception e){
            e.printStackTrace();
            throw new RRException("激励推送ACS记账失败",e);
        }
    }

    /**
     * 激励推送ACS记账
     * @param stimulateList
     */
    @Override
    @Transactional
    public void pushFinanceAccountStimulate(List<WorderChildInformationEntity> worderChildInformationList, List<WorderPmStimulateEntity> stimulateList){
        Map<Integer, Object[]> worderCostMap = new HashMap<>();
        Set<Integer> dotIdSet = new HashSet<>();

        Map<Integer, List<WorderPmStimulateEntity>> stimulateMap = stimulateList.stream().collect(Collectors.groupingBy(WorderPmStimulateEntity::getId, Collectors.toList()));


        for (WorderChildInformationEntity worderChildInformationEntity : worderChildInformationList) {
            Integer worderId = worderChildInformationEntity.getWorderId();
            if(!worderCostMap.containsKey(worderId)){
                WorderPmStimulateEntity worderPmStimulateEntity = stimulateMap.get(worderChildInformationEntity.getStimulateId()).get(0);

                String orderNo = "ZLW" + String.format("%06d", worderId) + String.format("%06d", worderChildInformationEntity.getId());
                String jylx = "6";
                Integer dotId = worderPmStimulateEntity.getDotId();
                dotIdSet.add(dotId);
                Object[] costInfo = {orderNo, null, dotId, null, jylx, null}; //OrderNo、companyNo、dotId、网点信息、交易类型、收入发票代码
                worderCostMap.put(worderId, costInfo);
            }
        }

        //查询相关的网点信息
        List<Map> dotInfoList = baseMapper.getDotAllInfoByDotIds(dotIdSet);
        Map<Integer, Map> dotIdInfoMap = new HashMap<>();
        for (Map map : dotInfoList) {
            Integer dotId = getIntegerValue(map, "dot_id");
            dotIdInfoMap.put(dotId, map);
        }
        //worderCostMap中记录关联的网点信息
        for (Map.Entry<Integer, Object[]> entry : worderCostMap.entrySet()) {
            Object[] value = entry.getValue();
            Integer dotId = (Integer)value[2];
            value[3] = dotIdInfoMap.get(dotId);
        }

        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<FncCollectionInformation>();
        List<ReqInsertOrUpdate> dataList = new ArrayList<>();
        List<CostInformationEntity> costList = new ArrayList<>();
        List<BalanceAcsPushRecordEntity> recordList = new ArrayList<>();
        Date date = new Date();
        String ds = new SimpleDateFormat("yyyy-MM-dd").format(date);
        for (WorderChildInformationEntity worderChildInformationEntity : worderChildInformationList) {
            WorderPmStimulateEntity worderPmStimulateEntity = stimulateMap.get(worderChildInformationEntity.getStimulateId()).get(0);
            DotInformationEntity dotInformationEntity = dotInformationService.getById(worderPmStimulateEntity.getDotId());
            WorderInformationEntity worderInformationEntity = worderInformationService.getById(worderPmStimulateEntity.getWorderId());
            CompanyInformationEntity companyInformationEntity = companyInformationService.getByCompanyId(worderInformationEntity.getCompanyId());
            Integer worderId = worderChildInformationEntity.getWorderId();
            Integer balancdChildId = worderChildInformationEntity.getId();
            Object[] costInfo = worderCostMap.get(worderId);//OrderNo、companyNo、dotId、网点信息、交易类型、收入发票代码
            String orderNo = (String)costInfo[0];
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            ReqInsertOrUpdate reqInsertOrUpdate = new ReqInsertOrUpdate();
            //激励
            reqInsertOrUpdate.setOrderNo(worderChildInformationEntity.getBalanceNo());
            reqInsertOrUpdate.setEncourageNo(worderChildInformationEntity.getBalanceNo());
            reqInsertOrUpdate.setOrderType("30");
            BigDecimal amount = worderChildInformationEntity.getDotBalanceFeeSum();
            if(amount.compareTo(BigDecimal.ZERO)>=0) {
                reqInsertOrUpdate.setEncourage_type("10");
            }else {
                reqInsertOrUpdate.setEncourage_type("20");
            }
            reqInsertOrUpdate.setAmountTaxExcluded(worderChildInformationEntity.getDotBalanceFee().toString());
            reqInsertOrUpdate.setAmountTaxIncluded(worderChildInformationEntity.getDotBalanceFeeSum().toString());
            reqInsertOrUpdate.setCompanyCode(balanceProperties.RRS_COMPANY_CODE);
            reqInsertOrUpdate.setCostAmountTaxExcluded(worderChildInformationEntity.getDotBalanceFee().toString());
            reqInsertOrUpdate.setCostAmountTaxIncluded(worderChildInformationEntity.getDotBalanceFeeSum().toString());
            reqInsertOrUpdate.setEncourageReason(worderPmStimulateEntity.getStimulateReason());
            reqInsertOrUpdate.setEncourageTime(sdf.format(worderPmStimulateEntity.getCreateTime()));
            reqInsertOrUpdate.setEncourageReason(worderPmStimulateEntity.getStimulateReasonValue());//激励原因
            reqInsertOrUpdate.setBranchName(dotInformationEntity.getBranchName());
            reqInsertOrUpdate.setBranchCode(dotInformationEntity.getBranchCode());
            reqInsertOrUpdate.setSupplierName(dotInformationEntity.getDotName());
            reqInsertOrUpdate.setSupplierVcode(dotInformationEntity.getVCode());
            reqInsertOrUpdate.setAssociatedDocNo(worderInformationEntity.getWorderNo());
            reqInsertOrUpdate.setPayForm("dh");
            dataList.add(reqInsertOrUpdate);

            Integer dotId = (Integer)costInfo[2];
            String jylx = (String)costInfo[4];
            String xblnr = (String)costInfo[5];
            Map dotInfoMap = (Map)costInfo[3];
            String dotVCode = getStringValue(dotInfoMap, "v_code"); 
            String dotName = getStringValue(dotInfoMap, "dot_name");
            String dotCity = getDotCity(dotInfoMap);
            String xref3 = orderNo;
            String sgtxt = "ST_ZLW+" + xref3 + "+" + dotVCode;
            /*----------------------  激励记账请求参数 start ----------------------*/
            String RowId = rowId(worderChildInformationEntity.getId(),2);
            FncCollectionInformation fncInformation = new FncCollectionInformation();
            fncInformation.setRowId(RowId);
            fncInformation.setOrderNo(RowId);
            fncInformation.setPayNo(RowId);
            fncInformation.setOrderCount(1);
            fncInformation.setSource("ZLW");
            fncInformation.setSourceMode("10");
            fncInformation.setSourceType("ST_ZLW");
            fncInformation.setOrderType("Z");
            fncInformation.setOrderDate(date);
            fncInformation.setPayType("电汇");
            fncInformation.setJylx(jylx);
            fncInformation.setBukrs(balanceProperties.RRS_COMPANY_CODE);
            fncInformation.setBudat(ds);
            fncInformation.setXblnr(xblnr);
            fncInformation.setWaers("CNY");
            fncInformation.setKunnr(null);
            fncInformation.setLifnr(dotVCode);
            fncInformation.setIfOnce("N");
            fncInformation.setName(dotName);
            fncInformation.setCity(dotCity);
            fncInformation.setDmbtr1(BigDecimal.ZERO);
            fncInformation.setDmbtr2(BigDecimal.ZERO);
//            fncInformation.setDmbtr3(BigDecimal.ZERO);
            fncInformation.setXref3(xref3);
            fncInformation.setSgtxt(sgtxt);

            if (worderPmStimulateEntity.getIncentiveType()==0){
                fncInformation.setYwms("C86");
                fncInformation.setBktxt("桩联网暂估成本Z");
                fncInformation.setOrderAmount(amount);
                fncInformation.setDmbtr(amount);
            }else{
                fncInformation.setYwms("C27");
                fncInformation.setBktxt("桩联网暂估成本F");
                fncInformation.setOrderAmount(amount);
                fncInformation.setDmbtr(amount);
            }

            fncCollectionInformationList.add(fncInformation);
            /*----------------------  激励记账请求参数 end ----------------------*/
            //推送记录
            BalanceAcsPushRecordEntity record = new BalanceAcsPushRecordEntity();
            BeanUtils.copyProperties(fncInformation, record);
            if(CollectionUtils.isNotEmpty(fncInformation.getOdsAccountingInfoDetailList())) {
                record.setOdsAccountingInfoDetail(JSON.toJSONString(fncInformation.getOdsAccountingInfoDetailList()));
            }
            recordList.add(record);
            //记录成本信息
            CostInformationEntity cost = new CostInformationEntity();
            BeanUtils.copyProperties(fncInformation, cost);
            //成本信息表保存金额为真实金额
            cost.setDmbtr(worderChildInformationEntity.getDotBalanceFeeSum());
            cost.setDotId(dotId);
            cost.setStimulateId(worderPmStimulateEntity.getId());
            cost.setBalanceId(worderChildInformationEntity.getId());
            costList.add(cost);
            //激励状态更新
            worderChildInformationEntity.setBalanceSetStatus(21);
            worderChildInformationEntity.setBalanceSetStatusValue("等待记账");
        }
        for (WorderPmStimulateEntity worderPmStimulateEntity : stimulateList) {
            worderPmStimulateEntity.setStatus(21);
        }
        try {
            //更新结算子订单状态
            worderChildInformationService.updateBatchById(worderChildInformationList);
            //更新激励
            worderPmStimulateService.updateBatchById(stimulateList);
            //保存推送记录
            balanceAcsPushRecordService.saveBatch(recordList);
            //保存成本信息
            costInformationService.saveOrUpdateBathchByRowId(costList);
            //推送ACS记账
//            List<FncCollectionInformation> reList = acsInterfaceUtil.acsAccount(fncCollectionInformationList);
            // 调用传输合并单、增项、激励工单接口
            doPushFinance(dataList);
            //推送财务中台
        } catch (Exception e){
            throw new RRException("激励推送ACS记账失败",e);
        }
    }

    /**
     * 记账信息接口
     *
     * @param dataList
     * @return out
     * @throws Exception
     */
    private void doPushFinance(List<ReqInsertOrUpdate> dataList) throws Exception {
        try {
            for (ReqInsertOrUpdate reqInsertOrUpdate : dataList) {
                JSONObject result = financeBusiness.companyInsertOrUpdate(reqInsertOrUpdate);
                if (!result.containsKey("code") || !InvoiceBusiness.SUCCESS.equals(result.getInteger("code"))) {
                    throw new Exception(result.getString("msg"));
                }
            }
        } catch (Exception e) {
            throw new Exception("记账信息接口发生异常",e);
        }
    }


    /**
     * 支付宝是0.55% 微信是0.6%  银联:0.65%
     * @param payType 支付方式  1：支付宝 2：微信 3:银联
     * @return
     */
    private BigDecimal getCommissionRate(int payType){
        switch (payType){
            case 1: return new BigDecimal("0.006");
            case 2: return new BigDecimal("0.006");
            case 3: return new BigDecimal("0.0065");
            case 4: return new BigDecimal("0.00301");
            default: return BigDecimal.ZERO;
        }
    }


    /**
     * 查询工单计收的ACS记账是否成功
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void queryWorderAcsAccount(){
        List<CompanyInvoiceEntity> vendorInvoiceEntityList = companyInvoiceService.list(
                new QueryWrapper<CompanyInvoiceEntity>().eq("status", 6));
        Map<String, CompanyInvoiceEntity> map = new HashMap<>();
        for (CompanyInvoiceEntity v : vendorInvoiceEntityList) {
            map.put(v.getInvoiceOrderNo(), v);
        }
        List<String> rowIdList = new ArrayList<>(map.keySet());
        List<CompanyInvoiceEntity> successInvoiceList = new ArrayList<>();
        /** 先查收入是否记账成功 **/
        try {
            List<String> resList = acsInterfaceUtil.queryAccountingStatus(rowIdList);
            for (String s : resList) {
                successInvoiceList.add(map.get(s));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用ACS查询SAP记账状态接口失败",e);
            throw new RRException("调用ACS查询SAP记账状态接口失败",e);
        }
        if(CollectionUtils.isNotEmpty(successInvoiceList)) {
            List<Integer> invoiceIdList = successInvoiceList.stream().map(CompanyInvoiceEntity::getId).collect(Collectors.toList());
            worderPmStimulateService.update(
                    new UpdateWrapper<WorderPmStimulateEntity>().in("invoice_id", invoiceIdList).ne("status", 14).set("status", 14));

            //获取开票单下所有工单的结算子订单
            Map<String, List<WorderChildInformationEntity>> childWorderMap = worderChildInformationService.selectListByInvoiceIds(invoiceIdList);
            /** 查询成本是否记账成功  **/
            List<Integer> updateStatusInvoiceIdList = new ArrayList<>(); //记录要更新状态的开票ID
            for (Integer invoiceId : invoiceIdList) {
                List<WorderInformationAccountEntity> worderList = baseMapper.selectList(
                        new QueryWrapper<WorderInformationAccountEntity>()
                                .eq("invoice_id", invoiceId));
                Map<String, Integer> map2 = new HashMap<>(); //rowId和结算子信息的映射
                List<String> rowIdList2 = new ArrayList<>();
                for (WorderInformationAccountEntity worderInformationAccountEntity : worderList) {
                    Integer worderId = worderInformationAccountEntity.getWorderId();
                    //获取结算子订单
                    List<WorderChildInformationEntity> childInformationEntityList = childWorderMap.get(invoiceId + "-" + worderId);
                    childInformationEntityList.forEach(worderChildInformationEntity -> {
                        if (IntegerEnum.ZERO.getValue().equals(worderChildInformationEntity.getBalanceSource()) && worderChildInformationEntity.getDotBalanceFeeSum().compareTo(BigDecimal.ZERO) > 0) {
                            //获取工单下结算子订单
                            String rowId1 = rowId(worderChildInformationEntity.getId(), 0);
                            map2.put(rowId1, worderChildInformationEntity.getId());
                            rowIdList2.add(rowId1);
                        }
                    });
                }
                int size = rowIdList2.size();
                List<Integer> successIdList = new ArrayList<>();
                try {
                    List<String> resList = acsInterfaceUtil.queryAccountingStatus(rowIdList2);
                    for (String s : resList) {
                        successIdList.add(map2.get(s));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("调用ACS查询SAP记账状态接口失败", e);
                    throw new RRException("调用ACS查询SAP记账状态接口失败", e);
                }
                if (CollectionUtils.isNotEmpty(successIdList)) {
                    //更新记账成功的工单的增项结算状态
                    //this.update(
                    //        new UpdateWrapper<WorderInformationAccountEntity>()
                    //                .in("worder_id", successIdList)
                    //                .set("worder_set_status", 3)
                    //                .set("worder_set_status_value", "已开票"));
                    if (successIdList.size() == size) {
                        updateChildWorderStatus(successIdList,false,0,3,"已开票");
                        //如果返回数量等于查询数量，说明成本都记账成功了，开票信息更新状态
                        updateStatusInvoiceIdList.add(invoiceId);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updateStatusInvoiceIdList)) {
                companyInvoiceService.update(new UpdateWrapper<CompanyInvoiceEntity>()
                        .in("id", updateStatusInvoiceIdList)
                        .set("status", 7));
            }
        }
    }

    /**
     * 查询工单计收的财务中台记账是否成功
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateWorderFinanceAccount(CompanyInvoiceEntity companyInvoiceEntity,RespSearchOrder respSearchOrder){
        Map<String, CompanyInvoiceEntity> map = new HashMap<>();
        map.put(companyInvoiceEntity.getInvoiceOrderNo(), companyInvoiceEntity);

        List<CompanyInvoiceEntity> successInvoiceList = new ArrayList<>();
        /** 先查收入是否记账成功 **/
        successInvoiceList.add(companyInvoiceEntity);
        if(CollectionUtils.isNotEmpty(successInvoiceList)) {
            List<Integer> invoiceIdList = successInvoiceList.stream().map(CompanyInvoiceEntity::getId).collect(Collectors.toList());
            worderPmStimulateService.update(
                    new UpdateWrapper<WorderPmStimulateEntity>().in("invoice_id", invoiceIdList).ne("status", 3).set("status", 3));
            //获取开票单下所有工单的结算子订单
            List<WorderChildInformationEntity> worderChildInformationEntityList = worderChildInformationService.getListByInvoiceIds(invoiceIdList);
            /** 查询成本是否记账成功  **/
            List<Integer> updateStatusInvoiceIdList = new ArrayList<>(); //记录要更新状态的开票ID
            RespSearchOrder finalRespSearchOrder = respSearchOrder;
            Map<String, BalanceAcsAccountingStatusEntity> accountingMap = new HashMap<>();
            for (WorderChildInformationEntity worderChildInformationEntity : worderChildInformationEntityList) {
                //批量保存记账结果
                batchSaveWorderAccountingStatus(finalRespSearchOrder.getData().getBookkeepingMap(), worderChildInformationEntity.getId(), companyInvoiceEntity.getInvoiceOrderNo(), accountingMap, worderChildInformationEntity.getBalanceNo());
            }
            if (!accountingMap.isEmpty()){
                this.balanceAcsAccountingStatusService.removeByIds(accountingMap.keySet());
                this.balanceAcsAccountingStatusService.saveBatch(accountingMap.values());
            }
            worderInformationService.update(
                    new UpdateWrapper<WorderInformationEntity>()
                            .eq("invoice_id", companyInvoiceEntity.getId())
                            .eq("is_delete",0)
                            .set("worder_set_status",3)
                            .set("worder_set_status_value","已开票"));
            worderChildInformationService.updateByInvoiceId(companyInvoiceEntity.getId());
            worderWaitAccountService.update(new UpdateWrapper<WorderWaitAccountEntity>()
                    .eq("invoice_id",companyInvoiceEntity.getId())
                    .eq("deleted",0)
                    .set("status",2)
                    .set("status_value","开票成功")
            );
        }
    }



    /**
     * 批量保存记账结果
     * @param list
     */
    private void batchSaveAccountingStatus(List<Map<String,Object>> list,Integer id,String OrderNo,String balanceNo){
        Map<String, BalanceAcsAccountingStatusEntity> map = new HashMap<>(); //避免rowId重复
        SaveAccountingStatus(list,id,OrderNo,map,balanceNo);
        if (!map.isEmpty()){
            this.balanceAcsAccountingStatusService.removeByIds(map.keySet());
            this.balanceAcsAccountingStatusService.saveBatch(map.values());
        }
    }

    /**
     *
     * @param list bookkeepingMap 报文
     * @param id worder_childInformation childId
     * @param OrderNo company_invoice 表 invoice_order_no
     * @param map 存放结果
     * @param balanceNo GD-开头的 结算子订单编号
     */
    private void SaveAccountingStatus(List<Map<String,Object>> list,Integer id,String OrderNo,Map<String, BalanceAcsAccountingStatusEntity> map,String balanceNo){
        for (Map<String, Object> stringObjectMap : list) {
            for(Map.Entry entry : stringObjectMap.entrySet()){
                String json = JSON.toJSONString(entry);
                JSONObject jsonObject = JSONObject.parseObject(json);
                Set<String> jsonKey = jsonObject.keySet();
                Iterator it =jsonKey.iterator();
                JSONArray jsonArray = jsonObject.getJSONArray(it.next().toString());
                if (jsonArray.size()>0){
                    Iterator iterator =jsonArray.iterator();
                    while(iterator.hasNext()){
                        JSONObject js = (JSONObject)iterator.next();
                        BalanceAcsAccountingStatusEntity entity = new BalanceAcsAccountingStatusEntity();
                        //if (js.get("ywms").toString().equals("C18")||js.get("ywms").toString().equals("C16")||js.get("ywms").toString().equals("F06")||(js.containsKey("subOrderNo")&&js.get("subOrderNo").equals(worderChildInformationEntity.getBalanceNo()))){
                        if (js.get("ywms").toString().equals("C16")){
                            String RowId = rowId(id,3);
                            entity.setAccountCode(js.get("accountCode").toString());
                            entity.setAccountDate(js.get("accountDate").toString());
                            entity.setRowId(RowId);
                            entity.setAccountDesc(js.get("bktxt").toString());
                            entity.setFlag("S");
                            entity.setAccountFlag("20");
                        } else if (js.get("ywms").toString().equals("C18")){
                            // 桩联网收入
                            entity.setAccountCode(js.get("accountCode").toString());
                            entity.setAccountDate(js.get("accountDate").toString());
                            entity.setRowId(OrderNo);
                            entity.setAccountDesc(js.get("bktxt").toString());
                            entity.setFlag("S");
                            entity.setAccountFlag("20");
                        } else if (js.get("ywms").toString().equals("F06")){
                            String RowId = rowId(id,4);
                            entity.setAccountCode(js.get("accountCode").toString());
                            entity.setAccountDate(js.get("accountDate").toString());
                            entity.setRowId(RowId);
                            entity.setAccountDesc(js.get("bktxt").toString());
                            entity.setFlag("S");
                            entity.setAccountFlag("20");
                        } else if (js.get("ywms").toString().equals("C26")&&(js.containsKey("subOrderNo")&&js.get("subOrderNo").equals(balanceNo))){
                            // 桩联网暂估成本
                            String RowId = rowId(id,0);
                            entity.setAccountCode(js.get("accountCode").toString());
                            entity.setAccountDate(js.get("accountDate").toString());
                            entity.setRowId(RowId);
                            entity.setAccountDesc(js.get("bktxt").toString());
                            entity.setFlag("S");
                            entity.setAccountFlag(js.get("estimationState").toString());
                        } else if (js.get("ywms").toString().equals("F58")){
                            String RowId = rowId(id,5);
                            entity.setAccountCode(js.get("accountCode").toString());
                            entity.setAccountDate(js.get("accountDate").toString());
                            entity.setRowId(RowId);
                            entity.setAccountDesc(js.get("bktxt").toString());
                            entity.setFlag("S");
                            entity.setAccountFlag(js.get("estimationState").toString());
                        } else if (js.get("ywms").toString().equals("C86")){
                            String RowId = rowId(id,2);
                            entity.setAccountCode(js.get("accountCode").toString());
                            entity.setAccountDate(js.get("accountDate").toString());
                            entity.setRowId(RowId);
                            entity.setAccountDesc(js.get("bktxt").toString());
                            entity.setFlag("S");
                            entity.setAccountFlag("20");
                        } else if (js.get("ywms").toString().equals("C27")){
                            String RowId = rowId(id,2);
                            entity.setAccountCode(js.get("accountCode").toString());
                            entity.setAccountDate(js.get("accountDate").toString());
                            entity.setRowId(RowId);
                            entity.setAccountDesc(js.get("bktxt").toString());
                            entity.setFlag("S");
                            entity.setAccountFlag("20");
                        }
                        if (StringUtils.isNotBlank(entity.getRowId())){
                            map.put(entity.getRowId(), entity);
                        }
                    }
                }
            }
        }
    }
    /**
     * 批量保存工单记账结果
     * @param list
     */
    private void batchSaveWorderAccountingStatus(List<Map<String,Object>> list,Integer id,String OrderNo
            ,Map<String, BalanceAcsAccountingStatusEntity> accountingMap,String balanceNo){
        SaveAccountingStatus(list,id,OrderNo,accountingMap,balanceNo);
    }

    /**
     * 批量保存记账结果
     * @param list
     */
    private void batchSaveStimulateAccountingStatus(List<Map<String,Object>> list,Integer id){
        List<BalanceAcsAccountingStatusEntity> entityList = new ArrayList<>();
        Map<String, BalanceAcsAccountingStatusEntity> map = new HashMap<>(); //避免rowId重复
        for (Map<String, Object> stringObjectMap : list) {
            String json = JSON.toJSONString(stringObjectMap);
            if (StringUtils.isNotBlank(json)){
                String RowId = rowId(id,2);
                json=json.replace("[", "");
                json=json.replace("]", "");
                JSONObject jsonObject = JSONObject.parseObject(json);
                BalanceAcsAccountingStatusEntity entity = new BalanceAcsAccountingStatusEntity();
                entity.setAccountCode(jsonObject.get("accountCode").toString());
                entity.setAccountDate(jsonObject.get("accountDate").toString());
                entity.setRowId(RowId);
                entity.setAccountDesc(jsonObject.get("bktxt").toString());
                entity.setFlag("S");
                entity.setAccountFlag(jsonObject.get("estimationState").toString());
                entityList.add(entity);
                map.put(entity.getRowId(), entity);
            }

        }
        if (!map.isEmpty()){
            this.balanceAcsAccountingStatusService.removeByIds(map.keySet());
            this.balanceAcsAccountingStatusService.saveBatch(map.values());
        }
    }


    /**
     * 查询增项收款的ACS记账是否成功
     */
    @Override
    public List<WorderInformationAccountEntity> queryIncreReceivablesAcsAccount(){
        List<WorderInformationAccountEntity> worderList = baseMapper.selectList(
                new QueryWrapper<WorderInformationAccountEntity>().eq("worder_incre_status", 9));
        Map<String, WorderInformationAccountEntity> map = new HashMap<>(); //rowId和工单信息的映射
        for (WorderInformationAccountEntity worderInformationAccountEntity : worderList) {
            Integer worderId = worderInformationAccountEntity.getWorderId();
            String rowId = rowId(worderId, 3);
            map.put(rowId, worderInformationAccountEntity);
        }
        List<String> rowIdList = new ArrayList<>(map.keySet());
        try {
            List<String> resList = acsInterfaceUtil.queryAccountingStatus(rowIdList);
            List<WorderInformationAccountEntity> entityList = new ArrayList<>();
            for (String s : resList) {
                entityList.add(map.get(s));
            }
            return entityList;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用ACS查询SAP记账状态接口失败",e);
            throw new RRException("调用ACS查询SAP记账状态接口失败",e);
        }
    }

    /**
     * 查询增项收款的财务中台记账是否成功
     */
    @Override
    public List<WorderInformationAccountEntity> queryIncreReceivablesFinanceAccount(){
        List<WorderInformationAccountEntity> worderList = baseMapper.selectList(
                new QueryWrapper<WorderInformationAccountEntity>().eq("worder_incre_status", 9));
        Map<String, WorderInformationAccountEntity> map = new HashMap<>(); //rowId和工单信息的映射
        for (WorderInformationAccountEntity worderInformationAccountEntity : worderList) {
            Integer worderId = worderInformationAccountEntity.getWorderId();
            String rowId = rowId(worderId, 3);
            map.put(rowId, worderInformationAccountEntity);
        }
        List<String> rowIdList = new ArrayList<>(map.keySet());
        List<WorderInformationAccountEntity> entityList = new ArrayList<>();
        try {
            for (String s : rowIdList) {
                ReqSearchOrder reqSearchOrder = new ReqSearchOrder();
                reqSearchOrder.setOrderNo(map.get(s).getWorderNo());
                reqSearchOrder.setOrderType("20");
                JSONObject rsp = financeBusiness.companySearchOrder(reqSearchOrder);
                if (!rsp.containsKey("code") || !InvoiceBusiness.SUCCESS.equals(rsp.getInteger("code"))) {
                    continue;
                }
                RespSearchOrder respSearchOrder = new RespSearchOrder();
                if (rsp!=null){
                    respSearchOrder = rsp.toJavaObject(RespSearchOrder.class);
                }
                if (respSearchOrder.getData().getFinancialProcess().equals("记收暂估完成")) {
                    entityList.add(map.get(s));
                }
            }
            return entityList;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用财务中台查询记账状态接口失败",e);
            throw new RRException("调用财务中台查询记账状态接口失败",e);
        }
    }
    /**
     * 增项收款的ACS记账成功后的处理：更新工单增项结算状态和用户开票
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void receivablesAcsAccountSuccess(WorderInformationAccountEntity worderInformationAccountEntity){
        //更新工单增项结算状态
        this.update(
                new UpdateWrapper<WorderInformationAccountEntity>()
                        .eq("worder_id", worderInformationAccountEntity.getWorderId())
                        .set("worder_incre_status",10)
                        .set("worder_incre_status_value","收款已记账"));
        //调用用户开票程序
        String worderNo = worderInformationAccountEntity.getWorderNo();
        BookkeepingRequest req = new BookkeepingRequest();
        req.setWorderNo(worderNo);
        req.setStatus(1);
        invoiceAPIService.bookkeepingPostProccessor(req);
    }

    /**
     * 增项收款的ACS记账成功后的处理：更新工单增项结算状态和用户开票
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void receivablesFinanceAccountSuccess(WorderInformationAccountEntity worderInformationAccountEntity){
        //更新工单增项结算状态
        this.update(
                new UpdateWrapper<WorderInformationAccountEntity>()
                        .eq("worder_id", worderInformationAccountEntity.getWorderId())
                        .set("worder_incre_status",10)
                        .set("worder_incre_status_value","收款已记账"));
    }

    /**
     * 查询增项计收的ACS记账是否成功
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void queryIncreAcsAccount(){
        List<WorderInformationAccountEntity> worderList = baseMapper.selectList(
                new QueryWrapper<WorderInformationAccountEntity>().eq("worder_incre_status", 11));
        /** 先查收入是否记账成功 **/
        Map<String, Integer> map = new HashMap<>(); //rowId和工单信息的映射
        List<String> rowIdList = new ArrayList<>();
        for (WorderInformationAccountEntity worderInformationAccountEntity : worderList) {
            Integer worderId = worderInformationAccountEntity.getWorderId();
            String rowId1 = rowId(worderId, 4);
            map.put(rowId1, worderId);
            rowIdList.add(rowId1);
        }
        List<Integer> successIdList = new ArrayList<>();
        try {
            List<String> resList = acsInterfaceUtil.queryAccountingStatus(rowIdList);
            for (String s : resList) {
                successIdList.add(map.get(s));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用ACS查询SAP记账状态接口失败",e);
            throw new RRException("调用ACS查询SAP记账状态接口失败",e);
        }
        /** 再查成本是否记账成功 **/
        Map<String, Integer> map2 = new HashMap<>(); //rowId和工单信息的映射
        List<String> rowIdList2 = new ArrayList<>();
        List<Integer> successIdList2 = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(successIdList)){
            for (Integer worderId : successIdList) {
                String rowId2 = rowId(worderId, 5);
                map2.put(rowId2, worderId);
                rowIdList2.add(rowId2);
            }
            try {
                List<String> resList = acsInterfaceUtil.queryAccountingStatus(rowIdList2);
                for (String s : resList) {
                    successIdList2.add(map2.get(s));
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("调用ACS查询SAP记账状态接口失败",e);
                throw new RRException("调用ACS查询SAP记账状态接口失败",e);
            }
        }
        if(CollectionUtils.isNotEmpty(successIdList2)){
            //更新记账成功的工单的增项结算状态
            this.update(
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .in("worder_id", successIdList2)
                            .set("worder_incre_status",1)
                            .set("worder_incre_status_value","增项待结算"));
        }
    }

    /**
     * 查询增项计收的ACS记账是否成功
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void queryIncreFinanceAccount(){
        List<WorderInformationAccountEntity> worderList = baseMapper.selectList(
                new QueryWrapper<WorderInformationAccountEntity>().in("worder_incre_status", 9,10,11));
        /** 先查收入是否记账成功 **/
        Map<String, String> map = new HashMap<>(); //rowId和工单信息的映射
        Map<String, Integer> map3 = new HashMap<>(); //rowId和工单信息的映射
        List<String> rowIdList = new ArrayList<>();
        for (WorderInformationAccountEntity worderInformationAccountEntity : worderList) {
            Integer worderId = worderInformationAccountEntity.getWorderId();
            String rowId1 = rowId(worderId, 4);
            map.put(rowId1, worderInformationAccountEntity.getWorderNo());
            map3.put(rowId1, worderId);
            rowIdList.add(rowId1);
        }
        List<Integer> successIdList = new ArrayList<>();
            for (String s : rowIdList) {
                try {
                    ReqSearchOrder reqSearchOrder = new ReqSearchOrder();
                    reqSearchOrder.setOrderNo(map.get(s));
                    reqSearchOrder.setOrderType("20");
                    JSONObject rsp = financeBusiness.companySearchOrder(reqSearchOrder);
                    if (!rsp.containsKey("code") || !InvoiceBusiness.SUCCESS.equals(rsp.getInteger("code"))) {
                        continue;
                    }
                    RespSearchOrder respSearchOrder = new RespSearchOrder();
                    if (rsp!=null){
                        respSearchOrder = rsp.toJavaObject(RespSearchOrder.class);
                    }
                    if (respSearchOrder.getData().getFinancialProcess().equals("收款记帐完成")){
                        this.update(
                                new UpdateWrapper<WorderInformationAccountEntity>()
                                        .eq("worder_id", map3.get(s))
                                        .set("worder_incre_status",10)
                                        .set("worder_incre_status_value","收款已记账"));
                    }
                    if (respSearchOrder.getData().getFinancialProcess().equals("开票完成")){
                        //获取发票下载地址
                        String invoiceUrl = respSearchOrder.getData().getInvoiceList().get(0).getViewUrl();
                        //根据工单获取流水号
//                         serialNo = respSearchOrder.getData().getOrderNo();
                        BillingOrderRecordDTO billingOrderRecordDTOResult = billingOrderRecodeMapper.findBillingOrderRecordByoOrderNo(respSearchOrder.getData().getOrderNo());
                        String serialNo = billingOrderRecordDTOResult.getSerialNo();
                        //通过操作流水号获取订单编号
//                        List<String> list = invoiceOrderItemsMapper.findInvoices(serialNo);
                        List<String> list = new ArrayList<>();
                        list.add(map.get(s));
                        //更新地址
                        worderInformationDao.updateMoreVoteCounting(list, 3, invoiceUrl, invoiceUrl);
                        //更新状态
                        billingRecodeMapper.updateBillingRecodeInfo(null, 3, serialNo);
                        InvoiceRecodeDTO invoiceRecode = new InvoiceRecodeDTO();
                        invoiceRecode.setSerialNo(serialNo);
                        // 发票代码＋发票号码
                        invoiceRecode.setInvoiceCode(respSearchOrder.getData().getInvoiceList().get(0).getNormalInvoiceCode() + respSearchOrder.getData().getInvoiceList().get(0).getNormalInvoiceNo());
                        invoiceRecodeMapper.updateInvoiceRecordByRespData(invoiceRecode);
                        billingRecodeMapper.updateBillingRecodeChargeStatus(1, serialNo);

                    }
                    if (respSearchOrder.getData().getFinancialProcess().equals("记收暂估完成")){
                        //获取发票下载地址
                        String invoiceUrl = respSearchOrder.getData().getInvoiceList().get(0).getViewUrl();
                        //根据工单获取流水号
                        BillingOrderRecordDTO billingOrderRecordDTOResult = billingOrderRecodeMapper.findBillingOrderRecordByoOrderNo(respSearchOrder.getData().getOrderNo());
                        String serialNo = billingOrderRecordDTOResult.getSerialNo();
                        //通过操作流水号获取订单编号
//                        List<String> list = invoiceOrderItemsMapper.findInvoices(serialNo);
                        List<String> list = new ArrayList<>();
                        list.add(map.get(s));
                        //更新地址
                        worderInformationDao.updateMoreVoteCounting(list, 3, invoiceUrl, invoiceUrl);
                        //更新状态
                        billingRecodeMapper.updateBillingRecodeInfo(null, 3, serialNo);
                        InvoiceRecodeDTO invoiceRecode = new InvoiceRecodeDTO();
                        invoiceRecode.setSerialNo(serialNo);
                        // 发票代码＋发票号码
                        invoiceRecode.setInvoiceCode(respSearchOrder.getData().getInvoiceList().get(0).getNormalInvoiceCode() + respSearchOrder.getData().getInvoiceList().get(0).getNormalInvoiceNo());
                        invoiceRecodeMapper.updateInvoiceRecordByRespData(invoiceRecode);
                        billingRecodeMapper.updateBillingRecodeChargeStatus(1, serialNo);


                        //批量保存记账结果
                        batchSaveAccountingStatus(respSearchOrder.getData().getBookkeepingMap(), Integer.valueOf(map3.get(s)),s,null);
                        successIdList.add(map3.get(s));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("调用财务中台查询增项记账状态接口失败",e);
                }
            }
            if(CollectionUtils.isNotEmpty(successIdList)){
                //更新记账成功的工单的增项结算状态
                this.update(
                        new UpdateWrapper<WorderInformationAccountEntity>()
                                .in("worder_id", successIdList)
                                .set("worder_incre_status",1)
                                .set("worder_incre_status_value","增项待结算"));
            }
    }

    /**
     * 查询激励的ACS记账是否成功
     */
    @Override
    @Transactional
    public void queryStimulateAcsAccount(){
        //需要查询记账状态的激励
        List<WorderPmStimulateEntity> stimulateEntities = worderPmStimulateService.list(
                new QueryWrapper<WorderPmStimulateEntity>().eq("status",21).eq("is_delete", 0));
        List<Integer> stimulateIdList = new ArrayList<>();
        for (WorderPmStimulateEntity entity : stimulateEntities) {
            stimulateIdList.add(entity.getId());
        }
        if(CollectionUtils.isEmpty(stimulateIdList)){
            return;
        }
        //查询成本信息，获取rowId
        List<CostInformationEntity> costList = costInformationService.list(
                new QueryWrapper<CostInformationEntity>().in("stimulate_id", stimulateIdList));
        Map<String, Integer> map = new HashMap<>(); //rowId和激励ID的映射
        Map<String, Integer> worderChildMap = new HashMap<>(); //rowId和结算子订单ID的映射
        for (CostInformationEntity e : costList) {
            map.put(e.getRowId(), e.getStimulateId());
            if (e.getBalanceId() != null) {
                worderChildMap.put(e.getRowId(), e.getBalanceId());
            }
        }
        List<String> rowIdList = new ArrayList<>(map.keySet());
        List<Integer> successsStimulateIdList = new ArrayList<>();
        List<Integer> successsWorderChildIdList = new ArrayList<>();
        try {
            List<String> resList = acsInterfaceUtil.queryAccountingStatus(rowIdList);
            for (String s : resList) {
                //记账成功
                successsStimulateIdList.add(map.get(s));
                if (worderChildMap.containsKey(s)) {
                    successsWorderChildIdList.add(worderChildMap.get(s));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用ACS查询SAP记账状态接口失败",e);
            throw new RRException("调用ACS查询SAP记账状态接口失败",e);
        }
        log.info("更新激励子订单状态 worderChildId:" + JSONArray.toJSONString(successsWorderChildIdList));
        updateChildWorderStatus(successsWorderChildIdList,false,1,22,"记账成功");
        //if(CollectionUtils.isNotEmpty(successsStimulateIdList)){
        //    worderPmStimulateService.update(
        //            new UpdateWrapper<WorderPmStimulateEntity>()
        //                    .in("id", successsStimulateIdList)
        //                    .set("status",22));
        //}
        //
        //if(CollectionUtils.isNotEmpty(successsWorderChildIdList)){
        //    worderChildInformationService.update(
        //            new UpdateWrapper<WorderChildInformationEntity>()
        //                    .in("id", successsWorderChildIdList)
        //                    .set("balance_set_status",22));
        //}
    }


    /**
     * 查询激励的财务中台记账是否成功
     */
    @Override
    @Transactional
    public void queryStimulateFinanceAccount(){
        //需要查询记账状态的激励
        List<WorderPmStimulateEntity> stimulateEntities = worderPmStimulateService.list(
                new QueryWrapper<WorderPmStimulateEntity>().eq("status",21).eq("is_delete", 0));
        List<Integer> stimulateIdList = new ArrayList<>();
        for (WorderPmStimulateEntity entity : stimulateEntities) {
            stimulateIdList.add(entity.getId());
        }
        if(CollectionUtils.isEmpty(stimulateIdList)){
            return;
        }
        //查询成本信息，获取rowId
        List<CostInformationEntity> costList = costInformationService.list(
                new QueryWrapper<CostInformationEntity>().in("stimulate_id", stimulateIdList));
        Map<String, Integer> map = new HashMap<>(); //rowId和激励ID的映射
        Map<String, Integer> worderChildMap = new HashMap<>(); //rowId和结算子订单ID的映射
        for (CostInformationEntity e : costList) {
            map.put(e.getRowId(), e.getStimulateId());
            if (e.getBalanceId() != null) {
                worderChildMap.put(e.getRowId(), e.getBalanceId());
            }
        }
        List<String> rowIdList = new ArrayList<>(map.keySet());
        List<Integer> successsStimulateIdList = new ArrayList<>();
        List<Integer> successsWorderChildIdList = new ArrayList<>();

            for (String s : rowIdList) {
                try {
                    WorderChildInformationEntity childInformationEntity = worderChildInformationService.getOne(
                            new QueryWrapper<WorderChildInformationEntity>().in("id",worderChildMap.get(s)));
                    if (childInformationEntity!=null){
                        ReqSearchOrder reqSearchOrder = new ReqSearchOrder();
                        reqSearchOrder.setOrderNo(childInformationEntity.getBalanceNo());
                        reqSearchOrder.setOrderType("30");
                        JSONObject rsp = financeBusiness.companySearchOrder(reqSearchOrder);
                        if (!rsp.containsKey("code") || !InvoiceBusiness.SUCCESS.equals(rsp.getInteger("code"))) {
                            continue;
                        }
                        RespSearchOrder respSearchOrder = new RespSearchOrder();
                        if (rsp!=null){
                            respSearchOrder = rsp.toJavaObject(RespSearchOrder.class);
                        }
                        if (!respSearchOrder.getData().equals("未查询到相关数据")){
                            //批量保存记账结果
                            batchSaveAccountingStatus(respSearchOrder.getData().getBookkeepingMap(),childInformationEntity.getId(),s,childInformationEntity.getBalanceNo());

                            List<Map<String,Object>> bookkeepingMap = respSearchOrder.getData().getBookkeepingMap();
                            for (Map<String, Object> stringObjectMap : bookkeepingMap) {
                                for(Map.Entry entry : stringObjectMap.entrySet()){
                                    String json = JSON.toJSONString(entry);
                                    if (StringUtils.isNotBlank(json)){
                                        json=json.replace("[", "");
                                        json=json.replace("]", "");
                                        JSONObject jsonObject = JSONObject.parseObject(json);
                                        Set<String> jsonKey = jsonObject.keySet();
                                        Iterator iterator =jsonKey.iterator();
                                        while(iterator.hasNext()){
                                            String key = (String) iterator.next();
                                            Object obj = jsonObject.get(key);
                                            JSONObject js = (JSONObject) JSONObject.toJSON(obj);
                                            if (StringUtils.isNotBlank(js.get("estimationState").toString())&&js.get("estimationState").equals("20")){
                                                successsWorderChildIdList.add(worderChildMap.get(s));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("调用财务中台查询激励记账状态接口失败",e);
                }
            }
            updateChildWorderStatus(successsWorderChildIdList,false,1,22,"记账成功");

    }




    /**
     * 推送商户通对账单
     * @param pd
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
//    @Transactional
    public void pushCvpBill(PublishDetail pd) {
        //查询数据
        List<CostInformationEntity> costList = new ArrayList<>();
        Map<Integer, List<Map>> worderMap = new HashMap<>();
        Map<Integer, List<Map>> increMap = new HashMap<>();
        Map<Integer, Map> stimulateMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(pd.getWorderIds())){
            costList.addAll(costInformationService.list(new QueryWrapper<CostInformationEntity>().in("worder_id", pd.getWorderIds())));
            List<Map> worderList = baseMapper.queryWorderByIds(pd.getWorderIds());
            for (Map map : worderList) {
                Integer worderId = getIntegerValue(map, "worder_id");
                if(!worderMap.containsKey(worderId)){
                    worderMap.put(worderId, new ArrayList<>());
                }
                worderMap.get(worderId).add(map);
            }
        }
        if(CollectionUtils.isNotEmpty(pd.getIncreIds())){
            costList.addAll(costInformationService.list(new QueryWrapper<CostInformationEntity>().in("incre_id", pd.getIncreIds())));
            List<Map> worderList = baseMapper.queryIncreWorderByIds(pd.getIncreIds());
            for (Map map : worderList) {
                Integer worderId = getIntegerValue(map, "worder_id");
                if(!increMap.containsKey(worderId)){
                    increMap.put(worderId, new ArrayList<>());
                }
                increMap.get(worderId).add(map);
            }
        }
        if(CollectionUtils.isNotEmpty(pd.getStimulateIds())){
            costList.addAll(costInformationService.list(new QueryWrapper<CostInformationEntity>().in("stimulate_id", pd.getStimulateIds())));
            List<Map> stimulateList = baseMapper.queryWorderByStimulateIds(pd.getStimulateIds());
            for (Map map : stimulateList) {
                stimulateMap.put(getIntegerValue(map, "id"), map);
            }
        }
        if(CollectionUtils.isEmpty(costList)){
            throw new RRException("对账单结算总金额必须大于0！");
        }
        Integer dotId = costList.get(0).getDotId();
        for (CostInformationEntity cost : costList) {
            if(!dotId.equals(cost.getDotId())){
                throw new RRException("必须同一个网点才能发布对账单！");
            }
        }
        List<String> rowIdList = new ArrayList<>();
        for (CostInformationEntity costInformationEntity : costList) {
            rowIdList.add(costInformationEntity.getRowId());
        }
        Map<String, String> accountingMap = new HashMap<>();
        try {
//            List<AccountingStatusResult> resList = acsInterfaceUtil.queryAccountingStatusResult(rowIdList);
//            for (AccountingStatusResult res : resList) {
//                if ("S".equals(res.getFlag()) && ("1".equals(res.getAccountFlag()) || "3".equals(res.getAccountFlag()))) {
//                    accountingMap.put(res.getRowId(), res.getAccountCode());
//                }
//            }
            Collection<BalanceAcsAccountingStatusEntity> accountingStatusEntities = balanceAcsAccountingStatusService.listByIds(rowIdList);
            for (BalanceAcsAccountingStatusEntity entity : accountingStatusEntities) {
                if ("S".equals(entity.getFlag()) && ("1".equals(entity.getAccountFlag()) || "3".equals(entity.getAccountFlag()))) {
                    accountingMap.put(entity.getRowId(), entity.getAccountCode());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //查询网点信息
        Map map = baseMapper.getOneDotAllInfoByDotId(dotId);
        String dotNo = getStringValue(map, "dot_code");
        String customerCode = getStringValue(map, "customer_code");
        String dotVCode = getStringValue(map, "v_code");
        String dotName = getStringValue(map, "dot_name");
        String dotCity = getDotCity(map);
//        String dotAddress = getStringValue(map, "dot_address");
        String branchCode = getStringValue(map, "branch_code");
        String branchName = getStringValue(map, "branch_name");
        String dotBank = getStringValue(map, "dot_bank");
        BigDecimal dotTaxRate = new BigDecimal(getStringValue(map, "dotTaxPoint").replaceAll("%","").trim());
        String bankAccount = getStringValue(map, "bank_account");
        String contractUrl = getStringValue(map, "contract_url");

        //整理时间参数
        Date date = new Date();
        GregorianCalendar c = new GregorianCalendar();
        c.setTime(date);
        XMLGregorianCalendar gc1 = null;
        try {
            gc1 = DatatypeFactory.newInstance().newXMLGregorianCalendar(c);
        } catch (DatatypeConfigurationException e) {
            e.printStackTrace();
            throw new RRException("调用商户通对账接口失败",e);
        }
        c.add(GregorianCalendar.DATE, 7);
        XMLGregorianCalendar gc2 = null;
        try {
            gc2 = DatatypeFactory.newInstance().newXMLGregorianCalendar(c);
        } catch (DatatypeConfigurationException e) {
            e.printStackTrace();
            throw new RRException("调用商户通对账接口失败",e);
        }
        //生成对账单单号
        //3位厂商序号
        String dotIdStr = dotId.toString();
        if(dotIdStr.length() > 3){
            dotIdStr = dotIdStr.substring(dotIdStr.length()-3);
        }else{
            while(dotIdStr.length()<3){
                dotIdStr = "0"+dotIdStr;
            }
        }
        //对账单单号为ZLW+6位日期+3位网点ID+3位序列
        String billNo = "ZLW"+new SimpleDateFormat("MMdd").format(date)+dotIdStr+getSerialNumber();
        //明细整理
        BigDecimal apAmt = BigDecimal.ZERO; //对账单应结总金额（不包含索赔激励的）
        BigDecimal rewardAmt = BigDecimal.ZERO; //激励总金额
        BigDecimal reduceAmt = BigDecimal.ZERO; //索赔总金额
        BigDecimal acAmt = BigDecimal.ZERO; //对账单应结总金额（包含索赔激励的）

//        /* --------------按照HCSP的推送接口整理参数并推送 start ----------------*/
//        List<IfLgSheetDetailLjDTO> ljList = new ArrayList<>();
//        List<IfLgSheetDetailLjExamineDTO> ljExamineList = new ArrayList<>();
//        List<BalanceCvpDetailEntity> detailList = new ArrayList<>();
//        List<BalanceCvpExamineEntity> examineList = new ArrayList<>();
//        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String dateStr = df.format(date);
//        XMLGregorianCalendar nowDate = null;
//        try {
//            GregorianCalendar c1 = new GregorianCalendar();
//            c1.setTime(date);
//            nowDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new RRException("调用商户通对账接口失败",e);
//        }
//        for (CostInformationEntity cost : costList) {
//            if(cost.getStimulateId() != null){
//                BigDecimal dmbtr = cost.getDmbtr();
//                BigDecimal amt = BigDecimal.ZERO;
//                if("C84".equals(cost.getYwms())){
//                    amt = dmbtr;
//                }else{
//                    amt = dmbtr.negate();
//                }
//                IfLgSheetDetailLjExamineDTO dto = new IfLgSheetDetailLjExamineDTO();
//
//                Map worder = stimulateMap.get(cost.getStimulateId());
//                dto.setExamineNo(cost.getStimulateId()+"");
//                dto.setDocNo(getStringValue(worder, "worder_no"));
//                dto.setExamineReason(getStringValue(worder, "stimulateReason"));
//                XMLGregorianCalendar createDate = null;
//                try {
//                    GregorianCalendar c1 = new GregorianCalendar();
//                    c1.setTime((Date)worder.get("create_time"));
//                    createDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    throw new RRException("调用商户通对账接口失败",e);
//                }
//                dto.setExamineDate(createDate);
//                dto.setExamineAmt(amt);
//                ljExamineList.add(dto);
//
//                dto.setProductCode(getStringValue(worder, "companyNo"));
//                dto.setProductName(getStringValue(worder, "companyName"));
//                dto.setProductVersion("激励结算");
//                dto.setProSeriesCode("ZLW");
//                dto.setProSeriesName("桩联网");
//                dto.setTotalAmt(null);
//                dto.setTripClass("");
//                rewardAmt = rewardAmt.add(amt);
//
//                BalanceCvpExamineEntity e = new BalanceCvpExamineEntity();
//                BeanUtils.copyProperties(dto, e);
//                e.setStimulateId(cost.getStimulateId());
//                if(dto.getCashedDate()!=null && dto.getCashedDate().toGregorianCalendar()!=null){
//                    e.setExamineDate(dto.getCashedDate().toGregorianCalendar().getTime());
//                }
//                if(dto.getExamineDate()!=null && dto.getExamineDate().toGregorianCalendar()!=null){
//                    e.setExamineDate(dto.getExamineDate().toGregorianCalendar().getTime());
//                }
//
//                examineList.add(e);
//            }else{
//                IfLgSheetDetailLjDTO dto = new IfLgSheetDetailLjDTO();
//                Integer worderId = null;
//                String balanceType = null;
//                if(cost.getWorderId() != null){
//                    worderId = cost.getWorderId();
//                    balanceType = "工单结算";
//                }else{
//                    worderId = cost.getIncreId();
//                    balanceType = "增项结算";
//                }
//                Map worder = worderMap.get(worderId);
//                dto.setDocNo(getStringValue(worder, "worder_no"));
//                dto.setDocType(balanceProperties.FEE_TYPE_NAME);
//                dto.setOrderType(balanceProperties.FEE_TYPE_CODE);
//                XMLGregorianCalendar createDate = null;
//                try {
//                    GregorianCalendar c1 = new GregorianCalendar();
//                    c1.setTime((Date)worder.get("create_time"));
//                    createDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    throw new RRException("调用商户通对账接口失败",e);
//                }
//                dto.setDocCreatedDate(createDate);
//                dto.setTotalAmt(cost.getDmbtr());
//                dto.setOperationAmt(BigDecimal.ZERO);
//                dto.setTransportAmt(BigDecimal.ZERO);
//                dto.setServiceStyleAmt(BigDecimal.ZERO);
//                dto.setTrafficAmt(BigDecimal.ZERO);
//                dto.setDistanceNum(BigDecimal.ZERO);
//                dto.setMaterialAmt(cost.getDmbtr());
//                dto.setCompensateAmt(BigDecimal.ZERO);
//                dto.setSubsidyAmt(BigDecimal.ZERO);
//                dto.setExamineAmt(BigDecimal.ZERO);
//                dto.setBrandConfirm("确认通过");
//                dto.setBrandVisit(dateStr);
//                dto.setBuyDate(createDate);
//                dto.setNodeFinish(dateStr);
//                dto.setProSeries("桩联网");
//                dto.setProductName(getStringValue(worder, "companyName"));
//                dto.setProductVersion(balanceType);
//                dto.setServiceType("安装");
//                dto.setServiceStyle("上门");
//                dto.setUserName(getStringValue(worder, "user_name"));
//                dto.setUserPhone(getStringValue(worder, "user_phone"));
//                dto.setUserAddr(getStringValue(worder, "address_dup"));
//                dto.setUserAsk("");
//                dto.setRepairWorker(getStringValue(worder, "candidate_attendant"));
//                dto.setVisitDate(nowDate);
//                dto.setVisitRemark("短信评价超期默认满意");
//                dto.setVisitResult("回访满意");
//
//                /* -------------暂估成本信息 start----------------*/
//                dto.setYwdh(cost.getOrderNo());
//                dto.setYwms(cost.getYwms());
//                dto.setBubrs(balanceProperties.RRS_COMPANY_CODE);
//                dto.setFinishDate((Date)worder.get("create_time"));
//                XMLGregorianCalendar budatGc = null;
//                Date budatDate = null;
//                try {
//                    GregorianCalendar c1 = new GregorianCalendar();
//                    budatDate = new SimpleDateFormat("yyyy-MM-dd").parse(cost.getBudat());
//                    c1.setTime(budatDate);
//                    budatGc = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    throw new RRException("调用商户通对账接口失败",e);
//                }
//                dto.setBudat(budatGc);
//                dto.setBktxt(cost.getBktxt());
//                dto.setXblnr(cost.getXblnr());
//                dto.setWaers(cost.getWaers());
//                dto.setKunnr(cost.getKunnr());
//                dto.setLifnr(cost.getLifnr());
//                dto.setName(cost.getName());
//                dto.setCity(cost.getCity());
//                dto.setDmbtr(cost.getDmbtr());
//                dto.setDmbtr1(cost.getDmbtr1());
//                dto.setDmbtr2(cost.getDmbtr2());
//                dto.setXref3(cost.getXref3());
//                dto.setSgtxt(cost.getSgtxt());
//                dto.setSourceType(cost.getSourceType());
//                /* -------------暂估成本信息 end----------------*/
//                ljList.add(dto);
//                apAmt = apAmt.add(cost.getDmbtr());
//
//                BalanceCvpDetailEntity e = new BalanceCvpDetailEntity();
//                BeanUtils.copyProperties(dto, e);
//                e.setWorderId(cost.getWorderId());
//                e.setIncreId(cost.getIncreId());
//                if(dto.getBuyDate()!=null && dto.getBuyDate().toGregorianCalendar()!=null){
//                    e.setBuyDate(dto.getBuyDate().toGregorianCalendar().getTime());
//                }
//                if(dto.getBudat()!=null && dto.getBudat().toGregorianCalendar()!=null){
//                    e.setBudat(dto.getBudat().toGregorianCalendar().getTime());
//                }
//                if(dto.getDocCreatedDate()!=null && dto.getDocCreatedDate().toGregorianCalendar()!=null){
//                    e.setDocCreatedDate(dto.getDocCreatedDate().toGregorianCalendar().getTime());
//                }
//                if(dto.getFinishDate()!=null && dto.getFinishDate().toGregorianCalendar()!=null){
//                    e.setFinishDate(dto.getFinishDate().toGregorianCalendar().getTime());
//                }
//                if(dto.getRebudat()!=null && dto.getRebudat().toGregorianCalendar()!=null){
//                    e.setRebudat(dto.getRebudat().toGregorianCalendar().getTime());
//                }
//                if(dto.getVisitDate()!=null && dto.getVisitDate().toGregorianCalendar()!=null){
//                    e.setVisitDate(dto.getVisitDate().toGregorianCalendar().getTime());
//                }
//                detailList.add(e);
//            }
//        }
//
//        acAmt = apAmt.add(rewardAmt);
//        if(acAmt.compareTo(BigDecimal.ZERO)<=0){
//            throw new RRException("对账单结算总金额必须大于0！");
//        }
//
//        /*----------------------  商户通对账接口参数对象 start ------------------*/
//        IfHcspSheetIn ifHcspSheetIn = new IfHcspSheetIn();
//        ifHcspSheetIn.setBillNo(billNo);
//        ifHcspSheetIn.setDetailCount(Long.valueOf(detailList.size()+examineList.size()));
//        ifHcspSheetIn.setApAmt(apAmt);
//        ifHcspSheetIn.setRewardAmt(rewardAmt);
//        ifHcspSheetIn.setReduceAmt(reduceAmt);
//        ifHcspSheetIn.setAcAmt(acAmt);
//        ifHcspSheetIn.setVendorCode(dotVCode);
//        ifHcspSheetIn.setVendorName(dotName);
//        ifHcspSheetIn.setStEntityCode(branchCode);
//        ifHcspSheetIn.setStEntityName(branchName);
//        ifHcspSheetIn.setBdEntityCode(balanceProperties.BUDGET_CODE);
//        ifHcspSheetIn.setBdEntityName(balanceProperties.BUDGET_NAME);
//        ifHcspSheetIn.setStUserCode(balanceProperties.BALANCE_STAFF_CODE);
//        ifHcspSheetIn.setStUserName(balanceProperties.BALANCE_STAFF_NAME);
//        ifHcspSheetIn.setFeeTypeCode(balanceProperties.FEE_TYPE_CODE);
//        ifHcspSheetIn.setFeeTypeName(balanceProperties.FEE_TYPE_NAME);
////        ifHcspSheetIn.setFeeTypeCode("0");
////        ifHcspSheetIn.setFeeTypeName("社会化品牌");
//        ifHcspSheetIn.setPeriodChar(new SimpleDateFormat("yyyyMM").format(date));
//        ifHcspSheetIn.setCreatedTime(gc1);
//        ifHcspSheetIn.setBillBeginDate(gc1);
//        ifHcspSheetIn.setBillEndDate(gc2);
////        ifHcspSheetIn.setBusinesstype("ST_ZLW");
//        ifHcspSheetIn.setOriginApp("LJ");
//        ifHcspSheetIn.setCAccountCode(balanceProperties.RRS_COMPANY_CODE);
//        ifHcspSheetIn.setCAccountName(balanceProperties.RRS_COMPANY_NAME);
//        ifHcspSheetIn.setCSaleType("内销");
//        ifHcspSheetIn.setCAccountNo(bankAccount);
//        ifHcspSheetIn.setCAccountBank(dotBank);
//        ifHcspSheetIn.setCPayTypeCode("X");
//        ifHcspSheetIn.setIApplied(acAmt);
//        ifHcspSheetIn.setCPayType("正常付款");
//        ifHcspSheetIn.setCInvoiceType("正常发票");
//        ifHcspSheetIn.setCInvoiceOption("发票先到");
//        ifHcspSheetIn.setCFeeItemCode("7010101");
//        ifHcspSheetIn.setCFeeItemName("材料款");
//        ifHcspSheetIn.setCSsupType("供应商");
//        ifHcspSheetIn.setCUserCode(balanceProperties.BALANCE_STAFF_CODE);
//        ifHcspSheetIn.setCUserName(balanceProperties.BALANCE_STAFF_NAME);
//        ifHcspSheetIn.setCDescriptionOn("桩联网工单结算");
//        ifHcspSheetIn.setCReason("合同文件 "+contractUrl);
//        ifHcspSheetIn.setLjExamineList(ljExamineList);
//        ifHcspSheetIn.setLjList(ljList);
//        //暂估信息，都为空
//        ifHcspSheetIn.setYwdh("");
//        ifHcspSheetIn.setYwms("");
//        ifHcspSheetIn.setBubrs("");
//        ifHcspSheetIn.setBudat(nowDate);
//        ifHcspSheetIn.setBktxt("");
//        ifHcspSheetIn.setWaers("");
//        ifHcspSheetIn.setLifnr("");
//        ifHcspSheetIn.setDmbtr(acAmt);
//        ifHcspSheetIn.setDmbtr1(BigDecimal.ZERO);
//        ifHcspSheetIn.setDmbtr2(BigDecimal.ZERO);
//        ifHcspSheetIn.setXref3("");
//        ifHcspSheetIn.setSgtxt("");
//        ifHcspSheetIn.setSourceType("");
//        /*----------------------  商户通对账接口参数对象 end ------------------*/
//        //保存推送数据的流水记录
//        BalanceCvpRecordEntity cvpRecordEntity = new BalanceCvpRecordEntity();
//        BeanUtils.copyProperties(ifHcspSheetIn, cvpRecordEntity);
//        cvpRecordEntity.setPushTime(date);
//        if(ifHcspSheetIn.getBudat()!=null && ifHcspSheetIn.getBudat().toGregorianCalendar()!=null){
//            cvpRecordEntity.setBudat(ifHcspSheetIn.getBudat().toGregorianCalendar().getTime());
//        }
//        if(ifHcspSheetIn.getBillBeginDate()!=null && ifHcspSheetIn.getBillBeginDate().toGregorianCalendar()!=null){
//            cvpRecordEntity.setBillBeginDate(ifHcspSheetIn.getBillBeginDate().toGregorianCalendar().getTime());
//        }
//        if(ifHcspSheetIn.getBillEndDate()!=null && ifHcspSheetIn.getBillEndDate().toGregorianCalendar()!=null){
//            cvpRecordEntity.setBillEndDate(ifHcspSheetIn.getBillEndDate().toGregorianCalendar().getTime());
//        }
//        if(ifHcspSheetIn.getCreatedTime()!=null && ifHcspSheetIn.getCreatedTime().toGregorianCalendar()!=null){
//            cvpRecordEntity.setCreatedTime(ifHcspSheetIn.getCreatedTime().toGregorianCalendar().getTime());
//        }
//        if(ifHcspSheetIn.getDPayable()!=null && ifHcspSheetIn.getDPayable().toGregorianCalendar()!=null){
//            cvpRecordEntity.setDPayable(ifHcspSheetIn.getDPayable().toGregorianCalendar().getTime());
//        }
//        balanceCvpRecordService.save(cvpRecordEntity);
//        balanceCvpDetailService.saveBatch(detailList);
//        balanceCvpExamineService.saveBatch(examineList);
//        //保存对账单信息
//        CvpBillEntity bill = new CvpBillEntity();
//        bill.setBillNo(billNo);
//        bill.setStatusFlag("0");
//        cvpBillDao.insert(bill);
//        //更新工单状态和激励状态，保存对账单明细
//        List<CvpBillDetailEntity> billDetailList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(pd.getWorderIds())) {
//            for (Integer id : pd.getWorderIds()) {
//                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), id, null, null));
//            }
//            //更新工单结算状态
//            baseMapper.update(null,
//                    new UpdateWrapper<WorderInformationAccountEntity>()
//                            .in("worder_id", pd.getWorderIds())
//                            .set("worder_set_status", 5)
//                            .set("worder_set_status_value", "网点结算已发布")
//                            .set("worder_publish_time", date));
//        }
//        if (CollectionUtils.isNotEmpty(pd.getIncreIds())) {
//            for (Integer id : pd.getIncreIds()) {
//                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), null, id, null));
//            }
//            //更新增项结算状态
//            baseMapper.update(null,
//                    new UpdateWrapper<WorderInformationAccountEntity>()
//                            .in("worder_id", pd.getIncreIds())
//                            .set("worder_incre_status", 2)
//                            .set("worder_incre_status_value", "增项结算已发布")
//                            .set("worder_incre_publish_time", date));
//        }
//        if (CollectionUtils.isNotEmpty(pd.getStimulateIds())) {
//            for (Integer id : pd.getIncreIds()) {
//                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), null, null, id));
//            }
//            //更新激励状态
//            worderPmStimulateService.update(
//                    new UpdateWrapper<WorderPmStimulateEntity>()
//                            .in("id", pd.getStimulateIds())
//                            .set("status", 13)
//                            .set("publish_time", date));
//        }
//        cvpBillDetailService.saveBatch(billDetailList);
//        //推送商户通
//        HcspResultInfo hcspResultInfo = dotHcspService.sendHcspSheet(ifHcspSheetIn);
//        if (!"1".equals(hcspResultInfo.getFlag())) {
//            throw new RRException("调用商户通对账接口失败:" + hcspResultInfo.getMsg());
//        }
//        /* --------------按照HCSP的推送接口整理参数并推送 end ----------------*/

        /* --------------按照LJ的推送接口整理参数并推送 start ----------------*/
        List<IfLgSheetDetailCkDTO> ljList = new ArrayList<>();
        List<BalanceCvpDetailEntity> detailList = new ArrayList<>();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = df.format(date);
        XMLGregorianCalendar nowDate = null;
        try {
            GregorianCalendar c1 = new GregorianCalendar();
            c1.setTime(date);
            nowDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RRException("调用商户通对账接口失败",e);
        }
        for (CostInformationEntity cost : costList) {
            if(!accountingMap.containsKey(cost.getRowId())){
                continue;
            }
            String accountCode = accountingMap.get(cost.getRowId());
            if(cost.getStimulateId() != null){
                BigDecimal dmbtr = cost.getDmbtr();
                BigDecimal amt = BigDecimal.ZERO;
                if("C86".equals(cost.getYwms())){
                    amt = dmbtr;
                }else{
                    amt = dmbtr.negate();
                }

                Map worder = stimulateMap.get(cost.getStimulateId());
                IfLgSheetDetailCkDTO dto = new IfLgSheetDetailCkDTO();

                dto.setOrderId(getStringValue(worder, "id"));
                dto.setOrderCode(getStringValue(worder, "worder_no")+"-st-"+getStringValue(worder, "id"));
                dto.setProductName(getStringValue(worder, "typeName")+"-激励");
                dto.setSupplierVcode(dotVCode);
                dto.setSupplierName(dotName);
                XMLGregorianCalendar createDate = null;
                try {
                    GregorianCalendar c1 = new GregorianCalendar();
                    c1.setTime((Date)worder.get("create_time"));
                    createDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RRException("调用商户通对账接口失败",e);
                }
                dto.setOrderCreateTime(createDate);
                dto.setOrderAmount(BigDecimal.ZERO);
                dto.setSettleAmount(amt); //必须与暂估成本的dmbtr一致
                dto.setProductQuantity(1L);
                dto.setInvoiceTaxRate(dotTaxRate);
                dto.setBranchCode(branchCode);
                dto.setAccountCreateTime(nowDate);
                dto.setOrderCreateTime(createDate);
                dto.setPayNo(cost.getPayNo());
                dto.setBuyerName(getStringValue(worder, "user_name"));
                dto.setBuyerCity(cost.getCity());
                dto.setBuyerMobile(getStringValue(worder, "user_phone"));

                /* -------------暂估成本信息 start----------------*/
                dto.setYwdh(cost.getOrderNo());
                dto.setYwms(cost.getYwms());
                dto.setBubrs(balanceProperties.RRS_COMPANY_CODE);
                XMLGregorianCalendar budatGc = null;
                Date budatDate = null;
                try {
                    GregorianCalendar c1 = new GregorianCalendar();
                    budatDate = new SimpleDateFormat("yyyy-MM-dd").parse(cost.getBudat());
                    c1.setTime(budatDate);
                    budatGc = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RRException("调用商户通对账接口失败",e);
                }
                dto.setOrderPayTime(budatGc);
                dto.setBudat(budatGc);
                dto.setBktxt(cost.getBktxt());
                dto.setXblnr(cost.getXblnr());
                dto.setWaers(cost.getWaers());
                dto.setKunnr(cost.getKunnr());
                dto.setLifnr(cost.getLifnr());
                dto.setName(cost.getName());
                dto.setCity(cost.getCity());
                dto.setDmbtr(cost.getDmbtr());
                dto.setDmbtr1(cost.getDmbtr1());
                dto.setDmbtr2(cost.getDmbtr2());
                dto.setXref3(cost.getXref3());
                dto.setSgtxt(cost.getSgtxt());
                dto.setSourceType(cost.getSourceType());
                dto.setRowIdzg(cost.getRowId());
                dto.setYwmszg(cost.getYwms());
                dto.setBukrszg(balanceProperties.RRS_COMPANY_CODE);
                dto.setGjahrzg(cost.getBudat().substring(0,4));
                dto.setBelnrzg(accountCode);
                /* -------------暂估成本信息 end----------------*/
                ljList.add(dto);
                apAmt = apAmt.add(cost.getDmbtr());

                BalanceCvpDetailEntity e = new BalanceCvpDetailEntity();
                BeanUtils.copyProperties(dto, e);
                e.setWorderId(cost.getWorderId());
                e.setIncreId(cost.getIncreId());
                detailList.add(e);
            }else{
                Integer worderId = null;
                String balanceType = null;
                String balanceTypeCode = null;
                List<Map> worderList;
                if(cost.getWorderId() != null){
                    worderId = cost.getWorderId();
                    balanceType = "-工单结算";
                    balanceTypeCode = "-1";
                    worderList = worderMap.get(worderId);
                }else{
                    worderId = cost.getIncreId();
                    balanceType = "-增项结算";
                    balanceTypeCode = "-2";
                    worderList = increMap.get(worderId);
                }
                for (Map worder : worderList) {
                    IfLgSheetDetailCkDTO dto = new IfLgSheetDetailCkDTO();
                    BigDecimal orderAmount = null;
                    if(cost.getWorderId() != null){
                        orderAmount = new BigDecimal(getStringValue(worder, "company_balance_fee_sum"));
                    }else{
                        orderAmount = new BigDecimal(getStringValue(worder, "user_actual_cost"));
                    }
                    dto.setOrderId(getStringValue(worder, "worder_id"));
                    dto.setOrderCode(getStringValue(worder, "worder_no")+balanceTypeCode);
                    dto.setProductName(getStringValue(worder, "typeName")+balanceType);
                    dto.setSupplierVcode(dotVCode);
                    dto.setSupplierName(dotName);
                    XMLGregorianCalendar createDate = null;
                    try {
                        GregorianCalendar c1 = new GregorianCalendar();
                        c1.setTime((Date)worder.get("create_time"));
                        createDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RRException("调用商户通对账接口失败",e);
                    }
//                    BigDecimal amt = new BigDecimal(getStringValue(worder,"balance_fee_sum"));
                    dto.setOrderCreateTime(createDate);
                    dto.setOrderAmount(orderAmount);
//                    dto.setSettleAmount(amt); //必须与暂估成本的dmbtr一致
                    dto.setSettleAmount(cost.getDmbtr());
                    dto.setProductQuantity(1L);
                    dto.setInvoiceTaxRate(dotTaxRate);
                    dto.setBranchCode(branchCode);
                    dto.setAccountCreateTime(nowDate);
                    dto.setOrderCreateTime(createDate);
                    dto.setPayNo(cost.getPayNo());
                    dto.setBuyerName(getStringValue(worder, "user_name"));
                    dto.setBuyerCity(cost.getCity());
                    dto.setBuyerMobile(getStringValue(worder, "user_phone"));

                    /* -------------暂估成本信息 start----------------*/
                    dto.setYwdh(cost.getOrderNo());
                    dto.setYwms(cost.getYwms());
                    dto.setBubrs(balanceProperties.RRS_COMPANY_CODE);
                    XMLGregorianCalendar budatGc = null;
                    Date budatDate = null;
                    try {
                        GregorianCalendar c1 = new GregorianCalendar();
                        budatDate = new SimpleDateFormat("yyyy-MM-dd").parse(cost.getBudat());
                        c1.setTime(budatDate);
                        budatGc = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RRException("调用商户通对账接口失败",e);
                    }
                    dto.setOrderPayTime(budatGc);
                    dto.setBudat(budatGc);
                    dto.setBktxt(cost.getBktxt());
                    dto.setXblnr(cost.getXblnr());
                    dto.setWaers(cost.getWaers()==null ? "CNY" : cost.getWaers());
                    dto.setKunnr(cost.getKunnr());
                    dto.setLifnr(cost.getLifnr());
                    dto.setName(cost.getName());
                    dto.setCity(cost.getCity());
//                    dto.setDmbtr(amt);
//                    dto.setDmbtr1(BigDecimal.ZERO);
//                    dto.setDmbtr2(BigDecimal.ZERO);
                    dto.setDmbtr(cost.getDmbtr());
                    dto.setDmbtr1(cost.getDmbtr1());
                    dto.setDmbtr2(cost.getDmbtr2());
                    dto.setXref3(cost.getXref3());
                    dto.setSgtxt(cost.getSgtxt());
                    dto.setSourceType(cost.getSourceType());
                    dto.setRowIdzg(cost.getRowId());
                    dto.setYwmszg(cost.getYwms());
                    dto.setBukrszg(balanceProperties.RRS_COMPANY_CODE);
                    dto.setGjahrzg(cost.getBudat().substring(0,4));
                    dto.setBelnrzg(accountCode);
                    /* -------------暂估成本信息 end----------------*/
                    ljList.add(dto);
                    apAmt = apAmt.add(cost.getDmbtr());

                    BalanceCvpDetailEntity e = new BalanceCvpDetailEntity();
                    BeanUtils.copyProperties(dto, e);
                    e.setWorderId(cost.getWorderId());
                    e.setIncreId(cost.getIncreId());
                    detailList.add(e);
                }
            }
        }

        acAmt = apAmt.add(rewardAmt);
        if(acAmt.compareTo(BigDecimal.ZERO)<=0){
            throw new RRException("对账单结算总金额必须大于0！");
        }

        /*----------------------  商户通对账接口参数对象 start ------------------*/
        IfLjSheetIn ifLjSheetIn = new IfLjSheetIn();
        ifLjSheetIn.setBillNo(billNo);
        ifLjSheetIn.setDetailCount(Long.valueOf(detailList.size()));
        ifLjSheetIn.setApAmt(apAmt);
        ifLjSheetIn.setRewardAmt(rewardAmt);
        ifLjSheetIn.setReduceAmt(reduceAmt);
        ifLjSheetIn.setAcAmt(acAmt);
        ifLjSheetIn.setVendorCode(dotVCode);
        ifLjSheetIn.setVendorName(dotName);
        ifLjSheetIn.setStEntityCode(branchCode);
        ifLjSheetIn.setStEntityName(branchName);
        ifLjSheetIn.setBdEntityCode(balanceProperties.BUDGET_CODE);
        ifLjSheetIn.setBdEntityName(balanceProperties.BUDGET_NAME);
        ifLjSheetIn.setStUserCode(balanceProperties.BALANCE_STAFF_CODE);
        ifLjSheetIn.setStUserName(balanceProperties.BALANCE_STAFF_NAME);
        ifLjSheetIn.setFeeTypeCode(balanceProperties.FEE_TYPE_CODE);
        ifLjSheetIn.setFeeTypeName(balanceProperties.FEE_TYPE_NAME);
//        ifHcspSheetIn.setFeeTypeCode("0");
//        ifHcspSheetIn.setFeeTypeName("社会化品牌");
        ifLjSheetIn.setPeriodChar(new SimpleDateFormat("yyyyMM").format(date));
        ifLjSheetIn.setCreatedTime(gc1);
        ifLjSheetIn.setBillBeginDate(gc1);
        ifLjSheetIn.setBillEndDate(gc2);
//        ifHcspSheetIn.setBusinesstype("ST_SH");
        ifLjSheetIn.setOriginApp("LJ");
        ifLjSheetIn.setCAccountCode(balanceProperties.RRS_COMPANY_CODE);
        ifLjSheetIn.setCAccountName(balanceProperties.RRS_COMPANY_NAME);
        ifLjSheetIn.setCSaleType("内销");
        ifLjSheetIn.setCAccountNo(bankAccount);
        ifLjSheetIn.setCAccountBank(dotBank);
        ifLjSheetIn.setCPayTypeCode("X");
        ifLjSheetIn.setIApplied(acAmt);
        ifLjSheetIn.setCPayType("正常付款");
        ifLjSheetIn.setCInvoiceType("正常发票");
        ifLjSheetIn.setCInvoiceOption("发票先到");
        ifLjSheetIn.setCFeeItemCode("7010101");
        ifLjSheetIn.setCFeeItemName("材料款");
        ifLjSheetIn.setCSsupType("供应商");
        ifLjSheetIn.setCUserCode(balanceProperties.BALANCE_STAFF_CODE);
        ifLjSheetIn.setCUserName(balanceProperties.BALANCE_STAFF_NAME);
        ifLjSheetIn.setCDescriptionOn("桩联网工单结算");
        ifLjSheetIn.setCReason("合同文件 "+contractUrl);
        ifLjSheetIn.setBusinesstype("ST_ZLW");
        ifLjSheetIn.setDPayable(gc2);
        List<IfLgSheetDetailCkDTO> ljList1 = ifLjSheetIn.getLjList();
        ljList1.addAll(ljList);
        /*----------------------  商户通对账接口参数对象 end ------------------*/
        //保存推送数据的流水记录
        BalanceCvpRecordEntity cvpRecordEntity = new BalanceCvpRecordEntity();
        BeanUtils.copyProperties(ifLjSheetIn, cvpRecordEntity);
        cvpRecordEntity.setPushTime(date);
        if(ifLjSheetIn.getBillBeginDate()!=null && ifLjSheetIn.getBillBeginDate().toGregorianCalendar()!=null){
            cvpRecordEntity.setBillBeginDate(ifLjSheetIn.getBillBeginDate().toGregorianCalendar().getTime());
        }
        if(ifLjSheetIn.getBillEndDate()!=null && ifLjSheetIn.getBillEndDate().toGregorianCalendar()!=null){
            cvpRecordEntity.setBillEndDate(ifLjSheetIn.getBillEndDate().toGregorianCalendar().getTime());
        }
        if(ifLjSheetIn.getCreatedTime()!=null && ifLjSheetIn.getCreatedTime().toGregorianCalendar()!=null){
            cvpRecordEntity.setCreatedTime(ifLjSheetIn.getCreatedTime().toGregorianCalendar().getTime());
        }
        if(ifLjSheetIn.getDPayable()!=null && ifLjSheetIn.getDPayable().toGregorianCalendar()!=null){
            cvpRecordEntity.setDPayable(ifLjSheetIn.getDPayable().toGregorianCalendar().getTime());
        }
        balanceCvpRecordService.save(cvpRecordEntity);
        balanceCvpDetailService.saveBatch(detailList);
        //保存对账单信息
        CvpBillEntity bill = new CvpBillEntity();
        bill.setBillNo(billNo);
        bill.setStatusFlag("0");
        cvpBillDao.insert(bill);
        //更新工单状态和激励状态，保存对账单明细
        List<CvpBillDetailEntity> billDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pd.getWorderIds())) {
            for (Integer id : pd.getWorderIds()) {
                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), id, null, null));
            }
            //更新工单结算状态
            baseMapper.update(null,
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .in("worder_id", pd.getWorderIds())
                            .set("worder_set_status", 5)
                            .set("worder_set_status_value", "网点结算已发布")
                            .set("worder_publish_time", date));
        }
        if (CollectionUtils.isNotEmpty(pd.getIncreIds())) {
            for (Integer id : pd.getIncreIds()) {
                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), null, id, null));
            }
            //更新增项结算状态
            baseMapper.update(null,
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .in("worder_id", pd.getIncreIds())
                            .set("worder_incre_status", 2)
                            .set("worder_incre_status_value", "增项结算已发布")
                            .set("worder_incre_publish_time", date));
        }
        if (CollectionUtils.isNotEmpty(pd.getStimulateIds())) {
            for (Integer id : pd.getIncreIds()) {
                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), null, null, id));
            }
            //更新激励状态
            worderPmStimulateService.update(
                    new UpdateWrapper<WorderPmStimulateEntity>()
                            .in("id", pd.getStimulateIds())
                            .set("status", 13)
                            .set("publish_time", date));
        }
        cvpBillDetailService.saveBatch(billDetailList);
        //推送商户通
        HcspResultInfo hcspResultInfo = dotHcspService.sendLjSheet(ifLjSheetIn);
        if (!"1".equals(hcspResultInfo.getFlag())) {
            throw new RRException("调用商户通对账接口失败:" + hcspResultInfo.getMsg());
        }
        /* --------------按照LJ的推送接口整理参数并推送 end ----------------*/
    }

    private String getDotCity(Map map){
        String dotAreaName = getStringValue(map, "dotAreaName");
        String dotCityName = getStringValue(map, "dotCityName");
        String dotCity;
        if("市辖区".equals(dotCityName)){
            dotCity = dotAreaName.substring(0, dotAreaName.length()-1);
        }else{
            dotCity = dotCityName.substring(0, dotCityName.length()-1);
        }
        return dotCity;
    }

    /**
     * 查询商户通对账单状态
     * @param bill
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void queryCvpBill(CvpBillEntity bill){

        ReqSelectSettlementStatus reqSelectSettlementStatus = new ReqSelectSettlementStatus();
        reqSelectSettlementStatus.setBillNo(bill.getBillNo());
        JSONObject respJson = financeBusiness.selectSettlementStatus(reqSelectSettlementStatus);
        RespSelectSettlementStatus respSelectSettlementStatus = new RespSelectSettlementStatus();

        SelectSettlementStatusData selectSettlementStatusData = new SelectSettlementStatusData();
        if (respJson != null) {
            respSelectSettlementStatus = respJson.toJavaObject(RespSelectSettlementStatus.class);
            if (!FinanceBusiness.getSUCCESS().equals(respSelectSettlementStatus.getCode())) {
                log.info("billNo:" + bill.getBillNo() + ",查询结算单状态接口失败:" + respSelectSettlementStatus.getMsg());
                return;
            }
            selectSettlementStatusData = respSelectSettlementStatus.getData();
        } else {
            respSelectSettlementStatus.setCode("500");
            respSelectSettlementStatus.setMsg("调用查询结算单状态接口失败");
            log.info("billNo:" + bill.getBillNo() + ",查询结算单状态接口失败:" + respSelectSettlementStatus.getMsg());

            return;
        }

//        IfSheetStatusIn ifSheetStatusIn = new IfSheetStatusIn();
//        ifSheetStatusIn.setBillNo(bill.getBillNo());
//        ifSheetStatusIn.setOriginApp("LJ");
//        SheetStatusResultInfo sheetStatusInfo = dotHcspService.getSheetStatusInfo(ifSheetStatusIn);
//        String statusFlag = sheetStatusInfo.getStatusFlag();
//        if(statusFlag.equals(bill.getStatusFlag())){
//            //如果状态没变，不做处理
//            return;
//        }
        //记录状态
//        bill.setStatusFlag(statusFlag);
        String statusFlag = selectSettlementStatusData.getStatus();
        bill.setStatusFlag(statusFlag);

        if(selectSettlementStatusData.getPayTime() != null) {
            bill.setPayDate(DateUtils.stringToDate(selectSettlementStatusData.getPayTime(), DateUtils.DATE_TIME_PATTERN));
        }
//        if(sheetStatusInfo.getAccountDate()!=null && sheetStatusInfo.getAccountDate().toGregorianCalendar() != null) {
//            bill.setAccountDate(sheetStatusInfo.getAccountDate().toGregorianCalendar().getTime());
//        }
//        if(sheetStatusInfo.getInvoiceDate()!=null && sheetStatusInfo.getInvoiceDate().toGregorianCalendar() != null) {
//            bill.setInvoiceDate(sheetStatusInfo.getInvoiceDate().toGregorianCalendar().getTime());
//        }
//        if(!"0".equals(statusFlag) && !"1".equals(statusFlag) && org.apache.commons.lang.StringUtils.isEmpty(bill.getInvoiceIds())) {
//            //如果已开票，且没有保存发票信息，则查询发票信息
//            IfInvoiceInObj ifInvoiceInObj = new IfInvoiceInObj();
//            ifInvoiceInObj.setBillNo(bill.getBillNo());
//            ifInvoiceInObj.setOriginApp("LJ");
//            InvoiceResultInfo invoiceInfo = dotHcspService.getInvoiceInfo(ifInvoiceInObj);
//            bill.setBillList(JSON.toJSONString(invoiceInfo.getBillList()));
//            if (CollectionUtils.isNotEmpty(invoiceInfo.getInvoiceList())) {
//                List<Integer> invoiceIds = new ArrayList<>();
//                for (IfInvoiceObj ifInvoiceObj : invoiceInfo.getInvoiceList()) {
//                    CvpInvoiceEntity cvpInvoiceEntity = new CvpInvoiceEntity();
//                    BeanUtils.copyProperties(invoiceInfo, cvpInvoiceEntity);
//                    cvpInvoiceEntity.setInvoiceTime(ifInvoiceObj.getInvoiceDate().toGregorianCalendar().getTime());
//
//                    List<CvpInvoiceEntity> list = cvpInvoiceService.list(
//                            new QueryWrapper<CvpInvoiceEntity>()
//                                    .eq("invoice_code", cvpInvoiceEntity.getInvoiceCode())
//                                    .eq("invoice_no", cvpInvoiceEntity.getInvoiceNo()));
//                    if(CollectionUtils.isEmpty(list)){
//                        cvpInvoiceService.save(cvpInvoiceEntity);
//                        invoiceIds.add(cvpInvoiceEntity.getId());
//                    }else{
//                        for (int i = 0; i <list.size(); i++) {
//                            invoiceIds.add(list.get(i).getId());
//                        }
//                    }
//                }
//                bill.setInvoiceIds(org.apache.commons.lang.StringUtils.join(invoiceIds,','));
//            }
//        }
        cvpBillDao.updateById(bill);

        //如果流程已经结束，更改相关工单和激励的状态
        if("10".equals(statusFlag)){
//        if("P".equals(statusFlag)){
//            List<CvpBillDetailEntity> billDeailList = cvpBillDetailService.list(new QueryWrapper<CvpBillDetailEntity>().eq("bill_id", bill.getId()));
//            List<Integer> worderIds = new ArrayList<>();
//            List<Integer> increIds = new ArrayList<>();
//            List<Integer> stimulateIds = new ArrayList<>();
//            for (CvpBillDetailEntity e : billDeailList) {
//                if(e.getWorderId()!=null){
//                    worderIds.add(e.getWorderId());
//                }else if(e.getIncreId()!=null){
//                    increIds.add(e.getIncreId());
//                }else{
//                    stimulateIds.add(e.getStimulateId());
//                }
//            }
            List<Integer> worderIds = null;
            List<Integer> increIds = null;
            List<Integer> stimulateIds = null;
            BalancePublishEntity balancePublishEntity = balancePublishService.getOne(new QueryWrapper<>(new BalancePublishEntity().setCvpBillId(bill.getId())));
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(balancePublishEntity.getWorderIds())){
                worderIds = idStr2idList(balancePublishEntity.getWorderIds());
            }
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(balancePublishEntity.getIncreIds())){
                increIds = idStr2idList(balancePublishEntity.getIncreIds());
            }
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(balancePublishEntity.getStimulateIds())){
                stimulateIds = idStr2idList(balancePublishEntity.getStimulateIds());
            }
            Date date = new Date();
            if(CollectionUtils.isNotEmpty(worderIds)){
                //更新工单结算状态
                //baseMapper.update(null,
                //        new UpdateWrapper<WorderInformationAccountEntity>()
                //                .in("worder_id", worderIds)
                //                .set("worder_set_status",7)
                //                .set("worder_set_status_value","网点已结算")
                //                .set("worder_publish_time", date));
                updateChildWorderStatus(worderIds,true,0,7,"网点已结算");
            }
            if(CollectionUtils.isNotEmpty(increIds)){
                //更新增项结算状态
                baseMapper.update(null,
                        new UpdateWrapper<WorderInformationAccountEntity>()
                                .in("worder_id", increIds)
                                .set("worder_incre_status",4)
                                .set("worder_incre_status_value","增项已结算")
                                .set("worder_incre_publish_time", date));
            }
            if(CollectionUtils.isNotEmpty(stimulateIds)){
                //更新激励状态
                updateChildWorderStatus(stimulateIds,true,1,14,"激励已结算");
                //worderPmStimulateService.update(
                //        new UpdateWrapper<WorderPmStimulateEntity>()
                //                .in("id", stimulateIds)
                //                .set("status",14)
                //                .set("publish_time", date));
            }
        }
    }

    /**
     * 更新结算子工单状态并且同步工单状态
     * @param ids
     * @param balanceSource
     * @param balanceSetStatus
     * @param message
     */
    private void updateChildWorderStatus(List<Integer> ids,Boolean pushState, int balanceSource, Integer balanceSetStatus, String message) {
        if(ids.isEmpty()){
            return;
        }
        //更新结算子工单状态
        worderChildInformationService.getBaseMapper().update(null, new UpdateWrapper<WorderChildInformationEntity>().in("id", ids).set("balance_set_status", balanceSetStatus).set("balance_set_status_value", message).set("publish_time", pushState ? LocalDateTime.now() : null));
        //查询结算子工单
        List<WorderChildInformationEntity> childInformationEntityList = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>().in("id", ids));
        //判断是否需要同步工单或者激励单状态，所有子结算工单更新完成，才会更新
        if(balanceSource == 0){
            //工单
            Set<Integer> worderIds = childInformationEntityList.stream().map(WorderChildInformationEntity::getWorderId).collect(Collectors.toSet());
            //查询工单下所有工单结算子工单
            List<WorderChildInformationEntity> childInformationList = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>().in("worder_id", worderIds).eq("balance_source",0).eq("is_delete", 0));
            //分组
            Map<Integer, List<WorderChildInformationEntity>> worderGroupBys = childInformationList.stream().collect(Collectors.groupingBy(WorderChildInformationEntity::getWorderId, Collectors.toList()));
            worderGroupBys.forEach((worderId, list) -> {
                long count = list.stream().filter(worderChildInformation -> !worderChildInformation.getBalanceSetStatus().equals(balanceSetStatus)).count();
                if (count <= 0) {
                    //更新工单结算状态
                    worderInformationDao.update(null, new UpdateWrapper<WorderInformationEntity>().set("worder_set_status", balanceSetStatus).set("worder_set_status_value", message).set("worder_publish_time", pushState ? DateUtils.getCurrentTime() : null).eq("worder_id", worderId));
                }
            });
        }else if(balanceSource == 1){
            //激励单
            //获取当前所有激励单ID
            Set<Integer> stimulateIds = childInformationEntityList.stream().map(WorderChildInformationEntity::getStimulateId).collect(Collectors.toSet());
            //查询激励单下所有激励结算子工单
            List<WorderChildInformationEntity> childInformationList = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>().in("stimulate_id", stimulateIds).eq("balance_source",1).eq("is_delete", 0));
            //分组
            Map<Integer, List<WorderChildInformationEntity>> stimulateGroupBys = childInformationList.stream().collect(Collectors.groupingBy(WorderChildInformationEntity::getStimulateId, Collectors.toList()));
            stimulateGroupBys.forEach((stimulateId, list) -> {
                long count = list.stream().filter(worderChildInformation -> !worderChildInformation.getBalanceSetStatus().equals(balanceSetStatus)).count();
                if (count <= 0) {
                    //更新激励单发布时间
                    worderPmStimulateDao.update(null, new UpdateWrapper<WorderPmStimulateEntity>().eq("id", stimulateId).set("status",balanceSetStatus).set("publish_time", pushState ? DateUtils.getCurrentTime() : null));
                }
            });
        }
    }


    /**
     * RowId为ZLW+1位类型编号+11位工单ID(或激励ID)
     * @param worderId
     * @param type 0: 网点工单结算 1：网点增项结算  2：激励结算 3:网点增项收款 4:网点增项收入 5:网点增项暂估成本
     * @return
     */
    private String rowId(Integer worderId, Integer type){
        return rowId("ZLW", worderId, type);
    }

    /**
     * RowId为ZLW+1位类型编号+11位工单ID(或激励ID)
     * @param prefix 前缀
     * @param worderId
     * @param type 0: 网点工单结算 1：网点增项结算  2：激励结算 3:网点增项收款 4:网点增项收入 5:网点增项暂估成本
     * @return
     */
    private String rowId(String prefix, Integer worderId, Integer type){
        //11位工单ID
        String worderIdStr = worderId.toString();
        if(worderIdStr.length() > 11){
            worderIdStr = worderIdStr.substring(worderIdStr.length()-3);
        }else{
            while(worderIdStr.length()<11){
                worderIdStr = "0"+worderIdStr;
            }
        }
//        ZLW0
        //RowId为ZLW+1位类型编号+11位工单ID
        String rowId = prefix+type+worderIdStr;
//        log.info("商户通接口的对账单号：{}", rowId);
        return rowId;
    }


    /**
     * RowId为ZZSAZLW+9位工单ID
     * @param worderId
     * @return
     */
    private String increOrderNo(Integer worderId){
        return increOrderNo("ZZSAZLW", worderId);
    }
    /**
     * RowId为ZZSAZLW+9位工单ID
     * @param prefix 前缀
     * @param worderId
     * @return
     */
    private String increOrderNo(String prefix, Integer worderId){
        //11位工单ID
        String worderIdStr = worderId.toString();
        if(worderIdStr.length() > 9){
            worderIdStr = worderIdStr.substring(worderIdStr.length()-3);
        }else{
            while(worderIdStr.length()<9){
                worderIdStr = "0"+worderIdStr;
            }
        }
        //RowId为ZLW+1位类型编号+11位工单ID
        String rowId = prefix+worderIdStr;
//        log.info("商户通接口的对账单号：{}", rowId);
        return rowId;
    }

    @Override
    @Transactional
    public void insertVendorInvoice(WorderInformationAccountVO worderInformation) {
        Integer companyId = getIntegerValue(worderInformation.getList().get(0),"companyId");
        //查询车企信息
        Map<String, Object> companyMap = baseMapper.getCompanyById(companyId);
        String companyNo = getStringValue(companyMap, "company_no"); //车企编码
        String companyName = getStringValue(companyMap, "company_name"); //车企名称
        String taxNo = getStringValue(companyMap, "tax_no"); //税号
        String companyBank = getStringValue(companyMap, "company_bank"); //开户行
        String bankAccount = getStringValue(companyMap, "bank_account"); //账号
        String companyMobile = getStringValue(companyMap, "company_mobile"); //电话
        Date date = new Date();
        //3位厂商序号
        String companyIdStr = companyId.toString();
        if(companyIdStr.length() > 3){
            companyIdStr = companyIdStr.substring(companyIdStr.length()-3);
        }else{
            while(companyIdStr.length()<3){
                companyIdStr = "0"+companyIdStr;
            }
        }
        //发票标识为ZLW+6位日期+3位厂商ID+3位序列
        String invoiceOrderNo = "ZLW"+new SimpleDateFormat("MMdd").format(date)+companyIdStr+getSerialNumber();
        //查询所有开票工单
        List<Integer> worderIdList = new ArrayList<>();
        for (Map object: worderInformation.getList()) {
            worderIdList.add(getIntegerValue(object, "id"));
        }
//        List<Map> worderList = baseMapper.listWorderByIds(worderIdList);
        List<WorderInformationAccountEntity> worderList = baseMapper.selectList(
                new QueryWrapper<WorderInformationAccountEntity>().in("worder_id", worderIdList));
        //查询所有开票工单关联的可以结算的激励（厂商对日日顺的）
        List<WorderPmStimulateEntity> stimulateList = worderPmStimulateService.list(
                new QueryWrapper<WorderPmStimulateEntity>()
                        .eq("stimulate_type",11)
                        .eq("status",18)
                        .eq("is_delete", 0)
                        .in("worder_id", worderIdList)
        );
        //查询该厂商的，工单已结算但是关联激励未结算的激励信息
        List<WorderPmStimulateEntity> stimulateList2 = worderPmStimulateService.selectNotBalanceStimulateOnBalanceWorderByCompanyId(companyId);
        stimulateList.addAll(stimulateList2);
        /*---------------------  金税开票 start -----------------------*/

        BigDecimal allFeeSum = BigDecimal.ZERO; //记录含税金额的累加和
        BigDecimal allTax = BigDecimal.ZERO; //记录税额的累加和
        BigDecimal allFee = BigDecimal.ZERO; //记录未税金额的累加和
        for (WorderInformationAccountEntity e : worderList) {
            BigDecimal eFeeSum = e.getCompanyBalanceFeeSum();//含税金额
            BigDecimal eTax = e.getCompanyBalanceFeeTax();//税额
            BigDecimal eFee = e.getCompanyBalanceFee();//不含税金额
            allFeeSum = allFeeSum.add(eFeeSum);
            allTax = allTax.add(eTax);
            allFee = allFee.add(eFee);
        }
        for (WorderPmStimulateEntity e : stimulateList) {
            BigDecimal eFeeSum = e.getPriceTax();//含税金额
            BigDecimal eTax = e.getStorageTax();//税额
            BigDecimal eFee = e.getStimulateFee();//不含税金额
            if(eFee.compareTo(BigDecimal.ZERO)<0 && eFee.compareTo(BigDecimal.ZERO)>0 ){
                eFee = eFee.negate();
            }
            allFeeSum = allFeeSum.add(eFeeSum);
            allTax = allTax.add(eTax);
            allFee = allFee.add(eFee);
        }
        //根据不含税金额的累加和，计算票面的含税金额、税额
        BigDecimal invoiveTax = allFee.multiply(balanceProperties.RRS_COMPANY_TAX_RATE)
                .setScale(2, balanceProperties.ROUND_MODE);
        BigDecimal invoiceFeeSum = allFee.add(invoiveTax);
        //整理发票体DTO
        InvoiceDetail detail = new InvoiceDetail();
        detail.setInvoiceId(invoiceOrderNo);
        detail.setCompany(balanceProperties.RRS_COMPANY_CODE);
        detail.setDetailsQuenc(1);
        detail.setGoodsNo(balanceProperties.GOODS_NO);
        detail.setGoodsName(balanceProperties.GOODS_NAME);
        detail.setModel(balanceProperties.GOODS_MODEL);
        detail.setStoreId(balanceProperties.PRODUCT_UNIT);
        detail.setGoodsNum(BigDecimal.ONE);
        detail.setTaxPrice(invoiceFeeSum);
        detail.setTaxAmount(invoiceFeeSum);
        detail.setNoTaxPrice(allFee);
        detail.setNoTaxAmount(allFee);
        detail.setTexForehead(invoiveTax);
        detail.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE.multiply(BigDecimal.valueOf(100L)));
        detail.setTypesNo(balanceProperties.PRODUCT_TAX_CODE);
        List<InvoiceDetail> invoiceDetailList = new ArrayList<>();
        invoiceDetailList.add(detail);
        //整理发票头DTO
        InvoiceHeader invoiceHeader = new InvoiceHeader();
        invoiceHeader.setInvoiceId(invoiceOrderNo);
        invoiceHeader.setCompany(balanceProperties.RRS_COMPANY_CODE);
        invoiceHeader.setCustomerCode(companyNo);
        invoiceHeader.setCustomerHeading(taxNo);
        invoiceHeader.setCustomerName(companyName);
        invoiceHeader.setCustomerMobile(companyMobile);
        String addressDup = getStringValue(companyMap, "address_dup") != null ? getStringValue(companyMap, "address_dup") : "";
        String[] addr = addressDup.split("_");
        invoiceHeader.setCustomerAddress(addr[addr.length-1]);
        invoiceHeader.setCustomerBank(companyBank);
        invoiceHeader.setCustomerBankNo(bankAccount);
        invoiceHeader.setPayee(balanceProperties.PAYEE);
        invoiceHeader.setReviewer(balanceProperties.REVIEWER);
        invoiceHeader.setInvoiceTypes(balanceProperties.INVOICE_TYPE);
        //调用金税开票接口
        boolean enterprisesFlag = enterprisesService.invoiceToJinShui(invoiceDetailList, invoiceHeader);
        if(!enterprisesFlag){
            throw new RRException("调用金税开票接口失败");
        }
        /*----------------------  金税开票 end ----------------------*/


        /*----------------------  信息入库 start ----------------------*/
        //用最后一个工单来调整尾差
        WorderInformationAccountEntity adjectedWorder = worderList.get(worderList.size() - 1);
        BigDecimal oldFeeSum = adjectedWorder.getCompanyBalanceFeeSum();//原来的含税金额
        BigDecimal oldTax = adjectedWorder.getCompanyBalanceFeeTax();//原来的税额
        BigDecimal oldFee = adjectedWorder.getCompanyBalanceFee();//不含税金额
        BigDecimal newFeeSum = oldFeeSum.add(invoiceFeeSum.subtract(allFeeSum));
        BigDecimal newTax = oldTax.add(invoiveTax.subtract(allTax));
        Integer adjectedWorderId = adjectedWorder.getWorderId();
        String adjectedDesc = new StringBuilder().append(oldFee).append(" ").append(oldTax).append(" ").append(oldFeeSum)
                .append(" >> ").append(oldFee).append(" ").append(newTax).append(" ").append(newFeeSum).toString();
        adjectedWorder.setCompanyBalanceFeeSum(newFeeSum);
        adjectedWorder.setCompanyBalanceFeeTax(newTax);
        //保存推送记录
        BalanceEnterprisesHeaderRecordEntity headerRecordEntity = new BalanceEnterprisesHeaderRecordEntity();
        BeanUtils.copyProperties(invoiceHeader, headerRecordEntity);
        balanceEnterprisesHeaderRecordService.save(headerRecordEntity);
        List<BalanceEnterprisesDetailRecordEntity> detailRecordEntities = new ArrayList<>();
        for (InvoiceDetail d : invoiceDetailList) {
            BalanceEnterprisesDetailRecordEntity de = new BalanceEnterprisesDetailRecordEntity();
            BeanUtils.copyProperties(d, de);
            de.setHeaderId(headerRecordEntity.getId());
            detailRecordEntities.add(de);
        }
        balanceEnterprisesDetailRecordService.saveBatch(detailRecordEntities);
        //保存开票信息
        VendorInvoiceEntity invoiceEntity = new VendorInvoiceEntity();
        invoiceEntity.setInvoiceFee(invoiceFeeSum);
        invoiceEntity.setTax(invoiveTax);
        invoiceEntity.setNoTaxFee(allFee);
        invoiceEntity.setStatus(0);
        invoiceEntity.setVendorId(companyId);
        invoiceEntity.setCreateTime(date);
        invoiceEntity.setInvoiceOrderNo(invoiceOrderNo);
        invoiceEntity.setAdjustedWorderId(adjectedWorderId);
        invoiceEntity.setAdjustedDesc(adjectedDesc);
        invoiceEntity.setNoReceivableFee(invoiceFeeSum);
        invoiceEntity.setReceivableFee(BigDecimal.ZERO);
        invoiceEntity.setNoPaymentFee(BigDecimal.ZERO);
        invoiceEntity.setPaymentFee(BigDecimal.ZERO);
//        vendorInvoiceService.save(invoiceEntity);
        //更新工单结算状态
        for (WorderInformationAccountEntity e : worderList) {
            e.setInvoiceId(invoiceEntity.getId());
            e.setWorderSetStatus(2);
            e.setWorderSetStatusValue("等待开票");
        }
        this.updateBatchById(worderList);
        //更新激励结算状态
        for (WorderPmStimulateEntity e : stimulateList) {
            e.setInvoiceId(invoiceEntity.getId());
            e.setStatus(13);
            e.setPublishTime(date);
            worderPmStimulateService.updateById(e);
        }
        /*----------------------  信息入库 end ----------------------*/
    }

    /**
     * 查询金税是否完成开票
     * @return
     * @param rowId
     */
    @Override
    public InvoiceResults queryVendorInvoiceFinished(String rowId,String companyInvoiceNo){
        Map<String, String> invoiceMap = new HashMap<>();
        invoiceMap.put("orderNo", rowId);
        invoiceMap.put("company", balanceProperties.RRS_COMPANY_CODE);
        InvoiceResults invoiceResults = enterprisesService.getInvoiceResultsFromJinShui(invoiceMap);

        if (null!=invoiceResults){
            if (null!=invoiceResults.getFLAG()) {
                /** 当子发票单号开票成功时，保存子发票单 **/
                /** 保存开票结果 **/
                CompanyInvoiceQueryResultEntity companyInvoiceQueryResultEntity = new CompanyInvoiceQueryResultEntity();
                companyInvoiceQueryResultEntity.setInvoiceCode(invoiceResults.getINVOICECODE());
                companyInvoiceQueryResultEntity.setRn(invoiceResults.getRN());
                companyInvoiceQueryResultEntity.setFlag(invoiceResults.getFLAG());
                companyInvoiceQueryResultEntity.setKprq(invoiceResults.getKPRQ());
                companyInvoiceQueryResultEntity.setNotaxamount(invoiceResults.getNOTAXAMOUNT());
                companyInvoiceQueryResultEntity.setTaxamount(invoiceResults.getTAXAMOUNT());
                companyInvoiceQueryResultEntity.setTotalamount(invoiceResults.getTOTALAMOUNT());
                companyInvoiceQueryResultEntity.setDrawer(invoiceResults.getDRAWER());
                companyInvoiceQueryResultEntity.setDmgs(invoiceResults.getDMGS());
                companyInvoiceQueryResultEntity.setCompanyInvoiceNo(companyInvoiceNo);
                companyInvoiceQueryResultEntity.setInvoiceId(rowId);
                /** 保存开票结果 **/
                companyInvoiceQueryResultService.saveOrUpdate(companyInvoiceQueryResultEntity);
            }
        }
        return invoiceResults;
    }

    private static Integer serialNo = 0;
    private synchronized static String getSerialNumber(){
        String serialStr = serialNo.toString();
        while(serialStr.length()<3){
            serialStr = "0"+serialStr;
        }
        serialNo = (serialNo + 1) % 10000;
        return serialStr;
    }
    private String getStringValue(Map worderMap, Object key){
        Object value = worderMap.get(key);
        return (value==null) ? "" : value.toString();
    }

    private Integer getIntegerValue(Map worderMap, Object key){
        Object value = worderMap.get(key);
        if(null == value){
            return null;
        }
        if(value instanceof Number){
            return ((Number)value).intValue();
        }
        if(value instanceof Boolean){
            boolean b = ((Boolean)value).booleanValue();
            if(b){
                return 1;
            }else {
                return 0;
            }
        }
        return Integer.valueOf(value.toString());
    }

    @Override
    public R querySurplusLimit() {
        AdvanceMoneyInfoEntity advanceMoneyInfoEntity = advanceMoneyInfoService.getAdvanceMoneyInfo();
        return R.ok().put("surplusLimit", advanceMoneyInfoEntity.getSurplusLimit());
    }

    @Override
    public PageUtils queryWorderInfoAduitList(ReceivableFeeAduitVO receivableFeeAduit){
        if(StringUtils.isEmpty(receivableFeeAduit.getCurPage()) || StringUtils.isEmpty(receivableFeeAduit.getLimit())){
            receivableFeeAduit.setCurPage(1);
            receivableFeeAduit.setLimit(10);
        }
        receivableFeeAduit.setPage((receivableFeeAduit.getCurPage() - 1)*receivableFeeAduit.getLimit());
        List<Map<String,Object>> list = new ArrayList();
        Integer totalCount = 0;
        switch (receivableFeeAduit.getBalanceType()){
            case 0:
                receivableFeeAduit.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE);
                list = baseMapper.queryTotalAduitList(receivableFeeAduit);
                totalCount = baseMapper.queryTotalAduitCount(receivableFeeAduit);
                break;
            case 33:
                //工单费用
                list = baseMapper.queryWorderInfoAduitList(receivableFeeAduit);
                totalCount = baseMapper.queryWorderInfoAduitCount(receivableFeeAduit);
                break;
            case 34:
                //网点增项费用
                list = baseMapper.querydotIncreBalanceAduitList(receivableFeeAduit);
                totalCount = baseMapper.querydotIncreBalanceAduitCount(receivableFeeAduit);
                break;
            case 35:
                receivableFeeAduit.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE);
                list = baseMapper.queryWorderStimulateList(receivableFeeAduit);
                totalCount = baseMapper.queryWorderStimulateCount(receivableFeeAduit);
                break;
            case 36:
                // 网点工单费用-新资金
                list = baseMapper.queryBalanceAdwanceMoneyWorderInfoAduitList(receivableFeeAduit);
                totalCount = baseMapper.queryBalanceAdwanceMoneyWorderInfoAduitCount(receivableFeeAduit);
                break;
            case 37:
                // 网点激励费用
                list = baseMapper.queryStimulateInfoAduitList(receivableFeeAduit);
                totalCount = baseMapper.queryStimulateInfoAduitCount(receivableFeeAduit);
                break;
            default:
                System.out.println("balanceType入参异常");
        }
        for (Map<String, Object> map : list) {
            if (StringUtils.isEmpty(map.get("stimulateId"))){
                //查询网点信息
                Map dotMap = this.worderInformationAccountDao.getOneDotAllInfoByDotId((Integer) map.get("dotId"));
                BigDecimal dotTaxRate = new BigDecimal(getStringValue(dotMap, "dotTaxPoint").replaceAll("%", "").trim());
                BigDecimal noFee = new BigDecimal(String.valueOf(map.get("dotIncreBalanceFee")));
                BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                BigDecimal includedMoney = noFee.multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                BigDecimal Tax = includedMoney.subtract(noFee);
                map.put("dotBalanceFeetax",Tax);
                map.put("dotBalanceFeeSum",includedMoney);
            }
        }
        return new PageUtils(list,totalCount == null ? 0 : totalCount, receivableFeeAduit.getCurPage(),receivableFeeAduit.getLimit());
    }

    //已回款工单审核
    @Override
    @Transactional
    public R receivableWorderAduit(ReceivableFeeAduitVO aduitVO){
        WorderAuditInfoEntity auditInfoEntity = new WorderAuditInfoEntity();
        auditInfoEntity.setFromId(aduitVO.getId());
        auditInfoEntity.setOperation(aduitVO.getOperation());
        auditInfoEntity.setBalanceType(aduitVO.getBalanceTypeValue());
        auditInfoEntity.setUserId(aduitVO.getUserId());
        auditInfoEntity.setAuditTime(new Date());
        auditInfoEntity.setAuditStatus(aduitVO.getAuditStatus());
        if(StringUtils.isNotBlank(aduitVO.getRefusalCause())){
            auditInfoEntity.setRefusalCause(aduitVO.getRefusalCause());
        }

        worderAuditInfoDao.insert(auditInfoEntity);
        WorderInformationAccountEntity worderInformation = new WorderInformationAccountEntity();
        worderInformation.setWorderId(auditInfoEntity.getFromId());
        switch (aduitVO.getBalanceType()){
            case 33:
                if(1 == auditInfoEntity.getAuditStatus()){
                    if("首次审核".equals(auditInfoEntity.getOperation())){
                        worderInformation.setWorderSetStatus(11);
                        worderInformation.setWorderSetStatusValue("网点工单首次审核通过");
                    }else if("二次审核".equals(auditInfoEntity.getOperation())){
                        worderInformation.setWorderSetStatus(12);
                        worderInformation.setWorderSetStatusValue("网点工单二次审核通过");
                    }else {
                        worderInformation.setWorderSetStatus(13);
                        worderInformation.setWorderSetStatusValue("网点工单三次审核通过");
                    }
                }else {
                    worderInformation.setWorderSetStatus(14);
                    worderInformation.setWorderSetStatusValue("网点工单审核不通过");
                }
                baseMapper.updateById(worderInformation);
                break;
            case 34:
                if(1 == auditInfoEntity.getAuditStatus()){
                    if("首次审核".equals(auditInfoEntity.getOperation())){
                        worderInformation.setWorderIncreStatus(5);
                        worderInformation.setWorderIncreStatusValue("网点增项首次审核通过");
                    }else if("二次审核".equals(auditInfoEntity.getOperation())){
                        worderInformation.setWorderIncreStatus(6);
                        worderInformation.setWorderIncreStatusValue("网点增项二次审核通过");
                    }else {
                        worderInformation.setWorderIncreStatus(7);
                        worderInformation.setWorderIncreStatusValue("网点增项三次审核通过");
                    }
                }else {
                    worderInformation.setWorderIncreStatus(8);
                    worderInformation.setWorderIncreStatusValue("网点增项审核不通过");
                }
                baseMapper.updateById(worderInformation);
                break;
            case 35:
                WorderPmStimulateEntity pmStimulateEntity = new WorderPmStimulateEntity();
                pmStimulateEntity.setId(aduitVO.getId());
                if(1 == auditInfoEntity.getAuditStatus()){
                    if("首次审核".equals(auditInfoEntity.getOperation())){
                        pmStimulateEntity.setStatus(17);
                    }else if("二次审核".equals(auditInfoEntity.getOperation())){
                        pmStimulateEntity.setStatus(18);
                    }else {
                        pmStimulateEntity.setStatus(19);
                    }
                }else {
                    pmStimulateEntity.setStatus(20);
                }
                worderPmStimulateService.updateById(pmStimulateEntity);
                break;
            default:
                return R.error(8888,"balanceType入参有误");
        }
        return R.ok();
    }


    @Override
    @Transactional
    public R worderReCommitAduit(ReceivableFeeAduitVO aduitVO){
        WorderInformationAccountEntity worderInformation = new WorderInformationAccountEntity();
        worderInformation.setWorderId(aduitVO.getId());
        switch (aduitVO.getBalanceType()){
            case 33:
                worderInformation.setWorderSetStatus(4);
                worderInformation.setWorderSetStatusValue("车企已回款");
//                worderInformation.setDotBalanceFee(aduitVO.getDotIncreBalanceFee());
                baseMapper.updateById(worderInformation);
                break;
            case 34:
                worderInformation.setWorderIncreStatus(1);
                worderInformation.setWorderIncreStatusValue("增项待结算");
//                worderInformation.setDotIncreBalanceFee(aduitVO.getDotIncreBalanceFee());
                baseMapper.updateById(worderInformation);
                break;
            case 35:
                WorderPmStimulateEntity pmStimulateEntity = new WorderPmStimulateEntity();
                pmStimulateEntity.setId(aduitVO.getId());
                pmStimulateEntity.setStatus(12);
//                pmStimulateEntity.setStimulateFee(aduitVO.getDotIncreBalanceFee());
                worderPmStimulateService.updateById(pmStimulateEntity);
                break;
            default:
                return R.error(8888,"balanceType入参有误");
        }
        return R.ok();
    }
    //厂商结算审核
    @Override
    @Transactional
    public R worderInfoAduit(WorderAuditInfoEntity auditInfoEntity){
        WorderInformationAccountEntity worderInformation = new WorderInformationAccountEntity();
        worderInformation.setWorderId(auditInfoEntity.getFromId());
        if(1 == auditInfoEntity.getAuditStatus() && "厂商结算二次审核".equals(auditInfoEntity.getOperation())){
            worderInformation.setWorderSetStatus(1);
            worderInformation.setWorderSetStatusValue("车企待结算");
        }else if(1 == auditInfoEntity.getAuditStatus() && "厂商结算首次审核".equals(auditInfoEntity.getOperation())){
            worderInformation.setWorderSetStatus(8);
            worderInformation.setWorderSetStatusValue("厂商结算首次审核通过");
        }else {
            worderInformation.setWorderSetStatus(9);
            worderInformation.setWorderSetStatusValue("厂商结算审核不通过");
        }
        worderAuditInfoDao.insert(auditInfoEntity);
        baseMapper.updateById(worderInformation);
        return R.ok();
    }

    @Override
    public List getDotInfoList(){
        return baseMapper.getDotInfoList();
    }

//    @Override
//    public void dotWorderBalancePublish(String worderIds) {
//        String[] worderIdArr = worderIds.split(",");
//        List<Integer> ids = new ArrayList<>();
//        for (String s : worderIdArr) {
//            ids.add(Integer.valueOf(s));
//        }
//        List<WorderInformationAccountEntity> list = baseMapper.selectList(
//                new QueryWrapper<WorderInformationAccountEntity>()
////                        .eq("worder_set_status", 4)
//                        .in("worder_id", ids));
//
//        for (WorderInformationAccountEntity worderInformationAccountEntity : list) {
//            this.dotBalanceInvoice(worderInformationAccountEntity);
//        }
//    }
//
//    @Override
//    public void dotIncreBalancePublish(String worderIds) {
//        String[] worderIdArr = worderIds.split(",");
//        List<Integer> ids = new ArrayList<>();
//        for (String s : worderIdArr) {
//            ids.add(Integer.valueOf(s));
//        }
//        List<WorderInformationAccountEntity> list = baseMapper.selectList(
//                new QueryWrapper<WorderInformationAccountEntity>()
////                        .eq("worder_Incre_status", 1)
//                        .in("worder_id", ids));
//
//        for (WorderInformationAccountEntity worderInformationAccountEntity : list) {
//            this.dotIncreBalanceInvoice(worderInformationAccountEntity);
//        }
//    }

    @Override
    public void queryAllAcsAccount() {
        List<BalanceAcsPushRecordEntity> list = balanceAcsPushRecordService.list();
        Set<String> rowIdSet = list.stream().map(e -> e.getRowId()).collect(Collectors.toSet());
        List<String> rowIdList = new ArrayList<>(rowIdSet);
        try {
            acsInterfaceUtil.queryAccountingStatusResult(rowIdList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用ACS查询SAP记账状态接口失败",e);
            throw new RRException("调用ACS查询SAP记账状态接口失败",e);
        }
    }


    /**
     * 逗号隔开的id字符串转为List
     * @param idStr 逗号隔开的ID字符串
     * @return
     */
    private List<Integer> idStr2idList(String idStr){
        List<Integer> list = new ArrayList<>();
        if(org.apache.commons.lang3.StringUtils.isEmpty(idStr)){
            return list;
        }
        String[] ids = idStr.split(",");
        for (String id : ids) {
            list.add(Integer.valueOf(id));
        }
        return list;
    }

}
