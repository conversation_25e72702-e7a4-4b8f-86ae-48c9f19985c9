package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by liqingchao on 2020/4/23.
 */
@Data
@TableName("balance_cvp_bill")
public class CvpBillEntity implements Serializable {
    @TableId
    private Integer id;//ID
    private String billNo;//  对账单号
    private String statusFlag;//  发票状态。0:未确认1:已确认2:已开票C:已作废R已入账G:已传BCC付款P:付款完成
    private Date payDate;
    private Date accountDate;
    private Date invoiceDate;
    private String batchNo;//  合并开票时的唯一标志，用来关联发票和账单
    private String billList;//合并开票的账单信息
    private String invoiceIds;//关联的发票
}
