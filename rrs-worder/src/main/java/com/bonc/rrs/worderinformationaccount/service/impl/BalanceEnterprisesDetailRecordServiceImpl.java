package com.bonc.rrs.worderinformationaccount.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.worderinformationaccount.dao.BalanceEnterprisesDetailRecordDao;
import com.bonc.rrs.worderinformationaccount.entity.BalanceEnterprisesDetailRecordEntity;
import com.bonc.rrs.worderinformationaccount.service.BalanceEnterprisesDetailRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by liqingchao on 2020/4/23.
 */
@Service("balanceEnterprisesDetailRecordService")
public class BalanceEnterprisesDetailRecordServiceImpl extends ServiceImpl<BalanceEnterprisesDetailRecordDao, BalanceEnterprisesDetailRecordEntity> implements BalanceEnterprisesDetailRecordService {
    @Override
    public List<BalanceEnterprisesDetailRecordEntity> listSumByInvoiceId(List<Integer> invoiceIdList) {
        return baseMapper.listSumByInvoiceId(invoiceIdList);
    }
}
