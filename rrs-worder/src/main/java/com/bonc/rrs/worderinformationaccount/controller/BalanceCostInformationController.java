package com.bonc.rrs.worderinformationaccount.controller;

import com.bonc.rrs.worderinformationaccount.entity.CostInformationEntity;
import com.bonc.rrs.worderinformationaccount.service.CostInformationService;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangyibo on 2020-12-16 11:53
 */

@RestController
@RequestMapping("/balance/cost/info")
public class BalanceCostInformationController {

    @Autowired(required = false)
    CostInformationService costInformationService;

//    @RequestMapping("/test")
    public R test(){
        CostInformationEntity costInformationEntity = new CostInformationEntity();
        costInformationEntity.setRowId("ZLW500000000435");

        List<CostInformationEntity> costInfoList = new ArrayList();
        costInfoList.add(costInformationEntity);
        Boolean flag = costInformationService.saveOrUpdateBathchByRowId(costInfoList);
        if(flag){
            return R.ok();
        }else {
            return R.error();
        }
    }
}
