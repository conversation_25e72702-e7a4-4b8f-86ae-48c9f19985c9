package com.bonc.rrs.worderinformationaccount.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worderinformationaccount.entity.CostInformationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Created by liqingchao on 2020/4/1.
 */
@Mapper
public interface CostInformationDao extends BaseMapper<CostInformationEntity>{

    CostInformationEntity getCostByWorderId(@Param("id") Integer worderId);

    CostInformationEntity getCostByIncreId(@Param("id") Integer worderId);

    CostInformationEntity getCostByStimulateId(@Param("id") Integer worderId);

}
