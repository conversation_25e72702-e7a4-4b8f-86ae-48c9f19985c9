package com.bonc.rrs.worderinformationaccount.entity;



import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/4/22.
 * ACS推送记录表实体类
 */
@Data
@TableName("balance_acs_push_record")
public class BalanceAcsPushRecordEntity implements Serializable {
    @TableId
    private Integer id;
    private Date pushTime;

    private String rowId;

    private Integer orderId;

    private Integer orderItemId;

    private Integer accountId;
    private String supplierVcode;

    private Integer accountBranch;

    private Integer accountType;

    private String orderCode;

    private String itemCode;
    /** 业务单据号 N*/
    private String orderNo;
    /** 支付单号 N*/
    private String payNo;
    /** 订单数量 N*/
    private Integer orderCount;
    /**数据来源 N*/
    private String source;
    /**单据模式 10采销，20平台，30分销，40 预约柜 N*/
    private String sourceMode;
    /**单据类型 */
    private String sourceType;
    /**分中心编码 */
    private String branchCode;
    /**区域1 */
    private String subRegion1;
    /**区域2 */
    private String subRegion2;
    /**正逆向标记  N*/
    private String orderType;
    /**正付款时间  N*/
    private Date orderDate;
    /**订单金额  N*/
    private BigDecimal orderAmount;
    /**支付方式 N*/
    private String payType;
    /**业务模式 N*/
    private String ywms;
    /**交易类型 N*/
    private String jylx;
    /**公司代码 N*/
    private String bukrs;
    /**过账日期 N*/
    private String budat;
    /**抬头文本 凭证的抬头例如：6.18商城支付宝 N*/
    private String bktxt;
    /**参照字段 传发票号，没有传空*/
    private String xblnr;
    /**货币码 默认，"CNY"  N*/
    private String waers;
    /**客户编码 一次性用户编码或商户的编码,根据不同业务模式看是否必填 N*/
    private String kunnr;
    /**供应商编码  第三方支付V编码，根据不同业务模式看是否必填 N*/
    private String lifnr;
    /**是否是一次性客户（Y/N）*/
    private String ifOnce;
    /**个人姓名 一次性客户时必填*/
    private String name;
    /**城市 一次性客户时必填*/
    private String city;
    /**上账金额（包含手续费）或含税金额*/
    //@DecimalMin(value = "0.01", message = "dmbtr最小值不能小于0.01")
    private BigDecimal dmbtr;
    /**手续费金额 N*/
    private BigDecimal dmbtr1;
    /**税金 N*/
    private BigDecimal dmbtr2;
    /**提现金额 N*/
    private BigDecimal dmbtr3;
    /**行项目单据号 N*/
    private String xref3;
    /**行项目文本 N*/
    private String sgtxt;
    /**行项目分配编号*/
    private String zuonr;
    private BigDecimal attribute1;

    private String attribute2;

    private BigDecimal attribute3;

    private String attribute4;

    private String attribute5;

    private String attribute6;

    private String attribute7;

    private String attribute8;

    private String attribute9;

    private String attribute10;

    private String attribute11;

    private String attribute12;

    private String attribute13;

    private BigDecimal attribute14;

    private BigDecimal attribute15;

    private String attribute16;

    private String attribute17;

    private String attribute18;

    private String attribute19;

    private String attribute20;

    private String attribute21;

    private String attribute22;

    private String attribute23;

    private String attribute24;

    private String attribute25;

    private String accountFlag;

    private String accountYear;

    private String accountCode;

    private String accountDesc;

    private Date accountDate;

    private Date accountCreateTime;

    private Integer xgjStatus;

    private String inviteCode;

    private String errorMsg;

    private Integer sendTimes;

    private Integer withdrawId;

    private Integer payId;

    private Integer bccSumid;

    private Date createTime;

    private Date updateTime;

    private Integer lfsStatus;

    private BigDecimal bccInvoiceTaxAmount;

    private Integer settleOrderId;

    private Integer transferSysStatus;

    private String odsAccountingInfoDetail;


}
