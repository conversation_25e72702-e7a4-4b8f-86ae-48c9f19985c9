/**
 * Copyright (C), 2024,
 */
package com.bonc.rrs.worderinformationaccount.common;

import com.bonc.rrs.sequence.service.SequenceService;
import com.bonc.rrs.workManager.service.AutoSendService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/13 10:53
 * @Version 1.0.0
 */

@Component("reSendWorderFWJLTask")
public class ReSendWorderFWJLTask implements ITask {

    @Autowired
    AutoSendService autoSendService;
    private static final Logger log = LoggerFactory.getLogger(ReSendWorderFWJLTask.class);
    @Override
    public void run(String params) {
        log.info("重新派单 reSendWorderTask 开始");
        autoSendService.reSendWorderFWJL(params);
        log.info("重新派单 reSendWorderTask 结束");
    }
}