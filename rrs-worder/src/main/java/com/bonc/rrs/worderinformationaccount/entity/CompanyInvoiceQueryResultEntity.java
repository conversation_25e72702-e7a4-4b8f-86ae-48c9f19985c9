package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyibo on 2020-09-22 16:34
 */

@TableName("company_invoice_query_result")
@Data
public class CompanyInvoiceQueryResultEntity implements Serializable {

    @TableId
    private Integer id;

    /**
     * 开票单号
     */
    private String companyInvoiceNo;

    /**
     * 发票代码(金税反馈数据)
     */
    private String invoiceCode;
    /**
     * 开票号(关联balance_enterprises_detail_record的invoice_id)
     */
    private String invoiceId;
    /**
     * (金税反馈数据)
     */
    private String rn;
    /**
     * (金税反馈数据)
     */
    private String flag;
    /**
     * (金税反馈数据)
     */
    private String kprq;
    /**
     * (金税反馈数据)
     */
    private String notaxamount;
    /**
     * (金税反馈数据)
     */
    private String taxamount;
    /**
     * (金税反馈数据)
     */
    private String totalamount;
    /**
     * (金税反馈数据)
     */
    private String drawer;
    /**
     * (金税反馈数据)
     */
    private String dmgs;

}
