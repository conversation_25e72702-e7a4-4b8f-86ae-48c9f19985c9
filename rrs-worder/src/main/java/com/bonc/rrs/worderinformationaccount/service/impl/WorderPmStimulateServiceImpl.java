package com.bonc.rrs.worderinformationaccount.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.worderinformationaccount.dao.WorderPmStimulateDao;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import com.bonc.rrs.worderinformationaccount.service.WorderPmStimulateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by liqingchao on 2020/4/27.
 */
@Service("worderPmStimulateService")
public class WorderPmStimulateServiceImpl extends ServiceImpl<WorderPmStimulateDao, WorderPmStimulateEntity>
        implements WorderPmStimulateService {
    @Override
    public List<WorderPmStimulateEntity> queryDotStimulateForBalance() {
        return baseMapper.queryDotStimulateForBalance();
    }

    @Override
    public List<WorderPmStimulateEntity> selectNotBalanceStimulateOnBalanceWorderByCompanyId(Integer companyId) {
        return baseMapper.selectNotBalanceStimulateOnBalanceWorderByCompanyId(companyId);
    }
}
