package com.bonc.rrs.worderinformationaccount.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.worderinformationaccount.dao.CostInformationDao;
import com.bonc.rrs.worderinformationaccount.dao.WorderInformationAccountDao;
import com.bonc.rrs.worderinformationaccount.entity.CostInformationEntity;
import com.bonc.rrs.worderinformationaccount.service.CostInformationService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by liqingchao on 2020/4/1.
 */
@Service("costInformationService")
public class CostInformationServiceImpl extends ServiceImpl<CostInformationDao, CostInformationEntity> implements CostInformationService {


    @Override
    public Boolean saveOrUpdateBathchByRowId(List<CostInformationEntity> entityList) {
        Integer count = IntegerEnum.ZERO.getValue();
        for (CostInformationEntity costInfo : entityList) {
            String rowId = costInfo.getRowId();
            baseMapper.delete(new QueryWrapper<CostInformationEntity>().eq("row_id", rowId));
            count += baseMapper.insert(costInfo);
        }
        return count.equals(entityList.size());
    }


    @Override
    public void saveOrUpdateBathch(List<CostInformationEntity> entityList) {
        List<String> rowIds = entityList.stream().map(CostInformationEntity::getRowId).collect(Collectors.toList());
        if(rowIds.size() > 0){
            baseMapper.delete(new QueryWrapper<CostInformationEntity>().in("row_id", rowIds));
            this.saveBatch(entityList);
        }
    }

    @Override
    public CostInformationEntity getCostByWorderId(Integer worderId){
        return baseMapper.getCostByWorderId(worderId);
    }

    @Override
    public CostInformationEntity getCostByIncreId(Integer worderId){
        return baseMapper.getCostByIncreId(worderId);
    }

    @Override
    public CostInformationEntity getCostByStimulateId(Integer worderId){
        return baseMapper.getCostByStimulateId(worderId);
    }
}
