package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/1/20.
 * 网点查询结算工单的数据对象
 */
@Data
@TableName("worder_pm_stimulate")
@ApiModel(value = "正负激励表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorderPmStimulateEntity implements Serializable {
    //ID
    @TableId
    private Integer id;
    //工单id
    private Integer worderId;
    //网点id
    private Integer dotId;
    //激励对象类型(10：网点   11：日日顺)
    private Integer stimulateType;
    //激励审核状态(10：待审核 11：已退回 12：待结算 13：已发布 14：已结算 15：已通过 16：不通过 17:激励首次审核通过 18:激励二次审核通过 19:激励三次审核通过 20:发布审核不通过 21:等待记账 22:记账成功)
    private Integer status;
    //不含税金额
    private BigDecimal stimulateFee;
    //税额
    private BigDecimal storageTax;
    //含税金额
    private BigDecimal priceTax;
    //激励原因 从字典表获取
    private String stimulateReason;

    @TableField(exist = false)
    private String stimulateReasonValue; //奖惩类型（字典表）即激励原因冗余
    //创建时间
    private Date createTime;
    //发票ID，激励对象为日日顺时使用
    private Integer invoiceId;
    //成本ID，激励对象为网点时使用
    private Integer costId;
    //发布时间
    private Date publishTime;
    //奖惩发起人id
    private Long userId;
    //奖惩发起人名称
    private String userName;
    /**
     * 正负激励 0:正激励 1:负激励
     */
    private Integer incentiveType;
    /**
     * 价格类型：0：含税价，1：不含税价
     */
    @TableField(exist = false)
    private Integer priceType;

    @TableField(exist = false)
    private String priceTypeValue;
    /**
     * 是否删除 0：未删除 1：已删除
     */
    private Integer isDelete;

    /**
     * 发布状态 1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:已发布 -1:第一次审核不通过 -2:第二次审核不通过
     */
    private Integer publishStatus;

    /**
     * 税点
     */
    private String taxPoint;

    /**
     * 结算金额
     */
    private BigDecimal balanceFee;

    /**
     * 结算金额(含税)
     */
    private BigDecimal balanceFeeTax;

    /**
     * 税点
     */
    private BigDecimal feeTax;

    /**
     * 原因类型(0:正常 1:特殊)
     */
    private Integer reasonType;

    /**
     * 正负激励 0:正激励 1:负激励
     */
    @TableField(exist = false)
    private String incentiveTypeValue; //激励类型（字典表）即激励原因冗余

    private String firstAuditUser;
    private Date firstAuditTime;
    private String secondAuditUser;
    private Date secondAuditTime;
}
