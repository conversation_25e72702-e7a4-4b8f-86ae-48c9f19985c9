package com.bonc.rrs.worderinformationaccount.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.datatype.XMLGregorianCalendar;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/4/2.
 */
@Data
@TableName("balance_cvp_invoice")
public class CvpInvoiceEntity implements Serializable{
    @TableId
    private Integer id;
    private String invoiceCode;
    private String invoiceNo;
    @TableField("invoice_date")
    private Date invoiceTime;
    private String invoiceType;
    private String buyerAddrTel;
    private String buyerBank;
    private String buyerIdentifyNo;
    private String buyerName;
    private String saleAddrTel;
    private String saleBank;
    private String saleIdentifyNo;
    private String saleName;
    private String serviceName;
    private String sheetNo;
    private BigDecimal taxAmt;
    private BigDecimal taxExcludeAmt;
    private BigDecimal taxRate;
    private BigDecimal taxTotalAmt;
    private String add1;
    private String add2;
    private String add3;
    private String add4;
    private String add5;
}
