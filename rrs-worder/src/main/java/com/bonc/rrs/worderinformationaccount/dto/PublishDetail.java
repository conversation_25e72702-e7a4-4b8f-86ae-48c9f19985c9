package com.bonc.rrs.worderinformationaccount.dto;

import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by liqingchao on 2020/4/25.
 */
@Data
public class PublishDetail implements Serializable{
    private List<Integer> worderIds = new ArrayList<>();
    private List<Integer> increIds = new ArrayList<>();
    private List<Integer> stimulateIds = new ArrayList<>();
}
