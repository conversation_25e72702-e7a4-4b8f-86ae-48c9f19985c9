package com.bonc.rrs.ca.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReceiveAfterSaleCheckRequest {

    @NotBlank
    private String detailNo;

    @NotNull
    private Integer aftersaleId;

    @NotNull
    private BigDecimal applyPrice;

    @NotBlank
    private String checkUserName;

    /**
     * 审核结果 0未通过 1通过
     */
    @NotNull
    private Integer checkResult;

    @NotBlank
    private String checkRemark;

    @NotNull
    private Date checkTime;
}
