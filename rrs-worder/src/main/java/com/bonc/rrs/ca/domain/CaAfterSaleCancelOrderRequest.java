package com.bonc.rrs.ca.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class CaAfterSaleCancelOrderRequest {

    @NotBlank(message = "订单详情号不能为空")
    private String detailNo;

    /**
     *
     */
    @NotNull(message = "订单退款编码不能为空")
    private Integer aftersaleId;

    /**
     *
     */
    @NotBlank(message = "下单时间不能为空")
    private String addTime;

    /**
     *
     */
    @NotBlank(message = "商品编号不能为空")
    private String commodityCode;

    /**
     *
     */
    @NotBlank(message = "商品名称不能为空")
    private String commodityName;

    /**
     *
     */
    @NotBlank(message = "商品sku编号不能为空")
    private String commoditySkuCode;

    /**
     *
     */
    @NotBlank(message = "商品sku名称不能为空")
    private String commoditySkuName;

    private BigDecimal commoditySkuPrice;

    /**
     *
     */
    @NotNull(message = "购买数量不能为空")
    private Integer commoditySkuNumber;

    /**
     *
     */
    @NotNull(message = "实付费用不能为空")
    private BigDecimal actualPrice;

    /**
     *
     */
    @NotNull(message = "⽀付⽅式不能为空")
    private Integer payMode;

    /**
     *
     */
    @NotBlank(message = "⽀付时间不能为空")
    private String payTime;

    /**
     *
     */
    @NotBlank(message = "购买用户姓名不能为空")
    private String userName;

    /**
     *
     */
    @NotBlank(message = "购买用户电话不能为空")
    private String userPhone;

    /**
     *
     */
    @NotBlank(message = "车架号不能为空")
    private String vehicleVin;

    /**
     *
     */
    @NotBlank(message = "安装联系⼈姓名不能为空")
    private String consigneeName;

    /**
     *
     */
    @NotBlank(message = "安装联系⼈电话不能为空")
    private String consigneePhone;

    /**
     *
     */
    @NotBlank(message = "安装联系人地址不能为空")
    private String consigneeAddress;

    /**
     *
     */
    @NotBlank(message = "留⾔备注不能为空")
    private String consigneeRemark;

    /**
     *
     */
    @NotBlank(message = "订单状态不能为空")
    private String orderStatus;

    /**
     *
     */
    @NotNull(message = "申请原因不能为空")
    private Integer applyReason;

    /**
     *
     */
    @NotBlank(message = "申请时间不能为空")
    private String applyDatetime;

    /**
     *
     */
    @NotNull(message = "退款金额不能为空")
    private BigDecimal applyPrice;

    /**
     *
     */
    @NotBlank(message = "申请人名称不能为空")
    private String applyUserName;

    /**
     *
     */
    @NotBlank(message = "申请人电话不能为空")
    private String applyUserPhone;

    /**
     *
     */
    @NotBlank(message = "申请详细描述不能为空")
    private String applyRemark;

    /**
     *
     */
    @NotBlank(message = "申请图片不能为空")
    private String applyPics;

    /**
     *
     */
    @NotNull(message = "退款进度不能为空")
    private Integer refundStatus;

    /**
     * 备注
     */
    private String remark;

}