package com.bonc.rrs.ca.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.ca.domain.*;
import com.bonc.rrs.ca.service.CaBizService;
import com.bonc.rrs.ca.service.CaMdmRefundInfoService;
import com.bonc.rrs.ca.service.ICaApiService;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.util.UserUtil;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.common.IdempotentCheck;
import com.bonc.rrs.worder.constant.AttributeCodeEnum;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.constant.IdempotentConstant;
import com.bonc.rrs.worder.constant.RedisConstant;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dto.dto.IncreaseFeeDto;
import com.bonc.rrs.worder.dto.vo.WorderMaterielVo;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.entity.po.IdempotentPo;
import com.bonc.rrs.worder.service.*;
import com.bonc.rrs.worderapp.dao.WorderOrderDao;
import com.bonc.rrs.worderapp.entity.dto.WorderInformationDto;
import com.bonc.rrs.worderapp.service.WorderOperationRecodeService;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.apache.velocity.util.Pair;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class CaBizServiceImpl implements CaBizService {

    private final BizRegionService bizRegionService;
    private final WorderTemplateService worderTemplateService;
    private final WorderInformationService worderInformationService;
    private final WorderIntfMessageService worderIntfMessageService;
    private final CaMdmRefundInfoService caMdmRefundInfoService;
    private final WorderInformationAttributeDao worderInformationAttributeDao;
    private final WorderInformationAttributeService worderInformationAttributeService;
    private final WorderRemarkLogService worderRemarkLogService;
    private final WorderOperationRecodeService worderOperationRecodeService;
    private final FlowCommon flowCommon;
    private final WorderOrderDao worderOrderDao;
    private final IdempotentCheck idempotentCheck;

    private final WorderExtFieldService worderExtFieldService;

    private final BizAttendantService bizAttendantService;

    private final ICaApiService caApiService;

    @Override
    public CaApiResponse saveOrder(CaOrderApiVO orderApiVO) {
        UserUtil.createDefaultLoginUser();
        if (worderInformationService.validateCompanyOrderNumberAndBrandExsitByBrandId(orderApiVO.getDetailNo(), String.valueOf(determineBrand(orderApiVO)))) {
            try {
                WorderIntfMessageEntity intfMessageEntity = worderIntfMessageService.getOneByOrderCode(String.valueOf(1), orderApiVO.getDetailNo());
                if (intfMessageEntity == null) {
                    WorderIntfMessageEntity messageEntity = WorderIntfMessageEntity.builder()
                            .intfCode("pushOrder")
                            .worderId(0)
                            .bid(1)
                            .data(JSON.toJSONString(orderApiVO))
                            .createTime(new Date())
                            .isTransfer(0)
                            .messageType(0)
                            .orderCode(orderApiVO.getDetailNo())
                            .build();
                    worderIntfMessageService.save(messageEntity);
                } else if (intfMessageEntity.getMessageType() != 1 || intfMessageEntity.getWorderId() == null) {
                    if (intfMessageEntity.getWorderId() == null) {
                        WorderInformationEntity byCompanyWorderNo = worderInformationService.getByCompanyWorderNo(orderApiVO.getDetailNo());
                        intfMessageEntity.setWorderId(byCompanyWorderNo.getWorderId());
                    }
                    intfMessageEntity.setMessageType(1);
                    worderIntfMessageService.updateById(intfMessageEntity);
                }
            } catch (Exception e) {
                log.error("保存报文失败:{}", JSON.toJSONString(orderApiVO), e);
            }

            return CaApiResponse.success();
        }

        WorderIntfMessageEntity messageEntity = WorderIntfMessageEntity.builder()
                .intfCode("pushOrder")
                .worderId(0)
                .bid(1)
                .data(JSON.toJSONString(orderApiVO))
                .createTime(new Date())
                .isTransfer(0)
                .messageType(0)
                .orderCode(orderApiVO.getDetailNo())
                .build();
        worderIntfMessageService.save(messageEntity);

        Pair<Long, String> worderIdNoPair = null;
        String worderNo = null;
        try {
            worderIdNoPair = ((CaBizService) AopContext.currentProxy()).doParseDataAndSaveOrder(orderApiVO);
            worderIntfMessageService.updateWorderIdById(messageEntity.getId(), Math.toIntExact(worderIdNoPair.getFirst()));
            worderNo = worderIdNoPair.getSecond();
        } catch (Exception e) {
            log.error("创建长安工单失败", e);
            SmsUtil.sendSms("15910305046", "门到门用户下单推送失败,车企订单号:" + orderApiVO.getDetailNo() + e.getMessage(), "【到每家科技服务】");
            worderIntfMessageService.updateMessageTypeById(messageEntity.getId(), 2, e.getMessage());
            throw e;
        }

        try {
            Results results = worderInformationService.goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME, null);
            if (results.getCode() != 0) {
                throw new RRException(results.getCode() + results.getMsg());
            }
            // 修改工单状态 0
            worderInformationService.updateWorderStatus(worderNo);
        } catch (Exception e) {
            log.error("{}派单失败", worderNo, e);
            SmsUtil.sendSms("15910305046", "门到门订单派单失败,原因:"+ e.getMessage() +",车企订单号:" + orderApiVO.getDetailNo(), "【到每家科技服务】");
        }
        return CaApiResponse.success();
    }

    @Lock4j(keys = "#orderApiVO.detailNo")
    @Override
    public Pair<Long, String> doParseDataAndSaveOrder(CaOrderApiVO orderApiVO) {
        Integer brandId = determineBrand(orderApiVO);
        // region
        RegionCode regionCode = determineRegion(orderApiVO);
        List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(
                brandId,
                WorderTypeEnum.SERVE_CONVEY_INSTALL.getId(),
                regionCode.getProvinceCode().intValue(),
                regionCode.getCityCode().intValue()
        );

        if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
            log.info("message save order " + orderApiVO.getDetailNo() + " 没有对应的工单模板");
            throw new RRException("Failed to save order " + orderApiVO.getDetailNo());
        }

        WorderTemplateDto worderTemplateDto = worderTemplateDtoList.get(0);

        if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(orderApiVO.getDetailNo(), worderTemplateDto.getTemplateId())) {
            log.info("message save order " + orderApiVO.getDetailNo() + " 车企订单号已存在，无法创建工单");
            throw new RRException("Failed to save order " + orderApiVO.getDetailNo());
        }

        // 匹配表情符
        String regex = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";

        // 745	8800517676	重庆长安汽车客户服务有限公司
        Integer companyId = 745;

        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        String address =
                regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + regionCode.getDetailedAddress();
        address = address.replaceAll(regex, "");

        String userName = orderApiVO.getConsigneeName();
        userName = userName.replaceAll(regex, "");

        String dispatchTime = orderApiVO.getAddTime();
        String vin = orderApiVO.getVehicleVin();
        String contactRemark = "";
        if (StringUtils.isNotBlank(orderApiVO.getConsigneeRemark())) {
            contactRemark = orderApiVO.getConsigneeRemark().replaceAll(regex, "");
        }
        worderInfoEntity.setPushOrderWorderSource("ca");
        worderInfoEntity.setUserName(userName);
        worderInfoEntity.setUserPhone(orderApiVO.getConsigneePhone());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(orderApiVO.getDetailNo());
        worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());

        worderInfoEntity.setCarBrand(String.valueOf(brandId));
        worderInfoEntity.setCarModel("4");
        worderInfoEntity.setCompanyId(companyId);

        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderSourceTypeValue("");
        worderInfoEntity.setWorderTypeId(5);

        worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
        worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1, "工单类型", worderInfoEntity.getWorderTypeId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(101, "车企名称", companyId));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(154, "车企派单日期", DateUtils.parseTimestamp(dispatchTime)));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(153, "VIN 车架号", vin));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(305, "备注-创建", contactRemark));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(306, "工单来源", ""));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(902, "客户姓名", worderInfoEntity.getUserName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(903, "安装地址", address));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(905, "客户手机", worderInfoEntity.getUserPhone()));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1694, "充电桩功率", orderApiVO.getCommodityName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1787, "商品名称", orderApiVO.getCommodityName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1781, "商品编号", orderApiVO.getCommodityCode()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1782, "商品sku编号", orderApiVO.getCommoditySkuCode()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1783, "商品sku名称", orderApiVO.getCommoditySkuName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1784, "商品sku价格", orderApiVO.getCommoditySkuPrice()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1785, "购买数量", orderApiVO.getCommoditySkuNumber()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1786, "实际费用", orderApiVO.getActualPrice()));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1775, "长安服务时间", ""));

        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);

        R r = worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
        if (!r.isOk()) {
            throw new RRException("save worder information error");
        }
        return new Pair<>((Long) r.get("worderId"),(String) r.get("worderNo"));
    }

    @Override
    public String dealWithOrder(String worderNo) {
        WorderInformationEntity worderInformationEntity = worderInformationService.getOne(
                new QueryWrapper<WorderInformationEntity>()
                        .eq("worder_no", worderNo)
        );
        if (worderInformationEntity == null) {
            return "工单不存在";
        }
        List<WorderExtFieldEntity> entitys = worderExtFieldService.getFieldsByWorderNo(worderNo);
        // 270	电源点图
        WorderExtFieldEntity f1 = entitys.stream().filter(e -> e.getFieldId().equals(270)).findFirst().orElse(null);
        //车位图
        WorderExtFieldEntity f2 = entitys.stream().filter(e -> e.getFieldId().equals(274)).findFirst().orElse(null);

        //编码
        WorderExtFieldEntity f3 = entitys.stream().filter(e -> e.getFieldId().equals(950)).findFirst().orElse(null);
        String code = "";
        if (f3 != null && StringUtils.isNotBlank(f3.getFieldValue())) {
            if (f3.getFieldValue().startsWith("86")) {
                code = "上海挚达科技发展股份有限公司";
            } else if (f3.getFieldValue().startsWith("34")) {
                code = "科大智能电气技术有限公司";
            } else if (f3.getFieldValue().startsWith("66")) {
                code = "上海循道新能源科技有限公司";
            }
        }
        List<WorderExtFieldEntity> fields = new ArrayList<>();
        WorderExtFieldEntity entity = WorderExtFieldEntity.create(1775, "长安服务时间", "");
        WorderExtFieldEntity entity2 = WorderExtFieldEntity.create(1778, "充电桩服务商", code);
        WorderExtFieldEntity entity3 = WorderExtFieldEntity.create(264, "电源点照片", f1 != null ? f1.getFieldValue() : "");
        WorderExtFieldEntity entity4 = WorderExtFieldEntity.create(265, "车位图", f2 != null ? f2.getFieldValue() : "");
        entity.setWorderNo(worderNo);
        entity.setCreateTime(new Date());

        entity2.setWorderNo(worderNo);
        entity2.setCreateTime(new Date());
        entity3.setWorderNo(worderNo);
        entity3.setCreateTime(new Date());
        entity4.setWorderNo(worderNo);
        entity4.setCreateTime(new Date());
        if (entitys.stream().filter(e -> e.getFieldId().equals(1775)).findFirst().orElse(null) == null) {
            fields.add(entity);
        }

        if (entitys.stream().filter(e -> e.getFieldId().equals(1778)).findFirst().orElse(null) == null) {
            fields.add(entity2);
        }
        if (entitys.stream().filter(e -> e.getFieldId().equals(264)).findFirst().orElse(null) == null) {
            fields.add(entity3);
        }
        if (entitys.stream().filter(e -> e.getFieldId().equals(265)).findFirst().orElse(null) == null) {
            fields.add(entity4);
        }
        if (!fields.isEmpty()) {
            worderExtFieldService.saveBatch(fields);
        }
        WorderInformationAttributeEntity worderInformationAttributeEntity =
                worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "worder_source", "pushOrder");
        if (worderInformationAttributeEntity == null) {
            WorderInformationAttributeEntity attributeEntity = new WorderInformationAttributeEntity();
            attributeEntity.setWorderId(worderInformationEntity.getWorderId());
            attributeEntity.setAttributeCode("worder_source");
            attributeEntity.setAttributeName("工单来源");
            attributeEntity.setAttributeValue("ca");
            attributeEntity.setAttribute("pushOrder");
            worderInformationAttributeDao.insert(attributeEntity);
        }

        WorderInformationAttributeEntity worderInformationAttributeEntity2 =
                worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_overProof", "ca");
        if (worderInformationAttributeEntity2 == null) {
            WorderInformationAttributeEntity attributeEntity2 = new WorderInformationAttributeEntity();
            attributeEntity2.setWorderId(worderInformationEntity.getWorderId());
            attributeEntity2.setAttributeCode("push_overProof");
            attributeEntity2.setAttributeName("推送增项-安装");
            attributeEntity2.setAttributeValue("0");
            attributeEntity2.setAttribute("ca");
            worderInformationAttributeDao.insert(attributeEntity2);
        }

        WorderInformationAttributeEntity worderInformationAttributeEntity3 =
                worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_order_complete", "ca");
        if (worderInformationAttributeEntity3 == null) {
            WorderInformationAttributeEntity attributeEntity3 = new WorderInformationAttributeEntity();
            attributeEntity3.setWorderId(worderInformationEntity.getWorderId());
            attributeEntity3.setAttributeCode("push_order_complete");
            attributeEntity3.setAttributeName("推送订单完成");
            attributeEntity3.setAttributeValue("0");
            attributeEntity3.setAttribute("ca");
            worderInformationAttributeDao.insert(attributeEntity3);
        }

        WorderInformationAttributeEntity worderInformationAttributeEntity4 =
                worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_ammeterStatus", "ca");
        if (worderInformationAttributeEntity4 == null) {
            WorderInformationAttributeEntity attributeEntity4 = new WorderInformationAttributeEntity();
            attributeEntity4.setWorderId(worderInformationEntity.getWorderId());
            attributeEntity4.setAttributeCode("push_ammeterStatus");
            attributeEntity4.setAttributeName("推送电表安装状态");
            attributeEntity4.setAttributeValue("0");
            attributeEntity4.setAttribute("ca");
            worderInformationAttributeDao.insert(attributeEntity4);
        }

        WorderInformationAttributeEntity worderInformationAttributeEntity5 =
                worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_updateFirstcontactTime", "ca");
        if (worderInformationAttributeEntity5 == null) {
            WorderInformationAttributeEntity attributeEntity5 = new WorderInformationAttributeEntity();
            attributeEntity5.setWorderId(worderInformationEntity.getWorderId());
            attributeEntity5.setAttributeCode("push_updateFirstcontactTime");
            attributeEntity5.setAttributeName("推送首联用户时间");
            attributeEntity5.setAttributeValue("0");
            attributeEntity5.setAttribute("ca");
            worderInformationAttributeDao.insert(attributeEntity5);
        }

        WorderInformationAttributeEntity worderInformationAttributeEntity6 =
                worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_serviceTime", "ca");
        if (worderInformationAttributeEntity6 == null) {
            WorderInformationAttributeEntity attributeEntity6 = new WorderInformationAttributeEntity();
            attributeEntity6.setWorderId(worderInformationEntity.getWorderId());
            attributeEntity6.setAttributeCode("push_serviceTime");
            attributeEntity6.setAttributeName("推送首联用户时间");
            attributeEntity6.setAttributeValue("0");
            attributeEntity6.setAttribute("ca");
            worderInformationAttributeDao.insert(attributeEntity6);
        }

        WorderInformationAttributeEntity worderInformationAttributeEntity7 =
                worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_canInstallOrders", "ca");
        if (worderInformationAttributeEntity7 == null) {
            WorderInformationAttributeEntity attributeEntity7 = new WorderInformationAttributeEntity();
            attributeEntity7.setWorderId(worderInformationEntity.getWorderId());
            attributeEntity7.setAttributeCode("push_canInstallOrders");
            attributeEntity7.setAttributeName("推送确认安装条件-勘测");
            attributeEntity7.setAttributeValue("0");
            attributeEntity7.setAttribute("ca");
            worderInformationAttributeDao.insert(attributeEntity7);
        }

        log.info("处理历史长安订单 {} 补充额外属性成功", worderNo);
        //如果是分配中 不处理
        if (worderInformationEntity.getWorderStatus() == 0) {
            return null;
        }

        log.info("处理历史长安订单 {} 步骤一开始", worderNo);
        //处理网点派单给服务兵上传
        //news-->门到门    门到门服务时间
        //news-->门到门    同步门到门电表安装状态
        //news-->门到门    同步带有安装师傅的订单
        String result1 = dealWithOrderSend(worderInformationEntity);
        if (StringUtils.isNotBlank(result1)) {
            return result1;
        }
        log.info("处理历史长安订单 {} 步骤一结束", worderNo);
        //如果是待勘测预约
        if (worderInformationEntity.getWorderExecStatus() == 2) {
            return "";
        }

        log.info("处理历史长安订单 {} 步骤二开始", worderNo);

        //如果在勘测中
        //news-->门到门 供应商添加首联用户时间同步门 cdz/order/install/updateFirstcontactTime
        //news-->门到门  供应商预约勘测时间【同步到⻔到⻔】
        String result2 = dealWithOrderConvery(worderInformationEntity);
        if (StringUtils.isNotBlank(result2)) {
            return result2;
        }
        log.info("处理历史长安订单 {} 步骤二结束", worderNo);

        //如果是勘测中
        if (worderInformationEntity.getWorderStatus() == 1) {
            return "";
        }
        //news -> 门到门 供应商确认安装条件同步订单(确认超标费用)
        log.info("处理历史长安订单 {} 步骤三开始", worderNo);
        String result3 = canInstallOrder(worderInformationEntity);
        log.info("处理历史长安订单 ");
        if (StringUtils.isNotBlank(result3)) {
            log.info("处理历史长安订单 {} 步骤三报错 错误 {}", worderNo, result3);

             return result3;
        }
        log.info("处理历史长安订单 {} 步骤三结束", worderNo);

        //如果是待安装预约
        if (worderInformationEntity.getWorderExecStatus() == 10) {
            return null;
        }

        log.info("处理历史长安订单 {} 步骤四开始", worderNo);
        //news-->门到门  供应商预约上⻔时间  cdz/order/install/visitTime
        String result4 = pushVisitTime(worderInformationEntity);
        if (StringUtils.isNotBlank(result4)) {
            return result4;
        }
        log.error("处理历史长安订单 {} 步骤四结束", worderNo);

        //如果是安装资料未提交 待安装 已提交待审核
        // 改成签到之后(execStatus = 待安装 11)就调安装完成
        if (worderInformationEntity.getWorderExecStatus() == 11) {
            return null;
        }
        log.info("处理历史长安订单 {} 步骤五开始", worderNo);
        //news -->门到门  供应商安装完成 cdz/order/install/installationOrdersComplete
        //news -->门到门  供应商修改超标费⽤ dz/order/install/confirmCanInstallOrders
        String result5 = pushInstallation(worderInformationEntity);
        if (StringUtils.isNotBlank(result5)) {
            return result5;
        }
        log.info("处理历史长安订单 {} 步骤五结束", worderNo);

        // 安装资料提交审核 时调增项
        if (worderInformationEntity.getWorderExecStatus() == 12) {
            return null;
        }
        String resultOfIncreas = pushIncreaseFee(worderInformationEntity);
        if (StringUtils.isNotBlank(resultOfIncreas)) {
            return resultOfIncreas;
        }

        log.info("处理历史长安订单 {} 步骤五结束", worderNo);
        //如果是待客服确认 安装资料整改中
        if (worderInformationEntity.getWorderExecStatus() == 14 || worderInformationEntity.getWorderExecStatus() == 15) {
            return null;
        }
        log.info("处理历史长安订单 {} 步骤六开始", worderNo);

        //news -->门到门   供应商上传安装资料 cdz/order/install/dataCheck
        String result6 = pushInstallDataCheck(worderInformationEntity);
        if (StringUtils.isNotBlank(result6)) {
            return result6;
        }
        log.info("处理历史长安订单 {} 步骤六结束", worderNo);
        return null;
    }

    private String pushInstallDataCheck(WorderInformationEntity worderInformationEntity) {
        String worderNo = worderInformationEntity.getWorderNo();
        try {
            List<WorderExtFieldEntity> fields = worderExtFieldService.getFieldsByWorderNo(worderNo);
            WorderInformationAttributeEntity attributeEntity =
                    worderInformationAttributeDao.selectAttributeByWorderNo(
                            worderNo,
                            "push_dataCheck", "ca"
                    );
            if (attributeEntity != null && attributeEntity.getAttributeValue().equals("0")) {
                CaApiResponse response = caApiService.pushInstallDataCheck(worderNo, worderInformationEntity.getCompanyOrderNumber(), fields);
                if (!response.getSuccess()) {
                    log.error("处理历史数据 pushInstallDataCheck {} 调用长安接口异常 {}", worderNo, response.getMessage());
                    return response.getMessage();
                }
                attributeEntity.setAttributeValue("1");
                worderInformationAttributeDao.updateById(attributeEntity);
            }
        } catch (IOException e) {
            log.error("处理历史数据 pushInstallDataCheck {} 调用长安接口异常", worderNo, e);
            return "调用长安接口异常";
        }
        return "";
    }

    private String pushInstallation(WorderInformationEntity worderInformationEntity) {
        try {
            WorderInformationAttributeEntity worderInformationAttributeEntity =
                    worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_order_complete", "ca");
            if (worderInformationAttributeEntity != null && worderInformationAttributeEntity.getAttributeValue().equals("0")) {

                CaApiResponse response = caApiService.pushOrdersComplete(worderInformationEntity.getWorderNo(),
                        worderInformationEntity.getCompanyOrderNumber());
                if (!response.getSuccess()) {
                    log.error("处理历史数据 pushInstallation {} 调用长安接口异常 {}", worderInformationEntity.getWorderNo(), response.getMessage());
                    return response.getMessage();
                }

                worderInformationAttributeEntity.setAttributeValue("1");
                worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
            }
        } catch (IOException e) {
            log.error("处理历史数据 pushInstallation {} 调用长安接口异常", worderInformationEntity.getWorderNo(), e);
        }
        return null;
    }

    private String pushIncreaseFee(WorderInformationEntity worderInformationEntity) {
        try {
            WorderInformationAttributeEntity worderInformationAttributeEntity =
                    worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_overProof", "ca");
            if (worderInformationAttributeEntity != null && worderInformationAttributeEntity.getAttributeValue().equals("0")) {
                //增项物料
                IncreaseFeeDto increaseFee = worderInformationService.getIncreaseFee(worderInformationEntity.getWorderId());
                List<Map<String, Object>> details = increaseFee.getDetails();
                List<WorderMaterielVo> materiels = worderInformationService.getMateriel(worderInformationEntity.getWorderId());
                List<OverProof> overProofs = null;
                if (!materiels.isEmpty()) {
                    overProofs = materiels.stream().map(m -> {
                        OverProof overProof = new OverProof();
                        //费用编码 费用名称
                        overProof.setPriceCode(m.getSku());
                        overProof.setPriceName(m.getMaterielName());
                        overProof.setUnit(m.getMaterielUnitValue());
                        overProof.setAmount(m.getNum());
                        Map<String, Object> map =
                                details.stream().filter(mm -> mm.get("materielName").toString().equals(m.getMaterielName())).findFirst().orElse(null);
                        if (map != null) {
                            overProof.setUnitPrice(new BigDecimal(map.get("balanceFee").toString()));
                        }
                        return overProof;
                    }).collect(Collectors.toList());
                }
                CaApiResponse response2 = caApiService.pushConfirmCanInstallOrders(worderInformationEntity.getWorderNo(),
                        worderInformationEntity.getCompanyOrderNumber(), overProofs);
                if (!response2.getSuccess()) {
                    log.error("处理历史数据 pushConfirmCanInstallOrders {} 调用长安接口异常 {}", worderInformationEntity.getWorderNo(), response2.getMessage());
                    return response2.getMessage();
                }
                worderInformationAttributeEntity.setAttributeValue("1");
                worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
            }
        } catch (IOException e) {
            log.error("处理历史数据 pushInstallation {} 调用长安接口异常", worderInformationEntity.getWorderNo(), e);
        }
        return null;
    }

        private String pushVisitTime (WorderInformationEntity worderInformationEntity){
            String worderNo = worderInformationEntity.getWorderNo();
            String companyOrderNumber = worderInformationEntity.getCompanyOrderNumber();
            //   OverProof overProof = new OverProof("FY002", "30米套包外线缆敷设(长安）", "米", 2, new BigDecimal("20"));
            try {
                String format = LocalDateTime.now().plusMinutes(10).format(DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN));

                CaApiResponse response = caApiService.pushVisitTime(worderNo, worderInformationEntity.getCompanyOrderNumber(), format);
                if (!response.getSuccess()) {
                    log.error("处理历史数据 pushVisitTime {} 调用长安接口异常 {}", worderNo, response.getMessage());
                    return response.getMessage();
                }
            } catch (IOException e) {
                log.error("处理历史数据 pushVisitTime {} 调用长安接口异常", worderNo, e);
                return "调用长安接口异常";
            }
            return "";
        }

        private String canInstallOrder (WorderInformationEntity worderInformationEntity){
            String worderNo = worderInformationEntity.getWorderNo();
            String companyOrderNumber = worderInformationEntity.getCompanyOrderNumber();
            //   OverProof overProof = new OverProof("FY002", "30米套包外线缆敷设(长安）", "米", 2, new BigDecimal("20"));
            try {
                WorderInformationAttributeEntity worderInformationAttributeEntity =
                        worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_canInstallOrders", "ca");
                if (worderInformationAttributeEntity != null && worderInformationAttributeEntity.getAttributeValue().equals("0")) {

                    CaApiResponse response = caApiService.pushCanInstallOrders(worderNo, worderInformationEntity.getCompanyOrderNumber(), 1, "1", null);
                    if (!response.getSuccess()) {
                        log.error("处理历史数据 canInstallOrder {} 调用长安接口异常 {}", worderNo, response.getMessage());
                        return response.getMessage();
                    }
                    worderInformationAttributeEntity.setAttributeValue("1");
                    worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
                }
            } catch (IOException e) {
                log.error("处理历史数据 canInstallOrder {} 调用长安接口异常", worderNo, e);
                return "调用长安接口异常";
            }
            return "";
        }

        private String dealWithOrderConvery (WorderInformationEntity worderInformationEntity){
            String worderNo = worderInformationEntity.getWorderNo();
            try {
                WorderInformationAttributeEntity worderInformationAttributeEntity =
                        worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_updateFirstcontactTime", "ca");
                if (worderInformationAttributeEntity != null && worderInformationAttributeEntity.getAttributeValue().equals("0")) {
                    List<WorderRemarkLogEntity> worderRemarkLogEntityList =
                            worderRemarkLogService.queryConnectTime(worderInformationEntity.getWorderNo());
                    Date firstContactTime = null;
                    if (worderRemarkLogEntityList != null && !worderRemarkLogEntityList.isEmpty()) {
                        firstContactTime = worderRemarkLogEntityList.get(0).getCreateTime();
                    } else {
                        firstContactTime = new Date();
                    }
                    CaApiResponse response1 = caApiService.pushFirstcontactTime(worderNo, worderInformationEntity.getCompanyOrderNumber(),
                            firstContactTime);
                    if (!response1.getSuccess()) {
                        log.error("处理历史数据 dealWithOrderConvery {} 调用长安接口异常 {}", worderNo, response1.getMessage());
                        return response1.getMessage();
                    }
                    worderInformationAttributeEntity.setAttributeValue("1");
                    worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
                }
                String format = LocalDateTime.now().plusMinutes(5).format(DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN));
                CaApiResponse response2 = caApiService.pushMeasureTime(worderNo, worderInformationEntity.getCompanyOrderNumber(), format);
                if (!response2.getSuccess()) {
                    log.error("处理历史数据 dealWithOrderConvery {} 调用长安接口异常 {}", worderNo, response2.getMessage());
                    return response2.getMessage();
                }
            } catch (IOException e) {
                log.error("处理历史数据 dealWithOrderConvery {} 调用长安接口异常", worderNo, e);
                return "调用长安接口异常";
            }
            return "";
        }

        private String dealWithOrderSend (WorderInformationEntity worderInformationEntity){
            String worderNo = worderInformationEntity.getWorderNo();
            String companyOrderNumber = worderInformationEntity.getCompanyOrderNumber();
            try {
                WorderInformationAttributeEntity worderInformationAttributeEntity =
                        worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_serviceTime", "ca");
                if (worderInformationAttributeEntity != null && worderInformationAttributeEntity.getAttributeValue().equals("0")) {
                    CaApiResponse response1 = caApiService.pushServiceTime(worderNo, companyOrderNumber);
                    if (!response1.getSuccess()) {
                        log.error("处理历史数据 dealWithOrderSend {} 调用长安接口异常 {}", worderNo, response1.getMessage());
                        return response1.getMessage();
                    }
                    worderInformationAttributeEntity.setAttributeValue("1");
                    worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
                }
                WorderInformationAttributeEntity worderInformationAttributeEntity2 =
                        worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_ammeterStatus", "ca");
                if (worderInformationAttributeEntity2 != null && worderInformationAttributeEntity2.getAttributeValue().equals("0")) {
                    CaApiResponse response2 = caApiService.pushAmmeterStatus(worderNo, companyOrderNumber, 1);
                    if (!response2.getSuccess()) {
                        log.error("处理历史数据 dealWithOrderSend {} 调用长安接口异常 {}", worderNo, response2.getMessage());
                        return response2.getMessage();
                    }
                    worderInformationAttributeEntity2.setAttributeValue("1");
                    worderInformationAttributeDao.updateById(worderInformationAttributeEntity2);
                }
                BizAttendantEntity bizAttendantEntity = bizAttendantService.getById(worderInformationEntity.getServiceId());
                CaApiResponse response3 = caApiService.pushTakeOrders(worderNo, companyOrderNumber, bizAttendantEntity.getName(),
                        bizAttendantEntity.getContact());
                if (!response3.getSuccess()) {
                    log.error("处理历史数据 dealWithOrderSend {} 调用长安接口异常 {}", worderNo, response3.getMessage());
                    return response3.getMessage();
                }
            } catch (IOException e) {
                log.error("处理历史数据 dealWithOrderSend {} 调用长安接口异常", worderNo, e);
                return "调用长安接口异常";
            }
            return "";
        }

        @Override
        public void orderRefundApply (CaAfterSaleCancelOrderRequest request){
            UserUtil.createDefaultLoginUser();
            WorderInformationEntity worderInformationEntity = worderInformationService.getOne(
                    new QueryWrapper<WorderInformationEntity>()
                            .eq("company_order_number", request.getDetailNo())
            );
            if (worderInformationEntity == null) {
                throw new RRException("未找到安装订单编号对应单号!");
            }
            if (worderInformationEntity.getWorderExecStatus() != null && worderInformationEntity.getWorderExecStatus() == 21) {
                throw new RRException("该单号已经取消!");
            }
            CaMdmRefundInfo one = caMdmRefundInfoService.lambdaQuery()
                    .eq(CaMdmRefundInfo::getDetailNo, request.getDetailNo())
                    .one();
            boolean exists = one == null;
            CaMdmRefundInfo caMdmRefundInfo = new CaMdmRefundInfo();
            BeanUtils.copyProperties(request, caMdmRefundInfo);
            if (exists) {
                caMdmRefundInfoService.save(caMdmRefundInfo);
            } else {
                caMdmRefundInfo.setId(one.getId());
                caMdmRefundInfoService.updateById(caMdmRefundInfo);
            }

            WorderInformationAttributeEntity attributeEntity = worderInformationAttributeService.lambdaQuery()
                    .eq(WorderInformationAttributeEntity::getWorderId, worderInformationEntity.getWorderId())
                    .eq(WorderInformationAttributeEntity::getAttributeCode, AttributeCodeEnum.MDMCancelOrder.getValue())
                    .one();
            if (attributeEntity != null) {
                worderInformationAttributeService.lambdaUpdate()
                        .eq(WorderInformationAttributeEntity::getWorderId, worderInformationEntity.getWorderId())
                        .eq(WorderInformationAttributeEntity::getAttributeCode, attributeEntity.getAttributeCode())
                        .set(WorderInformationAttributeEntity::getAttributeName, request.getApplyDatetime())
                        .set(WorderInformationAttributeEntity::getAttributeValue, String.valueOf(request.getApplyReason()))
                        .set(WorderInformationAttributeEntity::getAttribute, request.getApplyRemark())
                        .update();
            } else {
                worderInformationAttributeDao.insertMdmCancelOrder(worderInformationEntity.getWorderId(), request.getApplyDatetime(),
                        String.valueOf(request.getApplyReason()), request.getApplyRemark());
            }

            // saveLog
            String str = request.getApplyDatetime() + " " + request.getApplyUserName();
            String content =
                    request.getApplyUserName() + "关闭订单,原因:" + request.getApplyUserName() + "申请关闭订单, " + request.getApplyReason() + " " + request.getRemark();
            worderRemarkLogService.addOperationLog(worderInformationEntity.getWorderNo(), str + "关闭工单", content, 89);
        }

        @Override
        public void receiveAfterSaleCheck (ReceiveAfterSaleCheckRequest request){
            UserUtil.createDefaultLoginUser();
            caMdmRefundInfoService.lambdaUpdate()
                    .set(CaMdmRefundInfo::getApplyPrice, request.getApplyPrice())
                    .set(CaMdmRefundInfo::getCheckUserName, request.getCheckUserName())
                    .set(CaMdmRefundInfo::getCheckResult, request.getCheckResult())
                    .set(CaMdmRefundInfo::getCheckRemark, request.getCheckRemark())
                    .set(CaMdmRefundInfo::getCheckTime, request.getCheckTime())
                    .eq(CaMdmRefundInfo::getAftersaleId, request.getAftersaleId())
                    .eq(CaMdmRefundInfo::getDetailNo, request.getDetailNo())
                    .update();

            WorderInformationEntity worderInformationEntity = worderInformationService.lambdaQuery()
                    .select(WorderInformationEntity::getWorderId, WorderInformationEntity::getWorderNo)
                    .eq(WorderInformationEntity::getCompanyOrderNumber, request.getDetailNo())
                    .one();

            // 审核不通过
            if (request.getCheckResult() == 0) {
                // 取消取消订单的标记
                // 清除取消申请标识
                LambdaQueryWrapper<WorderInformationAttributeEntity> worderInformationAttributeWrapper = Wrappers.lambdaQuery();
                worderInformationAttributeWrapper.eq(WorderInformationAttributeEntity::getWorderId, worderInformationEntity.getWorderId());
                worderInformationAttributeWrapper.eq(WorderInformationAttributeEntity::getAttributeCode, AttributeCodeEnum.MDMCancelOrder.getValue());
                worderInformationAttributeDao.delete(worderInformationAttributeWrapper);
            } else {
                // 审核通过
                String str = request.getCheckTime() + " 长安_" + request.getCheckUserName();
                String content = "长安_" + request.getCheckUserName() + "关闭订单,原因:" + request.getCheckRemark();
                worderRemarkLogService.addOperationLog(worderInformationEntity.getWorderNo(), str + "关闭工单", content, 89);
                worderRemarkLogService.closeOrCancelUpdateStatus(worderInformationEntity.getWorderId(), 3, "关闭工单", 21, "取消服务", 6);
            }
        }

        @Override
        public CaApiResponse updateConfirmCanInstallOrders (CaApiRequestBody request){
            log.info("updateConfirmCanInstallOrders:{}", request);
            CaApiResponse apiResponse = this.getConfirmCanInstallOrdersParam(request);
            if (StringUtils.isEmpty(apiResponse.getMessage())) {
                // 插入工单日志
                String str = DateUtils.getCurrentTime() + " 长安_门到门系统已确认增项信息";
                worderRemarkLogService.addOperationLog(request.getDetailNo(), str, str, 89);
                apiResponse.setMessage("超标费用确认成功,");
                apiResponse.setCode("200");
                apiResponse.setSuccess(true);
            } else {
                apiResponse.setMessage("超标费用确认失败,");
                apiResponse.setCode("500");
                apiResponse.setSuccess(false);
            }
            return apiResponse;
        }

        @Override
        public CaApiResponse updateConfirmAccept (CaApiRequestBody requestBody){
            log.info("updateConfirmAccept:{}", requestBody);
            CaApiResponse apiResponse = this.getConfirmAcceptParam(requestBody);
            log.info("apiResponse --->" + apiResponse);
            if (StringUtils.isNotEmpty(apiResponse.getMessage())) {
                return apiResponse;
            }

            WorderInformationEntity worderInformationEntity = worderInformationService.getByCompanyWorderNo(requestBody.getDetailNo());
            if (worderInformationEntity == null) {
                throw new RRException("根据订单编号未查询到订单信息");
            }

            worderOperationRecodeService.setOperation(
                    worderInformationEntity,
                    "门到门确认验收",
                    requestBody.getInstallStatus() == 0 ? "不通过" : "通过" + StringUtils.defaultString(requestBody.getRemark()),
                    new Date()
            );
            if (requestBody.getInstallStatus() == 0) {
                SmsUtil.sendSms("15910305046", worderInformationEntity.getWorderNo() + "门到门确认验收不通过", "【到每家科技服务】");
            }
            // 0 否
            // if (requestBody.getInstallStatus() == 0) {
            //     WorderInformationDto worderInformationDto = new WorderInformationDto();
            //     worderInformationDto.setWorderNo(worderInformationEntity.getWorderNo());
            //     // 车企回退待客服确认
            //     worderInformationDto.setWorderExecStatus(23);
            //     worderOrderDao.updateWorderInformation(worderInformationDto);
            //     flowCommon.updateFlowStatus(worderInformationEntity.getWorderId(), "");
            // } else {
            //     // 1 是
            //     R r = this.confirmInstall(worderInformationEntity.getWorderId(), 2);
            //     if (!r.get("code").equals(0)) {
            //         throw new RRException(r.get("msg").toString());
            //     }
            // }
            return CaApiResponse.success();
        }

        public R confirmInstall (Integer worderId, Integer flag){
            // 由于跳过登陆认证，需要设置固定的登陆信息
            SysUserEntity sysUserEntity = new SysUserEntity();
            sysUserEntity.setUserId(89L);
            sysUserEntity.setUsername("系统自动");
            // 创建一个Subject.Builder
            Subject.Builder builder = new Subject.Builder();
            // 设置身份信息
            PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, "系统自动");
            builder.principals(principals);
            // 设置是否已经认证
            builder.authenticated(true);
            // 创建Subject实例
            Subject subject = builder.buildSubject();
            ThreadContext.bind(subject);

            // 重复校验获取执行状态
            IdempotentPo idempotentPo = idempotentCheck.functionBegin(RedisConstant.Prefix.BUSINESS
                    , "confirm"
                    , worderId
                    , flag);

            // 首次执行完成将返回首次结果
            if (IdempotentConstant.Result.FINISH.equals(idempotentPo.getCode())) {
                return R.ok().put("result", idempotentPo.getValue());
                // 首次调用未执行完成放回重复调用
            } else if (!IdempotentConstant.Result.SUCCESS.equals(idempotentPo.getCode())) {
                return R.ok().put("result", IdempotentConstant.Result.getName(idempotentPo.getCode()));
            }

            //安装中增加校验勘测不增加校验
            if (flag == 2 && worderInformationService.checkBanlanRule(worderId)) {
                return R.error(201, "该工单未匹配到工程结算规则！");
            }
            String result = worderInformationService.updateStatus(worderId, flag);
            // 执行结束更新执行状态
            idempotentCheck.functionFinish(RedisConstant.Prefix.BUSINESS
                    , result
                    , "confirm"
                    , worderId
                    , flag);
            return R.ok().put("result", result);
        }

        @Override
        public CaApiResponse updateSupplier (CaApiRequestBody requestBody){
            log.info("updateSupplier:{}", requestBody);

            // todo
            String oldSupplierCode = requestBody.getOldSupplierCode();
            if (oldSupplierCode == null) {
                throw new RRException("原供应商编码不能为空");
            }

            WorderInformationEntity worderInformationEntity = worderInformationService.getByCompanyWorderNo(requestBody.getDetailNo());
            if (worderInformationEntity == null) {
                throw new RRException("工单不存在");
            }

            WorderInformationAttributeEntity attributeEntity = worderInformationAttributeService.lambdaQuery()
                    .eq(WorderInformationAttributeEntity::getWorderId, worderInformationEntity.getWorderId())
                    .eq(WorderInformationAttributeEntity::getAttributeCode, AttributeCodeEnum.MDMCancelOrder.getValue())
                    .one();
            if (attributeEntity != null) {
                worderInformationAttributeService.lambdaUpdate()
                        .eq(WorderInformationAttributeEntity::getWorderId, worderInformationEntity.getWorderId())
                        .eq(WorderInformationAttributeEntity::getAttributeCode, attributeEntity.getAttributeCode())
                        .set(WorderInformationAttributeEntity::getAttributeName, "门到门更换供应商")
                        .set(WorderInformationAttributeEntity::getAttributeValue, "门到门更换供应商")
                        .set(WorderInformationAttributeEntity::getAttribute, "门到门更换供应商")
                        .update();
            } else {
                worderInformationAttributeDao.insertMdmCancelOrder(
                        worderInformationEntity.getWorderId(),
                        "门到门更换供应商",
                        "门到门更换供应商",
                        "门到门更换供应商"
                );
            }

            // 取消订单
            worderInformationService.cancelWorder(worderInformationEntity.getWorderNo(), worderInformationEntity.getCompanyOrderNumber());

            worderOperationRecodeService.setOperation(worderInformationEntity, "门到门更换供应商", "门到门关闭订单,原因: 门到门更换供应商，订单取消", new Date());
            return CaApiResponse.success();
        }

        @Override
        public CaApiResponse installAudit (CaApiRequestBody requestBody){
            CaApiResponse apiResponse = getInstallAuditRequestParam(requestBody);
            log.info("apiResponse --->" + apiResponse.toString());
            if (!StringUtils.isEmpty(apiResponse.getMessage())) {
                return apiResponse;
            }

            WorderInformationEntity worderInformationEntity = worderInformationService.getByCompanyWorderNo(requestBody.getDetailNo());
            if (worderInformationEntity == null) {
                throw new RRException("工单不存在");
            }

            String s;
            switch (requestBody.getCheckStatus()) {
                case 0:
                    s = "门到门待审核";
                    break;
                case 1:
                    s = "门到门审核通过";
                    break;
                case 2:
                    s = "门到门审核驳回";
                    break;
                default:
                    s = String.valueOf(requestBody.getCheckStatus());
            }

            String content = Stream.of(s, requestBody.getCheckReason(), requestBody.getRemark())
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining(" "));

            log.info("状态 -->" + requestBody.getCheckStatus());
            worderOperationRecodeService.setOperation(
                    worderInformationEntity,
                    "门到门审核资料",
                    content,
                    new Date()
            );

            //如果审核驳回，则将此工单状态置为待品牌客服审核,由品牌客服打回整改
            // 审核状态 0待审 核 1审核通过 2 审核驳回
            if (requestBody.getCheckStatus() == 2) {
                WorderInformationDto worderInformationDto = new WorderInformationDto();
                worderInformationDto.setWorderNo(worderInformationEntity.getWorderNo());
                //安装资料待客服审核
                worderInformationDto.setWorderExecStatus(15);
                // 拒绝，调用流程结束
                if (flowCommon.hasFlowByWorderNo(worderInformationEntity.getWorderNo())) {
                    //调用网点已接单流程
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInformationEntity.getWorderNo(),
                            FlowConstant.ProcessCode.StatusUpdate, FlowConstant.ProcessStatus.N);
                    // 流程调用失败直接返回
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        throw new RRException(executeFlowResultPo.getMsg());
                    }
                } else {
                    worderOrderDao.updateWorderInformation(worderInformationDto);
                }
            } else if (requestBody.getCheckStatus() == 1) {
                // 1 是
                R r = this.confirmInstall(worderInformationEntity.getWorderId(), 2);
                if (!r.get("code").equals(0)) {
                    throw new RRException(r.get("msg").toString());
                }
            }

            return CaApiResponse.success();
        }

        private CaApiResponse getInstallAuditRequestParam (CaApiRequestBody requestBody){
            CaApiResponse apiResponse = new CaApiResponse();
            StringBuilder failureMsg = getExtractPublic(requestBody);
            //审核状态  审核状态 0待审核 1审核通过 2审核驳回
            if (requestBody.getCheckStatus() == null) {
                failureMsg.append("审核状态不能为空,");
            }
            if (StringUtils.isBlank(requestBody.getCheckTime())) {
                failureMsg.append("审核时间不能为空,");
            }
            if (StringUtils.isBlank(requestBody.getCheckMan())) {
                failureMsg.append("审核人不能为空,");
            }
            //        if (StringUtils.isNull(requestBody.getCheckReason())) {
            //            failureMsg.append("审核原因不能为空,");
            //        }

            apiResponse.setMessage(failureMsg.toString());
            return apiResponse;
        }

        @Override
        public CaApiResponse unDo (CaApiRequestBody requestBody){
            log.info("unDo:{}", requestBody);
            return CaApiResponse.success();
        }

        @Override
        public String pushInstallDataCheckWorderNo (String worderNo){
            WorderInformationEntity worderInformationEntity = worderInformationService.getByWorderNo(worderNo);
            if (worderInformationEntity == null) {
                return "工单不存在";
            }
            return pushInstallDataCheck(worderInformationEntity);
        }

    @Override
    public String processAttData(String worderNo) {
        List<Integer> dataCheckTrueList = Arrays.asList(16, 17, 22, 23);
        List<Integer> worderIds = worderInformationService.selectCaWorderInfo();
        if (StringUtils.isNotBlank(worderNo)) {
            WorderInformationEntity informationEntity = worderInformationService.getByWorderNo(worderNo);
            worderIds.add(informationEntity.getWorderId());
        }
        // 定义日期和时间的格式
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 使用指定的日期和时间字符串生成 Date 对象
        String dateString = "2024-08-21 23:23:23";
        Date date;
        try {
            date = formatter.parse(dateString);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        worderIds.forEach(worderId -> {
            WorderInformationAttributeEntity worderInformationAttributeEntity =
                    worderInformationAttributeDao.selectAttributeByWorderId(String.valueOf(worderId), "worder_source", "pushOrder");
            if (worderInformationAttributeEntity == null) {
                WorderInformationAttributeEntity attributeEntity = new WorderInformationAttributeEntity();
                attributeEntity.setWorderId(worderId);
                attributeEntity.setAttributeCode("worder_source");
                attributeEntity.setAttributeName("工单来源");
                attributeEntity.setAttributeValue("ca");
                attributeEntity.setAttribute("pushOrder");
                attributeEntity.setCreateTime(date);
                worderInformationAttributeDao.insert(attributeEntity);
            }

            WorderInformationAttributeEntity worderInformationAttributeEntity2 =
                    worderInformationAttributeDao.selectAttributeByWorderId(String.valueOf(worderId), "push_overProof", "ca");
            if (worderInformationAttributeEntity2 == null) {
                WorderInformationAttributeEntity attributeEntity2 = new WorderInformationAttributeEntity();
                attributeEntity2.setWorderId(worderId);
                attributeEntity2.setAttributeCode("push_overProof");
                attributeEntity2.setAttributeName("推送增项-安装");
                attributeEntity2.setAttributeValue("0");
                attributeEntity2.setAttribute("ca");
                attributeEntity2.setCreateTime(date);
                worderInformationAttributeDao.insert(attributeEntity2);
            }

            WorderInformationAttributeEntity worderInformationAttributeEntity3 =
                    worderInformationAttributeDao.selectAttributeByWorderId(String.valueOf(worderId), "push_order_complete", "ca");
            if (worderInformationAttributeEntity3 == null) {
                WorderInformationAttributeEntity attributeEntity3 = new WorderInformationAttributeEntity();
                attributeEntity3.setWorderId(worderId);
                attributeEntity3.setAttributeCode("push_order_complete");
                attributeEntity3.setAttributeName("推送订单完成");
                attributeEntity3.setAttributeValue("0");
                attributeEntity3.setAttribute("ca");
                attributeEntity3.setCreateTime(date);
                worderInformationAttributeDao.insert(attributeEntity3);
            }

            WorderInformationAttributeEntity worderInformationAttributeEntity4 =
                    worderInformationAttributeDao.selectAttributeByWorderId(String.valueOf(worderId), "push_ammeterStatus", "ca");
            if (worderInformationAttributeEntity4 == null) {
                WorderInformationAttributeEntity attributeEntity4 = new WorderInformationAttributeEntity();
                attributeEntity4.setWorderId(worderId);
                attributeEntity4.setAttributeCode("push_ammeterStatus");
                attributeEntity4.setAttributeName("推送电表安装状态");
                attributeEntity4.setAttributeValue("0");
                attributeEntity4.setAttribute("ca");
                attributeEntity4.setCreateTime(date);
                worderInformationAttributeDao.insert(attributeEntity4);
            }

            WorderInformationAttributeEntity worderInformationAttributeEntity5 =
                    worderInformationAttributeDao.selectAttributeByWorderId(String.valueOf(worderId), "push_updateFirstcontactTime", "ca");
            if (worderInformationAttributeEntity5 == null) {
                WorderInformationAttributeEntity attributeEntity5 = new WorderInformationAttributeEntity();
                attributeEntity5.setWorderId(worderId);
                attributeEntity5.setAttributeCode("push_updateFirstcontactTime");
                attributeEntity5.setAttributeName("推送首联用户时间");
                attributeEntity5.setAttributeValue("0");
                attributeEntity5.setAttribute("ca");
                attributeEntity5.setCreateTime(date);
                worderInformationAttributeDao.insert(attributeEntity5);
            }

            WorderInformationAttributeEntity worderInformationAttributeEntity6 =
                    worderInformationAttributeDao.selectAttributeByWorderId(String.valueOf(worderId), "push_serviceTime", "ca");
            if (worderInformationAttributeEntity6 == null) {
                WorderInformationAttributeEntity attributeEntity6 = new WorderInformationAttributeEntity();
                attributeEntity6.setWorderId(worderId);
                attributeEntity6.setAttributeCode("push_serviceTime");
                attributeEntity6.setAttributeName("推送首联用户时间");
                attributeEntity6.setAttributeValue("0");
                attributeEntity6.setAttribute("ca");
                attributeEntity6.setCreateTime(date);
                worderInformationAttributeDao.insert(attributeEntity6);
            }

            WorderInformationAttributeEntity worderInformationAttributeEntity7 =
                    worderInformationAttributeDao.selectAttributeByWorderId(String.valueOf(worderId), "push_canInstallOrders", "ca");
            if (worderInformationAttributeEntity7 == null) {
                WorderInformationAttributeEntity attributeEntity7 = new WorderInformationAttributeEntity();
                attributeEntity7.setWorderId(worderId);
                attributeEntity7.setAttributeCode("push_canInstallOrders");
                attributeEntity7.setAttributeName("推送确认安装条件-勘测");
                attributeEntity7.setAttributeValue("0");
                attributeEntity7.setAttribute("ca");
                attributeEntity7.setCreateTime(date);
                worderInformationAttributeDao.insert(attributeEntity7);
            }

            WorderInformationAttributeEntity worderInformationAttributeEntity8 =
                    worderInformationAttributeDao.selectAttributeByWorderId(String.valueOf(worderId), "push_dataCheck", "ca");
            if (worderInformationAttributeEntity8 == null) {
                WorderInformationAttributeEntity attributeEntity8 = new WorderInformationAttributeEntity();
                attributeEntity8.setWorderId(worderId);
                attributeEntity8.setAttributeCode("push_dataCheck");
                attributeEntity8.setAttributeName("推送安装资料");

                WorderInfoEntity byWorderId = worderInformationService.getByWorderId(worderId);
                Integer execStatus = byWorderId.getWorderExecStatus();
                if (!(execStatus < 16) && dataCheckTrueList.contains(execStatus)) {
                    attributeEntity8.setAttributeValue("1");
                } else {
                    attributeEntity8.setAttributeValue("0");
                }
                attributeEntity8.setAttribute("ca");
                attributeEntity8.setCreateTime(date);
                worderInformationAttributeDao.insert(attributeEntity8);
            }
        });
        return "success";
    }

    @Override
    public String canInstallOrders(String worderNo) {
        WorderInformationEntity worderInformationEntity = worderInformationService.getOne(
                new QueryWrapper<WorderInformationEntity>()
                        .eq("worder_no", worderNo)
        );
        if (worderInformationEntity == null) {
            return "工单不存在";
        }

        WorderInformationAttributeEntity worderInformationAttributeEntity7 =
                worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_canInstallOrders", "ca");
        if (worderInformationAttributeEntity7 == null) {
            WorderInformationAttributeEntity attributeEntity7 = new WorderInformationAttributeEntity();
            attributeEntity7.setWorderId(worderInformationEntity.getWorderId());
            attributeEntity7.setAttributeCode("push_canInstallOrders");
            attributeEntity7.setAttributeName("推送确认安装条件-勘测");
            attributeEntity7.setAttributeValue("0");
            attributeEntity7.setAttribute("ca");
            worderInformationAttributeDao.insert(attributeEntity7);
        }

        return canInstallOrder(worderInformationEntity);
    }

    private CaApiResponse getConfirmCanInstallOrdersParam (CaApiRequestBody requestBody){
            CaApiResponse apiResponse = new CaApiResponse();
            StringBuilder failureMsg = getExtractPublic(requestBody);
            apiResponse.setMessage(failureMsg.toString());
            return apiResponse;
        }

        private StringBuilder getExtractPublic (CaApiRequestBody requestBody){
            StringBuilder failureMsg = new StringBuilder();
            if (StringUtils.isBlank(requestBody.getDetailNo())) {
                failureMsg.append("订单详情号不能为空,");
            }
            return failureMsg;
        }

        private CaApiResponse getConfirmAcceptParam (CaApiRequestBody requestBody){
            CaApiResponse apiResponse = new CaApiResponse();
            StringBuilder failureMsg = getExtractPublic(requestBody);
            //是否已验收 0否 1是
            if (requestBody.getInstallStatus() == null) {
                failureMsg.append("是否已验收不能为空,");
            }
            apiResponse.setMessage(failureMsg.toString());
            return apiResponse;
        }

        private RegionCode determineRegion (CaOrderApiVO orderApiVO){
            String consigneeAddress = orderApiVO.getConsigneeAddress();
            String[] split = consigneeAddress.split("-");
            if (split.length < 4) {
                log.error("companyWorderNo:{} {} 地址错误", orderApiVO.getDetailNo(), consigneeAddress);
                // throw new RRException("车企地址有误");
                SmsUtil.sendSms("15910305046", "长安工单地址有误，请手动修改工单，车企订单号" + orderApiVO.getDetailNo(), "【到每家科技服务】");
                return new RegionCode(1L, 32L, 378L, consigneeAddress);
            }
            RegionCode regionCode = new RegionCode();
            BizRegionEntity province = bizRegionService.getRegionByName(null, split[0], 1);

            regionCode.setProvinceCode(province == null ? null : province.getId());
            BizRegionEntity city = bizRegionService.getRegionByName(province == null ? null : province.getId(), split[1], 2);

            if (city != null) {
                regionCode.setCityCode(city.getId());
                if (regionCode.getProvinceCode() == null) {
                    regionCode.setProvinceCode(city.getPid());
                }
            }

            BizRegionEntity area;
            if (StringUtils.equals(split[2], "市辖区")) {
                area = bizRegionService.getFirstDistrictByCityId(city.getId());
            } else {
                area = bizRegionService.getRegionByName(city == null ? null : city.getId(), split[2], 3);
                if (area == null) {
                    area = city;
                }
            }

            if (area == null) {
                area = bizRegionService.getRegionByName(null, split[2], 3);
                if (area == null) {
                    throw new RRException("地址错误");
                }
                regionCode.setAreaCode(area.getId());
                regionCode.setCityCode(area.getPid());
            }
            regionCode.setAreaCode(area.getId());
            if (regionCode.getProvinceCode() == null) {
                city = bizRegionService.getById(regionCode.getCityCode());
                if (city != null) {
                    regionCode.setProvinceCode(city.getId());
                }
            }

            StringBuilder detailedAddress = new StringBuilder();
            for (int i = 3; i < split.length; i++) {
                detailedAddress.append(split[i]);
                if (i < split.length - 1) {
                    detailedAddress.append("-");
                }
            }

            regionCode.setDetailedAddress(detailedAddress.toString());
            return regionCode;
        }

        private Integer determineBrand (CaOrderApiVO orderApiVO){
            String commoditySkuName = orderApiVO.getCommoditySkuName();
            if (StringUtils.isBlank(commoditySkuName))
                throw new RRException("商品sku名称不能为空");
            if (commoditySkuName.contains("V标")) {
                // 引力
                return 106;
            } else {
                // 104	长安-启源
                return 104;
            }
        }
    }
