package com.bonc.rrs.ca.util;

import com.alibaba.fastjson.JSON;
import com.common.pay.common.utils.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: HttpClientExample
 * @projectName xkdg-server
 * @description: http请求远程接口
 * @date 2023/4/1711:43 AM
 */
@Component
public class CacHttpClient {
    private static String ACC_KEY;
    private static String SECRE_KEY;
    private static String SUPPLIER_CODE;
    private static String baseUrl;

    public static Map<String, Object> getSign(Map<String, Object> params) throws IOException {
        long time = System.currentTimeMillis();
        Map<String, Object> map = new HashMap<>();
        map.put("accKey", ACC_KEY);
        map.put("secretKey", SECRE_KEY);
        map.put("supplierCode", SUPPLIER_CODE);
        map.put("timestamp", String.valueOf(time));
        map.putAll(params);
        Map<String, Object> params1 = map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        String sign = M2MSignUtils.getSign(params1);
        if (!StringUtils.isEmpty(sign)) {
            map.put("sign", sign);
        } else {
            throw new RuntimeException("获取sign失败！！");
        }
        return map;
    }

    public static Map<String, Object> requestApi(String suffix, Map<String, Object> params) throws IOException {
        String url = baseUrl + suffix;
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        StringEntity requestEntity = new StringEntity(JSON.toJSONString(params), "utf-8");
        requestEntity.setContentEncoding("UTF-8");
        httpPost.setHeader("Content-type", "application/json;charset=UTF-8");
        httpPost.setEntity(requestEntity);
        CloseableHttpResponse response = httpclient.execute(httpPost);
        try {
            int statusCode = response.getStatusLine().getStatusCode();
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity);
            Map<String, Object> result = new HashMap<>(2);
            result.put("code", statusCode);
            result.put("body", responseBody);
            return result;
        } finally {
            response.close();
        }

    }

    @Value("${cacfg.ACC_KEY}")
    public void setAccKey(String accKey) {
        CacHttpClient.ACC_KEY = accKey;
    }

    @Value("${cacfg.SECRE_KEY}")
    public void setSecreKey(String secreKey) {
        CacHttpClient.SECRE_KEY = secreKey;
    }

    @Value("${cacfg.SUPPLIER_CODE}")
    public void setSupplierCode(String supplierCode) {
        CacHttpClient.SUPPLIER_CODE = supplierCode;
    }

    @Value("${cacfg.baseUrl}")
    public void setBaseUrl(String baseUrl) {
        CacHttpClient.baseUrl = baseUrl;
    }
}
