package com.bonc.rrs.ca.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 长安退款申请
 */
@Data
@TableName(value = "ca_mdm_refund_info")
public class CaMdmRefundInfo {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单详情号
     */
    @TableField(value = "detail_no")
    private String detailNo;

    /**
     * 订单退款编码
     */
    @TableField(value = "after_sale_id")
    private Integer aftersaleId;

    /**
     * 下单时间
     */
    @TableField(value = "add_time")
    private String addTime;

    /**
     * 商品编码
     */
    @TableField(value = "commodity_code")
    private String commodityCode;

    /**
     * 商品名称
     */
    @TableField(value = "commodity_name")
    private String commodityName;

    /**
     * 商品sku编码
     */
    @TableField(value = "commodity_sku_code")
    private String commoditySkuCode;

    /**
     * 商品sku名称
     */
    @TableField(value = "commodity_sku_name")
    private String commoditySkuName;

    /**
     * 购买单价
     */
    @TableField(value = "commodity_sku_price")
    private BigDecimal commoditySkuPrice;

    /**
     * 购买数量
     */
    @TableField(value = "commodity_sku")
    private Integer commoditySku;

    /**
     * 实付费用
     */
    @TableField(value = "actual_price")
    private BigDecimal actualPrice;

    /**
     * 支付方式
     */
    @TableField(value = "pay_mode")
    private Integer payMode;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private String payTime;

    /**
     * 购买用户姓名
     */
    @TableField(value = "username")
    private String username;

    /**
     * 购买用户电话
     */
    @TableField(value = "user_phone")
    private String userPhone;

    /**
     * 车架号
     */
    @TableField(value = "vehicle_vin")
    private String vehicleVin;

    /**
     * 安装联系人姓名
     */
    @TableField(value = "consignee_name")
    private String consigneeName;

    /**
     * 安装联系人电话
     */
    @TableField(value = "consignee_phone")
    private String consigneePhone;

    /**
     * 安装联系人地址
     */
    @TableField(value = "consignee_address")
    private String consigneeAddress;

    /**
     * 留言备注
     */
    @TableField(value = "consignee_remark")
    private String consigneeRemark;

    /**
     * 订单状态
     */
    @TableField(value = "order_status")
    private String orderStatus;

    /**
     * 申请原因
     */
    @TableField(value = "apply_reason")
    private Integer applyReason;

    /**
     * 申请时间
     */
    @TableField(value = "apply_datetime")
    private String applyDatetime;

    /**
     * 退款金额
     */
    @TableField(value = "apply_price")
    private BigDecimal applyPrice;

    /**
     * 申请人名称
     */
    @TableField(value = "apply_user_name")
    private String applyUserName;

    /**
     * 申请人电话
     */
    @TableField(value = "apply_userphone")
    private String applyUserphone;

    /**
     * 申请详细描述
     */
    @TableField(value = "apply_remark")
    private String applyRemark;

    /**
     * 申请图片
     */
    @TableField(value = "apply_pics")
    private String applyPics;

    /**
     * 退款进度
     */
    @TableField(value = "refund_status")
    private Integer refundStatus;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 审核⼈名称
     */
    @TableField(value = "check_user_name")
    private String checkUserName;

    /**
     * 审核结果 0未通过 1通过
     */
    @TableField(value = "check_result")
    private Integer checkResult;

    /**
     * 审核备注
     */
    @TableField(value = "check_remark")
    private String checkRemark;

    /**
     * 审核时间
     */
    @TableField(value = "check_time")
    private Date checkTime;

    /**
     * 0=未删除 1=删除
     */
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建日期
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 更新日期
     */
    @TableField(value = "updated_at")
    private Date updatedAt;
}