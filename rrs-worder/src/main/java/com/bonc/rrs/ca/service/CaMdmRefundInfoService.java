package com.bonc.rrs.ca.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.ca.domain.CaMdmRefundInfo;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;

public interface CaMdmRefundInfoService extends IService<CaMdmRefundInfo> {

    IPage<CaMdmRefundInfo> getList(String detailNo, Integer pageSize, Integer pageNum);

    void updateStatus(CaMdmRefundInfo caMdmRefundInfo, SysUserEntity user);
}
