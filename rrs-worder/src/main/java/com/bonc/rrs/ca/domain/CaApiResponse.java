package com.bonc.rrs.ca.domain;

import lombok.Data;

import java.io.Serializable;


/**
 * 接口返回值实体类
 *
 * <AUTHOR>
 */
@Data
public class CaApiResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 响应编码
     */
    private String code;

    /**
     * 响应描述
     */
    private String message;

    /**
     * 响应内容
     */
    private Object data;
    /**
     * 响应结果状态
     */
    private Boolean success;

    public static CaApiResponse success() {
        CaApiResponse response = new CaApiResponse();
        response.setMessage("成功");
        response.setCode("200");
        response.setSuccess(true);
        return response;
    }

    public boolean isSuccess() {
        return this.getSuccess();
    }
}