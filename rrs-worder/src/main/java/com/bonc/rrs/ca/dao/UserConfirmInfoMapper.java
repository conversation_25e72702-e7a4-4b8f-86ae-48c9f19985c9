package com.bonc.rrs.ca.dao;

import com.bonc.rrs.ca.domain.UserConfirmInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户确认超标费用Mapper接口
 * 
 * <AUTHOR> @date 2024-07-08
 */
@Mapper
public interface UserConfirmInfoMapper
{
    /**
     * 查询用户确认超标费用
     * 
     * @param confirmId 用户确认超标费用主键
     * @return 用户确认超标费用
     */
    public UserConfirmInfo selectUserConfirmInfoByConfirmId(Long confirmId);

    /**
     * 查询用户确认超标费用列表
     * 
     * @param userConfirmInfo 用户确认超标费用
     * @return 用户确认超标费用集合
     */
    public List<UserConfirmInfo> selectUserConfirmInfoList(UserConfirmInfo userConfirmInfo);

    /**
     * 新增用户确认超标费用
     * 
     * @param userConfirmInfo 用户确认超标费用
     * @return 结果
     */
    public int insertUserConfirmInfo(UserConfirmInfo userConfirmInfo);

    /**
     * 修改用户确认超标费用
     * 
     * @param userConfirmInfo 用户确认超标费用
     * @return 结果
     */
    public int updateUserConfirmInfo(UserConfirmInfo userConfirmInfo);

    /**
     * 删除用户确认超标费用
     * 
     * @param confirmId 用户确认超标费用主键
     * @return 结果
     */
    public int deleteUserConfirmInfoByConfirmId(Long confirmId);

    /**
     * 批量删除用户确认超标费用
     * 
     * @param confirmIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserConfirmInfoByConfirmIds(Long[] confirmIds);
}
