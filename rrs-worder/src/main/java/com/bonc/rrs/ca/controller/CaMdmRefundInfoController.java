package com.bonc.rrs.ca.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bonc.rrs.ca.domain.CaMdmRefundInfo;
import com.bonc.rrs.ca.service.CaMdmRefundInfoService;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserTokenEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.ShiroService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 长安退款申请(ca_mdm_refund_info)表控制层
 */
@RestController
@RequestMapping("/caMdmRefund")
@RequiredArgsConstructor
public class CaMdmRefundInfoController {

    private final CaMdmRefundInfoService caMdmRefundInfoService;
    private final ShiroService shiroService;

    /**
     * 查询长安门到门退款信息列表
     */
    @GetMapping("/list")
    public IPage<CaMdmRefundInfo> list(@RequestParam(value = "detailNo", required = false) String detailNo,
                                       @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                       @RequestParam(value = "pageNum", required = false) Integer pageNum) {
        pageSize = pageSize == null ? 10 : pageSize;
        pageNum = pageNum == null ? 1 : pageNum;
        return caMdmRefundInfoService.getList(detailNo, pageSize, pageNum);
    }

    @PostMapping("/update")
    public R update(HttpServletRequest request, @RequestBody CaMdmRefundInfo caMdmRefundInfo) {
        caMdmRefundInfoService.updateStatus(caMdmRefundInfo, getUser(request));
        return R.ok();
    }

    private SysUserEntity getUser(HttpServletRequest httpRequest) {
        // 从header中获取token
        String token = httpRequest.getHeader("token");

        // 如果header中不存在token，则从参数中获取token
        if (org.apache.commons.lang.StringUtils.isBlank(token)) {
            token = httpRequest.getParameter("token");
        }

        SysUserTokenEntity tokenEntity = shiroService.queryByToken(token);
        Long userId = tokenEntity == null ? null : tokenEntity.getUserId();
        SysUserEntity user = shiroService.queryUser(userId);
        String username = user == null ? null : user.getEmployeeName();
        if (username == null) {
            throw new RRException("未登录");
        }

        return user;
    }
}
