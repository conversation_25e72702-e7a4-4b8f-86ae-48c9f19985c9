package com.bonc.rrs.ca.service;

import com.bonc.rrs.ca.domain.CaApiRequestBody;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.domain.UserConfirmInfo;

import java.util.List;

/**
 * 用户确认超标费用Service接口
 * 
 * <AUTHOR> @date 2024-07-08
 */
public interface IUserConfirmInfoService
{
    /**
     * 查询用户确认超标费用
     * 
     * @param confirmId 用户确认超标费用主键
     * @return 用户确认超标费用
     */
    public UserConfirmInfo selectUserConfirmInfoByConfirmId(Long confirmId);

    /**
     * 查询用户确认超标费用列表
     * 
     * @param UserConfirmInfo 用户确认超标费用
     * @return 用户确认超标费用集合
     */
    public List<UserConfirmInfo> selectUserConfirmInfoList(UserConfirmInfo UserConfirmInfo);

    /**
     * 新增用户确认超标费用
     * 
     * @param UserConfirmInfo 用户确认超标费用
     * @return 结果
     */
    public int insertUserConfirmInfo(UserConfirmInfo UserConfirmInfo);

    /**
     * 修改用户确认超标费用
     * 
     * @param UserConfirmInfo 用户确认超标费用
     * @return 结果
     */
    public int updateUserConfirmInfo(UserConfirmInfo UserConfirmInfo);

    /**
     * 批量删除用户确认超标费用
     * 
     * @param confirmIds 需要删除的用户确认超标费用主键集合
     * @return 结果
     */
    public int deleteUserConfirmInfoByConfirmIds(Long[] confirmIds);

    /**
     * 删除用户确认超标费用信息
     * 
     * @param confirmId 用户确认超标费用主键
     * @return 结果
     */
    public int deleteUserConfirmInfoByConfirmId(Long confirmId);

    /**
     *用户确认超标费用【同步到供应商】
     * @param requestBody
     * @return
     */
    public CaApiResponse excessExpenses(CaApiRequestBody requestBody);
}
