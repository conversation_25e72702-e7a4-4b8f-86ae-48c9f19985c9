package com.bonc.rrs.ca.util;

import lombok.Data;


/**
 * 长安请求接口地址后缀
 *
 * <AUTHOR>
 */
@Data
public class CaUrlApi {

    /**
     * 供应商确认服务时间【同步到⻔到⻔】
     */
    public static final String SERVICE_TIME = "cdz/order/install/serviceTime";

    /**
     * 供应商确认电表安装状态【同步到⻔到⻔】
     */
    public static final String AMMETER_STATUS = "cdz/order/install/ammeterStatus";

    /**
     * 供应商接单同步带有安装师傅的订单【同步到⻔到⻔】
     */
    public static final String TAKE_ORDERS = "cdz/order/install/takeOrders";

    /**
     * 供应商添加首联用户时间同步门到门
     */
    public static final String UPDATE_FIRSTCONTACT_TIME = "cdz/order/install/updateFirstcontactTime";

    /**
     * 供应商预约勘测时间【同步到⻔到⻔】
     */
    public static final String MEASURE_TIME = "cdz/order/install/measureTime";

    /**
     * 供应商确认安装条件同步订单【同步到⻔到⻔】
     */
    public static final String CAN_INSTALL_ORDERS = "cdz/order/install/canInstallOrders";

    /**
     * 供应商预约上⻔时间【同步到⻔到⻔】
     */
    public static final String VISIT_TIME = "cdz/order/install/visitTime";

    /**
     * 供应商修改超标费⽤【同步到⻔到⻔】
     */
    public static final String CONFIRM_CAN_INSTALL_ORDERS = "cdz/order/install/confirmCanInstallOrders";

    /**
     * 供应商安装完成【同步到⻔到⻔】
     */
    public static final String INSTALLATION_ORDERS_COMPLETE = "cdz/order/install/installationOrdersComplete";

    /**
     * 供应商上传安装资料，提供给门到门运营商进行审核
     */
    public static final String install_data_check = "cdz/order/install/dataCheck";

    /**
     *
     * 安装服务商查询门到门产品供应商接口
     */
    public static final String PRODUCT_SUPPLIER_SKU = "cdz/order/install/productSupplierSku";

    /**
     *  退款
     * ⻔到⻔接收审核结果【同步到⻔到⻔】
     */
    public static final String CHECK_ORDER = "cdz/order/aftersale/checkOrder";

    /**
     * todo 维修
     * 供应商同步维权处理结果【同步到⻔到⻔】
     */
    public static final String RIGHT_CHECK_ORDER = "cdz/order/right/checkOrder";








}