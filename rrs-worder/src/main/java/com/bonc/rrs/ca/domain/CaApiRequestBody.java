package com.bonc.rrs.ca.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 接口返回值实体类
 *
 * <AUTHOR>
 */
@Data
public class CaApiRequestBody implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * xxx（由⻔到⻔提供）
     */
    private String accKey;

    /**
     * xxx（由⻔到⻔提供）
     */
    private String secretKey;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 签名
     */
    private String sign;

    /**
     * 订单详情号
     */
    private String detailNo;

    /**
     * 订单详情号
     */
    private String orderDetailNo;

    /**
     * yyyy-MM-dd HH:mm:s 服务时间
     */
    private String serviceTime;

    /**
     * 电表安装状态 0未安装 1已安装
     */
    private Integer ammeterStatus;

    /**
     * 备注
     */
    private String remark;


    /**
     * 安装师傅名称
     */
    private String installerName;

    /**
     * 安装师傅电话
     */
    private String installerPhone;


    /**
     * yyyy-MM-ddHH:mm:s 勘测时间
     */
    private String measureTime;


    /**
     * 是否可安装 0否 1是
     */
    private Integer canInstallStatus;

    /**
     * 不可安装 整改 建议（选择不可安装时必填）
     */
    private String changeOpinion;

    /**
     * 安装计划 0本次直接安装 1下次预约时间安装（选择可安装时必填）
     */
    private String installPlan;

    /**
     * 存在超标费⽤
     */
    private List<OverProof> overproofList;

    /**
     * yyyy-MM-ddHH:mm:s 上⻔安装时间
     */
    private String visitTime;


    /**
     * 是否已安装 0否 1是
     */
    private Integer installStatus;


    /**
     * 订单退款编码
     */
    private Integer aftersaleId;

    /**
     * 下单时间
     */
    private String addTime;

    /**
     * 订单退款金额
     */
    private BigDecimal applyPrice;

    /**
     * 商品编号
     */
    private String commodityCode;

    /**
     * 商品编号
     */
    private String commodityName;

    /**
     * 商品sku编号
     */
    private String commoditySkuCode;

    /**
     * 商品sku名称
     */
    private String commoditySkuName;

    /**
     * 商品sku价格
     */
    private BigDecimal commoditySkuPrice;

    /**
     * 购买数量
     */
    private Integer commoditySkuNumber;

    /**
     * 实付费⽤
     */
    private BigDecimal actualPrice;

    /**
     * ⽀付⽅式
     */
    private Integer payMode;

    /**
     * ⽀付时间  yyyy-MM-dd HH:mm:ss
     */
    private String payTime;

    /**
     * 购买⽤户姓名
     */
    private String userName;

    /**
     * 购买⽤户电话
     */
    private String userPhone;

    /**
     * ⻋架号
     */
    private String vehicleVin;

    /**
     * 安装联系⼈姓名
     */
    private String consigneeName;

    /**
     * 安装联系⼈电话
     */
    private String consigneePhone;

    /**
     * 安装联系⼈地址 (重庆市-重庆市-南岸区-重庆市南岸区万达⼴场1号楼（测试勿扰）)
     */
    private String consigneeAddress;

    /**
     * 留⾔备注
     */
    private String consigneeRemark;

    /**
     * 订单状态 订单状态 0已取消100待⽀付 200待发货 220已发货 240待签收
     * 300待使⽤ 400售后中 900已完成 920待安装 950已退单
     */
    private String orderStatus;

    /**
     * 申请原因
     */
    private Integer applyReason;

    /**
     * 申请时间
     */
    private String applyDatetime;

    /**
     * 申请人名称
     */
    private String applyUserName;

    /**
     * 申请人电话
     */
    private String applyUserPhone;

    /**
     * 申请详细描述
     */
    private String applyRemark;

    /**
     * 申请图片
     */
    private String applyPics;

    /**
     * 退款进度
     */
    private Integer refundStatus;

    /**
     * 审核⼈名称
     */
    private String checkUserName;

    /**
     * 审核结果 0未通过 1通过
     */
    private Integer checkResult;

    /**
     * 审核备注
     */
    private String checkRemark;

    /**
     * 审核时间
     */
    private String checkTime;

    /**
     * 订单维权编码
     */
    private String rightId;
    /**
     * 维权进度
     */
    private String rightStatus;

    /**
     * 原供应商编码
     */
    private String oldSupplierCode;

    /**
     * 确认人电话
     */
    private String affirmUserMobile;

    /**
     * 确认人姓名
     */
    private String affirmUserName;

    /**
     * 确认状态 1是 0否
     */
    private Integer affirmStatus;
    /**
     * 确认时间
     */
    private String affirmUserTime;


    /**
     * 订单主键
     */
    private Long orderId;

    /**
     * 勘测资料
     */
    private String surveyPics;

    /**
     * 验收资料
     */
    private String checkPics;

    /**
     * 上传资料时间
     */
    private String uploadTime;

    /**
     * 充电桩码
     */
    private String pilesCode;

    /**
     * 审核状态 0待审
     * 核 1审核通过 2
     * 审核驳回
     */
    private Integer checkStatus;

    /**
     * 审核人
     */
    private String checkMan;

    /**
     * 审核原因
     */
    private String checkReason;
    /**
     * 产品供应商产品SKU编码
     */
    private String supplierProductSkuCode;

    /**
     * 首联用户时间
     */
    private String firstcontactTime;

}