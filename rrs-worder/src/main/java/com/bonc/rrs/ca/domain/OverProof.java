package com.bonc.rrs.ca.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 超标费⽤
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OverProof {

    /**
     * 费用编码  必须与门到门供应商自己维护的编码一致
     */
    private String priceCode;

    /**
     * 费用名称
     */
    private String priceName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

}