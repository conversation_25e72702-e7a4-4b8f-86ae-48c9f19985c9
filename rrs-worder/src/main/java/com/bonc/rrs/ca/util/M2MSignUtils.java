package com.bonc.rrs.ca.util;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 门到门加签工具
 */
@Slf4j
public class M2MSignUtils {

    /**
     * @param map 包含所有请求参数的Map<参数名,参数值>
     * @return
     */
    public static String getSign(Map<String, Object> map) {
        String accKey = map.get("accKey").toString();
        String secretKey = map.get("secretKey").toString();
        String timestamp = map.get("timestamp").toString();
        if (Strings.isEmpty(accKey) || Strings.isEmpty(secretKey) || Strings.isEmpty(timestamp)) {
            return null;
        }
        Map<String, String> mapParam = new HashMap<>(15);
        map.forEach((key, value) -> {
            if (!(value instanceof List) && !(value instanceof Map) && !key.equals("sign") && ObjectUtil.isNotNull(value)) {
                mapParam.put(key, String.valueOf(value));
            }
        });
        TreeMap<String, String> treeMap = new TreeMap<>(mapParam);
        String queryString = MapUtils.mapJoin(treeMap, true, false);
        if (queryString.startsWith("?")) {
            queryString = queryString.substring(1);
            // Maps.toQueryString 中 URLEncoder.encode() 会将空格转换成+
            queryString = queryString.replaceAll("\\+", "%20");
        }
        log.info("queryString to get sign: {}", queryString);
        return MD5Utils.hash(queryString).toUpperCase();
    }

    public static void main(String[] args) {
        long l = System.currentTimeMillis();
        System.out.println(l);
        String pa = "acckey=2NS5XFLJ91AKMSN90776&detailno=C32E853F34B04BC0B5A11F475A44D0AD&secretkey=2BJZTFJGC89103M2RZAFARNQXV7ZGFO9&suppliercode=900014569&timestamp="+l;
        System.out.println(MD5Utils.hash(pa).toUpperCase());
    }
}
