package com.bonc.rrs.ca.service;

import com.bonc.rrs.ca.domain.*;
import org.apache.velocity.util.Pair;

public interface CaBizService {

    CaApiResponse saveOrder(CaOrderApiVO caOrderApiVO);

    Pair<Long, String> doParseDataAndSaveOrder(CaOrderApiVO orderApiVO);

    String dealWithOrder(String worderNo);

    void orderRefundApply(CaAfterSaleCancelOrderRequest request);

    void receiveAfterSaleCheck(ReceiveAfterSaleCheckRequest request);

    CaApiResponse updateConfirmCanInstallOrders(CaApiRequestBody requestBody);

    CaApiResponse updateConfirmAccept(CaApiRequestBody requestBody);

    CaApiResponse updateSupplier(CaApiRequestBody requestBody);

    CaApiResponse installAudit(CaApiRequestBody requestBody);

    CaApiResponse unDo(CaApiRequestBody requestBody);

    String pushInstallDataCheckWorderNo(String worderNo);

    String processAttData(String worderNo);

    String canInstallOrders(String worderNo);
}
