package com.bonc.rrs.ca.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户确认超标费用对象 xkdg_user_confirm_info
 * 
 * <AUTHOR> @date 2024-07-08
 */
@Data
public class UserConfirmInfo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 用户确认主键 */
    private Long confirmId;

    /** 订单详情号 */
    @NotNull(message = "订单详情号不能为空")
    private String orderNo;

    /** 确认人姓名 */
    @NotNull(message = "确认人姓名不能为空")
    private String affirmUserName;

    /** 确认人电话 */
    @NotNull(message = "确认人电话不能为空")
    private String affirmUserPhone;

    /** 确认时间 */
    @NotNull(message = "确认时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date affirmUserTime;

    /** 确认状态 */
    @NotNull(message = "确认状态不能为空")
    private Long affirmStatus;

    /**
     * 备注
     */
    private String remark;
}
