package com.bonc.rrs.ca.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.ca.domain.*;
import com.bonc.rrs.ca.service.CaBizService;
import com.bonc.rrs.ca.service.IUserConfirmInfoService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 长安门到门
 */
@Slf4j
@RestController
@RequestMapping("/dmj/openapi/caApi")
@Api(value = "/dmj/openapi/caApi", tags = "服务商订单处理控制器")
@RequiredArgsConstructor
public class CaServiceProvidersController {

    private final CaBizService caBizService;

    @Autowired
    private IUserConfirmInfoService userConfirmInfoService;

    /**
     * 门到门用户下单【同步到供应商】
     */
    @PostMapping("/install/pushOrder")
    public CaApiResponse orderCreate(@RequestBody @Valid CaOrderApiVO orderApiVO) {
        log.info("门到门用户下单【同步到供应商】入参------【{}】", orderApiVO.toString());
        return caBizService.saveOrder(orderApiVO);
    }

    /**
     * 门到门退款申请【同步到供应商】
     */
    @PostMapping("/install/orderRefundApply")
    public CaApiResponse refundRequest(@RequestBody CaAfterSaleCancelOrderRequest request) {
        log.info("门到门退款申请【同步到供应商】入参------【" + request.toString() + "】");
        caBizService.orderRefundApply(request);
        return CaApiResponse.success();
    }

    /**
     * 用户确认超标费用【同步到供应商】
     * todo worder_user_material表
     */
    @PostMapping("/install/excessExpenses")
    public CaApiResponse excessExpenses(@RequestBody CaApiRequestBody requestBody) {
        log.info("用户确认超标费用【同步到供应商】入参------【" + requestBody.toString() + "】");
        CaApiResponse apiResponse = userConfirmInfoService.excessExpenses(requestBody);
        return apiResponse;
    }

    /**
     * 门到门修改超标费⽤【同步到供应商】
     */
    @PostMapping("/install/confirmCanInstallOrders")
    public CaApiResponse confirmCanInstallOrders(@RequestBody CaApiRequestBody requestBody) throws IOException {
        log.info("门到门修改超标费【同步到供应商】入参------【" + requestBody.toString() + "】");
        CaApiResponse apiResponse = caBizService.updateConfirmCanInstallOrders(requestBody);
        return apiResponse;
    }

    /**
     * 门到门确认验收【同步到供应商】
     * 客户验收确认验收后，将数据推送到供应商
     * todo 如果为否的话 可以考虑车企审核不通过状态 1
     */
    @PostMapping("/install/orderConfirm")
    public CaApiResponse orderConfirm(@RequestBody CaApiRequestBody requestBody) throws IOException {
        log.info("门到门确认验收【同步到供应商】入参------【" + requestBody.toString() + "】");
        CaApiResponse apiResponse = caBizService.updateConfirmAccept(requestBody);
        return apiResponse;
    }

    /**
     * 厂家审核结果【同步到供应商】
     *
     * @see CaServiceProvidersController#checkOrder(ReceiveAfterSaleCheckRequest)
     * @deprecated aftersale/checkOrder is new
     */
    @PostMapping("/aftersale/manufactorCheckOrder")
    public CaApiResponse manufactorCheckOrder(@RequestBody CaApiRequestBody requestBody) throws IOException {
        log.info("厂家审核结果【同步到供应商】入参------【" + requestBody.toString() + "】");
        ReceiveAfterSaleCheckRequest receiveAfterSaleCheckRequest = JSONObject.parseObject(JSON.toJSONString(requestBody),
                ReceiveAfterSaleCheckRequest.class);
        caBizService.receiveAfterSaleCheck(receiveAfterSaleCheckRequest);
        return CaApiResponse.success();
    }

    /**
     * 门到门接收审核结果【同步到供应商】
     *
     * @see CaServiceProvidersController#manufactorCheckOrder(CaApiRequestBody)
     */
    @PostMapping("/aftersale/checkOrder")
    public CaApiResponse checkOrder(@Valid @RequestBody ReceiveAfterSaleCheckRequest request) throws IOException {
        log.info("门到门接收审核结果【同步到供应商】入参------【" + request.toString() + "】");
        caBizService.receiveAfterSaleCheck(request);
        return CaApiResponse.success();
    }

    /**
     * 门到门更换供应商【同步到原供应商】
     */
    @PostMapping("/supplier/updateSupplier")
    public CaApiResponse updateSupplier(@RequestBody CaApiRequestBody requestBody) throws IOException {
        log.info("门到门更换供应商【同步到供应商】入参------【" + requestBody.toString() + "】");
        CaApiResponse apiResponse = caBizService.updateSupplier(requestBody);
        return apiResponse;
    }

    /**
     * 审核安装资料【同步到供应商】
     */
    @PostMapping("/install/audit")
    public CaApiResponse installAudit(@RequestBody CaApiRequestBody requestBody) throws IOException {
        log.info("审核安装资料【同步到供应商】入参------【" + requestBody.toString() + "】");
        CaApiResponse apiResponse = caBizService.installAudit(requestBody);
        return apiResponse;
    }

    @RequestMapping("/*/**")
    public CaApiResponse unDo(HttpServletRequest request, @RequestBody(required = false) CaApiRequestBody requestBody) throws IOException {
        log.info("ca undo, {},{}", request.getRequestURI(), request.getRequestURL().toString());
        log.info("ca unDo------【" + (requestBody == null ? "" : requestBody.toString()) + "】");
        CaApiResponse apiResponse = caBizService.unDo(requestBody);
        return apiResponse;
    }

    //
    // ///----
    //
    // /**
    //  * 供应商确认服务时间【同步到供应商】
    //  */
    // @PostMapping("/install/serviceTime")
    // public  CaApiResponse serviceTime(@RequestBody CaApiRequestBody requestBody)  {
    //     log.info("供应商确认服务时间【同步到供应商】入参------【" + requestBody.toString()+"】");
    //     CaApiResponse apiResponse = caBizService.updateServiceTime(requestBody);
    //     return apiResponse;
    // }
    //
    // /**
    //  * 供应商确认电表安装状态【同步到供应商】  同步到首联表吗
    //  */
    // @PostMapping("/install/ammeterStatus")
    // public  CaApiResponse ammeterStatus(@RequestBody CaApiRequestBody requestBody) {
    //     log.info("供应商确认电表安装状态【同步到供应商】入参------【" + requestBody.toString()+"】");
    //     CaApiResponse apiResponse = caBizService.updateAmmeterStatus(requestBody);
    //     return apiResponse;
    // }
    //
    // /**
    //  * 门到门接单同步带有安装师傅的订单【同步到供应商】    有疑问
    //  */
    // @PostMapping("/install/takeOrders")
    // public  CaApiResponse takeOrders(@RequestBody CaApiRequestBody requestBody)  {
    //     log.info("门到门接单同步带有安装师傅的订单【同步到供应商】入参------【" + requestBody.toString()+"】");
    //     CaApiResponse apiResponse = caBizService.updateTakeOrders(requestBody);
    //     return apiResponse;
    // }
    //
    // /**
    //  * 供应商预约勘测时间【同步到供应商】        同步到订单列表吗
    //  */
    // @PostMapping("/install/measureTime")
    // public  CaApiResponse measureTime(@RequestBody CaApiRequestBody requestBody)  {
    //     log.info("供应商预约勘测时间【同步到供应商】入参------【" + requestBody.toString()+"】");
    //     CaApiResponse apiResponse = caBizService.updateMeasureTime(requestBody);
    //     return apiResponse;
    // }

}
