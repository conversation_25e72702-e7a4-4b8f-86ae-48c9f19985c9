/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.ca.controller;

import com.bonc.rrs.ca.service.CaBizService;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 处理历史数据
 * <AUTHOR>
 * @Date 2024/7/17 10:45
 * @Version 1.0.0
 */

@RestController
@RequestMapping("/caOldOrder")
@RequiredArgsConstructor
@Slf4j
public class CaOldOrderController {
    private final CaBizService caBizService;


    /**
     * 门到门用户下单【同步到供应商】
     */
    @GetMapping("/dealWithOrder")
    public R dealWithOrder(@RequestParam("worderNo") String worderNo) {
        log.info("门到门处理老数据【同步到供应商】入参------【{}】", worderNo);
        String result = caBizService.dealWithOrder(worderNo);
        if (StringUtils.isNotBlank(result)) {
            return R.error(worderNo + result);
        }
        return R.ok();
    }


    @GetMapping("/pushInstallDataCheckWorderNo")
    public R pushInstallDataCheckWorderNo(@RequestParam("worderNo") String worderNo) {
        log.info("门到门处理老数据【同步到供应商】入参------【{}】", worderNo);
        String result = caBizService.pushInstallDataCheckWorderNo(worderNo);
        if (StringUtils.isNotBlank(result)) {
            return R.error(worderNo + result);
        }
        return R.ok();
    }


    /**
     * 门到门用户下单【同步到供应商】
     */
    @PostMapping("/dealWithOrderBatch")
    public R orderCreate(@RequestBody List<String> worderNos) {
        log.info("门到门处理老数据【同步到供应商】入参------【{}】", worderNos);
        StringBuilder sb = new StringBuilder();
        for (String worderNo : worderNos) {
            String result = caBizService.dealWithOrder(worderNo);
            if (StringUtils.isNotBlank(result)) {
                sb.append(worderNo + result);
            }
        }
        String string = sb.toString();
        if (StringUtils.isNotBlank(string)) {
            return R.error(string);
        }
        return R.ok();
    }

    /**
     * 门到门用户下单【同步到供应商】
     */
    @GetMapping("/processAttData")
    public R processAttData(@RequestParam(value = "worderNo", required = false) String worderNo) {
        String result = caBizService.processAttData(worderNo);
        return R.ok(result);
    }

    /**
     * 门到门用户下单【同步到供应商】
     */
    @GetMapping("/canInstallOrders")
    public R canInstallOrders(@RequestParam(value = "worderNo") String worderNo) {
        String result = caBizService.canInstallOrders(worderNo);
        return R.ok(result);
    }
}