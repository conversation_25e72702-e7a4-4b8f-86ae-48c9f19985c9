package com.bonc.rrs.ca.domain;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 用户下单实体类
 *
 * <AUTHOR>
 */
@Data
public class CaOrderApiVO implements Serializable {

    private static final long serialVersionUID = 7991312071170690637L;

    /**
     * 订单详情号
     */
    @NotNull(message = "订单详情号不能为空")
    private String detailNo;

    /**
     * xxx 下单时间
     */
    private String addTime;

    /**
     * xxx（商品编号)
     */
    private String commodityCode;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品sku编号
     */
    private String commoditySkuCode;

    /**
     * 商品sku名称
     */
    @NotNull(message = "商品sku名称不能为空")
    private String commoditySkuName;

    /**
     * 商品sku价格
     */
    private String commoditySkuPrice;

    /**
     * 购买数量
     */
    private Integer commoditySkuNumber;

    /**
     * 实付费⽤
     */
    private BigDecimal actualPrice;

    /**
     * ⽀付⽅式
     */
    private Integer payMode;

    /**
     * ⽀付时间  yyyy-MM-dd HH:mm:ss
     */
    private String payTime;

    /**
     * 购买⽤户姓名
     */
    private String userName;

    /**
     * 购买⽤户电话
     */
    private String userPhone;

    /**
     * ⻋架号
     */
    private String vehicleVin;

    /**
     * 安装联系⼈姓名
     */
    private String consigneeName;

    /**
     * 安装联系⼈电话
     */
    private String consigneePhone;

    /**
     * 安装联系⼈地址 (重庆市-重庆市-南岸区-重庆市南岸区万达⼴场1号楼（测试勿扰）)
     */
    private String consigneeAddress;

    /**
     * 留⾔备注
     */
    private String consigneeRemark;

    /**
     * 订单状态 订单状态 0已取消100待⽀付 200待发货 220已发货 240待签收
     * 300待使⽤ 400售后中 900已完成 920待安装 950已退单
     */
    private String orderStatus;

    /**
     * 备注
     */
    private String remark;

}