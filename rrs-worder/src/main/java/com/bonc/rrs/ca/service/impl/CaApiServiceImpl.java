package com.bonc.rrs.ca.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.ca.domain.CaApiRequestBody;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.domain.OverProof;
import com.bonc.rrs.ca.service.ICaApiService;
import com.bonc.rrs.ca.util.CaUrlApi;
import com.bonc.rrs.ca.util.CacHttpClient;
import com.bonc.rrs.intf.service.IntfLogService;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.entity.ExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.worder.service.ExtFieldService;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import jdk.nashorn.internal.objects.NativeUint8Array;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 长安对接Service接口
 *
 * <AUTHOR> @date 2024-07-03
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class CaApiServiceImpl implements ICaApiService {

    private final WorderInformationAttributeDao worderInformationAttributeDao;
    private final IntfLogService intfLogService;

    private final ExtFieldService extFieldService;

    private final WorderExtFieldService worderExtFieldService;


    final SysFilesService sysFilesService;
    final SysDictionaryDetailService sysDictionaryDetailService;

    @Override
    public CaApiResponse commCaApi(String url, CaApiRequestBody requestBody) throws IOException {
        Map<String, Object> params = JSON.parseObject(JSON.toJSONString(requestBody), Map.class);
        // if (params.containsKey("overproofList")) {
        //     params.remove("overproofList");
        //     // List<OverProof> overproofList = requestBody.getOverproofList();
        //     // List<TreeMap> collect = overproofList.stream().map(e -> JSON.parseObject(JSON.toJSONString(e), TreeMap.class))
        //     //         .collect(Collectors.toList());
        //     // params.put("overproofList", collect);
        // }
        // 获取页签
        Map<String, Object> map = CacHttpClient.getSign(params);
        params.putAll(map);
        log.info("长安第三方请求地址【" + url + "】,请求入参【" + map + "】");
        CaApiResponse response = null;
        Map<String, Object> responseMap = null;
        try {
            responseMap = CacHttpClient.requestApi(url, params);
            log.info("url:{}, responseMap:{}", url, responseMap);
            response = new CaApiResponse();
            if (responseMap.get("code") != null && Integer.valueOf(responseMap.get("code").toString()) == 200) {
                response = JSONObject.parseObject(responseMap.get("body").toString(), CaApiResponse.class);
                log.info("长安调用接口信息:{}", responseMap.get("body").toString());
                if (!response.getSuccess() && !response.getMessage().contains("不能多次") && !response.getMessage().contains("不允许再次上传安装资料")) {
                    log.error("调用长安接口返回失败 订单号:{},错误信息 {}", requestBody.getDetailNo(), response.getMessage());
                    throw new RRException(response.getMessage());
                }
            } else {
                throw new RRException("长安接口调用失败！！！");
            }
        } catch (Exception e) {
            if (!(e instanceof RRException)) {
                log.error(e);
            }
            throw new RRException(e.getMessage());
        } finally {
            intfLogService.saveIntfLog(url, requestBody.getDetailNo(), url, "", 4, JSON.toJSONString(requestBody), response == null ? JSON.toJSONString(responseMap) : JSON.toJSONString(response));
        }
        return response;
    }


    @Override
    public CaApiResponse pushServiceTime(String worderNo, String companyOrderNumber) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        QueryWrapper<WorderExtFieldEntity> queryWrapper = new QueryWrapper<>();
        // 1775	长安服务时间
        queryWrapper.eq("worder_no", worderNo).eq("field_id", 1775);
        WorderExtFieldEntity one = worderExtFieldService.getOne(queryWrapper);
        if (StringUtils.isNotBlank(one.getFieldValue())) {
            requestBody.setServiceTime(one.getFieldValue());
        } else {
            String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss"));
            requestBody.setServiceTime(time);
            one.setFieldValue(time);
            worderExtFieldService.updateById(one);
        }
        requestBody.setDetailNo(companyOrderNumber);
        return commCaApi(CaUrlApi.SERVICE_TIME, requestBody);
    }

    @Override
    public CaApiResponse pushAmmeterStatus(String worderNo, String companyOrderNumber, Integer ammeterStatus) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setAmmeterStatus(ammeterStatus);
        requestBody.setDetailNo(companyOrderNumber);
        return commCaApi(CaUrlApi.AMMETER_STATUS, requestBody);
    }

    @Override
    public CaApiResponse pushTakeOrders(String worderNo, String companyOrderNumber, String installerName, String installerPhone) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        requestBody.setInstallerName(installerName);
        requestBody.setInstallerPhone(installerPhone);
        return commCaApi(CaUrlApi.TAKE_ORDERS, requestBody);
    }

    @Override
    public CaApiResponse pushFirstcontactTime(String worderNo, String companyOrderNumber, Date time) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        requestBody.setFirstcontactTime(DateUtils.format(time));
        return commCaApi(CaUrlApi.UPDATE_FIRSTCONTACT_TIME, requestBody);
    }

    @Override
    public CaApiResponse pushMeasureTime(String worderNo, String companyOrderNumber, String time) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        requestBody.setMeasureTime(time);
        return commCaApi(CaUrlApi.MEASURE_TIME, requestBody);
    }

    @Override
    public CaApiResponse pushCanInstallOrders(String worderNo, String companyOrderNumber, Integer canInstallStatus, String installPlan,
                                              List<OverProof> overproofList) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        requestBody.setCanInstallStatus(canInstallStatus);
        requestBody.setInstallPlan(installPlan);
        requestBody.setOverproofList(overproofList);
        return commCaApi(CaUrlApi.CAN_INSTALL_ORDERS, requestBody);
    }

    @Override
    public CaApiResponse pushVisitTime(String worderNo, String companyOrderNumber, String time) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        requestBody.setVisitTime(time);
        return commCaApi(CaUrlApi.VISIT_TIME, requestBody);
    }

    @Override
    public CaApiResponse pushOrdersComplete(String worderNo, String companyOrderNumber) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        requestBody.setInstallStatus(1);
        return commCaApi(CaUrlApi.INSTALLATION_ORDERS_COMPLETE, requestBody);
    }

    @Override
    public JSONArray selectProductSupplierSku(String companyOrderNumber) throws IOException {

        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        CaApiResponse response = commCaApi(CaUrlApi.PRODUCT_SUPPLIER_SKU, requestBody);
        JSONArray array = JSON.parseArray(response.getData().toString());
        return array;
    }

    @Override
    public CaApiResponse pushConfirmCanInstallOrders(String worderNo, String companyOrderNumber, List<OverProof> overproofList) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        requestBody.setOverproofList(overproofList);
        return commCaApi(CaUrlApi.CONFIRM_CAN_INSTALL_ORDERS, requestBody);
    }

    @Override
    public CaApiResponse pushInstallDataCheck(String worderNo, String companyOrderNumber, List<WorderExtFieldEntity> fields) throws IOException {
        boolean b = checkBydOrderByWorderNo(worderNo);
        if (!b) {
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(true);
            return response;
        }

        // 1778	充电桩服务商
        WorderExtFieldEntity f2 = fields.stream().filter(f -> f.getFieldId().equals(1778)).findFirst().orElse(null);
        // 950	充电桩编码
        WorderExtFieldEntity f3 = fields.stream().filter(f -> f.getFieldId().equals(950)).findFirst().orElse(null);
        List<Integer> fieldIds = fields.stream().map(w -> w.getFieldId()).collect(Collectors.toList());

        List<ExtFieldEntity> collect = extFieldService.listByIds(fieldIds).stream().collect(Collectors.toList());
        StringBuilder sb1 = new StringBuilder();
        StringBuilder sb2 = new StringBuilder();
        for (WorderExtFieldEntity entity : fields) {
            ExtFieldEntity extField = collect.stream().filter(e -> e.getFieldId().equals(entity.getFieldId())).findFirst().orElse(null);
            //勘测阶段图片
            if (extField != null && extField.getFieldType().equals(3) && extField.getFieldPurpose().equals(2) && StringUtils.isNotBlank(entity.getFieldValue())) {
                sb1.append(entity.getFieldValue()).append(",");
            }
            //安装阶段图片
            if (extField != null && extField.getFieldType().equals(3) && extField.getFieldPurpose().equals(3) && StringUtils.isNotBlank(entity.getFieldValue())) {
                sb2.append(entity.getFieldValue()).append(",");
            }
        }
        //
        if (sb1.length() > 0) {
            sb1.deleteCharAt(sb1.length() - 1);
        }
        if (sb2.length() > 0) {
            sb2.deleteCharAt(sb2.length() - 1);
        }
        if(StringUtils.isBlank(f2.getFieldValue())){
            CaApiResponse response = new CaApiResponse();
            response.setCode("500");
            response.setSuccess(false);
            response.setMessage("请选择正确的充电桩供应商");
            return response;
        }

        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(companyOrderNumber);
        requestBody.setSupplierCode("*********");
        JSONArray array = selectProductSupplierSku(companyOrderNumber);
        String supplierProductSkuCode = "";
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);
            if (f2.getFieldValue().equals(jsonObject.getString("supplierName"))) {
                supplierProductSkuCode = jsonObject.getString("supplierCode");
            }
        }
        if(StringUtils.isBlank(supplierProductSkuCode)){
            CaApiResponse response = new CaApiResponse();
            response.setSuccess(false);
            response.setCode("500");
            response.setMessage("请选择正确的充电桩供应商");
            return response;
        }
        requestBody.setSupplierProductSkuCode(supplierProductSkuCode);

        requestBody.setPilesCode(f3.getFieldValue());
        String fileds1 = sb1.toString();
        String fileds2 = sb2.toString();
        if (StringUtils.isNotBlank(fileds1)) {
            List<SysFileEntity> sysFileByIds1 = sysFilesService.getSysFileByIds(fileds1);
            String surveyPics = sysFileByIds1.stream().map(SysFileEntity::getPath).collect(Collectors.joining(","));
            //勘测资料
            requestBody.setSurveyPics(surveyPics);

        }
        if (StringUtils.isNotBlank(fileds2)) {
            List<SysFileEntity> sysFileByIds2 = sysFilesService.getSysFileByIds(fileds2);
            String checkPics = sysFileByIds2.stream().map(SysFileEntity::getPath).collect(Collectors.joining(","));
            //验收资料
            requestBody.setCheckPics(checkPics);
        }
        requestBody.setUploadTime(DateUtils.format(f2.getCreateTime()));
        return commCaApi(CaUrlApi.install_data_check, requestBody);
    }

    @Override
    public CaApiResponse pushAfterSaleCheckOrder(CaApiRequestBody requestBody) {
        requestBody.setTimestamp(System.currentTimeMillis());
        try {
            return this.commCaApi(CaUrlApi.CHECK_ORDER, requestBody);
        } catch (IOException e) {
            throw new RRException(e.getMessage());
        }
    }

    public boolean checkBydOrderByWorderNo(String worderNo) {
        if (StringUtils.isBlank(worderNo)) {
            return false;
        }
        // 查询是否存在订单属性信息
        WorderInformationAttributeEntity worderInformationAttributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo,
                "worder_source", "pushOrder");
        if (worderInformationAttributeEntity == null) {
            return false;
        }
        return "ca".equals(worderInformationAttributeEntity.getAttributeValue());
    }

    public CaApiResponse commCaApiList(String url, CaApiRequestBody requestBody, List<OverProof> proofs) throws IOException {
        Map<String, Object> params = JSON.parseObject(JSON.toJSONString(requestBody), Map.class);
        // 获取页签
        Map<String, Object> map = CacHttpClient.getSign(params);
        params.putAll(map);
        // 如果存在超标费用  将超标费用放进去 再请求长安接口
        if (proofs != null && proofs.size() > 0) {
            params.put("overproofList", proofs);
        }
        log.info("长安第三方请求地址【{}】,请求入参【{} 】", url, params);
        Map<String, Object> responseMap = CacHttpClient.requestApi(url, params);
        CaApiResponse response = new CaApiResponse();
        if (responseMap.get("code") != null && Integer.valueOf(responseMap.get("code").toString()) == 200) {
            response = JSONObject.parseObject(responseMap.get("body").toString(), CaApiResponse.class);
            log.info("长安调用接口信息>>>>>>>>>>>>>>>>>>>>>>>>>{}", responseMap.get("body").toString());
        } else {
            throw new RuntimeException("长安接口调用失败！！！");
        }
        return response;
    }

}
