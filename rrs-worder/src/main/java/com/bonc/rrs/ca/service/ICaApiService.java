package com.bonc.rrs.ca.service;

import com.alibaba.fastjson.JSONArray;
import com.bonc.rrs.ca.domain.CaApiRequestBody;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.domain.OverProof;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 长安对接Service接口
 *
 * <AUTHOR> @date 2024-07-03
 */
public interface ICaApiService {

    /**
     * 长安对接接口
     *
     * @param url         请求参数后缀
     * @param requestBody 请求参数requestBody
     * @return map 请求返回的数据
     */
    CaApiResponse commCaApi(String url, CaApiRequestBody requestBody) throws IOException;

    /**
     * 同步门到门服务时间
     *
     * @return
     */
    public CaApiResponse pushServiceTime(String worderNo, String companyOrderNumber) throws IOException;

    /**
     * 同步门到门电表安装状态
     * <p>
     * 电表安装状态 0未安装 1已安装
     *
     * @return
     */
    public CaApiResponse pushAmmeterStatus(String worderNo, String companyOrderNumber, Integer ammeterStatus) throws IOException;

    /**
     * 同步带有安装师傅的订单
     *
     * @return
     */
    public CaApiResponse pushTakeOrders(String worderNo, String companyOrderNumber, String installerName, String installerPhone) throws IOException;

    /**
     * 同步首联时间
     *
     * @return
     */
    public CaApiResponse pushFirstcontactTime(String worderNo, String companyOrderNumber, Date time) throws IOException;

    /**
     * 同步勘测预约时间
     *
     * @return
     */
    public CaApiResponse pushMeasureTime(String worderNo, String companyOrderNumber, String time) throws IOException;

    /**
     * 同步勘测信息
     * 是否可安装 0否 1是
     * 安装计划 0本次直接安装 1下次预约时间安装（选择可安装时必填）
     * 超标费用集合没有传空
     *
     * @return
     */
    public CaApiResponse pushCanInstallOrders(String worderNo, String companyOrderNumber, Integer canInstallStatus, String installPlan,
                                              List<OverProof> overproofList) throws IOException;

    /**
     * 同步上门预约时间
     *
     * @return
     */
    public CaApiResponse pushVisitTime(String worderNo, String companyOrderNumber, String time) throws IOException;

    /**
     * 供应商修改超标费⽤【同步到⻔到⻔】
     *
     * @param worderNo
     * @param companyOrderNumber
     * @param overproofList
     * @return
     * @throws IOException
     */
    public CaApiResponse pushConfirmCanInstallOrders(String worderNo, String companyOrderNumber, List<OverProof> overproofList) throws IOException;

    /**
     *
     *
     * @param worderNo
     * @param companyOrderNumber
     * @param supplierCode           服务商编码
     * @param supplierProductSkuCode 充电桩服务商
     * @param pilesCode              充电桩码
     * @return
     * @throws IOException
     */
    public CaApiResponse pushInstallDataCheck(String worderNo, String companyOrderNumber,List<WorderExtFieldEntity> fields) throws IOException;

    /**
     * 门到门接收审核结果【同步到门到门】
     */
    CaApiResponse pushAfterSaleCheckOrder(CaApiRequestBody requestBody);

    /**
     * 供应商安装完成【同步到⻔到⻔】
     *
     * @return
     */
    public CaApiResponse pushOrdersComplete(String worderNo, String companyOrderNumber) throws IOException;


    /**
     *
     * 供应商安装完成【同步到⻔到⻔】
     *
     * @return
     */
    public JSONArray selectProductSupplierSku(String companyOrderNumber) throws IOException;




}
