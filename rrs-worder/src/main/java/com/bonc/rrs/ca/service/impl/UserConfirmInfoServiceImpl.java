package com.bonc.rrs.ca.service.impl;

import com.bonc.rrs.ca.dao.UserConfirmInfoMapper;
import com.bonc.rrs.ca.domain.CaApiRequestBody;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.domain.UserConfirmInfo;
import com.bonc.rrs.ca.service.IUserConfirmInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 用户确认超标费用Service业务层处理
 *
 * <AUTHOR> @date 2024-07-08
 */
@Service
public class UserConfirmInfoServiceImpl implements IUserConfirmInfoService {
    @Autowired
    private UserConfirmInfoMapper userConfirmInfoMapper;

    /**
     * 查询用户确认超标费用
     *
     * @param confirmId 用户确认超标费用主键
     * @return 用户确认超标费用
     */
    @Override
    public UserConfirmInfo selectUserConfirmInfoByConfirmId(Long confirmId) {
        return userConfirmInfoMapper.selectUserConfirmInfoByConfirmId(confirmId);
    }

    /**
     * 查询用户确认超标费用列表
     *
     * @param UserConfirmInfo 用户确认超标费用
     * @return 用户确认超标费用
     */
    @Override
    public List<UserConfirmInfo> selectUserConfirmInfoList(UserConfirmInfo UserConfirmInfo) {
        return userConfirmInfoMapper.selectUserConfirmInfoList(UserConfirmInfo);
    }

    /**
     * 新增用户确认超标费用
     *
     * @param UserConfirmInfo 用户确认超标费用
     * @return 结果
     */
    @Override
    public int insertUserConfirmInfo(UserConfirmInfo UserConfirmInfo) {
        return userConfirmInfoMapper.insertUserConfirmInfo(UserConfirmInfo);
    }

    /**
     * 修改用户确认超标费用
     *
     * @param UserConfirmInfo 用户确认超标费用
     * @return 结果
     */
    @Override
    public int updateUserConfirmInfo(UserConfirmInfo UserConfirmInfo) {
        return userConfirmInfoMapper.updateUserConfirmInfo(UserConfirmInfo);
    }

    /**
     * 批量删除用户确认超标费用
     *
     * @param confirmIds 需要删除的用户确认超标费用主键
     * @return 结果
     */
    @Override
    public int deleteUserConfirmInfoByConfirmIds(Long[] confirmIds) {
        return userConfirmInfoMapper.deleteUserConfirmInfoByConfirmIds(confirmIds);
    }

    /**
     * 删除用户确认超标费用信息
     *
     * @param confirmId 用户确认超标费用主键
     * @return 结果
     */
    @Override
    public int deleteUserConfirmInfoByConfirmId(Long confirmId) {
        return userConfirmInfoMapper.deleteUserConfirmInfoByConfirmId(confirmId);
    }

    /**
     * 用户确认超标费用【同步到供应商】
     *
     * @param requestBody
     * @return
     */
    @Override
    public CaApiResponse excessExpenses(CaApiRequestBody requestBody) {
        UserConfirmInfo confirmInfo = new UserConfirmInfo();
        //订单编号
        confirmInfo.setOrderNo(requestBody.getDetailNo());
        //确认人姓名
        confirmInfo.setAffirmUserName(requestBody.getAffirmUserName());
        //确认人电话
        confirmInfo.setAffirmUserPhone(requestBody.getAffirmUserMobile());
        //确认状态
        confirmInfo.setAffirmStatus(requestBody.getAffirmStatus().longValue());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            //确认时间
            confirmInfo.setAffirmUserTime(sdf.parse(requestBody.getAffirmUserTime()));
        } catch (Exception e) {
            throw new RuntimeException("确认时间格式错误");
        }
        userConfirmInfoMapper.insertUserConfirmInfo(confirmInfo);
        return CaApiResponse.success();
    }

}
