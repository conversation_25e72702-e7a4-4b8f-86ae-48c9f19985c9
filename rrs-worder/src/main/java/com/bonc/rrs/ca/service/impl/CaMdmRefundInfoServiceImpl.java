package com.bonc.rrs.ca.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.ca.dao.CaMdmRefundInfoMapper;
import com.bonc.rrs.ca.domain.CaApiRequestBody;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.domain.CaMdmRefundInfo;
import com.bonc.rrs.ca.service.CaMdmRefundInfoService;
import com.bonc.rrs.ca.service.ICaApiService;
import com.bonc.rrs.worder.constant.AttributeCodeEnum;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.bonc.rrs.worderapp.service.WorderOperationRecodeService;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@RequiredArgsConstructor
public class CaMdmRefundInfoServiceImpl extends ServiceImpl<CaMdmRefundInfoMapper, CaMdmRefundInfo> implements CaMdmRefundInfoService {

    private final ICaApiService caApiService;
    private final WorderInformationService worderInformationService;
    private final WorderInformationAttributeDao worderInformationAttributeDao;
    private final WorderRemarkLogService worderRemarkLogService;
    private final WorderOperationRecodeService worderOperationRecodeService;

    @Override
    public IPage<CaMdmRefundInfo> getList(String detailNo, Integer pageSize, Integer pageNum) {
        return this.lambdaQuery()
                .eq(StringUtils.isNotBlank(detailNo), CaMdmRefundInfo::getDetailNo, detailNo)
                .page(new Page<>(pageNum, pageSize));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(CaMdmRefundInfo requestRefundInfo, SysUserEntity user) {
        CaMdmRefundInfo dbRefundInfo = this.getById(requestRefundInfo.getId());
        if (dbRefundInfo == null) {
            throw new RRException("数据不存在");
        }
        WorderInformationEntity worderInformationEntity = worderInformationService.getByCompanyWorderNo(dbRefundInfo.getDetailNo());
        if (worderInformationEntity == null) {
            throw new RRException("工单不存在");
        }

        Date date = new Date();
        String checkTime = DateUtils.format(date);
        String userName = StringUtils.isBlank(user.getEmployeeName()) ? user.getUsername() : user.getEmployeeName();

        // 推送到长安
        CaApiRequestBody requestBody = new CaApiRequestBody();
        requestBody.setDetailNo(dbRefundInfo.getDetailNo());
        requestBody.setAftersaleId(dbRefundInfo.getAftersaleId());
        requestBody.setApplyPrice(dbRefundInfo.getApplyPrice());
        requestBody.setCheckUserName(userName);
        requestBody.setCheckResult(requestRefundInfo.getCheckResult());
        requestBody.setCheckRemark(requestRefundInfo.getCheckRemark());
        requestBody.setCheckTime(checkTime);
        CaApiResponse response = caApiService.pushAfterSaleCheckOrder(requestBody);
        if (!response.isSuccess()) {
            throw new RRException("推送审核结果到门到门失败");
        }

        // 审核不通过
        if (requestRefundInfo.getCheckResult() == 0) {
            // 取消取消订单的标记
            // 清除取消申请标识
            LambdaQueryWrapper<WorderInformationAttributeEntity> worderInformationAttributeWrapper = Wrappers.lambdaQuery();
            worderInformationAttributeWrapper.eq(WorderInformationAttributeEntity::getWorderId, worderInformationEntity.getWorderId());
            worderInformationAttributeWrapper.eq(WorderInformationAttributeEntity::getAttributeCode, AttributeCodeEnum.MDMCancelOrder.getValue());
            worderInformationAttributeDao.delete(worderInformationAttributeWrapper);
        } else {
            // 审核通过
            String str = checkTime + " " + userName;
            String content = userName + "关闭订单,原因:" + dbRefundInfo.getApplyReason() + "," + dbRefundInfo.getRemark();
            worderRemarkLogService.addOperationLog(worderInformationEntity.getWorderNo(), str + "关闭工单", content, Math.toIntExact(user.getUserId()));
            worderRemarkLogService.closeOrCancelUpdateStatus(worderInformationEntity.getWorderId(), 3, "关闭工单", 21, "取消服务", 6);
        }

        String passStatus = requestRefundInfo.getCheckResult() == 0 ? "不通过" : "通过";

        worderOperationRecodeService.saveOperationAndAuditLog(user, passStatus, requestBody, worderInformationEntity, checkTime);

        this.lambdaUpdate()
                .eq(CaMdmRefundInfo::getId, requestRefundInfo.getId())
                .set(CaMdmRefundInfo::getCheckUserName, userName)
                .set(CaMdmRefundInfo::getCheckResult, requestRefundInfo.getCheckResult())
                .set(CaMdmRefundInfo::getCheckRemark, requestRefundInfo.getCheckRemark())
                .set(CaMdmRefundInfo::getCheckTime, date)
                .update();
    }

}
