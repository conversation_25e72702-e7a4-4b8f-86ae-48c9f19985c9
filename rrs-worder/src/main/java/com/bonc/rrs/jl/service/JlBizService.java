/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.service;

import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.domain.CaOrderApiVO;
import com.bonc.rrs.jl.domain.JlApixResponse;
import com.bonc.rrs.jl.domain.JlInstallParam;
import com.bonc.rrs.jl.domain.JlOrderApiVO;
import com.bonc.rrs.util.Results;
import org.apache.velocity.util.Pair;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:36
 * @Version 1.0.0
 */
public interface JlBizService {
    JlApixResponse saveOrder(JlOrderApiVO orderApiVO);

    /**
     * 解析数据并保存
     * @param orderApiVO
     * @return
     */
    Pair<Long, String> doParseDataAndSaveOrder(JlOrderApiVO orderApiVO);

    void bridgehubOrderNotify(String worderNo,String state,String time);

    JlApixResponse getInstallOrderInfo(JlInstallParam param);

    Results getRedirectUrl(String worderNo);
}