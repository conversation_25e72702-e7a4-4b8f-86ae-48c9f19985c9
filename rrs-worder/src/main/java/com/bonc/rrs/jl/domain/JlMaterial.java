/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/20 11:23
 * @Version 1.0.0
 */
@Data
public class JlMaterial implements Serializable {
    private static final long serialVersionUID = -709742044581300468L;
    private Integer id;
    private String itemName; // 增项名称
    private String specification; // 增项规格
    private String unit; // 单位
    private BigDecimal price; // 单价
    private BigDecimal number; // 数量
}