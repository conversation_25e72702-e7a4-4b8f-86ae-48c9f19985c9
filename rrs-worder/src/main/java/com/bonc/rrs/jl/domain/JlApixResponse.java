package com.bonc.rrs.jl.domain;

import lombok.Data;

import java.io.Serializable;


/**
 * 接口返回值实体类
 *
 * <AUTHOR>
 */
@Data
public class JlApixResponse implements Serializable {


    private static final long serialVersionUID = 9164030391706038851L;
    /**
     * 响应编码
     */
    private String code;

    /**
     * 响应描述
     */
    private String desc;

    /**
     * 响应内容
     */
    private Object data;
    /**
     * 响应结果状态
     */
    private Boolean success;

    public static JlApixResponse error(String msg) {
        JlApixResponse response = new JlApixResponse();
        response.setDesc(msg);
        response.setCode("1");
        response.setSuccess(false);
        return response;
    }

    public static JlApixResponse success() {
        JlApixResponse response = new JlApixResponse();
        response.setDesc("success");
        response.setCode("0");
        response.setSuccess(true);
        return response;
    }
    public static JlApixResponse success(Object o) {
        JlApixResponse response = new JlApixResponse();
        response.setDesc("success");
        response.setCode("0");
        response.setSuccess(true);
        response.setData(o);
        return response;
    }

    public boolean isSuccess() {
        return this.getSuccess();
    }
}