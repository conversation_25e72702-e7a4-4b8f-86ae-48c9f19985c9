/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.controller;

import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.domain.CaOrderApiVO;
import com.bonc.rrs.jl.domain.JlApixResponse;
import com.bonc.rrs.jl.domain.JlInstallParam;
import com.bonc.rrs.jl.domain.JlOrderApiVO;
import com.bonc.rrs.jl.service.JlBizService;
import com.gexin.fastjson.JSON;
import com.youngking.lenmoncore.common.utils.StringUtils;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:00
 * @Version 1.0.0
 */

@Slf4j
@RestController
@RequestMapping("/dmj/openapi/news/geely")
@Api(value = "/dmj/openapi/news/geely", tags = "服务商订单处理控制器")
@RequiredArgsConstructor
public class JlServiceProvidersController {
    private final JlBizService jlBizService;
    @Value("${bridgehub.paramToken}")
    private String paramToken;

    @PostMapping("/dispatchInstallOrder")
    public JlApixResponse orderCreate(@RequestHeader(name = "x-accesstoken") String token, @RequestBody @Valid JlOrderApiVO orderApiVO) {
        if (StringUtils.isBlank(token) || !token.equals(paramToken)) {
            return JlApixResponse.error("token校验失败");
        }
        log.info("订单创建请求参数：{}", JSON.toJSONString(orderApiVO));
        return jlBizService.saveOrder(orderApiVO);
    }


    @PostMapping("/getInstallOrderInfo")
    public JlApixResponse getInstallOrderInfo(@RequestHeader(name = "x-accesstoken") String token, @RequestBody @Valid JlInstallParam param) {
        if (StringUtils.isBlank(token) || !token.equals(paramToken)) {
            return JlApixResponse.error("token校验失败");
        }
        log.info("查询接口：{}", JSON.toJSONString(param));
        return jlBizService.getInstallOrderInfo(param);
    }


}