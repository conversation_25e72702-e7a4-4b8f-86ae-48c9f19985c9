/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/20 10:24
 * @Version 1.0.0
 */

@Data
public class OrderNotifyDTO implements Serializable {

    private static final long serialVersionUID = 3811472974724926270L;

    /**
     * 派单时传入的厂商工单唯一识别ID
     */
    private String servOrderId;

    /**
     * 工单状态
     */
    private String orderState;


    /**
     * 状态的更新时间
     */
    private String updateTime;

}