/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.attribute.entity.WorderInformationAttribute;
import com.bonc.rrs.balanceprocess.entity.BalanceFileEntity;
import com.bonc.rrs.branchbalance.entity.CompanyMaterielEntity;
import com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity;
import com.bonc.rrs.branchbalance.service.CompanyMaterielService;
import com.bonc.rrs.branchbalance.service.WorderUsedMaterielService;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.util.BydUrlApi;
import com.bonc.rrs.intf.service.IntfLogService;
import com.bonc.rrs.jl.contant.JLConstants;
import com.bonc.rrs.jl.domain.*;
import com.bonc.rrs.jl.service.JlBizService;
import com.bonc.rrs.jl.utils.OrderStatusUtils;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.util.UserUtil;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.common.IdempotentCheck;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.*;
import com.bonc.rrs.worderapp.dao.WorderOrderDao;
import com.bonc.rrs.worderapp.service.WorderOperationRecodeService;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.util.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:37
 * @Version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor

public class JlBizServiceImpl implements JlBizService {
    private final BizRegionService bizRegionService;
    private final WorderTemplateService worderTemplateService;
    private final WorderInformationService worderInformationService;
    private final WorderIntfMessageService worderIntfMessageService;
    private final WorderInformationAttributeService worderInformationAttributeService;
    private final WorderRemarkLogService worderRemarkLogService;
    private final WorderOperationRecodeService worderOperationRecodeService;
    private final FlowCommon flowCommon;
    private final WorderOrderDao worderOrderDao;
    private final IdempotentCheck idempotentCheck;
    private final BrandService brandService;
    private final IntfLogService intfLogService;
    private final WorderExtFieldService worderExtFieldService;
    private final SysFilesMapper sysFilesMapper;


    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final WorderUsedMaterielService worderUsedMaterielService;
    private final CompanyMaterielService companyMaterielService;
    private final BizRegionMappingService bizRegionMappingService;


    @Value("${bridgehub.url}")
    private String preUrl;

    @Value("${bridgehub.token}")
    private String token;
    @Value("${bridgehub.jlUrl}")
    private String jiUrl;

    @Override
    public JlApixResponse saveOrder(JlOrderApiVO orderApiVO) {
        WorderIntfMessageEntity messageEntity = WorderIntfMessageEntity.builder()
                .intfCode("pushOrder")
                .worderId(0)
                .bid(3)
                .data(JSON.toJSONString(orderApiVO))
                .createTime(new Date())
                .isTransfer(0)
                .messageType(0)
                .orderCode(orderApiVO.getServOrderNo())
                .build();
        worderIntfMessageService.save(messageEntity);
        threadPoolTaskExecutor.submit(() -> asyncSave(orderApiVO, messageEntity));
        return JlApixResponse.success();
    }


    void asyncSave(JlOrderApiVO orderApiVO, WorderIntfMessageEntity messageEntity) {
        UserUtil.createDefaultLoginUser();
        String worderNo = null;
        Pair<Long, String> worderIdNoPair = null;
        try {
            worderIdNoPair = doParseDataAndSaveOrder(orderApiVO);
            worderIntfMessageService.updateWorderIdById(messageEntity.getId(), Math.toIntExact(worderIdNoPair.getFirst()));
            worderNo = worderIdNoPair.getSecond();
        } catch (Exception e) {
            log.error("创建吉利工单失败", e);
            SmsUtil.sendSms("15910305046", "吉利下单推送失败,车企订单号:" + orderApiVO.getServOrderNo(), "【到每家科技服务】");
            worderIntfMessageService.updateMessageTypeById(messageEntity.getId(), 2, e.getMessage());
            throw e;
        }

        try {
            Results results = worderInformationService.goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME, null);
            if (results.getCode() != 0) {
                throw new RRException(results.getCode() + results.getMsg());
            }
            // 修改工单状态 0
            worderInformationService.updateWorderStatus(worderNo);
            //   sendNotice(orderApiVO.getServOrderId());
            //发送短信通知吉利车企
        } catch (Exception e) {
            log.error("{}派单失败", worderNo, e);
            SmsUtil.sendSms("15910305046", "吉利订单派单失败,原因:" + e.getMessage() + ",车企订单号:" + orderApiVO.getServOrderNo(), "【到每家科技服务】");
        }
    }

    public void sendNotice(String servOrderId) {
        bridgehubOrderNotify(servOrderId, JLConstants.OrderStatus.CREATED, DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
    }

    @Override
    public Pair<Long, String> doParseDataAndSaveOrder(JlOrderApiVO orderApiVO) {
        Integer brandId = determineBrand(orderApiVO);
        if (brandId == 0) {
            SmsUtil.sendSms("15910305046", "吉利未找对对应品牌 " + orderApiVO.getCarBrand() + " 车企订单号:" + orderApiVO.getServOrderNo());

            log.error("吉利 message save order 车企订单号 ｛｝  车辆品牌 ｛｝ 未找到", orderApiVO.getServOrderNo(), orderApiVO.getCarBrand());
            throw new RRException("Failed to save order  " + orderApiVO.getServOrderNo() + "品牌未找到");
        }
        // region
        RegionCode regionCode = determineRegion(orderApiVO);
        List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(
                brandId,
                WorderTypeEnum.SERVE_CONVEY_INSTALL.getId(),
                regionCode.getProvinceCode().intValue(),
                regionCode.getCityCode().intValue()
        );
        if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
            log.info("message save order " + orderApiVO.getServOrderNo() + " 没有对应的工单模板");
            throw new RRException("Failed to save order " + orderApiVO.getServOrderNo());
        }
        WorderTemplateDto worderTemplateDto = null;
        // 几何系列一共有两个派单模版，其中几何e萤火虫系列是20米套包模版，其他是30米套包模版
        if ("几何".equals(orderApiVO.getCarBrand()) && "几何E萤火虫".equals(orderApiVO.getCarModel())) {
            worderTemplateDto = worderTemplateDtoList.stream().filter(worderTemplateDto1 -> worderTemplateDto1.getTemplateId().equals(517)).findFirst().get();
        }
        else if ("银河".equals(orderApiVO.getCarBrand()) && "吉利星愿".equals(orderApiVO.getCarModel())) {
            worderTemplateDto = worderTemplateDtoList.stream().filter(worderTemplateDto1 -> worderTemplateDto1.getTemplateId().equals(702)).findFirst().get();
        }
        else {
            if ("银河".equals(orderApiVO.getCarBrand())) {
                worderTemplateDto = worderTemplateDtoList.stream().filter(worderTemplateDto1 -> worderTemplateDto1.getTemplateId().equals(518)).findFirst().get();
            } else {
                worderTemplateDto = worderTemplateDtoList.get(worderTemplateDtoList.size() - 1);
            }
        }

        if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(orderApiVO.getServOrderNo(), worderTemplateDto.getTemplateId())) {
            log.info("message save order " + orderApiVO.getServOrderNo() + " 车企订单号已存在，无法创建工单");
            throw new RRException("Failed to save order " + orderApiVO.getServOrderNo());
        }

        // 匹配表情符
        String regex = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";

        // 只有领克对应领克 其他对应吉利	 浙江吉利控股集团汽车销售有限公司 690 ，领克汽车销售有限公司692
        Integer companyId;
        if (orderApiVO.getCarBrand().equals("领克")) {
            companyId = 692;
        } else {
            companyId = 690;
        }

        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        String address =
                regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + regionCode.getDetailedAddress();
        address = address.replaceAll(regex, "");

        String userName = orderApiVO.getOwnerName();
        userName = userName.replaceAll(regex, "");

        //   String dispatchTime = orderApiVO.getAddTime();
        String vin = orderApiVO.getVinNo();
        String contactRemark = "";
//        if (StringUtils.isNotBlank(orderApiVO.getInstallRemark())) {
//            contactRemark = orderApiVO.getConsigneeRemark().replaceAll(regex, "");
//        }
        String useName = userName + "-" + orderApiVO.getCarModel() + "-" + orderApiVO.getVinNo();
        if ("几何E萤火虫".equals(orderApiVO.getCarModel()) || "吉利星愿".equals(orderApiVO.getCarModel())) {
            useName = useName + "-20米套包";
        }
        worderInfoEntity.setPushOrderWorderSource("jl");
        worderInfoEntity.setUserName(useName);
        worderInfoEntity.setUserPhone(orderApiVO.getOwnerPhone());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(orderApiVO.getServOrderNo());
        worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());

        worderInfoEntity.setCarBrand(String.valueOf(brandId));
        worderInfoEntity.setCarModel("4");
        worderInfoEntity.setCompanyId(companyId);
        worderInfoEntity.setCompanyOrderNumberId(orderApiVO.getServOrderId());
        worderInfoEntity.setPostcode("");
        String source = orderApiVO.getContactName() + orderApiVO.getContactPhone() + orderApiVO.getOrderRemark() + orderApiVO.getInstallRemark()+orderApiVO.getSource();
        worderInfoEntity.setWorderSourceTypeValue(source);
        worderInfoEntity.setWorderTypeId(5);

        worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
        worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1, "工单类型", worderInfoEntity.getWorderTypeId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(101, "车企名称", companyId));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(153, "VIN 车架号", vin));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(306, "工单来源", source));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(902, "客户姓名", worderInfoEntity.getUserName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(903, "安装地址", address));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(905, "客户手机", worderInfoEntity.getUserPhone()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(154, "车企派单日期", ""));
        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
        R r = worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
        if (!r.isOk()) {
            throw new RRException("save worder information error");
        }
        return new Pair<>((Long) r.get("worderId"), (String) r.get("worderNo"));
    }

    @Override
    public void bridgehubOrderNotify(String servOrderId, String state, String time) {
        OrderNotifyDTO dto = new OrderNotifyDTO();
        dto.setServOrderId(servOrderId);
        dto.setOrderState(state);
        dto.setUpdateTime(time);
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE.toString());
        headers.setContentType(type);
        headers.add("token", "appKey");

        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(dto), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = preUrl + JLConstants.ApiCode.UPDATEINSTALLORDERNOTIFY + BydUrlApi.PUSH_CONTACT.getCode();
        log.info("吉利接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("吉利接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(JLConstants.ApiCode.UPDATEINSTALLORDERNOTIFY, servOrderId, url, "吉利接口回传状态", 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
    }

    @Override
    public JlApixResponse getInstallOrderInfo(JlInstallParam param) {
        String servOrderId = param.getServOrderId();
        List<String> fields = param.getFields();
        JlOrderInstallInfo jlOrderInstallInfo = null;
        Map<String, Object> result = null;
        try {
            QueryWrapper<WorderInformationAttributeEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("attribute_value", servOrderId).eq("attribute_code", "company_order_id").eq("attribute", "jl").eq("is_delete", 0);
            WorderInformationAttributeEntity one = worderInformationAttributeService.getOne(queryWrapper);
            if (one == null) {
                return JlApixResponse.error("未找到工单");
            }
            WorderInfoEntity worderInfoEntity = worderInformationService.getWorderInfoEntityByWorderId(one.getWorderId());
            List<WorderExtFieldEntity> extFieldEntities = worderExtFieldService.getFieldsByWorderNo(worderInfoEntity.getWorderNo());
            jlOrderInstallInfo = new JlOrderInstallInfo();
            //
            String vinNo = "";
            if (StringUtils.isNotBlank(worderInfoEntity.getUserName())) {
                String[] split = worderInfoEntity.getUserName().split("-");
                if (split.length > 2) {
                    vinNo = worderInfoEntity.getUserName().split("-")[2]; // 车辆VIN码

                }
            }

            String pileNo = ""; // 充电桩编号

            // 取电方式 取电方式 1=报装 2=物业 3=自家 4=其他
            Integer powerSupplyMethod = null;
            Integer cableLen = null; // 实际电缆长度
            String cableType = ""; // 实际电缆规格
            String setupMethod = ""; // 安装方式  1=壁挂 2=立柱 3=其他
            String layingMethod = ""; // 敷设方式 1=PVC 2=镀锌 3=桥架 4=其他
            String completeTime = ""; // 安装完成时间
            String installRemark = ""; // 安装备注
            String carType="";// 车辆类型
            String carBuyDate="";// 车辆购买时间

            List<String> layingPhoto = new ArrayList<>(); // 敷设照片地址
            List<String> surveyPhoto = new ArrayList<>(); // 勘测单照片地址
            List<String> installPhoto = new ArrayList<>(); // 安装单照片地址
            List<String> powerSupplyPhoto = new ArrayList<>(); // 取电点照片
            List<String> nameplatePhoto = new ArrayList<>(); // 充电桩铭牌照片
            List<String> testPhoto = new ArrayList<>(); // 充电测试照片
            List<String> groupPhoto = new ArrayList<>(); // 人桩合照/完工图
            List<String> idCardPhoto = new ArrayList<>(); // 证件信息照片
            List<String> materialsPhoto = new ArrayList<>(); // 施工材料照片
            for (WorderExtFieldEntity entity : extFieldEntities) {
                //充电桩编码
                if (entity.getFieldId().equals(950)) {
                    pileNo = entity.getFieldValue();
                }
                //取电方式
                if (entity.getFieldId().equals(920)) {
                    if ("电力报装".equals(entity.getFieldValue())) {
                        powerSupplyMethod = 1;
                    } else if ("物业电".equals(entity.getFieldValue())) {
                        powerSupplyMethod = 2;
                    } else if ("自家电".equals(entity.getFieldValue())) {
                        powerSupplyMethod = 3;
                    } else {
                        powerSupplyMethod = 4;
                    }
                }

                //实际电缆长度
                if (entity.getFieldId().equals(1242)) {
                    String fieldValue = entity.getFieldValue();
                    try {
                        cableLen = Integer.parseInt(fieldValue = fieldValue.replace("米", ""));
                    } catch (NumberFormatException e) {
                        log.error("{} 实际电缆长度填写不规范", servOrderId);
                    }
                }

                //实际电缆规格
                if (entity.getFieldId().equals(1243)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        if (entity.getFieldValue().contains("6")) {
                            //   实际电缆规格 1=3x6mm^2 2=3x10mm^2
                            cableType = "1";
                        } else if (entity.getFieldValue().contains("10")) {
                            //   实际电缆规格 1=3x6mm^2 2=3x10mm^2
                            cableType = "2";
                        } else {
                            cableType = "1";
                        }
                    }

                }

                //安装方式
                if (entity.getFieldId().equals(921)) {
                    if ("自带立柱".equals(entity.getFieldValue())) {
                        setupMethod = "2";
                    } else if ("客户立柱".equals(entity.getFieldValue())) {
                        setupMethod = "2";
                    } else if ("壁挂".equals(entity.getFieldValue())) {
                        setupMethod = "1";
                    }else if (StringUtils.isNotBlank(entity.getFieldValue()) && entity.getFieldValue().contains("立柱")){
                        setupMethod = "2";
                    }else {
                        setupMethod = "1";
                    }
                }
                //安装备注
//                if (entity.getFieldId().equals(306)) {
//                    installRemark = entity.getFieldValue();
//                }

                //敷设方式
                if (entity.getFieldId().equals(922)) {
                    if ("pvc管".equals(entity.getFieldValue())) {
                        layingMethod = "1";
                    } else if ("镀锌管".equals(entity.getFieldValue())) {
                        layingMethod = "2";
                    } else if ("桥架".equals(entity.getFieldValue())) {
                        layingMethod = "3";
                    } else {
                        layingMethod = "4";
                    }
                }

                //安装完成时间
                if (entity.getFieldId().equals(1197) || entity.getFieldId().equals(942)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue()))
                        completeTime = entity.getFieldValue().substring(0, 10);
                }
                //
                //人桩合照/完工图
                // 吉利-完工照片
                if (entity.getFieldId().equals(1449)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        groupPhoto.addAll(collect);
//                        layingPhoto.addAll(collect);
                    }
                }

                //敷设照片
                //吉利-走线图
                if (entity.getFieldId().equals(1450)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        layingPhoto.addAll(collect);
                    }
                }

                //取电点照片
                //吉利-安装后取电点照片
                if (entity.getFieldId().equals(1451)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        powerSupplyPhoto.addAll(collect);
                    }
                }

                //安装单照片
                //吉利-竣工单
                if (entity.getFieldId().equals(1452)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        installPhoto.addAll(collect);
                    }
                }

                //充电桩铭牌照片
                //吉利-桩编码
                if (entity.getFieldId().equals(1453)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        nameplatePhoto.addAll(collect);
                    }
                }


                //人桩合照
                //吉利-人桩合影
//                if (entity.getFieldId().equals(1454)) {
//                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
//                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
//                        groupPhoto.addAll(collect);
//                    }
//                }


                //证件信息照片
                //吉利-人桩合影
                if (entity.getFieldId().equals(1455)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        idCardPhoto.addAll(collect);
                    }
                }

                //施工材料照片
                //吉利-充点卡照片 吉利-pvc管参数  吉利-首尾米标图 吉利-充电器接线图  吉利-漏保照片
                if (entity.getFieldId().equals(1484)
                        || entity.getFieldId().equals(1485) || entity.getFieldId().equals(1488) || entity.getFieldId().equals(1486)
                ) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        materialsPhoto.addAll(collect);
                    }
                }

                //充电测试照片
                //吉利-电压检测照片
                if (entity.getFieldId().equals(1489) || entity.getFieldId().equals(1482)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        testPhoto.addAll(collect);
                    }
                }

                //勘测单照片地址
                //勘测单
                if (entity.getFieldId().equals(1633)) {
                    if (StringUtils.isNotBlank(entity.getFieldValue())) {
                        List<String> collect = Arrays.stream(entity.getFieldValue().split(",")).collect(Collectors.toList());
                        surveyPhoto.addAll(collect);
                    }
                }
            }
            jlOrderInstallInfo.setServOrderId(servOrderId);
            jlOrderInstallInfo.setOrderState(OrderStatusUtils.getJlStatus(worderInfoEntity.getWorderStatus(), worderInfoEntity.getWorderExecStatus()));
            jlOrderInstallInfo.setCarBrand(brandService.getBaseMapper().selectById(worderInfoEntity.getBrandId()).getBrandName());
            jlOrderInstallInfo.setServOrderNo(worderInfoEntity.getCompanyOrderNumber());
            jlOrderInstallInfo.setVinNo(vinNo);
            jlOrderInstallInfo.setPileNo(pileNo);
            if (powerSupplyMethod != null) {
                jlOrderInstallInfo.setPowerSupplyMethod(powerSupplyMethod);
            }
            if (cableLen != null) {
                jlOrderInstallInfo.setCableLen(cableLen);
            }
            List<String> all = Stream.of(groupPhoto, layingPhoto, powerSupplyPhoto, installPhoto, nameplatePhoto, testPhoto, surveyPhoto, materialsPhoto, idCardPhoto).flatMap(List::stream).collect(Collectors.toList());
            List<BalanceFileEntity> balanceFileEntities = null;
            if (!CollectionUtils.isEmpty(all)) {
                balanceFileEntities = sysFilesMapper.selectBatchIds(all);
            }
            jlOrderInstallInfo.setCableType(cableType);
            jlOrderInstallInfo.setSetupMethod(setupMethod);
            jlOrderInstallInfo.setLayingMethod(layingMethod);
            jlOrderInstallInfo.setCompleteTime(completeTime);
            jlOrderInstallInfo.setInstallRemark(installRemark);
            jlOrderInstallInfo.setLayingPhoto(getPicUrl(balanceFileEntities, layingPhoto));
            jlOrderInstallInfo.setSurveyPhoto(getPicUrl(balanceFileEntities, surveyPhoto));
            jlOrderInstallInfo.setInstallPhoto(getPicUrl(balanceFileEntities, installPhoto));
            jlOrderInstallInfo.setPowerSupplyPhoto(getPicUrl(balanceFileEntities, powerSupplyPhoto));
            jlOrderInstallInfo.setNameplatePhoto(getPicUrl(balanceFileEntities, nameplatePhoto));
            jlOrderInstallInfo.setTestPhoto(getPicUrl(balanceFileEntities, testPhoto));
            jlOrderInstallInfo.setGroupPhoto(getPicUrl(balanceFileEntities, groupPhoto));
            jlOrderInstallInfo.setIdCardPhoto(getPicUrl(balanceFileEntities, idCardPhoto));
            //施工材料照片 不能超过5张 漏保是必填的，还要PVC管参数（用户自布线时上传免责协议），其他的检查的不严，这样，
            // 如果资料超过了5张，我们依次优先选择 漏保、PVC管参数
            if (materialsPhoto.size() > 5) {
                materialsPhoto = dealWithMaterialsPhoto(extFieldEntities);
            }
            jlOrderInstallInfo.setMaterialsPhoto(getPicUrl(balanceFileEntities, materialsPhoto));
            //如果有增项
            if (fields.contains("addItem")) {
                QueryWrapper<WorderUsedMaterielEntity> qw = new QueryWrapper<>();
                qw.eq("worder_id", worderInfoEntity.getWorderId());
                List<WorderUsedMaterielEntity> list = worderUsedMaterielService.list(qw);
                if (!CollectionUtils.isEmpty(list)) {
                    List<Integer> materielIds = list.stream().map(w -> w.getMaterielId()).collect(Collectors.toList());
                    QueryWrapper<CompanyMaterielEntity> qw2 = new QueryWrapper<>();
                    qw2.eq("is_use", 1).eq("company_name", "jl").in("system_materiel_id", materielIds);
                    List<CompanyMaterielEntity> companyMaterielEntities = companyMaterielService.list(qw2);
                    if (!CollectionUtils.isEmpty(companyMaterielEntities)) {
                        List<JlMaterial> jlMaterials = companyMaterielEntities.stream().map(c -> {
                            JlMaterial jlMaterial = new JlMaterial();
                            BeanUtils.copyProperties(c, jlMaterial);
                            jlMaterial.setId(c.getCompanyMaterielId());
                            jlMaterial.setPrice(new BigDecimal(c.getPrice()));
                            WorderUsedMaterielEntity worderUsedMaterielEntity = list.stream().filter(w -> w.getMaterielId().equals(c.getSystemMaterielId())).findFirst().get();
                            jlMaterial.setNumber(worderUsedMaterielEntity.getNum());
                            return jlMaterial;
                        }).collect(Collectors.toList());
                        jlOrderInstallInfo.setAddItem(jlMaterials);
                        BigDecimal reduce = jlMaterials.stream().map(j -> j.getPrice().multiply(j.getNumber())).reduce(BigDecimal.ZERO, BigDecimal::add);
                        jlOrderInstallInfo.setAddItemRealPrice(reduce);
                    }

                }
            }
            result = getFiledMap(fields, jlOrderInstallInfo);
        } catch (Exception e) {
            log.error("吉利获取车企资料未知异常", e);
            return JlApixResponse.error("系统未知异常");
        }
//        jlOrderInstallInfo.setAddItem();
//        jlOrderInstallInfo.setAddItemRealPrice();

        return JlApixResponse.success(result);
    }

    private List<String> dealWithMaterialsPhoto(List<WorderExtFieldEntity> extFieldEntities) {
        List<String> materialsPhoto = new ArrayList<>();
        //优先选择 漏保、PVC管参数
        WorderExtFieldEntity entity1 = extFieldEntities.stream().filter(f -> f.getFieldId().equals(1486)).findFirst().orElse(null);
        if (entity1 != null && StringUtils.isNotBlank(entity1.getFieldValue())) {
            List<String> collect = Arrays.stream(entity1.getFieldValue().split(",")).collect(Collectors.toList());
            materialsPhoto.addAll(collect);
        }
        //PVC管参数
        WorderExtFieldEntity entity2 = extFieldEntities.stream().filter(f -> f.getFieldId().equals(1484)).findFirst().orElse(null);
        if (entity2 != null && StringUtils.isNotBlank(entity2.getFieldValue())) {
            List<String> collect = Arrays.stream(entity2.getFieldValue().split(",")).collect(Collectors.toList());
            materialsPhoto.addAll(collect);
        }

        //吉利-充点卡照片
        WorderExtFieldEntity entity3 = extFieldEntities.stream().filter(f -> f.getFieldId().equals(1483)).findFirst().orElse(null);
        if (entity3 != null && StringUtils.isNotBlank(entity3.getFieldValue())) {
            List<String> collect = Arrays.stream(entity3.getFieldValue().split(",")).collect(Collectors.toList());
            materialsPhoto.add(collect.get(0));
        }

        //吉利-首尾米标图
        WorderExtFieldEntity entity4 = extFieldEntities.stream().filter(f -> f.getFieldId().equals(1485)).findFirst().orElse(null);
        if (entity4 != null && StringUtils.isNotBlank(entity4.getFieldValue())) {
            List<String> collect = Arrays.stream(entity4.getFieldValue().split(",")).collect(Collectors.toList());
            materialsPhoto.add(collect.get(0));
        }

        //吉利-充电器接线图
        WorderExtFieldEntity entity5 = extFieldEntities.stream().filter(f -> f.getFieldId().equals(1488)).findFirst().orElse(null);
        if (entity5 != null && StringUtils.isNotBlank(entity5.getFieldValue())) {
            List<String> collect = Arrays.stream(entity5.getFieldValue().split(",")).collect(Collectors.toList());
            materialsPhoto.add(collect.get(0));
        }
        return materialsPhoto;
    }

    @Override
    public Results getRedirectUrl(String worderNo) {
        WorderInformationEntity worderInfo = worderInformationService.getByWorderNo(worderNo);
        if (worderInfo == null) {
            return Results.message(500, "未找到工单信息");
        }
        QueryWrapper<WorderInformationAttributeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("worder_id", worderInfo.getWorderId()).eq("attribute_code", "company_order_id").eq("attribute", "jl");
        WorderInformationAttributeEntity one = worderInformationAttributeService.getOne(queryWrapper);
        if (one == null) {
            return Results.message(500, "历史订单未对接吉利车企无法跳转");
        }
        String redirect = jiUrl + "?id=" + one.getAttributeValue();
        Map<String, String> map = new HashMap<>();
        map.put("url", redirect);
        return Results.message(0, "成功", map);
    }

    @SneakyThrows
    private Map<String, Object> getFiledMap(List<String> fields, JlOrderInstallInfo jlOrderInstallInfo) {
        Class<? extends JlOrderInstallInfo> aClass = jlOrderInstallInfo.getClass();
        Map<String, Object> map = new HashMap<>();
        for (String filed : fields) {
            Field cableTypeField = aClass.getDeclaredField(filed);
            // 如果cableType字段是private的，需要以下操作
            cableTypeField.setAccessible(true);
            map.put(filed, cableTypeField.get(jlOrderInstallInfo));
        }

        return map;

    }

    private String getPicUrl(List<BalanceFileEntity> fileEntities, List<String> list) {
        if (CollectionUtils.isEmpty(fileEntities) || CollectionUtils.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (String s : list) {
            BalanceFileEntity balanceFileEntity = fileEntities.stream().filter(f -> f.getFileId().toString().equals(s)).findFirst().orElse(null);
            if (balanceFileEntity != null) {
                sb.append(balanceFileEntity.getPath()).append(",");
            }
        }
        String string = sb.toString();
        if (StringUtils.isNotBlank(string)) {
            string = string.substring(0, string.length() - 1);
        }
        return string;
    }

    private RegionCode determineRegion(JlOrderApiVO orderApiVO) {
        RegionCode regionCode = new RegionCode();
        String code = orderApiVO.getServAdCode();
        String servAddress = orderApiVO.getServAddress();
        regionCode.setDetailedAddress(servAddress);
        BizRegionEntity b = bizRegionService.getInfoByCode(Integer.parseInt(code));
        if (b == null) {
            QueryWrapper<BizRegionMappingEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("company_name", "jl").eq("code", code);
            BizRegionMappingEntity one = bizRegionMappingService.getOne(queryWrapper);
            if (one != null) {
                b = bizRegionService.getById(one.getBizRegionId());
            } else {
                SmsUtil.sendSms("15910305046", "服务地区编码错误，请手动修改工单 原报文信息:" + JSON.toJSONString(orderApiVO), "【到每家科技服务】");
                //北京市朝阳区
                b = bizRegionService.getById(378);
            }

        }
        BizRegionEntity bizRegionEntity = bizRegionService.getBaseMapper().selectById(b.getPid());
        regionCode.setAreaCode(b.getId());
        regionCode.setCityCode(b.getPid());
        regionCode.setProvinceCode(bizRegionEntity.getPid());
        return regionCode;
    }

    private Integer determineBrand(JlOrderApiVO orderApiVO) {
        String carBrand = orderApiVO.getCarBrand();
        if (StringUtils.isNotBlank(carBrand)) {
            QueryWrapper<BrandEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("brand_name", carBrand);
            BrandEntity one = brandService.getOne(queryWrapper);
            return one != null ? one.getId() : 0;
        } else {
            return 0;
        }
    }


}
