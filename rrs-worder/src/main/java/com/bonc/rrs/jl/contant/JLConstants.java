/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.contant;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/20 10:26
 * @Version 1.0.0
 */
public interface JLConstants {


    interface ApiCode {
        //报修单创建
        String UPDATEINSTALLORDERNOTIFY = "/api/bridgehub/geely/updateInstallOrderNotify";

        //报修
    }

    interface OrderStatus {

        // 已创建
        String CREATED = "created";
        //已分派
        String ASSIGNED = "assigned";
        // 已完成
        String COMPLETED = "completed";
        //审核通过
        String AUDITED = "audited";
        //已关闭
        String CLOSED = "closed";
    }
}
