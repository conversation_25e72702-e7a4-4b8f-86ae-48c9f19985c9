package com.bonc.rrs.jl.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class JlOrderInstallInfo  implements Serializable {
    private static final long serialVersionUID = -3187207875174679877L;
    private String servOrderId; // 详情中返回的servOrderId应与请求id相同
    private String orderState; // 工单状态
    private String carBrand; // 汽车品牌
    private String servOrderNo; // 服务订单编号
    private String vinNo; // 车辆VIN码
    private String pileNo; // 充电桩编号
    // 取电方式 取电方式 1=报装 2=物业 3=自家 4=其他
    private Integer powerSupplyMethod;
    private Integer cableLen; // 实际电缆长度
    private String cableType; // 实际电缆规格
    private String setupMethod; // 安装方式
    private String layingMethod; // 敷设方式
    private String completeTime; // 安装完成时间
    private String installRemark; // 安装备注
    private String layingPhoto; // 敷设照片地址
    private String surveyPhoto; // 勘测单照片地址
    private String installPhoto; // 安装单照片地址
    private String powerSupplyPhoto; // 取电点照片
    private String nameplatePhoto; // 充电桩铭牌照片
    private String testPhoto; // 充电测试照片
    private String groupPhoto; // 人桩合照/完工图
    private String idCardPhoto; // 证件信息照片
    private String materialsPhoto; // 施工材料照片
    private List<JlMaterial> addItem; // 增项清单
    private BigDecimal addItemRealPrice; // 实际增项费用
}