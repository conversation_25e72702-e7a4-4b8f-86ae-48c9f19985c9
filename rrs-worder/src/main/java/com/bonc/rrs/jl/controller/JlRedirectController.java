/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.controller;

import com.bonc.rrs.baidumap.annotations.LogPrint;
import com.bonc.rrs.jl.service.JlBizService;
import com.bonc.rrs.util.Results;
import com.youngking.lenmoncore.common.utils.StringUtils;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/27 11:09
 * @Version 1.0.0
 */

@Slf4j
@RestController
@RequestMapping("/geely")
@Api(value = "/geely", tags = "服务商订单处理控制器")
@RequiredArgsConstructor
public class JlRedirectController {
    private final JlBizService jlBizService;
    @RequestMapping(value = "getRedirectUrl", method = {RequestMethod.GET, RequestMethod.POST},produces = "application/json;charset=UTF-8")
    @ResponseBody
    @LogPrint
    public Results getRedirectUrl(@RequestParam(value = "worderNo") String worderNo){
        if(StringUtils.isBlank(worderNo)){
            return Results.message(500,"工单号不能为空",null);
        }
        return jlBizService.getRedirectUrl(worderNo);
    }
}