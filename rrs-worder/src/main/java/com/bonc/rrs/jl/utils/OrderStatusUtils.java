/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.utils;

import com.bonc.rrs.jl.contant.JLConstants;
import io.swagger.models.auth.In;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/20 17:04
 * @Version 1.0.0
 */
public class OrderStatusUtils {

    public static String getJlStatus(Integer worderStaus, Integer worderExecStatus){
        if(worderStaus == 0){
            return JLConstants.OrderStatus.CREATED;
        }else if(worderStaus == 6){
            return JLConstants.OrderStatus.CLOSED;
        }
        else if(worderStaus == 1){
            return JLConstants.OrderStatus.ASSIGNED;
        }else if( worderExecStatus == 15){
            return JLConstants.OrderStatus.COMPLETED;
        }else if(worderExecStatus == 16){
            return JLConstants.OrderStatus.AUDITED;
        }else if(worderStaus == 2){
            return JLConstants.OrderStatus.ASSIGNED;
        }
        else if(worderStaus == 4){
            return JLConstants.OrderStatus.AUDITED;
        }
        return null;
    }
}