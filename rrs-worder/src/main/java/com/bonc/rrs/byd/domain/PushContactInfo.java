package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 服务商联系信息
 * 
 * <AUTHOR>
 */
@Data
public class PushContactInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作人
     */
    @ApiModelProperty(name = "操作人",required = true)
    private String operatePerson;

    /**
     * 安装订单编号
     */
    @ApiModelProperty(name = "安装订单编号",required = true)
    private String orderCode;

    /**
     * 首次联系时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(name = "首次联系时间 格式：yyyy-MM-dd HH:mm:ss",required = true)
    private String firstContactTime;

    /**
     * 预计上门勘测时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(name = "预计上门勘测时间 格式：yyyy-MM-dd HH:mm:ss")
    private String planToSurveyTime;

    /**
     * 预计上门安装时间  格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(name = "预计上门安装时间  格式：yyyy-MM-dd HH:mm:ss")
    private String planToInstallTime;

    /**
     * 联系信息集合
     */
    private List<ContactRecord> contactRecordList;

}