package com.bonc.rrs.byd.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 报修联系信息回传
 * 
 * <AUTHOR>
 */
@Data
public class AoPushContactInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作人
     */
    private String operatePerson;

    /**
     * 安装订单编号
     */
    private String orderCode;

    /**
     * 首次联系时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String firstContactTime;

    /**
     * 预计上门服务时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String planToSurveyTime;

    /**
     * 联系信息集合
     */
    private List<ContactRecord> contactRecordList;

}