package com.bonc.rrs.byd.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 消息已读状态实体类
 */
@Data
public class PushNotifyReadRecord {

    /**
     * 消息通知id，厂端推送消息通知时传入
     */
    private String id;

    /**
     * 已读人
     */
    private String readByName;

    /**
     * 已读时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String readTime;

    /**
     * 已读渠道，1:安装商系统接口回传已读，2:CPIM系统安装商视角已读，传固定值：1
     */
    private Integer readChannel = 1;

    /**
     * 状态，1:已读/0:未读，传固定值：1
     */
    private Integer readStatus = 1;
}
