package com.bonc.rrs.byd.domain;

import lombok.Data;

/**
 * 厂端审核后推送审核信息/服务商取消审核信息
 *
 * <AUTHOR>
 */
@Data
public class PushSubmitInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 安装订单编号   多个安装订单编号，安装订单编号中间用’,’隔开,单次最多 100 个订单编号
     */
    private String orderCode;

    /**
     * 审核结果  1 通过，2 拒绝
     */
    private String result;

    /**
     * 拒绝原因
     */
    private String remark;

    /**
     * 审核人
     */
    private String examinePerson;

    /**
     * 审核时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String examineDate;


}
