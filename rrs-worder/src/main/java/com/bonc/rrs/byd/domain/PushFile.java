package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 上传文件，获取短链接
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushFile implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 附件链接地址
     */
    @ApiModelProperty(value = "附件链接地址")
    private String url;
}
