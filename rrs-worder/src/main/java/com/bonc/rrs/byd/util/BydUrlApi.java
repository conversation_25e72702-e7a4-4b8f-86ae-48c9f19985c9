package com.bonc.rrs.byd.util;

/**
 * 比亚迪请求接口地址
 *
 * <AUTHOR>
 */
public enum BydUrlApi {

    // 创建枚举内部类方法
    PUSH_CONTACT("/jumpto/openapi/sp/pushContactInfo", "服务商联系信息回传"),
    PUSH_SURVEY("/jumpto/openapi/sp/pushSurveyInfo", "服务商勘测信息回传"),
    PUSH_INSTALLATION("/jumpto/openapi/sp/pushInstallationInfo", "服务商安装信息回传"),
    PUSH_ACCESSORIES("/jumpto/openapi/sp/pushAccessoriesInfo", "服务商安装附件信息回传"),
    PUSH_SUBMIT("/jumpto/openapi/sp/pushSubmitReviewInfo", "服务商提交审核"),
    PUSH_CANCEL("/jumpto/openapi/sp/pushCancelReviewInfo", "服务商取消审核信息回传"),
    PUSH_PROCESSING("/jumpto/openapi/sp/ao/pushProcessing", "服务商售后信息回传"),
    PUSH_INSTALLATIONINFO("/jumpto/openapi/sp/pushInstallationInfo", "服务商安装信息回传"),
    PUSH_ACCESSORIESINFO("/jumpto/openapi/sp/pushAccessoriesInfo", "安装附件信息回传"),
    PUSH_REVIEWINFO("/jumpto/openapi/sp/ao/pushReviewInfo", "CPIM 服务商报修订单审核信息回传"),
    PUSH_REPAIRACCESSORIESINFO("/jumpto/openapi/sp/ao/pushAccessoriesInfo", "CPIM 服务商报修信息回传"),
    AO_PUSH_CONTACT("/jumpto/openapi/sp/ao/pushContactInfo", "服务商报修联系信息回传"),
    PUSH_NOTIFY_READ_RECORD("/jumpto/openapi/sp/pushNotifyReadRecord", "服务商消息已读状态回传"),
    PUSH_BRANCH_RECORD("/jumpto/openapi/sp/pushBranchRecord", "服务商安装订单信息网点记录回传"),
    AO_PUSH_BRANCH_RECORD("/jumpto/openapi/sp/ao/pushBranchRecord", "服务商维修订单信息网点记录回传"),
    PUSH_REPAIRACCESSORIESINFO_NEW("/jumpto/openapi/sp/ao/pushRefactorProcessing", "CPIM 服务商报修信息（重构）回传"),
    FILE_UPLOAD("/jumpto/openapi/sp/file/upload", "服务商上传附件"),
    PUSH_SPLIT_INFO("/jumpto/openapi/sp/ao/pushSplitInfo", "服务商回传拆桩信息"),
    PUSH_MOVE_INSTALL_INFO("/jumpto/openapi/sp/ao/pushInstallationInfo", "服务商回传移桩安装信息"),
    GET_TEMP_LINK("/jumpto/openapi/sp/file/getTemporaryLink", "服务商获取附件临时链接"),

    // 检查订单相关接口
    ET_PUSH_CONTACT("/jumpto/openapi/sp/et/pushContactInfo", "服务商回传检查任务联系信息"),
    ET_PUSH_PROCESSING("/jumpto/openapi/sp/et/pushProcessing", "服务商回传检查信息"),
    ET_PUSH_REVIEW("/jumpto/openapi/sp/et/pushReviewInfo", "服务商检查订单提交审核"),
    ET_PUSH_CANCEL_REVIEW("/jumpto/openapi/sp/et/pushCancelReviewInfo", "服务商取消订单审核信息回传"),

    // 2.15 接口
    PUSH_MOVE_PILE_INSTALL_SOCKET("/jumpto/openapi/sp/pushMovePileInstallSocket", "服务商回传客户是否存在挪桩，加装插座意向");

    private final String code;
    private final String desc;

    BydUrlApi(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
//    /** 服务商联系信息回传 */
//    public static final String PUSH_CONTACT = "/jumpto/openapi/sp/pushContactInfo";
//
//    /** 服务商勘测信息回传 */
//    public static final String PUSH_SURVEY = "/jumpto/openapi/sp/pushSurveyInfo";
//
//    /** 服务商安装信息回传 */
//    public static final String PUSH_INSTALLATION = "/jumpto/openapi/sp/pushInstallationInfo";
//
//    /** 服务商安装附件信息回传 */
//    public static final String PUSH_ACCESSORIES = "/jumpto/openapi/sp/pushAccessoriesInfo";
//
//    /**服务商提交审核 */
//    public static final String PUSH_SUBMIT = "/jumpto/openapi/sp/pushSubmitReviewInfo";
//
//    /**服务商取消审核信息回传 */
//    public static final String PUSH_CANCEL = "/jumpto/openapi/sp/pushCancelReviewInfo";
//
//    /**服务商售后信息回传 */
//    public static final String PUSH_PROCESSING = "/jumpto/openapi/sp/ao/pushProcessing";
//
//    /**服务商安装信息回传 */
//    public static final String PUSH_INSTALLATIONINFO = "/jumpto/openapi/sp/pushInstallationInfo";
//
//    /**安装附件信息回传 */
//    public static final String PUSH_ACCESSORIESINFO = "/jumpto/openapi/sp/pushAccessoriesInfo";
//
//    /**CPIM 服务商报修订单审核信息回传 */
//    public static final String PUSH_REVIEWINFO = "/jumpto/openapi/sp/ao/pushReviewInfo";
//
//    /**CPIM 服务商报修订单审核信息回传 */
//    public static final String PUSH_REPAIRACCESSORIESINFO = "/jumpto/openapi/sp/ao/pushAccessoriesInfo";
//
//    /** 服务商报修联系信息回传 */
//    public static final String AO_PUSH_CONTACT = "/jumpto/openapi/sp/ao/pushContactInfo";
}
