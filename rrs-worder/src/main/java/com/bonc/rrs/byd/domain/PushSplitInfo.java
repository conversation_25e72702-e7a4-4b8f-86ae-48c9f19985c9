package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 服务商回传拆桩信息
 * @Author: tangchuheng
 * @Date: 2024/4/15
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
@ToString
public class PushSplitInfo implements Serializable {
    /**
     * 报修订单编号
     */
    @ApiModelProperty(value = "报修订单编号", required = true)
    private String orderCode;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private String operatePerson;

    /**
     * 售后完成时间
     */
    @ApiModelProperty(value = "售后完成时间，格式：yyyy-MM-dd HH:mm:ss", required = true)
    private String finishTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true)
    private String desc;

    /**
     * 客户是否留桩
     */
    @ApiModelProperty(value = "客户是否留桩", required = true, notes = "0-否，1-是")
    private String needSaveWallBox;

    /**
     * 拆桩附件
     */
    @ApiModelProperty(value = "拆桩附件", required = true, notes = "附件参数取值字段名称")
    private String picAttrs;
}
