package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 安装附件信息-老
 *
 * <AUTHOR>
 */
@Data
public class PushAccessoriesInfoOld {

    private static final long serialVersionUID = 1L;

    /**
     * 操作人
     */
    private String operatePerson;

    /**
     * 安装订单编号
     */
    @ApiModelProperty(required = true)
    private String orderCode;

    /**
     * 施工使用线缆 大小限制 10M
     */
    @ApiModelProperty(required = true)
    private String constructionImage;

    /**
     * 充电桩序列码照片 大小限制 10M
     */
    @ApiModelProperty(required = true)
    private String sequenceImage;

    /**
     * 工具垫 大小限制 10M
     */
    @ApiModelProperty(required = true)
    private String toolBlanketImage;

    /**
     * 电源点火零电压 大小限制 10M
     */
    @ApiModelProperty(required = true)
    private String zeroVoltageImage;

    /**
     * 电源点火地电压 大小限制 10M
     */
    @ApiModelProperty(required = true)
    private String groundVoltageImage;

    /**
     * 放弃电力报装免责声明 大小限制 10M
     */
    private String disclaimersImage;

    /**
     * 同级负载确认书 大小限制 10M
     */
    private String loadConfirmationImage;

    /**
     * 用线始端 大小限制 10M
     */
    @ApiModelProperty(required = true)
    private String lineStartImage;

    /**
     * 用线末端 大小限制 10M
     */
    @ApiModelProperty(required = true)
    private String lineEndImage;

    /**
     * 电源点正面 0.5m 近景 大小限制 10M
     */
    @ApiModelProperty(required = true)
    private String powerSupplyImage;

    /**
     * 敷设路径全景照片 该参数支持多张上传中间用“,”隔开，并且最多上传五张，每张大小限制10M
     */
    @ApiModelProperty(required = true)
    private String layingPathImage;

    /**
     * 漏保上端火零绝缘电阻 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String fireZeroResistanceImage;

    /**
     * 漏保上端火地绝缘电阻 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String fireGroundResistanceImage;

    /**
     * 漏保下端火零电压 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String fireZeroVoltageImage;

    /**
     * 漏保下端火地电压 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String fireGroundVoltageImage;

    /**
     * 漏保下端零地电压 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String zeroGroundVoltageImage;

    /**
     * 接地线或接地极 大小限制10M
     */
    private String groundWireImage;

    /**
     * 充电桩接线照片 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String connectionImage;

    /**
     * 人桩合照 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String manPileImage;

    /**
     * 增项收费单 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String increaseChargeImage;

    /**
     * 安装确认单 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String confirmationImage;

    /**
     * 试充照片 大小限制10M
     */
    @ApiModelProperty(required = true)
    private String trialChargeImage;

    /**
     * 其他照片 该参数支持多张上传，中间用“,”隔开，并且最多上传五张，每张大小限制10M
     */
    private String image1;

    /**
     * 桩体侧面二维码图片
     * （腾势、仰望、方程豹）必传，（两网）非必传
     * 大小限制10M
     */
    private String sideCodeImage;

    /**
     * 网络信号测试结图片
     * 是（腾势、仰望、方程豹）必传，（两网）非必传
     * 大小限制10M
     */
    private String networkResultImage;

    /**
     * 绑定成功页面/未绑定报备截图
     * （腾势、仰望、方程豹）必传，（两网）非必传
     * 大小限制10M
     */
    private String bindStatusImage;

    /**
     * 充电桩铭牌图片
     * （腾势、仰望、方程豹）非必传，（两网）必传
     * 大小限制10M
     */
    private String pileNameplateImage;

}
