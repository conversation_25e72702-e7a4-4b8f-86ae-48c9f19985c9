package com.bonc.rrs.byd.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.byd.domain.BydFaultCodes;
import com.bonc.rrs.byd.domain.LeafMediaDTO;
import com.bonc.rrs.byd.service.BydFaultCodesService;
import com.bonc.rrs.worder.entity.ExtFieldEntity;
import com.bonc.rrs.worder.service.ExtFieldService;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/worder/fault-codes")
public class BydFaultCodes4PCController {

    @Autowired
    private BydFaultCodesService bydFaultCodesService;

    @Autowired
    private ExtFieldService extFieldService;

    /**
     * 获取所有故障码（包含递归查询）
     * GET /fault-codes/all
     */
    @GetMapping("/all")
    public R getAllFaultCodes() {
        List<BydFaultCodes> faultCodes = bydFaultCodesService.getAllFaultCodes();
        return R.ok().putList(faultCodes);
    }

    /**
     * 根据父节点编码获取子节点
     * GET /fault-codes/children?parentCode={code}
     */
    @GetMapping("/children")
    public ResponseEntity<List<BydFaultCodes>> getChildrenByParentCode(@RequestParam String parentCode) {
        List<BydFaultCodes> children = bydFaultCodesService.getAllChildren(parentCode);
        return ResponseEntity.ok(children);
    }

    @GetMapping("/leaf-field")
    public ResponseEntity<LeafMediaDTO> getLeafField(@RequestParam String nodeCode) {
        if (nodeCode == null || nodeCode.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        BydFaultCodes node = bydFaultCodesService.getOne(
                new QueryWrapper<BydFaultCodes>().eq("code", nodeCode)
        );

        LeafMediaDTO leafField = collectLeafFields(node);
        return ResponseEntity.ok(Objects.requireNonNull(leafField));
    }

    private LeafMediaDTO collectLeafFields(BydFaultCodes node) {
        if (node.getIsLeaf() == 1) {
            List<ExtFieldEntity> imageFields = null;
            List<ExtFieldEntity> videoFields = null;
            if (node.getImageCode() != null) {
//                node.getImageCode();是一个json，取json中的所有value值
                JSONObject imageCodes = JSONUtil.parseObj(node.getImageCode());
                //取imageCodes中的value值，以逗号分割拼接成字符串
                String imageFieldIds = imageCodes.values().stream().map(Object::toString).collect(Collectors.joining(","));
                imageFields = extFieldService.list(new QueryWrapper<ExtFieldEntity>().in("field_id", imageFieldIds));
            }

            if (node.getVideoCode() != null) {
//                node.getVideoCode();是一个json，取json中的所有value值
                JSONObject videoCodes = JSONUtil.parseObj(node.getVideoCode());
                //取videoCodes中的value值，以逗号分割拼接成字符串
                String videoFieldIds = videoCodes.values().stream().map(Object::toString).collect(Collectors.joining(","));
                videoFields = extFieldService.list(new QueryWrapper<ExtFieldEntity>().in("field_id", videoFieldIds));
            }
            return new LeafMediaDTO(
                    node.getCode(),
                    imageFields,
                    videoFields
            );
        }
        return null;
    }
}
