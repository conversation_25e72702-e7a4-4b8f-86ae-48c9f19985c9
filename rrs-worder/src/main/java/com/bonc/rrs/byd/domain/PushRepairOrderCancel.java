package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户报修订单取消信息推送服务商
 *
 */
@Data
public class PushRepairOrderCancel {

    @ApiModelProperty(value = "报修订单编号", required = true)
    private String orderCode;

    @ApiModelProperty(value = "订单取消时间", required = true)
    private String cancelDate;

    @ApiModelProperty(value = "订单取消描述")
    private String cancelDesc;

    @ApiModelProperty(value = "订单取消原因")
    private String cancelReason;
}
