package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 服务商回传客户是否存在挪桩，加装插座意向
 * @Description: 2.15 服务商回传客户是否存在挪桩，加装插座意向
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("客户挪桩加装插座意向")
public class PushMovePileInstallSocket {

    @ApiModelProperty(value = "安装订单编号", required = true)
    private String orderCode;

    @ApiModelProperty(value = "客户是否存在挪桩、加装插座意向", required = true,
                      notes = "0否 1是。仅从\"否\"变成\"是\" 仅能操作1次，不可再回传")
    private String movePileInstallSocket;

    @ApiModelProperty(value = "操作人", required = true)
    private String operatePerson;
}
