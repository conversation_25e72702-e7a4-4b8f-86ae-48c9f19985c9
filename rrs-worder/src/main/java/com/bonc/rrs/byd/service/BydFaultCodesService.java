package com.bonc.rrs.byd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.byd.domain.BydFaultCodes;
import java.util.List;

public interface BydFaultCodesService extends IService<BydFaultCodes> {

    List<BydFaultCodes> getAllFaultCodes();

    /**
     * 获取所有故障码（包含递归查询）
     * @return 完整故障码列表
     */
    List<BydFaultCodes> getAllFaultCodesTree();

    /**
     * 递归查询所有子节点
     * @param parentCode 父节点编码
     * @return 故障码列表
     */
    List<BydFaultCodes> getAllChildren(String parentCode);
}
