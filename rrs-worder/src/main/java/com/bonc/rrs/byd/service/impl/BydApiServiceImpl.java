package com.bonc.rrs.byd.service.impl;

import com.alibaba.fastjson.JSON;
import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.byd.util.BydUrlApi;
import com.bonc.rrs.byd.util.Sha256SignUtils;
import com.bonc.rrs.intf.service.IntfLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 比亚迪对接Service接口
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BydApiServiceImpl implements IBydApiService {

    @Value("${byd.url}")
    private String urlPath;

    @Value("${byd.AppSecret}")
    private String appSecret;

    @Value("${byd.APP_KEY}")
    private String appKey;

    final IntfLogService intfLogService;

    final RestTemplate restTemplate;
    /**
     * 服务商联系信息回传
     *
     * @param pushContactInfo 服务商联系信息
     * @return
     */
    @Override
    public OtherApiResponse pushContactInfo(PushContactInfo pushContactInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushContactInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushContactInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_CONTACT.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_CONTACT.getCode(), pushContactInfo.getOrderCode(), url, BydUrlApi.PUSH_CONTACT.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }
    /**
     * 服务商勘测信息回传
     *
     * @param pushSurveyInfo 服务商联系信息
     * @return
     */
    @Override
    public OtherApiResponse pushSurveyInfo(PushSurveyInfo pushSurveyInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushSurveyInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushSurveyInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_SURVEY.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_SURVEY.getCode(), pushSurveyInfo.getOrderCode(), url, BydUrlApi.PUSH_SURVEY.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * 服务商勘测信息回传--老
     *
     * @param pushSurveyInfoOld 服务商联系信息
     * @return
     */
    @Override
    public OtherApiResponse pushSurveyInfoOld(PushSurveyInfoOld pushSurveyInfoOld) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushSurveyInfoOld), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushSurveyInfoOld), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_SURVEY.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_SURVEY.getCode(), pushSurveyInfoOld.getOrderCode(), url, BydUrlApi.PUSH_SURVEY.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * 服务商提交审核
     *
     * @param pushSubmitReviewInfo 服务商提交审核信息
     * @return
     */
    @Override
    public OtherApiResponse pushSubmitReviewInfo(PushSubmitReviewInfo pushSubmitReviewInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushSubmitReviewInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushSubmitReviewInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_SUBMIT.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_SUBMIT.getCode(), pushSubmitReviewInfo.getOrderCode(), url, BydUrlApi.PUSH_SUBMIT.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * CPIM 服务商报修订单审核信息回传
     *
     * @param pushReviewInfo CPIM 服务商报修订单审核信息回传
     * @return
     */
    @Override
    public OtherApiResponse pushReviewInfo(PushReviewInfo pushReviewInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushReviewInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushReviewInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_REVIEWINFO.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_REVIEWINFO.getCode(), pushReviewInfo.getOrderCode(), url, BydUrlApi.PUSH_REVIEWINFO.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushProcessing(PushProcessingInfo pushProcessingInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushProcessingInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushProcessingInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_PROCESSING.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_PROCESSING.getCode(), pushProcessingInfo.getOrderCode(), url, BydUrlApi.PUSH_PROCESSING.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushInstallationInfo(PushInstallationInfo pushInstallationInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushInstallationInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushInstallationInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_INSTALLATIONINFO.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_INSTALLATIONINFO.getCode(), pushInstallationInfo.getOrderCode(), url, BydUrlApi.PUSH_INSTALLATIONINFO.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushInstallationInfoOld(PushInstallationInfoOld pushInstallationInfoOld) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushInstallationInfoOld), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushInstallationInfoOld), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_INSTALLATIONINFO.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_INSTALLATIONINFO.getCode(), pushInstallationInfoOld.getOrderCode(), url, BydUrlApi.PUSH_INSTALLATIONINFO.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushAccessoriesInfo(PushAccessoriesInfo pushAccessoriesInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushAccessoriesInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushAccessoriesInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_ACCESSORIESINFO.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_ACCESSORIESINFO.getCode(), pushAccessoriesInfo.getOrderCode(), url, BydUrlApi.PUSH_ACCESSORIESINFO.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushAccessoriesInfoOld(PushAccessoriesInfoOld pushAccessoriesInfoOld) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushAccessoriesInfoOld), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushAccessoriesInfoOld), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_ACCESSORIESINFO.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_ACCESSORIESINFO.getCode(), pushAccessoriesInfoOld.getOrderCode(), url, BydUrlApi.PUSH_ACCESSORIESINFO.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushRepairAccessoriesInfo(PushRepairAccessoriesInfo pushRepairAccessoriesInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushRepairAccessoriesInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushRepairAccessoriesInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_REPAIRACCESSORIESINFO.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_REPAIRACCESSORIESINFO.getCode(), pushRepairAccessoriesInfo.getOrderCode(), url, BydUrlApi.PUSH_REPAIRACCESSORIESINFO.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }


    /**
     * 服务商提交审核
     *
     * @param pushSubmitInfo 服务商取消审核信息回传
     * @return
     */
    @Override
    public OtherApiResponse pushSubmitInfo(PushSubmitInfo pushSubmitInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushSubmitInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushSubmitInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_CANCEL.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_CANCEL.getCode(), pushSubmitInfo.getOrderCode(), url, BydUrlApi.PUSH_CANCEL.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * 封装请求头
     *
     * @param map
     * @return
     */
    private HttpHeaders packageParam(Map<String, Object> map) {
        String nonce = String.valueOf(System.currentTimeMillis());
        String curTime = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        //生成签名
        String sign = Sha256SignUtils.getSign(appSecret, nonce, curTime, map);
        //封装参数发送请求
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE.toString());
        headers.setContentType(type);
        headers.add("APP_KEY", appKey);
        headers.add("Nonce", nonce);
        headers.add("Cur_Time", curTime);
        headers.add("sign", sign);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        return headers;
    }

    /**
     * 服务商联系信息回传
     *
     * @param aoPushContactInfo 服务商联系信息
     * @return
     */
    @Override
    public OtherApiResponse aoPushContactInfo(AoPushContactInfo aoPushContactInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(aoPushContactInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(aoPushContactInfo), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.AO_PUSH_CONTACT.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.AO_PUSH_CONTACT.getCode(), aoPushContactInfo.getOrderCode(), url, BydUrlApi.AO_PUSH_CONTACT.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushNotifyReadRecord(PushNotifyReadRecord pushNotifyReadRecord, String orderCode) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushNotifyReadRecord), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushNotifyReadRecord), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_NOTIFY_READ_RECORD.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_NOTIFY_READ_RECORD.getCode(), orderCode, url, BydUrlApi.PUSH_NOTIFY_READ_RECORD.getDesc(), 1, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * 服务商安装订单信息网点记录回传
     */
    @Override
    public OtherApiResponse pushBranchRecord(PushBranchRecord pushBranchRecord) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushBranchRecord), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushBranchRecord), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_BRANCH_RECORD.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_BRANCH_RECORD.getCode(), pushBranchRecord.getOrderCode(), url, BydUrlApi.PUSH_BRANCH_RECORD.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /**
     * 服务商维修订单信息网点记录回传
     */
    @Override
    public OtherApiResponse aoPushBranchRecord(PushBranchRecord pushBranchRecord) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushBranchRecord), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushBranchRecord), headers);
        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.AO_PUSH_BRANCH_RECORD.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.AO_PUSH_BRANCH_RECORD.getCode(), pushBranchRecord.getOrderCode(), url, BydUrlApi.AO_PUSH_BRANCH_RECORD.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }


    /*
    * 服务商报修信息回传--新
     */
    @Override
    public OtherApiResponse pushRepairAccessoriesInfoNew(RepairAfterSalesInfo repairAfterSalesInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(repairAfterSalesInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(repairAfterSalesInfo), headers);
//        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_REPAIRACCESSORIESINFO_NEW.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_REPAIRACCESSORIESINFO_NEW.getCode(), repairAfterSalesInfo.getOrderCode(), url, BydUrlApi.PUSH_REPAIRACCESSORIESINFO_NEW.getDesc(), 1, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /*
     * 服务商获取附件临时链接
     */
    @Override
    public OtherApiResponse getTempLink(TempLink tempLink ) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(tempLink), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(tempLink), headers);
//        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.GET_TEMP_LINK.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
//        intfLogService.saveIntfLog(BydUrlApi.FILE_UPLOAD.getCode(), pushRepairAccessoriesInfo.getOrderCode(), url, BydUrlApi.FILE_UPLOAD.getDesc(), 1, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    /*
     * 服务商上传附件,获取短链
     */
    @Override
    public OtherApiResponse fileUpload(PushFile pushFile) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushFile), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushFile), headers);
//        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.FILE_UPLOAD.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
//        intfLogService.saveIntfLog(BydUrlApi.FILE_UPLOAD.getCode(), pushRepairAccessoriesInfo.getOrderCode(), url, BydUrlApi.FILE_UPLOAD.getDesc(), 1, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushSplitInfo(PushSplitInfo pushSplitInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushSplitInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushSplitInfo), headers);
//        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_SPLIT_INFO.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_SPLIT_INFO.getCode(), pushSplitInfo.getOrderCode(), url, BydUrlApi.PUSH_SPLIT_INFO.getDesc(), 1, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushMoveInstallInfo(PushMoveInstallInfo pushMoveInstallInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushMoveInstallInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushMoveInstallInfo), headers);
//        RestTemplate restTemplate = new RestTemplate();
        String url = urlPath + BydUrlApi.PUSH_MOVE_INSTALL_INFO.getCode();
        log.info("比亚迪接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_MOVE_INSTALL_INFO.getCode(), pushMoveInstallInfo.getOrderCode(), url, BydUrlApi.PUSH_MOVE_INSTALL_INFO.getDesc(), 1, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    // ==================== 检查订单相关接口实现 ====================

    @Override
    public OtherApiResponse pushCheckContactInfo(PushCheckContactInfo pushCheckContactInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushCheckContactInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushCheckContactInfo), headers);
        String url = urlPath + BydUrlApi.ET_PUSH_CONTACT.getCode();
        log.info("比亚迪检查订单联系信息回传接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪检查订单联系信息回传接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.ET_PUSH_CONTACT.getCode(), pushCheckContactInfo.getSubOrderCode(), url, BydUrlApi.ET_PUSH_CONTACT.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushCheckProcessing(PushCheckProcessing pushCheckProcessing) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushCheckProcessing), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushCheckProcessing), headers);
        String url = urlPath + BydUrlApi.ET_PUSH_PROCESSING.getCode();
        log.info("比亚迪检查信息回传接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪检查信息回传接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.ET_PUSH_PROCESSING.getCode(), pushCheckProcessing.getSubOrderCode(), url, BydUrlApi.ET_PUSH_PROCESSING.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushCheckReviewInfo(PushCheckReviewInfo pushCheckReviewInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushCheckReviewInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushCheckReviewInfo), headers);
        String url = urlPath + BydUrlApi.ET_PUSH_REVIEW.getCode();
        log.info("比亚迪检查订单提交审核接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪检查订单提交审核接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.ET_PUSH_REVIEW.getCode(), pushCheckReviewInfo.getSubOrderCode(), url, BydUrlApi.ET_PUSH_REVIEW.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    @Override
    public OtherApiResponse pushCheckCancelReviewInfo(PushCheckCancelReviewInfo pushCheckCancelReviewInfo) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushCheckCancelReviewInfo), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushCheckCancelReviewInfo), headers);
        String url = urlPath + BydUrlApi.ET_PUSH_CANCEL_REVIEW.getCode();
        log.info("比亚迪检查订单取消审核接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪检查订单取消审核接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.ET_PUSH_CANCEL_REVIEW.getCode(), pushCheckCancelReviewInfo.getSubOrderCode(), url, BydUrlApi.ET_PUSH_CANCEL_REVIEW.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

    // ==================== 2.15 接口实现 ====================

    @Override
    public OtherApiResponse pushMovePileInstallSocket(PushMovePileInstallSocket pushMovePileInstallSocket) {
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(pushMovePileInstallSocket), Map.class);
        HttpHeaders headers = packageParam(map);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(pushMovePileInstallSocket), headers);
        String url = urlPath + BydUrlApi.PUSH_MOVE_PILE_INSTALL_SOCKET.getCode();
        log.info("比亚迪服务商回传客户是否存在挪桩，加装插座意向接口：{}，入参：{}", url, JSON.toJSONString(request));
        ResponseEntity<OtherApiResponse> responseEntity = restTemplate.postForEntity(url, request, OtherApiResponse.class);
        log.info("比亚迪服务商回传客户是否存在挪桩，加装插座意向接口：{}，出参：{}", url, JSON.toJSONString(responseEntity));
        intfLogService.saveIntfLog(BydUrlApi.PUSH_MOVE_PILE_INSTALL_SOCKET.getCode(), pushMovePileInstallSocket.getOrderCode(), url, BydUrlApi.PUSH_MOVE_PILE_INSTALL_SOCKET.getDesc(), 1, JSON.toJSONString(request), JSON.toJSONString(responseEntity));
        return responseEntity.getBody();
    }

}
