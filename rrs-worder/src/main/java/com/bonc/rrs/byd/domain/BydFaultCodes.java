package com.bonc.rrs.byd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @TableName byd_fault_codes
 */
@TableName(value ="byd_fault_codes")
@Data
public class BydFaultCodes implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 层级：1=一级，2=二级，3=三级
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 故障码，如A111
     */
    @TableField(value = "code")
    private String code;

    /**
     * 故障名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 父级代码
     */
    @TableField(value = "parent_code")
    private String parentCode;

    /**
     * 完整父路径名称
     */
    @TableField(value = "parent_name")
    private String parentName;

    /**
     * 图片代码列表
     */
    @TableField(value = "image_code")
    private String imageCode;

    /**
     * 图片名称列表
     */
    @TableField(value = "image_name")
    private String imageName;

    /**
     * 视频代码列表
     */
    @TableField(value = "video_code")
    private String videoCode;

    /**
     * 视频名称列表
     */
    @TableField(value = "video_name")
    private String videoName;

    /**
     * 是否叶子节点：0=否，1=是
     */
    @TableField(value = "is_leaf")
    private Integer isLeaf;

    @TableField(exist = false)
    private List<BydFaultCodes> children;

}
