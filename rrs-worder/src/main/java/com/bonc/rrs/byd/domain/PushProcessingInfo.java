package com.bonc.rrs.byd.domain;

import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: CPIM 服务商售后信息回入参
 * @Author: liujunpeng
 * @Date: 2024/2/28 14:51
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
@ToString
public class PushProcessingInfo implements Serializable {
    /**
     * 报修订单编号 是 字符串 50
     */
    private String orderCode;
    /**
     * 操作人 是 字符串 50
     */
    private String operatePerson;
    /**
     * 售后完成时间 是 字符串 50 格式：yyyy-MM-dd HH:mm:ss
     */
    private String finishTime;
    /**
     * 售后验证码 否 字符串 50
     */
    private String aftersaleCode;
    /**
     * 是否换桩 保内报修类 必传 字符串 0-否，1-是，售后类型为报修时 必传
     */
    private String needExchange;
    /**
     * 充电桩编号 否 字符串 50 换桩为 1-是时，必填
     */
    private String exchangeWallboxCode;
    /**
     * 描述 是 字符串 200
     */
    private String remark;
    /**
     * 客户是否留桩 保内拆桩或 保外拆桩时 必传 字符串 0-否，1-是，保内或保外售后类 型为拆桩时传参。
     */
    private String needReserve;

    /**
     * 是否上门解决
     * 必传
     * true-是，false-否
     */
    private Boolean doorSolution;
}
