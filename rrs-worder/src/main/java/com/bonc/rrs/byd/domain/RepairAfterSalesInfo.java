package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 服务商报修订单售后信息回传
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepairAfterSalesInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报修订单编号
     */
    @ApiModelProperty(value = "报修订单编号", required = true)
    private String orderCode;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private String operatePerson;

    /**
     * 售后完成时间
     */
    @ApiModelProperty(value = "售后完成时间，格式：yyyy-MM-dd HH:mm:ss", required = true)
    private String finishTime;

    /**
     * 是否上门解决
     * 0-否，1-是
     */
    @ApiModelProperty(value = "是否上门解决", required = true, notes = "必传参数，0-否，1-是")
    private String doorSolution;

    /**
     * 故障代码
     */
    @ApiModelProperty(value = "故障代码", required = true)
    private String faultCode;

    /**
     * 故障原因编码
     * 注意：当doorSolution为"0"(否)时不填，其他情况必填
     */
    @ApiModelProperty(value = "故障原因编码", notes = "当是否上门解决(doorSolution)为0(否)时不填，其他情况必填。参考附件中的故障原因编码列表。")
    private String faultReasonCode;

    /**
     * 图片附件
     * 根据规则判断是否需要传
     */
    @ApiModelProperty(value = "图片附件", notes = "根据业务规则判断是否需要传。附件为短链接。")
    private String picAttrs;

    /**
     * 视频附件
     * 根据规则判断是否需要传
     */
    @ApiModelProperty(value = "视频附件", notes = "根据业务规则判断是否需要传。附件为短链接。")
    private String videoAttrs;

    /**
     * 更换的主材
     * 根据规则判断是否需要传
     */
    @ApiModelProperty(value = "更换的主材", notes = "11-更换的主材=充电桩,12-更换的主材=接地极,13-更换的主材=漏保,14-更换的主材=线缆且6平方" +
            ",15-更换的主材=线缆且10平方,16-更换的主材=线缆且16平方,99- 无")
    private String exchangeMainMaterial;

    /**
     * 更换的辅材
     * 根据规则判断是否需要传
     */
    @ApiModelProperty(value = "更换的辅材", notes = "101-空开,102-保护箱,103-套管,104-立柱,105-吊筋,106-吊丝,107-其他,127-无")
    private String exchangeAuxiliaryMaterial;

    /**
     * 充电桩编码
     * 根据规则判断是否需要传
     */
    @ApiModelProperty(value = "充电桩编码", notes = "根据业务规则判断是否需要传")
    private String newPileCode;

    /**
     * 返厂物流单号
     * 根据规则判断是否需要传
     */
    @ApiModelProperty(value = "返厂物流单号", notes = "根据业务规则判断是否需要传")
    private String expressNumber;

    /**
     * 故障是否解决
     * 根据规则判断是否需要传
     * 0-否，1-是
     */
    @ApiModelProperty(value = "故障是否解决", notes = "根据业务规则判断是否需要传。0-否，1-是")
    private String faultSolved;

    /**
     * 故障是否解决描述
     * 根据规则判断是否需要传
     */
    @ApiModelProperty(value = "故障是否解决描述", notes = "根据业务规则判断是否需要传")
    private String faultSolvedDesc;

    /**
     * 是否涉及原安装工程整改
     * 根据规则判断是否需要传
     * 0-否，1-是
     */
    @ApiModelProperty(value = "是否涉及原安装工程整改", notes = "根据业务规则判断是否需要传。0-否，1-是")
    private String rectification;

    /**
     * 整改米数
     * 整改为是时必填
     */
    @ApiModelProperty(value = "整改米数", notes = "整改为是时必填，最大支持3位数")
    private String rectificationMeter;

    /**
     * 其他辅材描述
     * 辅材为其他时必填
     */
    @ApiModelProperty(value = "其他辅材描述", notes = "辅材为其他时必填")
    private String otherMaterialDesc;
}

