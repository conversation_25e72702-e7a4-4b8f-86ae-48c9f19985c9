package com.bonc.rrs.byd.domain;

import lombok.Data;

import java.io.Serializable;


/**
 * 服务商联系信息记录
 * 
 * <AUTHOR>
 */
@Data
public class ContactRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  联系时间  格式：yyyy-MM-dd HH:mm:ss
     */
    private String contactTime;

    /**
     *  联系内容
     */
    private String contactContent;

    /**
     *  再次联系时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String  nextContactTime;


}