package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CPIM推送安装订单风险信息
 * @Description: 2.14 CPIM推送安装订单风险信息
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("安装订单风险信息")
public class PushRiskInfo {

    @ApiModelProperty(value = "安装订单编号", required = true)
    private String orderCode;

    @ApiModelProperty(value = "风险等级", required = true, 
                      notes = "11、12：一级风险；21、22、23、24：二级风险；31、32、33：三级风险；41、42、43、44、45：四级风险")
    private String riskGrade;

    @ApiModelProperty(value = "风险原因", required = true)
    private String riskReason;
} 