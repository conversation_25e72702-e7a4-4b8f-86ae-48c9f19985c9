package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 费用详情
 */
@Data
public class ClaimFeeDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目名
     */
    @ApiModelProperty(value = "项目名", required = true)
    private String projectName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 维修含税单价
     */
    @ApiModelProperty(value = "维修含税单价", required = true)
    private String fixUnitPrice;

    /**
     * 理赔含税单价
     */
    @ApiModelProperty(value = "理赔含税单价")
    private String claimUnitPrice;

    /**
     * 次
     */
    @ApiModelProperty(value = "次", required = true)
    private String unitCount;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private String unitFlag;

    /**
     * 维修价格
     */
    @ApiModelProperty(value = "维修价格", required = true)
    private String fixPrice;

    /**
     * 理赔价格
     */
    @ApiModelProperty(value = "理赔价格")
    private String claimPrice;
}
