package com.bonc.rrs.byd.controller;

import com.bonc.rrs.byd.domain.PushBranchRecord;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.entity.BizAttendantEntity;
import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderOperationRecord;
import com.bonc.rrs.worder.service.BizAttendantService;
import com.bonc.rrs.worder.service.DotInformationService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderOperationRecordService;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.lenmoncore.common.validator.ValidatorUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * @Description: 给比亚迪推送历史数据
 * @Author: louis
 * @Date: 2025/2/10 给比亚迪推送历史数据
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/dmj/openapi/bydApi/oldOrder")
@Api(value = "/dmj/openapi/bydApi/oldOrder", tags = "给比亚迪推送历史数据")
@RequiredArgsConstructor
public class OldOrderDealController {

    @Autowired
    private ProviderBusinessService providerBusinessService;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private DotInformationService dotInformationService;
    @Autowired
    private WorderOperationRecordService worderOperationRecordService;
    @Autowired
    private SysFilesService sysFilesService;
    @Autowired
    private BizAttendantService bizAttendantService;

    @Value("${bridgehub.paramToken}")
    private String paramToken;

    @PostMapping("/pushBranchRecord")
    @ApiOperation(value = "CPIM 推送安装订单基本安装信息给指定供应商接口", notes = "CPIM 推送安装订单基本安装信息给指定供应商接口")
    public Results pushOrder(@RequestHeader(name = "x-accesstoken") String token, String companyOrderNumber) {

        if (StringUtils.isBlank(token) || !token.equals(paramToken)) {
            return Results.message(500,"token校验失败");
        }

        WorderInformationEntity worderInformation = worderInformationService.getByCompanyWorderNo(companyOrderNumber);
        if (worderInformation == null) {
            return Results.message(500,"未找到工单信息");
        }
        Integer serviceId = worderInformation.getServiceId();
        if (serviceId == null) {
            return Results.message(500,"未找到服务兵信息");
        }
        DotInformationEntity dotInfo = dotInformationService.getInfoById(worderInformation.getDotId());
        if (dotInfo == null) {
            return Results.message(500,"未找到网点信息");
        }
        BizAttendantEntity bizAttendantEntity = bizAttendantService.getById(serviceId);
        if (bizAttendantEntity == null) {
            return Results.message(500,"未找到服务兵信息");
        }
        WorderOperationRecord record = worderOperationRecordService.lambdaQuery()
                .select(WorderOperationRecord::getCreateTime)
                .eq(WorderOperationRecord::getWorderNo, worderInformation.getWorderNo())
                .eq(WorderOperationRecord::getType, 3)
                .eq(WorderOperationRecord::getAffectedUserId, dotInfo.getDotId())
                .orderByDesc(WorderOperationRecord::getId)
                .last("limit 1")
                .one();
        if (record == null) {
            return Results.message(500,"未找到派单网点记录");
        }
        try {
            Result result = providerBusinessService.callBusiness(
                    () -> {
                        PushBranchRecord pushBranchRecord = new PushBranchRecord();
                        pushBranchRecord.setOrderCode(worderInformation.getCompanyOrderNumber());
                        //②	增加必传字段手机号、电工证编号、电工证照片、电工证有效期开始时间、电工证有效期结束时间
                        pushBranchRecord.setIssuedBranchTime(DateUtils.format(record.getCreateTime()));
                        pushBranchRecord.setBranchCode(dotInfo.getVCode());
                        pushBranchRecord.setBranchName(dotInfo.getDotName());
                        pushBranchRecord.setPersonnelNumber(bizAttendantEntity.getContact());
                        pushBranchRecord.setPersonnelName(bizAttendantEntity.getName());
                        pushBranchRecord.setPersonnelPhoneNumber(bizAttendantEntity.getContact());
                        //电工证编号
                        pushBranchRecord.setElectricianCertificateNumber(bizAttendantEntity.getElectricianCertificateNumber());
                        //电工证照片
                        String electricianCertificate = bizAttendantEntity.getElectricianCertificate();
                        if (StringUtils.isNotBlank(electricianCertificate)) {
                            String[] fileIds = electricianCertificate.split(",");
                            Optional.of(sysFilesService.getSysFileById(fileIds[0]))
                                    .ifPresent(fileVo -> {
                                        try {
                                            String imageUrl = FileUtils.copyImage(fileVo.getNewName());
                                            pushBranchRecord.setElectricianCertificateImage(imageUrl);
                                        } catch (Exception e) {
                                            throw new RRException("电工证图片转换异常");
                                        }
                                    });
                        }
                        //电工证有效期开始时间、电工证有效期结束时间
                        pushBranchRecord.setExpirationDateBegin(bizAttendantEntity.getElectricianEffectiveStart());
                        pushBranchRecord.setExpirationDateEnd(bizAttendantEntity.getElectricianEffectiveEnd());

                        ValidatorUtils.validateEntity(pushBranchRecord);

                        return BusinessProcessPo.builder().worderTypeId(worderInformation.getWorderTypeId()).pushBranchRecord(pushBranchRecord).build();
                    },
                    worderInformation.getWorderId(),
                    "pushBranchRecord"
            );
            if (result.getCode()!=0) {
                return Results.message(500,result.getMsg());
            }
            return Results.message(0, "成功");
        } catch (Exception e) {
            log.error("推送网点信息给比亚迪失败: {}", e.getMessage());
            return Results.message(500,e.getMessage());
        }
    }

}
