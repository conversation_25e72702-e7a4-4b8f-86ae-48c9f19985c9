package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 服务商回传移桩安装信息
 * @Author: tangchuheng
 * @Date: 2024/4/15
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
@ToString
public class PushMoveInstallInfo implements Serializable {
    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private String operatePerson;

    /**
     * 报修订单编号
     */
    @ApiModelProperty(value = "报修订单编号", required = true)
    private String orderCode;

    /**
     * 售后完成时间
     */
    @ApiModelProperty(value = "售后完成时间，格式：yyyy-MM-dd HH:mm:ss", required = true)
    private String finishTime;

    /**
     * 充电桩编码
     */
    @ApiModelProperty(value = "充电桩编码", required = true)
    private String wallboxCode;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true)
    private String desc;

    /**
     * 取电方式
     */
    @ApiModelProperty(value = "取电方式", required = true, notes = "1 国网电，2 物业电，3 入户电，10 其它")
    private String powerSupplyMethod;

    /**
     * 线缆品牌
     */
    @ApiModelProperty(value = "线缆品牌", required = true, notes = "1 桂林国际，2恒飞线缆，3 自布线，4 联嘉祥，5-万马，10其他")
    private String cableBrand;

    /**
     * 线缆规格
     */
    @ApiModelProperty(value = "线缆规格")
    private String cableType;

    /**
     * 线缆长度
     */
    @ApiModelProperty(value = "线缆长度", required = true)
    private Integer cableLength;

    /**
     * 断路器品牌
     */
    @ApiModelProperty(value = "断路器品牌")
    private String breakerBrand;

    /**
     * 断路器型号
     */
    @ApiModelProperty(value = "断路器型号")
    private String breakerType;

    /**
     * 是否安装立柱
     */
    @ApiModelProperty(value = "是否安装立柱", required = true, notes = "1 否， 2是")
    private String installStake;

    /**
     * 是否安装保护箱
     */
    @ApiModelProperty(value = "是否安装保护箱", required = true, notes = "1 否， 2是")
    private String installProtectingBox;

    /**
     * 是否接地极
     */
    @ApiModelProperty(value = "是否接地极", required = true, notes = "1 否， 2是")
    private String groundElectrode;

    /**
     * 前端线材
     */
    @ApiModelProperty(value = "前端线材", required = true, notes = "1 铜 ，2铝，9其他")
    private String frontEndCable;

    /**
     * 移桩安装附件
     */
    @ApiModelProperty(value = "移桩安装附件", required = true, notes = "附件参数取值字段名称")
    private String extendPicAttrs;
}
