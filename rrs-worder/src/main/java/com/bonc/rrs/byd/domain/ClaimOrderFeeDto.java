package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 索赔订单费用推送服务商
 *
 * <AUTHOR>
 */
@Data
public class ClaimOrderFeeDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报修订单编号
     */
    @ApiModelProperty(value = "报修订单号", required = true)
    private String orderCode;

    /**
     * 费用详情
     */
    @ApiModelProperty(value = "索赔明细")
    private List<ClaimFeeDetailDto> claimFeeDetailBOList;

}

