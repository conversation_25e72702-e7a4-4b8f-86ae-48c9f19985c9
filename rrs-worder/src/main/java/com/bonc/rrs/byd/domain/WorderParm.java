
package com.bonc.rrs.byd.domain;

import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 查询小卡订单入参
 * <AUTHOR>
 * @Date 2024/4/19 16:03
 * @Version 1.0.0
 */
@Data
public class WorderParm implements Serializable {

    private Integer worderId;
    /**
     * 工单号
     */
    private String worderNo;

    /**
     * 车企订单号
     */
    private String companyOrderNumber;
    /**
     * 用户电话
     */
    private String userPhone;

    /**
     * 用户姓名
     */
    private String userName;

    private Integer page;

    private Integer limit;
}