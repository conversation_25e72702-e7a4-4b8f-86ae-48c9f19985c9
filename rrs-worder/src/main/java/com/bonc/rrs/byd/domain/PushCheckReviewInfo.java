package com.bonc.rrs.byd.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检查订单提交审核
 * @Description: 5.4 服务商检查订单提交审核
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查订单提交审核")
public class PushCheckReviewInfo {

    @ApiModelProperty("操作人")
    private String operatePerson;

    @ApiModelProperty("子订单编号")
    private String subOrderCode;

    @ApiModelProperty("提审时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String commitDate;
}
