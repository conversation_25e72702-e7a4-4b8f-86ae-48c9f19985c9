package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 关闭订单后推送关闭订单信息
 *
 * <AUTHOR>
 */
@Data
public class PushCloseOrder {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "安装订单编号", required = true)
    private String orderCode;

    @ApiModelProperty(value = "关闭原因", required = true)
    private String remark;

    @ApiModelProperty(value = "关闭订单操作人", required = true)
    private String modifyPerson;

    @ApiModelProperty(value = "关闭订单操作时间 格式：yyyy-MM-dd HH:mm:ss", required = true)
    private String modifyDate;


}
