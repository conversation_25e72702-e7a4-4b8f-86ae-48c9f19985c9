package com.bonc.rrs.byd.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.byd.domain.BydFaultCodes;
import com.bonc.rrs.byd.dao.BydFaultCodesMapper;
import com.bonc.rrs.byd.service.BydFaultCodesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【byd_fault_codes】的数据库操作Service实现
* @createDate 2025-05-19 18:10:38
*/
@Slf4j
@Service
public class BydFaultCodesServiceImpl extends ServiceImpl<BydFaultCodesMapper, BydFaultCodes>
    implements BydFaultCodesService{

    @Resource
    private BydFaultCodesMapper baseMapper;

    @Override
    public boolean save(BydFaultCodes entity) {
        return baseMapper.insert(entity) > 0;
    }

    /**
     * 获取所有故障码（包含递归查询）
     * @return 完整故障码列表
     */
    @Override
    public List<BydFaultCodes> getAllFaultCodes() {
        return baseMapper.selectList(new QueryWrapper<>());
    }

    /**
     * 获取所有故障码（包含递归查询）
     * @return 完整故障码列表
     */
    @Override
    public List<BydFaultCodes> getAllFaultCodesTree() {
        // 先获取所有根节点（level=1）
        QueryWrapper<BydFaultCodes> rootQuery = new QueryWrapper<>();
        rootQuery.eq("level", 1);
        List<BydFaultCodes> rootNodes = baseMapper.selectList(rootQuery);


        // 递归获取所有子节点
        for (BydFaultCodes rootNode : rootNodes) {
            rootNode.setChildren(getAllChildren(rootNode.getCode()));
        }

        return rootNodes;
    }

    @Override
    public List<BydFaultCodes> getAllChildren(String parentCode) {
        if (parentCode == null || parentCode.isEmpty()) {
            log.warn("Parent code is null or empty");
            return Collections.emptyList();
        }

        try {
            List<BydFaultCodes> allNodes = baseMapper.selectList(new QueryWrapper<>());

            BydFaultCodes parentNode = allNodes.stream()
                    .filter(faultCode -> faultCode.getCode().equals(parentCode))
                    .findFirst()
                    .orElse(null);
            // 构建节点映射表
            Map<String, BydFaultCodes> nodeMap = allNodes.stream()
                    .filter(faultCode -> faultCode.getParentCode() != null)
                    .filter(faultCode -> faultCode.getParentCode().equals(parentCode))
                    .collect(Collectors.toMap(BydFaultCodes::getCode, node -> node));

            // 构建树结构
            List<BydFaultCodes> rootNodes = new ArrayList<>();
            Map<String, List<BydFaultCodes>> childrenMap = new HashMap<>();

            for (BydFaultCodes node : allNodes) {
                String code = node.getCode();
                String currentParentCode = node.getParentCode();

                if (currentParentCode == null || !nodeMap.containsKey(currentParentCode)) {
                    rootNodes.add(node);
                }

                childrenMap.computeIfAbsent(currentParentCode, k -> new ArrayList<>()).add(node);
            }

            // 使用队列构建完整树结构
            Deque<BydFaultCodes> queue = new ArrayDeque<>();
            queue.add(parentNode);

            while (!queue.isEmpty()) {
                BydFaultCodes currentNode = queue.poll();
                List<BydFaultCodes> children = childrenMap.getOrDefault(currentNode.getCode(), Collections.emptyList());
                currentNode.setChildren(children);
                queue.addAll(children);
            }

            return Objects.requireNonNull(parentNode).getChildren();
        } catch (Exception e) {
            log.error("Error building fault code tree: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

}
