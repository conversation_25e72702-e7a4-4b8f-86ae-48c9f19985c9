package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * CPIM 报修订单信息推送服务商
 * 
 * <AUTHOR>
 */
@Data
public class PushRepairOrderBody implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保修订单信息
     */
    @ApiModelProperty(value = "保修订单信息", required = true)
    private List<PushRepairOrderData> data;
}