package com.bonc.rrs.byd.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.byd.domain.BydFaultCodesMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【byd_fault_codes_mapping】的数据库操作Mapper
 * @createDate 2025-05-22 16:12:50
 */
public interface BydFaultCodesMappingMapper extends BaseMapper<BydFaultCodesMapping> {

    /**
     * 根据故障代码查询关联信息
     * @param faultCode 故障代码
     * @return 关联信息
     */
    List<BydFaultCodesMapping> selectByFaultCode(@Param("faultCode") String faultCode);
}
