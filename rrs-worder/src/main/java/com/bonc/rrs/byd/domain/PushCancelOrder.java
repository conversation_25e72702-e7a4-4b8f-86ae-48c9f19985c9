package com.bonc.rrs.byd.domain;

import lombok.Data;

/**
 * 用户取消安装后推送取消信息
 *
 * <AUTHOR>
 */
@Data
public class PushCancelOrder {
    private static final long serialVersionUID = 1L;

    /**
     * 安装订单编号
     */
    private String orderCode;

    /**
     * 订单取消时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String cancelDate;

    /**
     * 订单取消描述
     */
    private String cancelDesc;

    /**
     * 订单取消原因
     */
    private String cancelReason;


}
