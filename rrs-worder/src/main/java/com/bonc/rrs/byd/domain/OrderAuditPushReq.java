package com.bonc.rrs.byd.domain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: CPIM 报修订单审核信息推送服务商接口入参
 * @Author: liujunpeng
 * @Date: 2024/2/27 10:16
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@Api("CPIM 报修订单审核信息推送服务商接口入参")
public class OrderAuditPushReq implements Serializable {
    /**
     * 报修订单编号 是 字符串 50
     */
    @ApiModelProperty(value = "报修订单编号", required = true)
    private String orderCode;
    /**
     * 审核结果 是 字符串 10 1 通过，2 拒绝
     */
    @ApiModelProperty(value = "审核结果", required = true)
    private String result;
    /**
     * 拒绝原因 否 字符串 200
     */
    @ApiModelProperty(value = "拒绝原因")
    private String remark;
    /**
     * 审核人 是 字符串 50
     */
    @ApiModelProperty(value = "审核人", required = true)
    private String examinePerson;
    /**
     * 审核时间 是 字符串 50 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "审核时间", required = true)
    private String examineDate;
}
