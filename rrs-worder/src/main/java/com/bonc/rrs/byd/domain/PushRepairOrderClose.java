package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户报修订单关闭信息推送服务商
 *
 */
@Data
public class PushRepairOrderClose {

    @ApiModelProperty(value = "报修订单编号", required = true)
    private String orderCode;

    @ApiModelProperty(value = "关闭原因",required = true)
    private String remark;

    @ApiModelProperty(value = "关闭订单操作人",required = true)
    private String modifyPerson;

    @ApiModelProperty(value = "关闭订单操作时间", required = true)
    private String modifyDate;
}
