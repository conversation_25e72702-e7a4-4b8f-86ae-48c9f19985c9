package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * CPIM 推送安装订单基本安装信息接收参数
 * 
 * <AUTHOR>
 */
@Data
public class ParamBody implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 安装订单信息
     */
    @ApiModelProperty(value = "安装订单信息", required = true)
    private List<PushOrderBody> data;

}