package com.bonc.rrs.byd.domain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * CPIM 安装订单暂停信息
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@Api("CPIM 安装订单暂停信息推送服务商接口入参")
public class PushSusPendOrder implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 安装订单编号 是 字符串 50
     */
    @ApiModelProperty(value = "安装订单编号", required = true)
    private String orderCode;
    /**
     * 暂停原因 是 字符串 100
     */
    @ApiModelProperty(value = "暂停原因", required = true)
    private String suspendDesc;
    /**
     * 操作人 是 字符串 50
     */
    @ApiModelProperty(value = "操作人", required = true)
    private String suspendPerson;
    /**
     * 操作时间 是 字符串 50 格式：yyyy-MM-dd HH:mm
     */
    @ApiModelProperty(value = "操作时间", required = true)
    private String suspendDate;
}
