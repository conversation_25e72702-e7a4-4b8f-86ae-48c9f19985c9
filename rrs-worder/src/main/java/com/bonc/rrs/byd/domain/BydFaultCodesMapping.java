package com.bonc.rrs.byd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @TableName byd_fault_codes_mapping
 */
@TableName(value ="byd_fault_codes_mapping")
@Data
public class BydFaultCodesMapping implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 类型 1=图片，2=视频
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 扩展字段id
     */
    @TableField(value = "field_id")
    private Integer fieldId;

    @TableField(exist = false)
    private String fieldName;

    @TableField(exist = false)
    private String fieldType;

    @TableField(exist = false)
    private String fieldClass;

    @TableField(exist = false)
    private String fieldValue;

    @TableField(exist = false)
    private String isNotnull;

    @TableField(exist = false)
    private String isNessary;

    @TableField(exist = false)
    private String fileUrl;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
