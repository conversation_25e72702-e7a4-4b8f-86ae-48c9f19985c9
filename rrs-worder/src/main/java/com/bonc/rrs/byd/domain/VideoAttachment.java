package com.bonc.rrs.byd.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 视频附件信息
 * 附件类型，可选值如下：
 * - parallelContrastTestOld: 并联对照测试（旧桩）
 * - parallelContrastTestNew: 并联对照测试（新桩）
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoAttachment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 故障视频
     */
    @ApiModelProperty(value = "故障视频")
    private String faultVideo;

    /**
     * 并联对照测试（旧桩）
     */
    @ApiModelProperty(value = "并联对照测试（旧桩）")
    private String parallelContrastTestOld;

    /**
     * 并联对照测试（新桩）
     */
    @ApiModelProperty(value = "并联对照测试（新桩）")
    private String parallelContrastTestNew;

    /**
     * 重启测试
     */
    @ApiModelProperty(value = "重启测试")
    private String restartTest;

    /**
     * 零、地并接测试
     */
    @ApiModelProperty(value = "零、地并接测试")
    private String zeroAndConnectTest;

    /**
     * 刷卡区功能测试
     */
    @ApiModelProperty(value = "刷卡区功能测试")
    private String swipeAreaTest;

    /**
     * cc电阻、cp电压测试
     */
    @ApiModelProperty(value = "cc电阻、cp电压测试")
    private String ccResistanceCpVoltageTest;
}
