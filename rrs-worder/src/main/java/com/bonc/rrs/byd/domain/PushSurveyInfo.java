package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务商勘测信息
 *
 * <AUTHOR>
 */
@Data
public class PushSurveyInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 操作人
     */
    @ApiModelProperty(name = "操作人",required = true)
    private String operatePerson;

    /**
     * 安装订单编号
     */
    @ApiModelProperty(name = "安装订单编号",required = true)
    private String orderCode;

    /**
     * 小区信息
     */
    @ApiModelProperty(name = "小区信息")
    private String plotName;

    /**
     * 公共站信息
     */
    @ApiModelProperty(name = "公共站信息")
    private String publicChargingStation;

    /**
     * 物业名称
     */
    @ApiModelProperty(name = "物业名称")
    private String propertyName;

    /**
     * 物业联系人
     */
    @ApiModelProperty(name = "物业联系人")
    private String propertyContact;

    /**
     * 物业联系电话
     */
    @ApiModelProperty(name = "物业联系电话")
    private String propertyTelephone;

    /**
     * 是否需要立柱   1 是 0 否
     */
    @ApiModelProperty(name = "是否需要立柱   1 是 0 否")
    private String needStake;

    /**
     * 车位状况  1 产权固定，2 长租固定，3 办公地车位，4 自家工厂，5无固定车位，10 其它
     */
    @ApiModelProperty(name = "车位状况  1 产权固定，2 长租固定，3 办公地车位，4 自家工厂，5无固定车位，10 其它")
    private String carportStatus;

    /**
     * 住宅类型  1 农村自建房,2 城市小区,3 城市自建房,4 工厂, 100 其它;客户自提桩为’否’ 时，必填
     */
    @ApiModelProperty(name = "住宅类型  1 农村自建房,2 城市小区,3 城市自建房,4 工厂, 100 其它;客户自提桩为’否’ 时，必填")
    private String housingType;

    /**
     * 是否需要电力报装   1 是 0 否;客户自提桩为’否’ 时，必填
     */
    @ApiModelProperty(name = "是否需要电力报装   1 是 0 否;客户自提桩为’否’ 时，必填")
    private String emeterRequestProgress;

    /**
     * 电力报装完成时间   格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(name = "电力报装完成时间   格式：yyyy-MM-dd HH:mm:ss")
    private String emeterRequestCompletedTime;

    /**
     * 勘测完成时间   格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(name = "勘测完成时间   格式：yyyy-MM-dd HH:mm:ss", required = true)
    private String surveyCompletedTime;

    /**
     * 勘测结论    1 可以安装,2 不可以安装;客户自提桩为’否’时，必填
     */
    @ApiModelProperty(name = "勘测结论    1 可以安装,2 不可以安装;客户自提桩为’否’时，必填")
    private String surveyResult;

    /**
     * 勘测备注
     */
    @ApiModelProperty(name = "勘测备注")
    private String surveyRemark;

    /**
     * 勘测确认书   客户自提桩为’否’时，必填
     */
//    @ApiModelProperty(name = "勘测确认书   客户自提桩为’否’时，必填")
//    private String confirmation;

    /**
     * 免责协议   客户不符合安装条件，仍坚持安装时。
     */
    @ApiModelProperty(name = "免责协议   客户不符合安装条件，仍坚持安装时。")
    private String agreementImage;

    /**
     * 客户自提桩   0 否 1 是
     */
    @ApiModelProperty(name = "客户自提桩   0 否 1 是", required = true)
    private String selfPick;

    /**
     * 充电桩编码   客户自提桩为’是’时，必填
     */
    @ApiModelProperty(name = "充电桩编码   客户自提桩为’是’时，必填")
    private String wallboxCode;

    /**
     * 自提桩申请单   客户自提桩为’是’时，必填
     */
    @ApiModelProperty(name = "自提桩申请单   客户自提桩为’是’时，必填")
    private String selfPickImage;

    /**
     * 充电桩序列码照片   客户自提桩为’是’时，必填
     */
    @ApiModelProperty(name = "充电桩序列码照片   客户自提桩为’是’时，必填")
    private String pileSequenceImage;

    /**
     *  行驶证/购车发票   客户自提桩为’是’时，必填
     */
    @ApiModelProperty(name = "行驶证/购车发票   客户自提桩为’是’时，必填")
    private String drivingLicenseImage;


}
