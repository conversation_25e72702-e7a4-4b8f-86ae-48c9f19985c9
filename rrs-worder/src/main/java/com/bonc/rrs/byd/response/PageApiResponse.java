
package com.bonc.rrs.byd.response;

import com.youngking.lenmoncore.common.utils.PageUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/4/22 14:30
 * @Version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageApiResponse implements Serializable {
    private Integer code;

    private PageUtils page;
}