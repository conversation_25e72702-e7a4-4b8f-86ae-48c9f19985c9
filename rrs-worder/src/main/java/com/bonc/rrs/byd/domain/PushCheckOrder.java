package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * 检查订单信息
 * @Description: 5.1 CPIM检查订单信息推送服务商
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查订单信息")
public class PushCheckOrder {

    @ApiModelProperty(value = "订单编号", required = true)
    private String orderCode;

    @ApiModelProperty(value = "任务序号", required = true)
    private String taskSerial;

    @ApiModelProperty(value = "子订单编号", required = true)
    private String subOrderCode;

    @ApiModelProperty(value = "订单类型 10：六年三检 20：随心检查 30：仰望定检", required = true)
    private String orderType;

    @ApiModelProperty(value = "充电桩编码", required = true)
    private String wallboxCode;

    @ApiModelProperty(value = "质保截止日期 yyyy-MM-dd", required = true)
    private String warrantyEnd;

    @ApiModelProperty(value = "派单时间", required = true)
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dispatchTime;

    @ApiModelProperty(value = "联系人姓名", required = true)
    private String contactName;

    @ApiModelProperty(value = "联系人手机号", required = true)
    private String contactMobile;

    @ApiModelProperty(value = "省编码", required = true)
    private String provinceCode;

    @ApiModelProperty(value = "省", required = true)
    private String province;

    @ApiModelProperty(value = "市编码", required = true)
    private String cityCode;

    @ApiModelProperty(value = "市", required = true)
    private String city;

    @ApiModelProperty(value = "区编码", required = true)
    private String areaCode;

    @ApiModelProperty(value = "区", required = true)
    private String area;

    @ApiModelProperty(value = "详细地址", required = true)
    private String address;

    @ApiModelProperty(value = "充电桩名称", required = true)
    private String wallboxName;

    @ApiModelProperty(value = "充电桩功率", required = true)
    private String wallboxPower;

    @ApiModelProperty(value = "品牌 40-海洋，50-王朝， 30-仰望", required = true)
    private String carBrand;

    @ApiModelProperty(value = "安装订单号", required = true)
    private String installOrderCode;

    @ApiModelProperty(value = "是否推送安装信息 0-不推送 1-推送 安装订单的服务商是检查订单的服务商，不推送，值为0；安装订单服务商不是检查订单服务商，推送，值为1。", required = true)
    private String pushInstallation;

    @ApiModelProperty("安装完成时间")
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date installationCompletedTime;

    @ApiModelProperty("物料编码")
    private String wallboxMaterialCode;

    @ApiModelProperty("取电方式 1 国网电，2 物业电，3 入户电，10 其它")
    private String powerSupplyMethod;

    @ApiModelProperty("线缆品牌 1 桂林国际，2恒飞线缆，3 自布线，4 联嘉祥，5-万马，10其他")
    private String cableBrand;

    @ApiModelProperty("线缆规格")
    private String cableType;

    @ApiModelProperty("线缆长度")
    private String cableLength;

    @ApiModelProperty("断路器品牌")
    private String breakerBrand;

    @ApiModelProperty("断路器型号")
    private String breakerType;

    @ApiModelProperty("是否安装立柱 1 否， 2是")
    private String installStake;

    @ApiModelProperty("是否安装保护箱 1 否， 2是")
    private String installProtectingBox;

    @ApiModelProperty("是否接地极 1 否， 2是")
    private String groundElectrode;

    @ApiModelProperty("前端线材 1 铜 ，2铝，9其他")
    private String frontEndCable;

    @ApiModelProperty("图片附件")
    private String picAttrs;

    /**
     * 订单类型 10：六年三检 20：随心检查 30：仰望定检
     */
    @Getter
    @AllArgsConstructor
    public enum OrderType {
        NULL("", ""),
        LIU_NIAN_SAN_JIAN("10", "六年三检"),
        SUI_XIN_JIAN_CHA("20", "随心检查"),
        YANG_WANG("30", "仰望定检");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            return Stream.of(OrderType.values())
                    .filter(c -> Objects.equals(c.getCode(), code))
                    .findFirst()
                    .orElse(NULL)
                    .getName();
        }

    }

    /**
     * 是否推送安装信息枚举 (0-不推送 1-推送)
     */
    @Getter
    @AllArgsConstructor
    public enum PushInstallation {
        NULL("", ""),
        NO("0", "不推送"),
        YES("1", "推送");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            return Stream.of(PushInstallation.values())
                    .filter(c -> Objects.equals(c.getCode(), code))
                    .findFirst()
                    .orElse(NULL)
                    .getName();
        }

    }

    /**
     * 取电方式枚举 (1 国网电，2 物业电，3 入户电，10 其它)
     */
    @Getter
    @AllArgsConstructor
    public enum PowerSupplyMethod {
        NULL("", ""),
        STATE_GRID("1", "国网电"),
        PROPERTY("2", "物业电"),
        HOUSEHOLD("3", "入户电"),
        OTHER("10", "其它");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            return Stream.of(PowerSupplyMethod.values())
                    .filter(c -> Objects.equals(c.getCode(), code))
                    .findFirst()
                    .orElse(NULL)
                    .getName();
        }

    }

    /**
     * 线缆品牌枚举 (1 桂林国际，2恒飞线缆，3 自布线，4 联嘉祥，5-万马，10其他)
     */
    @Getter
    @AllArgsConstructor
    public enum CableBrand {
        NULL("", ""),
        GUILIN_INTERNATIONAL("1", "桂林国际"),
        HENGFEI_CABLE("2", "恒飞线缆"),
        SELF_WIRING("3", "自布线"),
        LIANJIAXIANG("4", "联嘉祥"),
        WANMA("5", "万马"),
        OTHER("10", "其它");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            return Stream.of(CableBrand.values())
                    .filter(c -> Objects.equals(c.getCode(), code))
                    .findFirst()
                    .orElse(NULL)
                    .getName();
        }

    }

    /**
     * 是否安装立柱枚举 (1 否， 2是)
     */
    @Getter
    @AllArgsConstructor
    public enum YesOrNo {
        NULL("", ""),
        NO("1", "否"),
        YES("2", "是");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            return Stream.of(YesOrNo.values())
                    .filter(c -> Objects.equals(c.getCode(), code))
                    .findFirst()
                    .orElse(NULL)
                    .getName();
        }

    }

    /**
     * 前端线材枚举 (1 铜 ，2铝，9其他)
     */
    @Getter
    @AllArgsConstructor
    public enum FrontEndCable {
        NULL("", ""),
        COPPER("1", "铜"),
        ALUMINUM("2", "铝"),
        OTHER("9", "其他");

        private final String code;
        private final String name;

        public static String getNameByCode(String code) {
            return Stream.of(FrontEndCable.values())
                    .filter(c -> Objects.equals(c.getCode(), code))
                    .findFirst()
                    .orElse(NULL)
                    .getName();
        }

    }
}
