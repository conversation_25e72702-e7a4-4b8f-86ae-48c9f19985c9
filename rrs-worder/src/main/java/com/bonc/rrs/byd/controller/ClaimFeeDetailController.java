package com.bonc.rrs.byd.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bonc.rrs.byd.domain.ClaimFeeDetail;
import com.bonc.rrs.byd.service.ClaimFeeDetailService;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 费用明细控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("worder/claimFeeDetails")
public class ClaimFeeDetailController {

    @Autowired
    private ClaimFeeDetailService claimFeeDetailService;

    // 查询费用明细（支持分页）
    @GetMapping("/list")
    public R getClaimFeeDetailList(
            @RequestParam(required = false) String someCondition, // 示例参数，请根据实际业务修改
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) Integer pageNum) {

        // 构建分页参数
        Page<ClaimFeeDetail> page = new Page<>();
        page.setSize(pageSize == null || pageSize <= 0 ? 10 : pageSize);
        page.setCurrent(pageNum == null || pageNum <= 0 ? 1 : pageNum);

        // 调用 service 查询数据
        IPage<ClaimFeeDetail> ipage = claimFeeDetailService.page(page);
        page.setRecords(ipage.getRecords())
                .setTotal(ipage.getTotal())
                .setPages(ipage.getPages());

        return R.ok().put("data", page);
    }

    // 导出费用明细为 Excel 文件
    @PostMapping("/export")
    public void exportClaimFeeDetails(HttpServletResponse response, @RequestBody Map<String, Object> params) throws IOException {
        // 提取查询条件
        // 示例：从 params 中获取 startTime、endTime 或其他筛选条件
        List<ClaimFeeDetail> list = claimFeeDetailService.list();

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("索赔订单费用明细", "utf-8") + ".xlsx");

        // 使用 EasyExcel 写出数据
        EasyExcel.write(response.getOutputStream())
                .head(ClaimFeeDetail.class)
                .autoCloseStream(Boolean.TRUE)
                .sheet("Sheet1")
                .doWrite(list);
    }

}
