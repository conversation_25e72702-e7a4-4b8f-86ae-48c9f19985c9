package com.bonc.rrs.byd.util;

import com.alibaba.fastjson.JSONObject;
import com.gexin.fastjson.JSON;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;

/**
 * sha256加签工具
 */
public class Sha256SignUtils {

    private static final Logger log = LoggerFactory.getLogger(Sha256SignUtils.class);

    /**
     * 生成签名
     *
     * @param appSecret
     * @param params
     * @return
     */
    public static String getSign(String appSecret, String nonce, String curTime, Map<String, Object> params) {
        log.info("生成签名传入的参数:{}", params);
        log.info("生成签名传入的参数:{}", JSON.toJSONString(params));
        StringBuilder content = new StringBuilder();
        content.append(appSecret).append("&").append(nonce).append("&").append(curTime).append("&");
        TreeMap<String, Object> sortedParams = new TreeMap<>(params);
        // 遍历参数，将值直接转换为字符串加入，保持嵌套JSON结构不变
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            content.append(entry.getKey()).append("=").append(jsonValueToString(entry.getValue())).append("&");
        }

        // 移除最后一个多余的 &
        String result = content.deleteCharAt(content.length() - 1).toString();
        log.info("生成签名拼接后的参数:{}", result);

        // 计算SHA-256签名值
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            byte[] bytes = messageDigest.digest(result.getBytes("UTF-8"));
            result = Hex.encodeHexString(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }

        log.info("生成签名的结果:{}", result);
        return result;
    }

    private static String jsonValueToString(Object value) {
        if (value instanceof Map) {
            // 使用 GSON 或其他库直接转换嵌套的Map为JSON字符串
            TreeMap treeMap = new TreeMap<>((Map) value);
            return JSONObject.toJSONString(treeMap);
        } else {
            return String.valueOf(value);
        }
    }

    /**
     * 检查指定的对象列表是否不为空。
     */
    private static boolean areNotEmpty(Object... values) {
        boolean result = true;
        if (values == null) {
            result = false;
        } else {
            for (Object value : values) {
                if (value == null) {
                    return false;
                } else {
                    return true;
                }
            }
        }
        return result;
    }

    /**
     * 验签
     *
     * @param sign
     * @param appSecret
     * @param nonce
     * @param curTime
     * @param params
     * @return
     */
    public static boolean verify(String sign, String appSecret, String nonce, String curTime, Map<String, Object> params) {
        if (sign == null || sign.equals("")) {
            return false;
        }
        return sign.equals(getSign(appSecret, nonce, curTime, params));
    }

}
