package com.bonc.rrs.byd.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 检查订单联系信息回传
 * @Description: 5.2 服务商回传检查任务联系信息
 * @Author: louis
 * @Date: 2025/07/21
 * @Version: 1.0
 */
@Data
@ApiModel("检查订单联系信息回传")
public class PushCheckContactInfo {

    @ApiModelProperty("操作人")
    private String operatePerson;

    @ApiModelProperty("子订单编号")
    private String subOrderCode;

    @ApiModelProperty("首次联系时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String firstContactTime;

    @ApiModelProperty("预计上门服务时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String planToServeTime;

    @ApiModelProperty("联系记录列表")
    private List<ContactRecord> contactRecordList;

    @Data
    @ApiModel("联系记录")
    public static class ContactRecord {

        @ApiModelProperty("联系时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date contactTime;

        @ApiModelProperty("联系内容")
        private String contactContent;

        @ApiModelProperty("再次联系时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date nextContactTime;
    }
}
