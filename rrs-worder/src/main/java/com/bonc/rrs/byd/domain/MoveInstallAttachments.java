package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 移桩安装附件
 * @Author: tangchuheng
 * @Date: 2024/4/15
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MoveInstallAttachments implements Serializable {
    /**
     * 施工使用线缆
     */
    @ApiModelProperty(value = "施工使用线缆", required = true)
    private String constructionImage;
    
    /**
     * 充电桩序列码
     */
    @ApiModelProperty(value = "充电桩序列码", required = true)
    private String sequenceImage;
    
    /**
     * 用线始端
     */
    @ApiModelProperty(value = "用线始端", required = true)
    private String lineStartImage;
    
    /**
     * 用线末端
     */
    @ApiModelProperty(value = "用线末端", required = true)
    private String lineEndImage;
    
    /**
     * 接地线或接地极
     */
    @ApiModelProperty(value = "接地线或接地极", required = true)
    private String groundWireImage;
    
    /**
     * 电源点火零电压
     */
    @ApiModelProperty(value = "电源点火零电压", required = true)
    private String zeroVoltageImage;
    
    /**
     * 人桩合照
     */
    @ApiModelProperty(value = "人桩合照", required = true)
    private String manPileImage;
    
    /**
     * 安装确认单
     */
    @ApiModelProperty(value = "安装确认单", required = true)
    private String confirmationImage;
    
    /**
     * 增项收费单
     */
    @ApiModelProperty(value = "增项收费单", required = true)
    private String increaseChargeImage;
    
    /**
     * 漏保上端火零绝缘电阻
     */
    @ApiModelProperty(value = "漏保上端火零绝缘电阻", required = true)
    private String fireZeroResistanceImage;
    
    /**
     * 漏保下端零地电压
     */
    @ApiModelProperty(value = "漏保下端零地电压", required = true)
    private String zeroGroundVoltageImage;
    
    /**
     * 试充照片
     */
    @ApiModelProperty(value = "试充照片", required = true)
    private String trialChargeImage;
    
    /**
     * 充电桩铭牌图片
     */
    @ApiModelProperty(value = "充电桩铭牌图片", required = true)
    private String pileNameplateImage;
    
    /**
     * 放弃电力报装免责声明
     */
    @ApiModelProperty(value = "放弃电力报装免责声明")
    private String disclaimersImage;
    
    /**
     * 同级负载确认书
     */
    @ApiModelProperty(value = "同级负载确认书")
    private String loadConfirmationImage;
}