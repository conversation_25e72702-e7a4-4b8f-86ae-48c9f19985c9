package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 根据短链获取临时链接
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TempLink implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 附件短链
     */
    @ApiModelProperty(value = "短链")
    private String url;
}
