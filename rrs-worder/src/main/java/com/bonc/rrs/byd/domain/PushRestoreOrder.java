package com.bonc.rrs.byd.domain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * CPIM 安装订单恢复执行信息
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@Api("CPIM 安装订单恢复执行信息推送服务商接口入参")
public class PushRestoreOrder implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 安装订单编号
     */
    @ApiModelProperty(value = "安装订单编号", required = true)
    private String orderCode;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private String operatePerson;

    /**
     * 操作时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "操作时间", required = true)
    private String modifyDate;
}
