package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务商安装信息
 *
 * <AUTHOR>
 */
@Data
public class PushInstallationInfo {

    private static final long serialVersionUID = 1L;

    /**
     * 操作人
     */
    private String operatePerson;

    /**
     * 安装订单编号
     */
    @ApiModelProperty(name = "安装订单编号",required = true)
    private String orderCode;

    /**
     * 充电桩编号
     */
    @ApiModelProperty(name = "充电桩编号",required = true)
    private String wallboxCode;

    /**
     * 安装完成时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(name = "安装完成时间",required = true)
    private String installationCompletedTime;

    /**
     * 安装验证码
     */
    private String installationCode;

    /**
     * 取电方式    1 国网电，2 物业电，3 入户电，10 其它
     */
    private String powerSupplyMethod;

    /**
     * 线缆品牌 1 桂林国际，2恒飞线缆，3 自布线，10其他
     */
    private String cableBrand;

    /**
     * 线缆规格
     */
    private String cableType;

    /**
     * 线缆长度 正的整数和小数
     */
    private String cableLength;

    /**
     * 断路器品牌
     */
    private String breakerBrand;

    /**
     * 断路器型号
     */
    private String breakerType;

    /**
     * 是否安装立柱，0 否， 1是
     */
    private String installStake;

    /**
     * 是否安装保护箱，0 否， 1是
     */
    private String installProtectingBox;

    /**
     * 是否接地极 1 否， 2是 非必填
     */
    private String groundElectrode;

    /**
     * 前端线材 1 铜 ，2铝，9其他
     */
    private String frontEndCable;

}
