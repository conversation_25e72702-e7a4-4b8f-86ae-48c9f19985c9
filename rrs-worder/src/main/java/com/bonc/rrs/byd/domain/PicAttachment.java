package com.bonc.rrs.byd.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 图片附件信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PicAttachment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 人桩合照
     */
    @ApiModelProperty(value = "人桩合照")
    private String manPileImage;

    /**
     * 维修确认单
     */
    @ApiModelProperty(value = "维修确认单")
    private String repairConfirmDocument;

    /**
     * 增项收费单
     */
    @ApiModelProperty(value = "增项收费单")
    private String increaseChargeImage;

    /**
     * 旧漏保照片
     */
    @ApiModelProperty(value = "旧漏保照片")
    private String oldLeakageProtectionPhoto;

    /**
     * 新漏保照片
     */
    @ApiModelProperty(value = "新漏保照片")
    private String newLeakageProtectionPhoto;

    /**
     * 旧桩铭牌
     */
    @ApiModelProperty(value = "旧桩铭牌")
    private String oldPileMark;

    /**
     * 新桩铭牌
     */
    @ApiModelProperty(value = "新桩铭牌")
    private String newPileMark;

    /**
     * 技术认可换桩截图
     */
    @ApiModelProperty(value = "技术认可换桩截图")
    private String technicalRecognitionPCS;

    /**
     * 结构故障照片
     */
    @ApiModelProperty(value = "结构故障照片")
    private String structureFailurePhoto;

    /**
     * 旧线缆照片
     */
    @ApiModelProperty(value = "旧线缆照片")
    private String oldCablePhoto;

    /**
     * 新线缆照片
     */
    @ApiModelProperty(value = "新线缆照片")
    private String newCablePhoto;

    /**
     * 技术人员认可换线缆截图
     */
    @ApiModelProperty(value = "技术人员认可换线缆截图")
    private String technicalPersonnelRCS;

    /**
     * 充电桩背板电压（带载）
     */
    @ApiModelProperty(value = "充电桩背板电压（带载）")
    private String chargePileBackVoltageLoad;

    /**
     * CC-PE电阻
     */
    @ApiModelProperty(value = "CC-PE电阻")
    private String ccPeResistance;

    /**
     * 取电点火零电压
     */
    @ApiModelProperty(value = "取电点火零电压")
    private String electricityPointFZV;

    /**
     * 环境温度
     */
    @ApiModelProperty(value = "环境温度")
    private String environmentTemperature;

    /**
     * 温度超出要求部位温度测量值
     */
    @ApiModelProperty(value = "温度超出要求部位温度测量值")
    private String temperatureOutOfRPMV;

    /**
     * 技术人员认可换桩截图
     */
    @ApiModelProperty(value = "技术人员认可换桩截图")
    private String technicalPersonnelRCPS;

    /**
     * 环温测量数值或外部热源照片
     */
    @ApiModelProperty(value = "环温测量数值或外部热源照片")
    private String hydroTMNOrEHSP;

    /**
     * 温度数值
     */
    @ApiModelProperty(value = "温度数值")
    private String temperatureNumber;

    /**
     * 螺丝扭矩数值
     */
    @ApiModelProperty(value = "螺丝扭矩数值")
    private String screwTorqueNumber;

    /**
     * 钳形表电流数值
     */
    @ApiModelProperty(value = "钳形表电流数值")
    private String cylinderCurrentNumber;

    /**
     * 充电桩背板接线照片
     */
    @ApiModelProperty(value = "充电桩背板接线照片")
    private String chargePileBackPinImage;

    /**
     * 背板照片
     */
    @ApiModelProperty(value = "背板照片")
    private String chargePileBackImage;

    /**
     * 充电桩背板火零电压
     */
    @ApiModelProperty(value = "充电桩背板火零电压")
    private String chargePileBackFireZeroVoltage;

    /**
     * 取电点空开火零电压
     */
    @ApiModelProperty(value = "取电点空开火零电压")
    private String electricityPointEmptyFZV;

    /**
     * 取电点零地电压
     */
    @ApiModelProperty(value = "取电点零地电压")
    private String electricityPointZeroVoltage;

    /**
     * 旧接地极照片
     */
    @ApiModelProperty(value = "旧接地极照片")
    private String oldGroundElectrodeImage;

    /**
     * 新接地极照片
     */
    @ApiModelProperty(value = "新接地极照片")
    private String newGroundElectrodeImage;

    /**
     * 取电点空开零地电压
     */
    @ApiModelProperty(value = "取电点空开零地电压")
    private String electricityPointEZV;

    /**
     * 充电桩背板照片
     */
    @ApiModelProperty(value = "充电桩背板照片")
    private String chargePileBackPhoto;

    /**
     * 枪头照片
     */
    @ApiModelProperty(value = "枪头照片")
    private String chargePileGunHeadImage;

    /**
     * 充电桩背板火零电压（带载）
     */
    @ApiModelProperty(value = "充电桩背板火零电压（带载）")
    private String chargePileBackFZVL;

    /**
     * 其他图片
     */
    @ApiModelProperty(value = "其他图片")
    private String otherImage;

}
