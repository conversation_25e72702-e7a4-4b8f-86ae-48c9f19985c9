package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * CPIM推送消息通知给指定供应商接口
 */
@Data
public class PushNotifyMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息通知id
     */
    @ApiModelProperty(value = "消息通知id", required = true)
    private String id;

    /**
     * 通知类型
     * 1:订单关闭通知
     * 2:转单通知
     * 3:订单审核驳回通知
     * 4:知识库更新通知
     * 5:修改安装信息通知
     * 6:长期未安装即将关闭订单通知
     * 8:客户催促安装通知
     * 9:客户催促报修通知
     */
    @ApiModelProperty(value = "通知类型", required = true)
    private Integer notifyType;

    /**
     * 安装订单编号
     */
    @ApiModelProperty(value = "安装订单编号", required = true)
    private String orderCode;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", required = false)
    private String vin;

    /**
     * 知识库文件编号
     */
    @ApiModelProperty(value = "知识库文件编号", required = false)
    private String cmsCode;

    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容", required = true)
    private String notifyContent;
}
