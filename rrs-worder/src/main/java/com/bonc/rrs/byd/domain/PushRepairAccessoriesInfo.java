package com.bonc.rrs.byd.domain;

import lombok.Data;

/**
 * 维修附件信息
 */
@Data
public class PushRepairAccessoriesInfo {
    /**
     * 报修订单编号
     */
    private String orderCode;

    /**
     * 操作人
     */
    private String operatePerson;

    /**
     * 生产企业技术人员的微信同意更换充电桩截图
     */
    private String replaceImage;

    /**
     * 客户维修确认单
     */
    private String confirmationImage;

    /**
     * 新桩照片（含序列号）
     */
    private String newWallboxImage;

    /**
     * 旧桩更换图片（含序列号）
     */
    private String usedWallboxImage;

    /**
     * 增项收费报价单
     */
    private String addChargeImage;

    /**
     * 维修确认单
     */
    private String repairtConfirmationImage;

    /**
     * 返厂快递单号
     */
    private String expressNumber;

    /**
     * 漏保更换前 漏保更换前附件 非必填
     */
    private String leakageReplacementBeginImage;

    /**
     * 漏保更换前 漏保更换后附件 非必填
     */
    private String leakageReplacementAfterImage;

    /**
     * 其他 其他 非必填
     */
    private String otherImage;
}
