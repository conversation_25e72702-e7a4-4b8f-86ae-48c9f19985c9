package com.bonc.rrs.byd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 比亚迪索赔订单费用明细表
 * <AUTHOR>
 *  claim_fee_detail
 */
@TableName(value ="claim_fee_detail")
@Data
public class ClaimFeeDetail implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报修订单号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 项目名
     */
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 维修含税单价
     */
    @TableField(value = "fix_unit_price")
    private String fixUnitPrice;

    /**
     * 理赔含税单价
     */
    @TableField(value = "claim_unit_price")
    private String claimUnitPrice;

    /**
     * 次
     */
    @TableField(value = "unit_count")
    private String unitCount;

    /**
     * 单价
     */
    @TableField(value = "unit_flag")
    private String unitFlag;

    /**
     * 维修价格
     */
    @TableField(value = "fix_price")
    private String fixPrice;

    /**
     * 理赔价格
     */
    @TableField(value = "claim_price")
    private String claimPrice;

}
