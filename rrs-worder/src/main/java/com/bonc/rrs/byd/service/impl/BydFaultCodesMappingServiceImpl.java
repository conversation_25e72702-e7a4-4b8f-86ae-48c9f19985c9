package com.bonc.rrs.byd.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.byd.domain.BydFaultCodesMapping;
import com.bonc.rrs.byd.service.BydFaultCodesMappingService;
import com.bonc.rrs.byd.dao.BydFaultCodesMappingMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【byd_fault_codes_mapping】的数据库操作Service实现
* @createDate 2025-05-22 16:12:50
*/
@Service
public class BydFaultCodesMappingServiceImpl extends ServiceImpl<BydFaultCodesMappingMapper, BydFaultCodesMapping>
    implements BydFaultCodesMappingService{

    @Override
    public List<BydFaultCodesMapping> getBydFaultCodesMapping(String faultCode) {
        return this.baseMapper.selectByFaultCode(faultCode);
    }
}




