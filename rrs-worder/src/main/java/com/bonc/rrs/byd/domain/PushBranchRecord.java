package com.bonc.rrs.byd.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class PushBranchRecord {

    /**
     * 安装订单编号备注
     */
    @NotBlank(message = "车企单号不能为空")
    private String orderCode;

    /**
     * 下发网点时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "下发网点时间不能为空")
    private String issuedBranchTime;

    /**
     * 网点编号
     */
    @NotBlank(message = "网点编号不能为空")
    private String branchCode;

    /**
     * 网点名称
     */
    @NotBlank(message = "网点名称不能为空")
    private String branchName;

    /**
     * 施工人员编号 必填
     */
    @NotBlank(message = "施工人员编号不能为空")
    private String personnelNumber;

    /**
     * 施工人员名称 必填
     */
    @NotBlank(message = "施工人员名称不能为空")
    private String personnelName;

//    personnelPhoneNumber	手机号	是	字符串	11	无数据：NO
//    electricianCertificateNumber	电工证编号	是	字符串	50	无数据：NO
//    electricianCertificateImage	电工证照片	是	字符串	500	无数据：NO
//    expirationDateBegin	电工证有效期开始时间	是	字符串	20	格式：yyyy-MM-dd，无数据：NO
//    expirationDateEnd	电工证有效期结束时间	是	字符串	20	格式：yyyy-MM-dd，无数据：NO
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String personnelPhoneNumber;
    /**
     * 电工证编号
     */
    @NotBlank(message = "电工证编号不能为空")
    private String electricianCertificateNumber;

    /**
     * 电工证照片
     */
    @NotBlank(message = "电工证照片不能为空")
    private String electricianCertificateImage;

    /**
     * 电工证有效期开始时间
     */
    @NotBlank(message = "电工证有效期开始时间不能为空")
    private String expirationDateBegin;

    /**
     * 电工证有效期结束时间
     */
    @NotBlank(message = "电工证有效期结束时间不能为空")
    private String expirationDateEnd;


}
