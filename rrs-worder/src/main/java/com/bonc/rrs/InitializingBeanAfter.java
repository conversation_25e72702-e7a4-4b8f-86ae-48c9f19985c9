package com.bonc.rrs;

import com.alipay.api.AlipayApiException;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.DefaultAlipayClient;
import com.bonc.rrs.sparesettlement.config.TicketConfigs;
import com.bonc.rrs.sparesettlement.config.WordConfigs;
import com.common.pay.alipay.config.Configs;
import com.common.pay.wxpay.config.WxConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * @ProjectName: lenmon-parent
 * @Package: com.bonc.rrs.pay
 * @ClassName: InitializingBeanAfter
 * @Author: admin
 * @Description:
 * @Date: 2020/8/28 14:55
 * @Version: 1.0
 */
@Slf4j
@Component
public class InitializingBeanAfter implements InitializingBean {

     private static DefaultAlipayClient alipayClient = null;

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("------------------开始执行bean 被初始化后的数据方法----------------------");
        WxConfig.init("wx/wxpayconfig.properties");
        Configs.init("ali/alipayconfig.properties");
        TicketConfigs.init("ticket.properties");
        WordConfigs.init("word.properties");
        try {
            alipayClient = new DefaultAlipayClient(constructCertAlipayRequest());
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
    }


    private static CertAlipayRequest constructCertAlipayRequest(){
        CertAlipayRequest certAlipayRequest = new CertAlipayRequest();
        certAlipayRequest.setServerUrl(Configs.getOpenApiDomain());
        certAlipayRequest.setAppId( Configs.getAppid());
        certAlipayRequest.setPrivateKey(Configs.getPrivateKey());
        certAlipayRequest.setFormat( Configs.getDataType());
        certAlipayRequest.setCharset( Configs.getCharset());
        certAlipayRequest.setSignType(Configs.getSignType());
        certAlipayRequest.setCertPath(Configs.getAppCertPath());
        certAlipayRequest.setAlipayPublicCertPath(Configs.getAliPayPublicCertPath());
        certAlipayRequest.setRootCertPath(Configs.getAlipayRootCertPath());
        return certAlipayRequest;
    }

    public static DefaultAlipayClient getAlipayClient(){
        return alipayClient;
    }
}
