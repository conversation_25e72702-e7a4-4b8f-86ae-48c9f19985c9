package com.bonc.rrs.lx.dto;

import lombok.Data;

import java.io.Serializable;


/**
 * 接口返回值实体类
 *
 * <AUTHOR>
 */
@Data
public class LxApixResponse implements Serializable {


    private static final long serialVersionUID = 9164030391706038851L;
    /**
     * 响应编码
     */
    private String code;

    /**
     * 响应描述
     */
    private String desc;

    /**
     * 响应内容
     */
    private Object data;
    /**
     * 响应结果状态
     */
    private Boolean success;

    public static LxApixResponse error(String msg) {
        LxApixResponse response = new LxApixResponse();
        response.setDesc(msg);
        response.setCode("1");
        response.setSuccess(false);
        return response;
    }

    public static LxApixResponse success() {
        LxApixResponse response = new LxApixResponse();
        response.setDesc("success");
        response.setCode("0");
        response.setSuccess(true);
        return response;
    }
    public static LxApixResponse success(Object o) {
        LxApixResponse response = success();
        response.setData(o);
        return response;
    }

    public boolean isSuccess() {
        return this.getSuccess();
    }
}
