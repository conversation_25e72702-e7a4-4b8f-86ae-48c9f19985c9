package com.bonc.rrs.lx.dto;

import com.deepoove.poi.data.PictureRenderData;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LxImages {
    //安装确认单1
    private List<PictureRenderData> azqr1 = new ArrayList<>();
    //安装确认单2
    private List<PictureRenderData> azqr2 = new ArrayList<>();
    //充电桩铭牌
    private List<PictureRenderData> sn = new ArrayList<>();
    //充电桩外箱桩码标签
    private List<PictureRenderData> snbox = new ArrayList<>();
    //取电点
    private List<PictureRenderData> qdd = new ArrayList<>();
    //取电点接地
    private List<PictureRenderData> qddjd = new ArrayList<>();
    //回路控制箱内部接线
    private List<PictureRenderData> hlkzx = new ArrayList<>();
    //人桩合照
    private List<PictureRenderData> rzhz = new ArrayList<>();
    //充电模拟器测试
    private List<PictureRenderData> cdmncs = new ArrayList<>();
    //试充时零地电压
    private List<PictureRenderData> scslddy = new ArrayList<>();
    //电缆标识
    private List<PictureRenderData> dlbz = new ArrayList<>();
    //电缆首尾米标1
    private List<PictureRenderData> dlswmb1 = new ArrayList<>();
    //电缆首尾米标2
    private List<PictureRenderData> dlswmb2 = new ArrayList<>();
    //线路绝缘检测
    private List<PictureRenderData> xljyjc = new ArrayList<>();
    //线路走向1
    private List<PictureRenderData> xlzx1 = new ArrayList<>();
    //线路走向2
    private List<PictureRenderData> xlzx2 = new ArrayList<>();
    //线路走向3
    private List<PictureRenderData> xlzx3 = new ArrayList<>();
    //其他照片
    private List<PictureRenderData> qtzp = new ArrayList<>();
    //清理现场照片
    private List<PictureRenderData> qlxczp = new ArrayList<>();
    //回访记录
    private List<PictureRenderData> hfjl = new ArrayList<>();

}