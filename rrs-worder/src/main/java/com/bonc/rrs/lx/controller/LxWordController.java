package com.bonc.rrs.lx.controller;

import com.bonc.rrs.lx.service.LxWordService;
import com.bonc.rrs.workManager.dao.BrandMapper;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lx/word")
public class LxWordController {
    @Autowired
    SysFilesService sysFilesService;
    @Autowired(required = false)
    BrandMapper brandMapper;

    @Autowired
    private LxWordService lxWordService;

    //校验是否有文件接口
    @RequestMapping("/findByValue")
    public R findByValue(String worderNo, Integer purpose) {
        //判断文件是否存在
        String name = (purpose == 2 ? "勘测报告-word" : "安装报告-word");
        Boolean aBoolean = sysFilesService.fingByValue(worderNo, name);
        if (!aBoolean) {
            return R.error("该工单已有报告文件,确认要生成/上传文件吗？");
        }
        return R.ok();
    }

    /**
     * 根据工单查品牌
     */
    @RequestMapping("/getBrandCarID")
    public Integer getBrandCarID(String worderNo) {
        Integer id = brandMapper.selectBrandCarID(worderNo);
        return id;
    }

    @GetMapping("/generationWord")
    public R generationWord(@RequestParam String worderNo) throws Exception {
        //安装工单报告下载
        return lxWordService.downloadReport(worderNo);
    }
}
