/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.lx.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:05
 * @Version 1.0.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class LxRepairOrderApiDto extends LxOrderApiDto {
    private static final long serialVersionUID = 1L;

    /**
     * 安装单号
     * 147
     */
    @ApiModelProperty(value = "安装单号")
    private String installOrderNo;

    /**
     * 车企派单日期
     * 154
     */
    @ApiModelProperty(value = "车企派单日期", required = true)
    private String dispatchDate;

    /**
     * 理想充电桩型号
     * 1055
     */
    @ApiModelProperty(value = "理想充电桩型号", required = true)
    private String chargeType;

    /**
     * 安装城市
     * 1056
     */
    @ApiModelProperty(value = "安装城市", required = true)
    private String installCity;

    /**
     * 安装完成时间
     * 1057
     */
    @ApiModelProperty(value = "安装完成时间", required = true)
    private String installFinishTime;

    /**
     * 待维修原充电桩编号
     * 1058
     */
    @ApiModelProperty(value = "待维修原充电桩编号", required = true)
    private String oldChargeNo;

    /**
     * 客户反映故障描述
     * 1129
     */
    @ApiModelProperty(value = "客户反映故障描述")
    private String faultDesc;

    /**
     * 已执行的故障排查
     * 1141
     */
    @ApiModelProperty(value = "已执行的故障排查")
    private String faultPoll;

    /**
     * 线路是否在保
     * 1142
     */
    @ApiModelProperty(value = "线路是否在保",required = true)
    private String lineGuarantee;

    /**
     * 充电桩是否在保
     * 1143
     */
    @ApiModelProperty(value = "充电桩是否在保",required = true)
    private String chargeGuarantee;

    /**
     * 是否官方移桩服务
     * 1206
     */
    @ApiModelProperty(value = "是否官方移桩服务",required = true)
    private String isOfficialMove;

    /**
     * 联系信息备注
     *
     */
    @ApiModelProperty(value = "联系信息备注")
    private String contactRemark;

}
