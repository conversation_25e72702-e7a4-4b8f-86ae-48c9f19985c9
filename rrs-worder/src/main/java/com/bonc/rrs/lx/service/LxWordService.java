package com.bonc.rrs.lx.service;

import cn.hutool.core.io.FileUtil;
import com.bonc.rrs.lx.config.LxConfig;
import com.bonc.rrs.lx.dto.LxImages;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.workManager.dao.AutidOrderMapper;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.policy.ListRenderPolicy;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LxWordService {
    @Autowired
    private SysFilesService sysFilesService;

    @Autowired
    private WorderInformationService worderInformationService;

    @Autowired
    private LxConfig lxConfig;

    @Autowired
    private AutidOrderMapper autidOrderMapper;

    /**
     * @param worderNo 工单号
     */
    public R downloadReport(String worderNo) {
        List<Map<String, Object>> list = autidOrderMapper.fandByValue(1630, worderNo, 3);
        if (CollectionUtils.isNotEmpty(list)) {
            Object fieldValue = list.get(0).get("field_value");
            if (fieldValue == null) {
                return R.error("无效的文件ID");
            }
            String fileId = fieldValue.toString();
            SysFileEntity sysFile = sysFilesService.getSysFileById(fileId);
            String url = sysFile.getPath();
            return R.ok(url);
        }

        List<Map<String, Object>> images = sysFilesService.getUrl(worderNo, 3);
        if (CollectionUtils.isEmpty(images)) {
            return R.error("该工单没有图片");
        }
        WorderInformationEntity entity = worderInformationService.getByWorderNo(worderNo);
        LxImages lxImages = new LxImages();
        images.forEach(image -> {
            try {
                String fieldId = image.get("field_id").toString();
                String filePath = image.get("path").toString();
                if (StringUtils.isNotBlank(filePath)) {
                    PictureRenderData pictureRenderData = null;
                    switch (fieldId) {
                        case "269"://安装确认单1
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getAzqr1().add(pictureRenderData);
                            break;
                        case "1017"://增项报价单-安装
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getAzqr2().add(pictureRenderData);
                            break;
                        case "979"://充电桩铭牌
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getSn().add(pictureRenderData);
                            break;
                        case "1383"://充电桩外箱桩码标签
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getSnbox().add(pictureRenderData);
                            break;
                        case "264"://取电点
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getQdd().add(pictureRenderData);
                            break;
                        case "1389"://取电点　1389
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getQdd().add(pictureRenderData);
                            break;
                        case "1384"://取电点接地
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getQddjd().add(pictureRenderData);
                            break;
                        case "1390"://回路控制箱内部接线
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getHlkzx().add(pictureRenderData);
                            break;
                        case "274"://人桩合照
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getRzhz().add(pictureRenderData);
                            break;
                        case "273"://充电模拟器测试
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getCdmncs().add(pictureRenderData);
                            break;
                        case "966"://试充时零地电压
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getScslddy().add(pictureRenderData);
                            break;
                        case "973"://电缆标识
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getDlbz().add(pictureRenderData);
                            break;
                        case "278"://电缆首尾米标1
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getDlswmb1().add(pictureRenderData);
                            break;
                        case "310"://电缆首尾米标2
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getDlswmb2().add(pictureRenderData);
                            break;
                        case "981"://线路绝缘检测
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getXljyjc().add(pictureRenderData);
                            break;
                        case "279"://线路走向1
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getXlzx1().add(pictureRenderData);
                            break;
                        case "287"://线路走向2
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getXlzx2().add(pictureRenderData);
                            break;
                        case "16"://线路走向3
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getXlzx3().add(pictureRenderData);
                            break;
                        case "1789"://清理现场照片
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getQlxczp().add(pictureRenderData);
                            break;
                        case "1382"://回访记录
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getHfjl().add(pictureRenderData);
                            break;
                        case "1427"://其他照片
                            pictureRenderData = resizeAndCompressImage(filePath, 600, 730, 1.0f);
                            lxImages.getQtzp().add(pictureRenderData);
                            break;
                        default:
                            //lxImages.getQtzp().add(pictureRenderData);
                            break;
                    }
                }
            } catch (Exception e) {
                log.warn("图片转换异常,image = {}", image, e);
            }
        });

        XWPFTemplate template = null;
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("理想安装报告模板.docx")) {
            if (inputStream == null) {
                log.error("无法找到模板文件");
                return R.error("无法找到模板文件");
            }

            template = XWPFTemplate.compile(inputStream, Configure.newBuilder().addPlugin('%', new ListRenderPolicy()).build()).render(lxImages);
            String targetFileName = entity.getUserName() + "-竣工报告.docx";
            template.writeToFile(lxConfig.getReportTempPath() + targetFileName);
            String url = upload(worderNo,targetFileName);
            return R.ok(url);
        } catch (Exception e) {
            log.error("生成安装报告失败, 异常详情: ", e);
            return R.error("生成安装报告失败");
        } finally {
            if (template != null) {
                try {
                    template.close();
                } catch (IOException e) {
                    log.error("关闭模板失败, 异常详情: ", e);
                }
            }
        }
    }

    /**
     * 上传勘测文件到oss
     */
    public String upload(String worderNo,String targetFileName) {
        File tempFile = FileUtil.file(lxConfig.getReportTempPath(), targetFileName);
        try {
            if (FileUtil.isNotEmpty(tempFile)) {
                Map<String, String> urlMap = FileUtils.getOssURL(tempFile, targetFileName);
                String path = urlMap.get("url");
                String name = urlMap.get("name");
                String md5Str = urlMap.get("md5Str");
                //判断工单是否有id 有的话就修改 没有的话就添加
                Integer fileId=0;
                SysFileEntity sysFileEntity = new SysFileEntity();
                sysFileEntity.setOldName(targetFileName);
                sysFileEntity.setNewName(name);
                sysFileEntity.setPath(path);
                sysFileEntity.setFileCode(md5Str);
                fileId= sysFilesService.saveSysFile(sysFileEntity);
                if (fileId>0) {
                    int updated = autidOrderMapper.updateAutidInfo(1630, String.valueOf(fileId), worderNo, 3);
                    if (updated > 0) {
                        log.info("更新安装报告成功");
                    }
                }
                return urlMap.get("url");
            }
            return null;
        } catch (IOException e) {
            log.error("安装报告上传OSS失败", e);
            return null;
        } finally {
            // 删除本地word文件
            if (tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("删除临时文件失败: " + tempFile.getAbsolutePath());
                }
            }
        }
    }

    public static PictureRenderData resizeAndCompressImage(String filePath, int maxWidth, int maxHeight, float quality) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            Thumbnails.of(new URL(filePath))
//                    .size(maxWidth, maxHeight)
                    .scale(quality)
                    .toOutputStream(baos);
            byte[] imageBytes = baos.toByteArray();
            int count=0;
            while (imageBytes.length > 1024 * 1024 && quality > 0.1 && count <= 10) {
                try (ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes)) {
                    quality -= 0.1f;
                    baos.reset();
                    Thumbnails.of(bais).scale(1.0f).outputQuality(quality).toOutputStream(baos);
                    imageBytes = baos.toByteArray();
                    count++;
                }
            }

            return new PictureRenderData(maxWidth, maxHeight, ".jpg", imageBytes);

        } catch (IOException e) {
            log.error("图片压缩异常: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("未知异常: {}", e.getMessage(), e);
        }
        return null;
    }

}
