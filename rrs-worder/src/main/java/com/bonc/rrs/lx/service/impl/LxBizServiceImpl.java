package com.bonc.rrs.lx.service.impl;

import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.gace.config.GaceConfig;
import com.bonc.rrs.gace.util.GeoCode;
import com.bonc.rrs.gace.util.GeoCodeResponse;
import com.bonc.rrs.lx.dto.LxApixResponse;
import com.bonc.rrs.lx.dto.LxOrderApiDto;
import com.bonc.rrs.lx.dto.LxOrderExistDto;
import com.bonc.rrs.lx.dto.LxRepairOrderApiDto;
import com.bonc.rrs.lx.service.LxBizService;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.util.UserUtil;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.BizRegionService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youngking.lenmoncore.common.exception.RRException;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:37
 * @Version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LxBizServiceImpl implements LxBizService {
    private final WorderInformationService worderInformationService;
    private final BizRegionService bizRegionService;
    private final GaceConfig gaceConfig;


    @Override
    public List<Integer> supportTemplate() {
        return Arrays.asList(16, 17, 22, 23);
    }

    @Override
    public LxApixResponse saveOrder(LxOrderApiDto orderApiVO) {
//        WorderIntfMessageEntity messageEntity = WorderIntfMessageEntity.builder()
//                .intfCode("pushOrder")
//                .worderId(0)
//                .bid(3)
//                .data(JSON.toJSONString(orderApiVO))
//                .createTime(new Date())
//                .isTransfer(0)
//                .messageType(0)
//                .orderCode(orderApiVO.getServOrderNo())
//                .build();
//        worderIntfMessageService.save(messageEntity);
        return asyncSave(orderApiVO);
    }


    LxApixResponse asyncSave(LxOrderApiDto orderApiVO) {
        UserUtil.createDefaultLoginUser();
        String worderNo = null;
        WorderInfoEntity worderInfoEntity = null;
        try {
            if ("6".equals(orderApiVO.getWorderType())) {
                worderInfoEntity = doParseDataAndSaveRepairOrder((LxRepairOrderApiDto) orderApiVO);
            } else {
                worderInfoEntity = doParseDataAndSaveOrder(orderApiVO);
            }

            worderNo = worderInformationService.saveWorderInformation(worderInfoEntity,new HashMap<>());
            if (worderNo.equals("errorLength")){
                return LxApixResponse.error("卡泰驰SN编码长度应为24位，请检查后再提交。");
            }

            if (worderNo.contains("error:")){
                String[] split = worderNo.split(":");
                return LxApixResponse.error("相同区域,品牌和服务类型有多个客服["+ split[1] +"],请账户设置正确后再提交。");
            }

//            worderIntfMessageService.updateWorderIdById(messageEntity.getId(), worderInfoEntity.getWorderId());
        } catch (Exception e) {
            log.error("创建理想工单失败", e);
            SmsUtil.sendSms("15910305046", "理想下单推送失败,车企订单号:" + orderApiVO.getServOrderNo(), "【到每家科技服务】");
//            worderIntfMessageService.updateMessageTypeById(messageEntity.getId(), 2, e.getMessage());
            throw e;
        }

        try {
            Results results = worderInformationService.goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME, null);
            if (results.getCode() != 0) {
                throw new RRException(results.getCode() + results.getMsg());
            }
            // 修改工单状态 0
            worderInformationService.updateWorderStatus(worderNo);
            return LxApixResponse.success();
        } catch (Exception e) {
            log.error("{}派单失败", worderNo, e);
            SmsUtil.sendSms("15910305046", "理想订单派单失败,原因:" + e.getMessage() + ",车企订单号:" + orderApiVO.getServOrderNo(), "【到每家科技服务】");
            return LxApixResponse.error("理想订单派单失败,原因:" + e.getMessage() + ",车企订单号:" + orderApiVO.getServOrderNo());
        }
    }

    public WorderInfoEntity doParseDataAndSaveRepairOrder(LxRepairOrderApiDto apiDto) {

        if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(apiDto.getServOrderNo(), Integer.valueOf(apiDto.getTemplateId()))) {
            log.info("message save order " + apiDto.getServOrderNo() + " 车企订单号已存在，无法创建工单");
            throw new RRException("Failed to save order " + apiDto.getServOrderNo());
        }

        // 匹配表情符
        String regex = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";

        Integer companyId = 673;

        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        LxRegionCode regionCode = new LxRegionCode();
        regionCode.setProvinceName(apiDto.getProvinceCode());
        regionCode.setCityName(apiDto.getCityCode());
        regionCode.setAreaName(apiDto.getAreaCode());
        regionCode.setDetailAddress(apiDto.getDetailAddress());
        try {

            if (StringUtils.isBlank(regionCode.getAreaName()) && StringUtils.isNotBlank(regionCode.getDetailAddress())) {
                Map<String, Object> params = new HashMap<>();
                params.put("key", gaceConfig.getAmapParam().getKey());
                params.put("address", regionCode.getProvinceName() + regionCode.getCityName() + apiDto.getDetailAddress());

                String ampResult = HttpUtil.get(gaceConfig.getAmapParam().getUrl(), params);

                ObjectMapper objectMapper = new ObjectMapper();
                GeoCodeResponse response = objectMapper.readValue(ampResult, GeoCodeResponse.class);
                if (response.getStatus() == 1 && response.getCount()>0) {
                    GeoCode geoCode = response.getGeocodes().get(0);
                    List<String> district = geoCode.getDistrict();
                    if ( !CollectionUtils.isEmpty(district)) {
                        regionCode.setAreaName(district.get(0));
                    }
                    regionCode.setDetailAddress(apiDto.getDetailAddress());
                }else {
                    log.error("高德地图接口返回异常,异常信息:"+ampResult);
                }
            }
        } catch (Exception e) {
            log.error("高德地图接口异常,异常信息:"+e.getMessage());
        }

        determineRegion(regionCode);

        String address =
                regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + regionCode.getDetailAddress();
        address = address.replaceAll(regex, "");

        String userName = apiDto.getContactName();
        userName = userName.replaceAll(regex, "");

        worderInfoEntity.setPushOrderWorderSource("lx");
        worderInfoEntity.setUserName(userName);
        worderInfoEntity.setUserPhone(apiDto.getContactPhone());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(apiDto.getServOrderNo());
        worderInfoEntity.setTemplateId(Integer.valueOf(apiDto.getTemplateId()));

        worderInfoEntity.setCarBrand("18");
        worderInfoEntity.setCarModel(apiDto.getCarModel());
        worderInfoEntity.setCompanyId(companyId);
        worderInfoEntity.setCompanyOrderNumberId(apiDto.getServOrderId());
        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderTypeId(Integer.valueOf(apiDto.getWorderType()));

        worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
        worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1, "工单类型", worderInfoEntity.getWorderTypeId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(101, "车企名称", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(102, "分中心", apiDto.getFzx()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(147, "安装单号", apiDto.getInstallOrderNo()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(154, "车企派单日期", apiDto.getDispatchDate()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(306, "工单来源", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(902, "客户姓名", worderInfoEntity.getUserName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(903, "安装地址", address));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(905, "客户手机", worderInfoEntity.getUserPhone()));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1171,"客户ID号",apiDto.getCustId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1055, "理想充电桩型号", apiDto.getChargeType()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1056, "安装城市", apiDto.getInstallCity()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1057, "安装完成时间", apiDto.getInstallFinishTime()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1058, "待维修原充电桩编号", apiDto.getOldChargeNo()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1129, "客户反映故障描述", apiDto.getFaultDesc()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1141, "已执行的故障排查", apiDto.getFaultPoll()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1142, "线路是否在保", apiDto.getLineGuarantee()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1143, "充电桩是否在保", apiDto.getChargeGuarantee()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1206, "是否官方移桩服务", apiDto.getIsOfficialMove()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(911, "联系信息备注", apiDto.getContactRemark()));



        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
        return worderInfoEntity;
    }

    private WorderInfoEntity doParseDataAndSaveOrder(LxOrderApiDto apiDto) {

        if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(apiDto.getServOrderNo(), Integer.valueOf(apiDto.getTemplateId()))) {
            log.info("message save order " + apiDto.getServOrderNo() + " 车企订单号已存在，无法创建工单");
            throw new RRException("Failed to save order " + apiDto.getServOrderNo());
        }

        // 匹配表情符
        String regex = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";

        Integer companyId = 673;

        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        worderInfoEntity.setCarBrand("92");
        // 360校验厂商id
        worderInfoEntity.setWorderSourceTypeValue("");
        worderInfoEntity.setPostcode("");

        LxRegionCode regionCode = new LxRegionCode();
        regionCode.setProvinceName(apiDto.getProvinceCode());
        regionCode.setCityName(apiDto.getCityCode());
        regionCode.setAreaName(apiDto.getAreaCode());
        regionCode.setDetailAddress(apiDto.getDetailAddress());

        determineRegion(regionCode);

        String address =
                regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + regionCode.getDetailAddress();
        address = address.replaceAll(regex, "");

        String userName = apiDto.getContactName();
        userName = userName.replaceAll(regex, "");

        worderInfoEntity.setPushOrderWorderSource("lx");
        worderInfoEntity.setUserName(userName);
        worderInfoEntity.setUserPhone(apiDto.getContactPhone());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(apiDto.getServOrderNo());
        worderInfoEntity.setTemplateId(Integer.valueOf(apiDto.getTemplateId()));

        worderInfoEntity.setCarBrand("18");
        worderInfoEntity.setCarModel(apiDto.getCarModel());
        worderInfoEntity.setCompanyId(companyId);
        worderInfoEntity.setCompanyOrderNumberId(apiDto.getServOrderId());
        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderTypeId(Integer.valueOf(apiDto.getWorderType()));

        worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
        worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1, "工单类型", worderInfoEntity.getWorderTypeId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(101, "车企名称", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(102, "分中心", apiDto.getFzx()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(306, "工单来源", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(902, "客户姓名", worderInfoEntity.getUserName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(903, "安装地址", address));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(905, "客户手机", worderInfoEntity.getUserPhone()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(154, "车企派单日期", apiDto.getDispatchDate()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1038, "安装订单号", apiDto.getInstallOrderNo()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1045, "理想车型", apiDto.getCarModel()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1170, "服务类型", apiDto.getServiceType()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1171,"客户ID号",apiDto.getCustId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1172, "创建人", apiDto.getCreateName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1224, "是否预勘测订单", apiDto.getIsPreCheck()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1391, "工单级别", apiDto.getWorderLevel()));


        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
       return worderInfoEntity;
    }

    @Override
    public LxApixResponse checkOrdersExist(LxOrderExistDto orderExistDto) {
        WorderInformationEntity entity = worderInformationService.getByCompanyWorderNo(orderExistDto.getServOrderNo());

        return entity==null? LxApixResponse.error("订单不存在") : LxApixResponse.success();
    }

    @Override
    public LxApixResponse checkOrdersExist(List<String> companyOrderNos) {
        List<WorderInformationEntity> list = worderInformationService.lambdaQuery().in(WorderInformationEntity::getCompanyOrderNumber, companyOrderNos).list();

        Map<String, String> worders = list.stream().collect(Collectors.toMap(WorderInformationEntity::getCompanyOrderNumber, WorderInformationEntity::getWorderNo, (e1, e2) -> e2));

        Map<String,Boolean> map = new HashMap<>();
        companyOrderNos.forEach(s -> {
            map.put(s, worders.containsKey(s));
        });

        return LxApixResponse.success(map);
    }

    public void determineRegion(@NotNull LxRegionCode regionInfo) {
        // 初始化临时变量
        BizRegionEntity province = null;
        BizRegionEntity city = null;
        BizRegionEntity area = null;
        if (StringUtils.equalsAny(regionInfo.getCityName(), "市辖区","直辖区")) {
            regionInfo.setCityName(regionInfo.getProvinceName());
        }
        if (StringUtils.equalsAny(regionInfo.getAreaName(), "市辖区","直辖区")) {
            regionInfo.setAreaName(regionInfo.getCityName());
        }

        // 根据省市区名称查询数据
        if (StringUtils.isNotBlank(regionInfo.getProvinceName())) {
            province = bizRegionService.lambdaQuery()
                    //.eq(BizRegionEntity::getPid, null)
                    .like(BizRegionEntity::getName, regionInfo.getProvinceName())
                    .eq(BizRegionEntity::getType, 1)
                    .last("limit 1")
                    .one();
        }
        if (StringUtils.isNotBlank(regionInfo.getCityName())) {
            LambdaQueryChainWrapper<BizRegionEntity> wrapper = bizRegionService.lambdaQuery();
            if (province != null) {
                wrapper.eq(BizRegionEntity::getPid, province.getId());
            }
            city = wrapper.like(BizRegionEntity::getName, regionInfo.getCityName())
                    .eq(BizRegionEntity::getType, 2)
                    .last("limit 1")
                    .one();
            if (city == null) {
                wrapper = bizRegionService.lambdaQuery();
                area = wrapper.like(BizRegionEntity::getName, regionInfo.getCityName())
                        .eq(BizRegionEntity::getType, 3)
                        .last("limit 1")
                        .one();
            }
        }
        if (StringUtils.isNotBlank(regionInfo.getAreaName())) {
            LambdaQueryChainWrapper<BizRegionEntity> wrapper = bizRegionService.lambdaQuery();
            if (city != null) {
                wrapper.eq(BizRegionEntity::getPid, city.getId());
            }
            BizRegionEntity area1 = wrapper.like(BizRegionEntity::getName, regionInfo.getAreaName())
                    .eq(BizRegionEntity::getType, 3)
                    .last("limit 1")
                    .one();
            area = area1 == null ? area : area1;
        }

        // 自下而上递归设置省市区编码
        if (area != null) {
            regionInfo.setAreaCode(area.getId());
            regionInfo.setAreaName(area.getName());
            city = city == null ? bizRegionService.getById(area.getPid()) : city;
            if (city != null) {
                regionInfo.setCityCode(city.getId());
                regionInfo.setCityName(city.getName());
                province = province == null ? bizRegionService.getById(city.getPid()) : province;
                if (province != null) {
                    regionInfo.setProvinceCode(province.getId());
                    regionInfo.setProvinceName(province.getName());
                }
            }
        } else if (city != null) {
            regionInfo.setCityCode(city.getId());
            regionInfo.setCityName(city.getName());
            province = province==null? bizRegionService.getById(city.getPid()) : province;
            if (province != null) {
                regionInfo.setProvinceCode(province.getId());
                regionInfo.setProvinceName(province.getName());
            }
            area = bizRegionService.getFirstDistrictByCityId(city.getId());
            if (area != null) {
                regionInfo.setAreaCode(area.getId());
                regionInfo.setAreaName(area.getName());
            }
        }

        // 验证最终结果
        if (regionInfo.getProvinceCode() == null || regionInfo.getCityCode() == null || regionInfo.getAreaCode() == null) {
            throw new RRException("地址错误");
        }
    }

    @Data
    public static class LxRegionCode {

        /**
         * 省份编码
         */
        private Long provinceCode;

        private String provinceName;
        /**
         * 市编码
         */
        private Long cityCode;

        private String cityName;
        /**
         * 区编码
         */
        private Long areaCode;

        private String areaName;

        /**
         * 详细地址
         */
        private String detailAddress;
    }

    @SneakyThrows
    public static void main(String[] args) {
        String ampResult = "{\"status\":\"1\",\"info\":\"OK\",\"infocode\":\"10000\",\"count\":\"1\",\"geocodes\":[{\"formatted_address\":\"新疆维吾尔自治区石河子市宏新花园\",\"country\":\"中国\",\"province\":\"新疆维吾尔自治区\",\"city\":[],\"district\":\"石河子市\",\"township\":[],\"neighborhood\":{\"name\":[],\"type\":[]},\"building\":{\"name\":[],\"type\":[]},\"adcode\":\"659001\",\"street\":[],\"number\":[],\"location\":\"86.093482,44.307235\",\"level\":\"住宅区\"}]}";
        ObjectMapper objectMapper = new ObjectMapper();
        GeoCodeResponse response = objectMapper.readValue(ampResult, GeoCodeResponse.class);
        System.out.println(response);
    }

}
