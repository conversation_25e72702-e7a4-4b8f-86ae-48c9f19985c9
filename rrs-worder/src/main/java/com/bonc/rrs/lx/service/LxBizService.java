/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.lx.service;

import com.bonc.rrs.lx.dto.LxApixResponse;
import com.bonc.rrs.lx.dto.LxOrderApiDto;
import com.bonc.rrs.lx.dto.LxOrderExistDto;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:36
 * @Version 1.0.0
 */
public interface LxBizService {

    List<Integer> supportTemplate();

    LxApixResponse saveOrder(LxOrderApiDto orderApiDto);

    LxApixResponse checkOrdersExist(LxOrderExistDto orderExistDto);

    LxApixResponse checkOrdersExist(List<String> companyOrderNos);

}
