package com.bonc.rrs.lx.controller;

import com.bonc.rrs.lx.dto.LxApixResponse;
import com.bonc.rrs.lx.dto.LxOrderApiDto;
import com.bonc.rrs.lx.dto.LxOrderExistDto;
import com.bonc.rrs.lx.dto.LxRepairOrderApiDto;
import com.bonc.rrs.lx.service.LxBizService;
import com.bonc.rrs.spider.strategy.OrderStrategyFactory;
import com.gexin.fastjson.JSON;
import com.youngking.lenmoncore.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:00
 * @Version 1.0.0
 */

@Slf4j
@RestController
@RequestMapping("/dmj/openapi/news/lx")
@Api(value = "/dmj/openapi/news/lx", tags = "理想爬虫接口")
@RequiredArgsConstructor
public class LxServiceProvidersController {
    private final LxBizService lxBizService;
    private final OrderStrategyFactory strategyFactory;

    @Value("${bridgehub.paramToken}")
    private String paramToken;

    @PostMapping("/dispatchOrder")
    @ApiOperation(value = "理想爬虫推送安装工单接口", notes = "理想爬虫推送安装工单接口")
    public LxApixResponse orderCreate(@RequestHeader(name = "x-accesstoken") String token, @RequestBody @Valid LxOrderApiDto orderApiVO) {
        if (StringUtils.isBlank(token) || !token.equals(paramToken)) {
            return LxApixResponse.error("token校验失败");
        }
        log.info("订单创建请求参数：{}", JSON.toJSONString(orderApiVO));
        try {
            return lxBizService.saveOrder(orderApiVO);
        } catch (Exception e) {
            return LxApixResponse.error(e.getMessage());
        }
    }

    @PostMapping("/dispatchRepairOrder")
    @ApiOperation(value = "理想爬虫推送维修工单接口", notes = "理想爬虫推送维修工单接口")
    public LxApixResponse repairOrderCreate(@RequestHeader(name = "x-accesstoken") String token, @RequestBody @Valid LxRepairOrderApiDto orderApiVO) {
        if (StringUtils.isBlank(token) || !token.equals(paramToken)) {
            return LxApixResponse.error("token校验失败");
        }

        log.info("维修订单创建请求参数：{}", JSON.toJSONString(orderApiVO));
        try {
            return lxBizService.saveOrder(orderApiVO);
        } catch (Exception e) {
            return LxApixResponse.error(e.getMessage());
        }
    }


    @PostMapping("/order/exist")
    @ApiOperation(value = "查询工单是否存在接口", notes = "查询工单是否存在接口")
    public LxApixResponse getInstallOrderInfo(@RequestBody @Valid LxOrderExistDto orderExistDto) {
        log.info("查询接口：{}", JSON.toJSONString(orderExistDto));
        return lxBizService.checkOrdersExist(orderExistDto);
    }

    @PostMapping("/orders/exist")
    @ApiOperation(value = "批量查询工单是否存在接口", notes = "批量查询工单是否存在接口")
    public LxApixResponse getInstallOrderInfo(@RequestBody @Valid List<String> orderNos) {
        log.info("查询接口：{}", JSON.toJSONString(orderNos));
        return lxBizService.checkOrdersExist(orderNos);
    }

}
