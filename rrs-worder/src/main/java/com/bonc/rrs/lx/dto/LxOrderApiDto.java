/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.lx.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:05
 * @Version 1.0.0
 */

@Data
public class LxOrderApiDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 厂商订单id
     */
    @ApiModelProperty(value = "车企订单id")
    private String servOrderId;
    /**
     *厂商页面显示的服务工单号，用于对照查询
     */
    @ApiModelProperty(value = "车企订单号", required = true)
    @NotBlank(message = "车企订单号不能为空")
    private String servOrderNo;

    /**
     *厂商页面显示的安装订单号
     */
    @ApiModelProperty(value = "安装订单号")
    //@NotBlank(message = "安装订单号不能为空")
    private String installOrderNo;

    /**
     * 模板编号
     */
    @ApiModelProperty(value = "模板编号", required = true)
    @NotBlank(message = "模板编号不能为空")
    private String templateId;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名", required = true)
    @NotBlank(message = "客户姓名不能为空")
    private String contactName;
    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话", required = true)
    @NotBlank(message = "客户电话不能为空")
    private String contactPhone;

    /**
     * 省份代码
     */
    @ApiModelProperty(value = "省份代码", required = true)
    @NotBlank(message = "省份代码不能为空")
    private String provinceCode;

    /**
     * 城市代码
     */
    @ApiModelProperty(value = "城市代码", required = true)
    @NotBlank(message = "城市代码不能为空")
    private String cityCode;

    /**
     * 区域代码
     */
    @ApiModelProperty(value = "区域代码")
    private String areaCode;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message = "详细地址不能为空")
    private String detailAddress;
    /**
     * 车辆VIN码
     */
    @ApiModelProperty(value = "车辆VIN码")
    private String vinNo;
    /**
     * 车辆品牌
     */
    @ApiModelProperty(value = "车辆品牌")
    private String carBrand;
    /**
     * 理想车型
     */
    @ApiModelProperty(value = "理想车型")
    private String carModel;

    /**
     * 客户ID号
     */
    @ApiModelProperty(value = "客户ID号")
    private String custId;

    /**
     * 服务类型
     */
    @ApiModelProperty(value = "服务类型")
    private String serviceType;

    /**
     * 分中心
     */
    @ApiModelProperty(value = "分中心")
    private String fzx;

    /**
     * 工单类型
     * 2--安装，5--勘安，6--维修
     */
    @ApiModelProperty(value = "工单类型",required = true)
    @NotBlank(message = "工单类型不能为空")
    private String worderType;

    /**
     * 是否预勘测订单
     */
    @ApiModelProperty(value = "是否预勘测订单")
    private String isPreCheck;

    /**
     * 工单级别
     */
    @ApiModelProperty(value = "工单级别")
    private String worderLevel;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createName;

    /**
     * 车企派单日期
     * 154
     */
    @ApiModelProperty(value = "车企派单日期")
    private String dispatchDate;

}
