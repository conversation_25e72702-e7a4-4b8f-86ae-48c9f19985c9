body {
    background: #fafafa;
    color: #708090;
    /* font: 300 100.1% "Helvetica Neue", Helvetica, "Arial Unicode MS", Arial, sans-serif; */
	font-family: <PERSON>erd<PERSON>, sans-serif, Arial;
	font-size: 10px;
}
.wrapper{
	height: 100%;
	position: relative;
	width: 100%;
}
/*
#holder {
    height: 480px;
    width: 640px;
	b_ackground: #F8F8FF;
	
	-moz-border-radius: 10px;
	-webkit-border-radius: 10px;
	-webkit-box-shadow: 0 1px 3px #666;
	background: #DDD url(./images/bg.png);
	/* background: #DDD url(./images/checker-bg.png); * /
	b_order:0px solid #dedede;
}
*/
div.diagramHolder {
	float:left; 
}
div.diagram{
	border:1px solid #dedede;
	margin: 5px;
	padding: 5px;
	background: #FFF;
}
div.diagram.hidden{
	display:none;
}
svg {
	background: #DDD url(./images/bg.png);
}

div.diagram-info {
	float:left;
	position: relative;
	padding: 5px;
}

/* Breadcrumbs */

#diagramBreadCrumbs {
	margin-left: 2px;
	margin-right: 2px;
	margin-top: 10px;
}
#diagramBreadCrumbs ul {
	list-style: none;
	
	background-color: white;
	border: 1px solid #DEDEDE;
	border-color: #C0C2C5;
	margin: 0;
	
	margin-bottom: 10px;
	margin-left: 0;
	
	-webkit-padding-start: 0px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	border-radius: 4px;
}
#diagramBreadCrumbs li {
	/*text-decoration: underline;*/
	display: inline-block;
	vertical-align: middle;
	padding-left: .75em;
	padding-right: 0;
	cursor: pointer;
}
#diagramBreadCrumbs li.selected {
	color: #9370DB;
	color: #4876FF;
	color: #4F94CD;
	font-weight: bold;
}
#diagramBreadCrumbs li span {
	background: url(images/breadcrumbs.png) no-repeat 100% 50%;
	display: block;
	padding: .5em 15px .5em 0;
}


/* Progress bar */

.ui-progressbar { 
	height: 25px;
	/*height:2em; text-align: left; overflow: hidden; */
	background: white;
	border: 1px solid #949DAD;
	margin: 2px;
	overflow: hidden;
	padding: 1px;
	position: relative;

	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	border-radius: 4px;
}
.ui-progressbar .ui-progressbar-value {
	m_argin: -1px;
	height:100%;
	background: #D4E4FF;
	
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	border-radius: 4px;
}
.ui-widget-header a { color: #222222/*{fcHeader}*/; }

.ui-progressbar .ui-progressbar-label{
	position: absolute;
	margin-top: 7px;
	border:0px solid red;
	width: 100%;
	text-align: center;
}