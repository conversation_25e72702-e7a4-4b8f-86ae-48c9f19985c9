<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <collaboration id="Collaboration">
    <participant id="pool1" name="工单流程泳道图" processRef="worderProcess"></participant>
  </collaboration>
  <process id="worderProcess" name="worderProcess" isExecutable="true">
    <documentation>工单流程</documentation>
    <laneSet id="laneSet_worderProcess">
      <lane id="lane1" name="客服人员">
        <flowNodeRef>startevent1</flowNodeRef>
        <flowNodeRef>exclusivegateway4</flowNodeRef>
        <flowNodeRef>exclusivegateway9</flowNodeRef>
        <flowNodeRef>endevent1</flowNodeRef>
        <flowNodeRef>exclusivegateway10</flowNodeRef>
        <flowNodeRef>usertask7</flowNodeRef>
        <flowNodeRef>usertask8</flowNodeRef>
        <flowNodeRef>usertask14</flowNodeRef>
        <flowNodeRef>usertask15</flowNodeRef>
      </lane>
      <lane id="lane2" name="项目经理">
        <flowNodeRef>usertask1</flowNodeRef>
        <flowNodeRef>exclusivegateway2</flowNodeRef>
        <flowNodeRef>exclusivegateway8</flowNodeRef>
        <flowNodeRef>exclusivegateway3</flowNodeRef>
        <flowNodeRef>usertask4</flowNodeRef>
        <flowNodeRef>usertask6</flowNodeRef>
        <flowNodeRef>usertask9</flowNodeRef>
        <flowNodeRef>usertask12</flowNodeRef>
        <flowNodeRef>exclusivegateway12</flowNodeRef>
      </lane>
      <lane id="lane3" name="网点">
        <flowNodeRef>usertask16</flowNodeRef>
      </lane>
      <lane id="lane5" name="服务兵">
        <flowNodeRef>usertask2</flowNodeRef>
        <flowNodeRef>usertask5</flowNodeRef>
        <flowNodeRef>usertask3</flowNodeRef>
        <flowNodeRef>usertask10</flowNodeRef>
        <flowNodeRef>usertask13</flowNodeRef>
        <flowNodeRef>usertask11</flowNodeRef>
      </lane>
    </laneSet>
    <startEvent id="startevent1" name="Start"></startEvent>
    <sequenceFlow id="flow1" sourceRef="startevent1" targetRef="usertask1"></sequenceFlow>
    <userTask id="usertask1" name="项目经理安排勘测" activiti:assignee="${userId}"></userTask>
    <userTask id="usertask4" name="初审勘测报告是否合格" activiti:assignee="${userId}"></userTask>
    <exclusiveGateway id="exclusivegateway2" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow9" sourceRef="usertask4" targetRef="exclusivegateway2"></sequenceFlow>
    <sequenceFlow id="flow10" name="不合格" sourceRef="exclusivegateway2" targetRef="usertask5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isAccord==0}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask6" name="是否再次勘测" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow12" name="合格" sourceRef="exclusivegateway2" targetRef="usertask6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isAccord==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow13" sourceRef="usertask6" targetRef="exclusivegateway3"></sequenceFlow>
    <userTask id="usertask9" name="与客户和服务兵预约充电桩安装时间" activiti:assignee="${userId}"></userTask>
    <userTask id="usertask12" name="初审安装资料是否合格" activiti:assignee="${userId}"></userTask>
    <exclusiveGateway id="exclusivegateway8" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow39" sourceRef="usertask12" targetRef="exclusivegateway8"></sequenceFlow>
    <sequenceFlow id="flow40" name="不合格" sourceRef="exclusivegateway8" targetRef="usertask13">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isQualified==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow42" name="合格" sourceRef="exclusivegateway8" targetRef="usertask14">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isQualified==1}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask14" name="审核安装资料并上传资料到主机厂" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow43" sourceRef="usertask14" targetRef="exclusivegateway9"></sequenceFlow>
    <exclusiveGateway id="exclusivegateway3" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow14" name="是" sourceRef="exclusivegateway3" targetRef="usertask2">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isSurveyAgain==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow49" name="否" sourceRef="exclusivegateway3" targetRef="usertask7">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isSurveyAgain==0}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask7" name="审核勘测资料并上传资料到主机厂" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow19" sourceRef="usertask7" targetRef="exclusivegateway10"></sequenceFlow>
    <userTask id="usertask8" name="充电桩是否到场" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow20" sourceRef="usertask8" targetRef="exclusivegateway4"></sequenceFlow>
    <userTask id="usertask15" name="用户评星" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow46" sourceRef="usertask15" targetRef="endevent1"></sequenceFlow>
    <exclusiveGateway id="exclusivegateway4" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow21" name="否" sourceRef="exclusivegateway4" targetRef="usertask8">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isBePresent==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow57" name="是" sourceRef="exclusivegateway4" targetRef="usertask9">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isBePresent==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow58" name="分配到服务兵" sourceRef="exclusivegateway12" targetRef="usertask2">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${toWho==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow59" sourceRef="usertask9" targetRef="usertask10"></sequenceFlow>
    <exclusiveGateway id="exclusivegateway9" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow44" name="不合格" sourceRef="exclusivegateway9" targetRef="usertask12">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isQualified==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow45" name="合格" sourceRef="exclusivegateway9" targetRef="usertask15">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isQualified==1}]]></conditionExpression>
    </sequenceFlow>
    <endEvent id="endevent1" name="End"></endEvent>
    <exclusiveGateway id="exclusivegateway10" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow60" name="合格" sourceRef="exclusivegateway10" targetRef="usertask8">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isQualified==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow61" name="不合格" sourceRef="exclusivegateway10" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isQualified==0}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="exclusivegateway12" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow63" sourceRef="usertask1" targetRef="exclusivegateway12"></sequenceFlow>
    <sequenceFlow id="flow64" name="分配到网点" sourceRef="exclusivegateway12" targetRef="usertask16">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${toWho==1}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask2" name="现场勘测" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow7" name="勘测完成" sourceRef="usertask2" targetRef="usertask3"></sequenceFlow>
    <userTask id="usertask5" name="修改勘测报告" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow11" sourceRef="usertask5" targetRef="usertask4"></sequenceFlow>
    <userTask id="usertask3" name="提交勘测报告" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow8" sourceRef="usertask3" targetRef="usertask4"></sequenceFlow>
    <userTask id="usertask10" name="安装充电桩" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow37" name="安装完成" sourceRef="usertask10" targetRef="usertask11"></sequenceFlow>
    <userTask id="usertask13" name="修改资料" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow41" sourceRef="usertask13" targetRef="usertask12"></sequenceFlow>
    <userTask id="usertask11" name="提交安装完成资料" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow38" sourceRef="usertask11" targetRef="usertask12"></sequenceFlow>
    <userTask id="usertask16" name="分配任务到服务兵" activiti:assignee="${userId}"></userTask>
    <sequenceFlow id="flow65" sourceRef="usertask16" targetRef="usertask2"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_Collaboration">
    <bpmndi:BPMNPlane bpmnElement="Collaboration" id="BPMNPlane_Collaboration">
      <bpmndi:BPMNShape bpmnElement="pool1" id="BPMNShape_pool1">
        <omgdc:Bounds height="866.0" width="1461.0" x="70.0" y="405.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="lane1" id="BPMNShape_lane1">
        <omgdc:Bounds height="171.0" width="1441.0" x="90.0" y="405.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="lane2" id="BPMNShape_lane2">
        <omgdc:Bounds height="330.0" width="1441.0" x="90.0" y="576.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="lane3" id="BPMNShape_lane3">
        <omgdc:Bounds height="110.0" width="1441.0" x="90.0" y="906.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="lane5" id="BPMNShape_lane5">
        <omgdc:Bounds height="255.0" width="1441.0" x="90.0" y="1016.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startevent1" id="BPMNShape_startevent1">
        <omgdc:Bounds height="35.0" width="41.0" x="150.0" y="490.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask1" id="BPMNShape_usertask1">
        <omgdc:Bounds height="55.0" width="105.0" x="120.0" y="680.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask4" id="BPMNShape_usertask4">
        <omgdc:Bounds height="73.0" width="105.0" x="590.0" y="799.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway2" id="BPMNShape_exclusivegateway2">
        <omgdc:Bounds height="40.0" width="40.0" x="452.0" y="815.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask6" id="BPMNShape_usertask6">
        <omgdc:Bounds height="55.0" width="105.0" x="420.0" y="708.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask9" id="BPMNShape_usertask9">
        <omgdc:Bounds height="64.0" width="105.0" x="750.0" y="692.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask12" id="BPMNShape_usertask12">
        <omgdc:Bounds height="55.0" width="141.0" x="1080.0" y="792.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway8" id="BPMNShape_exclusivegateway8">
        <omgdc:Bounds height="40.0" width="40.0" x="960.0" y="799.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask14" id="BPMNShape_usertask14">
        <omgdc:Bounds height="81.0" width="121.0" x="920.0" y="467.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway3" id="BPMNShape_exclusivegateway3">
        <omgdc:Bounds height="40.0" width="40.0" x="300.0" y="715.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask7" id="BPMNShape_usertask7">
        <omgdc:Bounds height="81.0" width="115.0" x="260.0" y="467.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask8" id="BPMNShape_usertask8">
        <omgdc:Bounds height="55.0" width="105.0" x="610.0" y="480.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask15" id="BPMNShape_usertask15">
        <omgdc:Bounds height="55.0" width="105.0" x="1260.0" y="480.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway4" id="BPMNShape_exclusivegateway4">
        <omgdc:Bounds height="40.0" width="40.0" x="780.0" y="487.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway9" id="BPMNShape_exclusivegateway9">
        <omgdc:Bounds height="40.0" width="40.0" x="1130.0" y="487.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endevent1" id="BPMNShape_endevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="1430.0" y="490.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway10" id="BPMNShape_exclusivegateway10">
        <omgdc:Bounds height="40.0" width="40.0" x="470.0" y="487.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway12" id="BPMNShape_exclusivegateway12">
        <omgdc:Bounds height="40.0" width="40.0" x="152.0" y="806.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask2" id="BPMNShape_usertask2">
        <omgdc:Bounds height="55.0" width="105.0" x="270.0" y="1160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask5" id="BPMNShape_usertask5">
        <omgdc:Bounds height="55.0" width="105.0" x="420.0" y="1079.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask3" id="BPMNShape_usertask3">
        <omgdc:Bounds height="55.0" width="105.0" x="590.0" y="1160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask10" id="BPMNShape_usertask10">
        <omgdc:Bounds height="55.0" width="105.0" x="750.0" y="1160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask13" id="BPMNShape_usertask13">
        <omgdc:Bounds height="55.0" width="105.0" x="930.0" y="1079.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask11" id="BPMNShape_usertask11">
        <omgdc:Bounds height="55.0" width="105.0" x="1100.0" y="1160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask16" id="BPMNShape_usertask16">
        <omgdc:Bounds height="55.0" width="105.0" x="190.0" y="930.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
        <omgdi:waypoint x="170.0" y="525.0"></omgdi:waypoint>
        <omgdi:waypoint x="172.0" y="680.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow9" id="BPMNEdge_flow9">
        <omgdi:waypoint x="590.0" y="835.0"></omgdi:waypoint>
        <omgdi:waypoint x="492.0" y="835.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow10" id="BPMNEdge_flow10">
        <omgdi:waypoint x="472.0" y="855.0"></omgdi:waypoint>
        <omgdi:waypoint x="472.0" y="1079.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="36.0" x="429.0" y="880.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow12" id="BPMNEdge_flow12">
        <omgdi:waypoint x="472.0" y="815.0"></omgdi:waypoint>
        <omgdi:waypoint x="472.0" y="763.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="437.0" y="752.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow13" id="BPMNEdge_flow13">
        <omgdi:waypoint x="420.0" y="735.0"></omgdi:waypoint>
        <omgdi:waypoint x="340.0" y="735.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow39" id="BPMNEdge_flow39">
        <omgdi:waypoint x="1080.0" y="819.0"></omgdi:waypoint>
        <omgdi:waypoint x="1000.0" y="819.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow40" id="BPMNEdge_flow40">
        <omgdi:waypoint x="980.0" y="839.0"></omgdi:waypoint>
        <omgdi:waypoint x="982.0" y="1079.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="36.0" x="939.0" y="871.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow42" id="BPMNEdge_flow42">
        <omgdi:waypoint x="980.0" y="799.0"></omgdi:waypoint>
        <omgdi:waypoint x="980.0" y="548.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="943.0" y="630.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow43" id="BPMNEdge_flow43">
        <omgdi:waypoint x="1041.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="1130.0" y="507.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow14" id="BPMNEdge_flow14">
        <omgdi:waypoint x="320.0" y="755.0"></omgdi:waypoint>
        <omgdi:waypoint x="322.0" y="1160.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="12.0" x="299.0" y="846.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow49" id="BPMNEdge_flow49">
        <omgdi:waypoint x="320.0" y="715.0"></omgdi:waypoint>
        <omgdi:waypoint x="317.0" y="548.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="12.0" x="295.0" y="600.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow19" id="BPMNEdge_flow19">
        <omgdi:waypoint x="375.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="470.0" y="507.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow20" id="BPMNEdge_flow20">
        <omgdi:waypoint x="715.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="780.0" y="507.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow46" id="BPMNEdge_flow46">
        <omgdi:waypoint x="1365.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="1430.0" y="507.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow21" id="BPMNEdge_flow21">
        <omgdi:waypoint x="800.0" y="487.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.0" y="442.0"></omgdi:waypoint>
        <omgdi:waypoint x="660.0" y="442.0"></omgdi:waypoint>
        <omgdi:waypoint x="662.0" y="480.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="12.0" x="715.0" y="380.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow57" id="BPMNEdge_flow57">
        <omgdi:waypoint x="800.0" y="527.0"></omgdi:waypoint>
        <omgdi:waypoint x="802.0" y="692.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="12.0" x="806.0" y="547.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow58" id="BPMNEdge_flow58">
        <omgdi:waypoint x="172.0" y="846.0"></omgdi:waypoint>
        <omgdi:waypoint x="171.0" y="1187.0"></omgdi:waypoint>
        <omgdi:waypoint x="270.0" y="1187.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="100.0" x="92.0" y="880.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow59" id="BPMNEdge_flow59">
        <omgdi:waypoint x="802.0" y="756.0"></omgdi:waypoint>
        <omgdi:waypoint x="802.0" y="1052.0"></omgdi:waypoint>
        <omgdi:waypoint x="802.0" y="1160.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow44" id="BPMNEdge_flow44">
        <omgdi:waypoint x="1150.0" y="527.0"></omgdi:waypoint>
        <omgdi:waypoint x="1150.0" y="792.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="36.0" x="1110.0" y="554.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow45" id="BPMNEdge_flow45">
        <omgdi:waypoint x="1170.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="1260.0" y="507.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="1198.0" y="470.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow60" id="BPMNEdge_flow60">
        <omgdi:waypoint x="510.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="610.0" y="507.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="527.0" y="467.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow61" id="BPMNEdge_flow61">
        <omgdi:waypoint x="490.0" y="527.0"></omgdi:waypoint>
        <omgdi:waypoint x="642.0" y="799.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="36.0" x="549.0" y="620.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow63" id="BPMNEdge_flow63">
        <omgdi:waypoint x="172.0" y="735.0"></omgdi:waypoint>
        <omgdi:waypoint x="172.0" y="806.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow64" id="BPMNEdge_flow64">
        <omgdi:waypoint x="172.0" y="846.0"></omgdi:waypoint>
        <omgdi:waypoint x="242.0" y="930.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="100.0" x="188.0" y="860.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow7" id="BPMNEdge_flow7">
        <omgdi:waypoint x="375.0" y="1187.0"></omgdi:waypoint>
        <omgdi:waypoint x="590.0" y="1187.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="48.0" x="491.0" y="1142.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow11" id="BPMNEdge_flow11">
        <omgdi:waypoint x="472.0" y="1079.0"></omgdi:waypoint>
        <omgdi:waypoint x="642.0" y="872.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow8" id="BPMNEdge_flow8">
        <omgdi:waypoint x="642.0" y="1160.0"></omgdi:waypoint>
        <omgdi:waypoint x="642.0" y="872.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow37" id="BPMNEdge_flow37">
        <omgdi:waypoint x="855.0" y="1187.0"></omgdi:waypoint>
        <omgdi:waypoint x="1100.0" y="1187.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="48.0" x="971.0" y="1142.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow41" id="BPMNEdge_flow41">
        <omgdi:waypoint x="982.0" y="1079.0"></omgdi:waypoint>
        <omgdi:waypoint x="1150.0" y="847.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow38" id="BPMNEdge_flow38">
        <omgdi:waypoint x="1152.0" y="1160.0"></omgdi:waypoint>
        <omgdi:waypoint x="1150.0" y="847.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow65" id="BPMNEdge_flow65">
        <omgdi:waypoint x="242.0" y="985.0"></omgdi:waypoint>
        <omgdi:waypoint x="322.0" y="1160.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>