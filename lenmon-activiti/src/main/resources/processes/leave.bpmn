<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="com.platform.modules.oa.leave" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:yaoqiang="http://bpmn.sourceforge.net" exporter="Yaoqiang BPMN Editor" exporterVersion="5.3" expressionLanguage="http://www.w3.org/1999/XPath" id="m1552638556870" name="" targetNamespace="com.platform.modules.oa.leave" typeLanguage="http://www.w3.org/2001/XMLSchema" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://bpmn.sourceforge.net/schemas/BPMN20.xsd">
  <process id="leave" isClosed="false" isExecutable="true" name="请假流程" processType="None">
    <documentation id="leave_D_1" textFormat="text/plain"><![CDATA[请假流程演示]]></documentation>
    <extensionElements>
      <yaoqiang:description/>
      <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
      <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
    </extensionElements>
    <startEvent activiti:initiator="applyUserId" id="startevent1" isInterrupting="true" name="Start" parallelMultiple="false">
      <outgoing>flow2</outgoing>
      <outputSet/>
    </startEvent>
    <userTask activiti:candidateGroups="dept" activiti:exclusive="true" completionQuantity="1" id="deptLeaderAudit" implementation="##unspecified" isForCompensation="false" name="部门领导审批" startQuantity="1">
      <incoming>flow2</incoming>
      <incoming>flow10</incoming>
      <outgoing>flow3</outgoing>
    </userTask>
    <exclusiveGateway gatewayDirection="Unspecified" id="exclusivegateway5" name="Exclusive Gateway">
      <incoming>flow3</incoming>
      <outgoing>flow4</outgoing>
      <outgoing>flow5</outgoing>
    </exclusiveGateway>
    <userTask activiti:assignee="${applyUserId}" activiti:exclusive="true" completionQuantity="1" id="modifyApply" implementation="##unspecified" isForCompensation="false" name="调整申请" startQuantity="1">
      <extensionElements>
        <activiti:taskListener delegateExpression="${leaveModifyProcessor}" event="complete"/>
      </extensionElements>
      <incoming>flow4</incoming>
      <incoming>flow9</incoming>
      <outgoing>flow11</outgoing>
    </userTask>
    <userTask activiti:candidateGroups="hr" activiti:exclusive="true" completionQuantity="1" id="hrAudit" implementation="##unspecified" isForCompensation="false" name="人事审批" startQuantity="1">
      <incoming>flow5</incoming>
      <outgoing>flow6</outgoing>
    </userTask>
    <exclusiveGateway gatewayDirection="Unspecified" id="exclusivegateway6" name="Exclusive Gateway">
      <incoming>flow6</incoming>
      <outgoing>flow7</outgoing>
      <outgoing>flow9</outgoing>
    </exclusiveGateway>
    <userTask activiti:assignee="${applyUserId}" activiti:exclusive="true" completionQuantity="1" id="reportBack" implementation="##unspecified" isForCompensation="false" name="销假" startQuantity="1">
      <extensionElements>
        <activiti:taskListener delegateExpression="${leaveReportProcessor}" event="complete"/>
      </extensionElements>
      <incoming>flow7</incoming>
      <outgoing>flow8</outgoing>
    </userTask>
    <endEvent id="endevent1" name="End">
      <incoming>flow8</incoming>
      <incoming>flow12</incoming>
      <inputSet/>
    </endEvent>
    <exclusiveGateway gatewayDirection="Unspecified" id="exclusivegateway7" name="Exclusive Gateway">
      <incoming>flow11</incoming>
      <outgoing>flow10</outgoing>
      <outgoing>flow12</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="flow2" sourceRef="startevent1" targetRef="deptLeaderAudit"/>
    <sequenceFlow id="flow3" sourceRef="deptLeaderAudit" targetRef="exclusivegateway5"/>
    <sequenceFlow id="flow4" name="不同意" sourceRef="exclusivegateway5" targetRef="modifyApply">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${!deptLeaderPass}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow5" name="同意" sourceRef="exclusivegateway5" targetRef="hrAudit">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deptLeaderPass}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow6" sourceRef="hrAudit" targetRef="exclusivegateway6"/>
    <sequenceFlow id="flow7" name="同意" sourceRef="exclusivegateway6" targetRef="reportBack">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${hrPass}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow8" sourceRef="reportBack" targetRef="endevent1"/>
    <sequenceFlow id="flow9" name="不同意" sourceRef="exclusivegateway6" targetRef="modifyApply">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${!hrPass}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow10" name="重新申请" sourceRef="exclusivegateway7" targetRef="deptLeaderAudit">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${reApply}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow11" sourceRef="modifyApply" targetRef="exclusivegateway7"/>
    <sequenceFlow id="flow12" name="结束流程" sourceRef="exclusivegateway7" targetRef="endevent1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${!reApply}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="Yaoqiang_Diagram-leave" name="New Diagram" resolution="96.0">
    <bpmndi:BPMNPlane bpmnElement="leave">
      <bpmndi:BPMNShape bpmnElement="startevent1" id="Yaoqiang-startevent1">
        <omgdc:Bounds height="32.0" width="32.0" x="10.0" y="95.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="29.0" x="11.5" y="135.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="deptLeaderAudit" id="Yaoqiang-deptLeaderAudit">
        <omgdc:Bounds height="55.0" width="105.0" x="90.0" y="80.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="72.0" x="106.5" y="99.58"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway5" id="Yaoqiang-exclusivegateway5" isMarkerVisible="false">
        <omgdc:Bounds height="42.0" width="42.0" x="250.0" y="87.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="100.0" x="221.0" y="131.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="modifyApply" id="Yaoqiang-modifyApply">
        <omgdc:Bounds height="55.0" width="105.0" x="218.0" y="190.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="50.0" x="245.5" y="209.58"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="hrAudit" id="Yaoqiang-hrAudit">
        <omgdc:Bounds height="55.0" width="105.0" x="360.0" y="75.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="50.0" x="387.5" y="94.58"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway6" id="Yaoqiang-exclusivegateway6" isMarkerVisible="false">
        <omgdc:Bounds height="42.0" width="42.0" x="505.0" y="80.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="100.0" x="476.0" y="124.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="reportBack" id="Yaoqiang-reportBack">
        <omgdc:Bounds height="55.0" width="105.0" x="590.0" y="70.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="28.0" x="628.5" y="89.58"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endevent1" id="Yaoqiang-endevent1">
        <omgdc:Bounds height="32.0" width="32.0" x="625.0" y="285.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="24.0" x="629.0" y="325.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway7" id="Yaoqiang-exclusivegateway7" isMarkerVisible="false">
        <omgdc:Bounds height="42.0" width="42.0" x="250.0" y="285.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="100.0" x="221.0" y="329.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow12" id="Yaoqiang-flow12">
        <omgdi:waypoint x="292.5" y="306.0"/>
        <omgdi:waypoint x="625.5" y="301.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="60.0" x="428.5" y="285.58"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow11" id="Yaoqiang-flow11">
        <omgdi:waypoint x="271.0" y="245.0"/>
        <omgdi:waypoint x="271.0" y="285.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="6.0" x="268.0" y="255.33"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow10" id="Yaoqiang-flow10">
        <omgdi:waypoint x="257.5" y="299.0"/>
        <omgdi:waypoint x="142.0" y="299.0"/>
        <omgdi:waypoint x="142.0" y="135.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="58.0" x="113.0" y="257.33"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow9" id="Yaoqiang-flow9">
        <omgdi:waypoint x="521.0" y="116.5"/>
        <omgdi:waypoint x="521.0" y="217.0"/>
        <omgdi:waypoint x="323.5" y="217.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="56.0" x="444.5" y="199.58"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow8" id="Yaoqiang-flow8">
        <omgdi:waypoint x="641.0" y="125.0"/>
        <omgdi:waypoint x="641.0" y="285.0078144082805"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="6.0" x="638.0" y="195.09"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow7" id="Yaoqiang-flow7">
        <omgdi:waypoint x="541.5" y="95.0"/>
        <omgdi:waypoint x="568.0" y="95.0"/>
        <omgdi:waypoint x="590.5" y="95.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="54.0" x="539.0" y="77.58"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow6" id="Yaoqiang-flow6">
        <omgdi:waypoint x="465.5" y="100.0"/>
        <omgdi:waypoint x="475.0" y="100.0"/>
        <omgdi:waypoint x="506.5" y="100.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="6.0" x="483.0" y="90.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow5" id="Yaoqiang-flow5">
        <omgdi:waypoint x="289.5" y="105.0"/>
        <omgdi:waypoint x="322.0" y="105.0"/>
        <omgdi:waypoint x="360.5" y="105.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="99.0" x="275.5" y="87.58"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow4" id="Yaoqiang-flow4">
        <omgdi:waypoint x="271.0" y="128.5"/>
        <omgdi:waypoint x="271.0" y="190.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="101.0" x="220.5" y="141.83"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow3" id="Yaoqiang-flow3">
        <omgdi:waypoint x="195.5" y="105.0"/>
        <omgdi:waypoint x="225.0" y="105.0"/>
        <omgdi:waypoint x="253.5" y="105.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="6.0" x="221.5" y="95.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow2" id="Yaoqiang-flow2">
        <omgdi:waypoint x="42.46871942267131" y="110.0"/>
        <omgdi:waypoint x="73.0" y="110.0"/>
        <omgdi:waypoint x="90.5" y="110.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="6.0" x="63.48" y="100.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
