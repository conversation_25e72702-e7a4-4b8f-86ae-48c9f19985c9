<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.youngking</groupId>
	<artifactId>lenmon-parent</artifactId>
	<packaging>pom</packaging>
	<version>1.0-SNAPSHOT</version>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.1.3.RELEASE</version>
	</parent>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>21</java.version>
		<mybatisplus.version>3.1.2</mybatisplus.version>
		<mysql.version>5.1.38</mysql.version>
		<mssql.version>4.0</mssql.version>
		<oracle.version>11.2.0.3</oracle.version>
		<druid.version>1.1.22</druid.version>
		<quartz.version>2.3.0</quartz.version>
		<commons.lang.version>2.6</commons.lang.version>
		<commons.fileupload.version>1.2.2</commons.fileupload.version>
		<commons.io.version>2.5</commons.io.version>

		<commons.configuration.version>1.10</commons.configuration.version>
		<shiro.version>1.4.0</shiro.version>
		<jwt.version>0.7.0</jwt.version>
		<kaptcha.version>0.0.9</kaptcha.version>

		<aliyun.oss.version>2.8.3</aliyun.oss.version>
		<qcloud.cos.version>4.4</qcloud.cos.version>
		<swagger.version>2.7.0</swagger.version>

		<fastjson.version>1.2.83</fastjson.version>
		<hutool.version>4.1.1</hutool.version>
		<lombok.version>1.18.4</lombok.version>

		<!--wagon plugin 配置 -->
		<service-path>/work/renrenwithactiviti</service-path>
		<pack-name>${project.artifactId}-${project.version}.jar</pack-name>
		<remote-addr>192.168.1.10:22</remote-addr>
		<remote-username>root</remote-username>
		<remote-passwd>123456</remote-passwd>

	</properties>

	<modules>
		<module>lenmon-core</module>
		<module>lenmon-activiti</module>
		<module>lenmon-sys</module>
		<module>lenmon-app</module>
		<module>rrs-worder</module>
		<module>lenmon-admin</module>
		 <module>storage-admin</module>
		 <module>storage-common</module>
		<module>common-pay</module>
		 <module>storage-core</module>
	</modules>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.2</version>
		</dependency>
		<!--<dependency> -->
		<!--<groupId>org.springframework.boot</groupId> -->
		<!--<artifactId>spring-boot-devtools</artifactId> -->
		<!--<optional>true</optional> -->
		<!--</dependency> -->
		<!--<dependency> <groupId>com.baomidou</groupId> <artifactId>mybatis-plus-boot-starter</artifactId>
			<version>${mybatisplus.version}</version> <exclusions> <exclusion> <groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-generator</artifactId> </exclusion> <exclusion>
			<groupId>org.mybatis</groupId> <artifactId>mybatis</artifactId> </exclusion>
			</exclusions> </dependency> -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>3.0.7.1</version>
		</dependency>
		<!--<dependency> <groupId>org.mybatis</groupId> <artifactId>mybatis</artifactId>
			<version>3.5.1</version> </dependency> -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql.version}</version>
		</dependency>
		<!--oracle驱动 -->
		<!--<dependency> <groupId>com.oracle</groupId> <artifactId>ojdbc6</artifactId>
			<version>${oracle.version}</version> </dependency> -->
		<!--mssql驱动 -->
		<!--<dependency> <groupId>com.microsoft.sqlserver</groupId> <artifactId>sqljdbc4</artifactId>
			<version>${mssql.version}</version> </dependency> -->
		<!--postgresql驱动 -->
		<!-- <dependency> <groupId>org.postgresql</groupId> <artifactId>postgresql</artifactId>
			</dependency> -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>${druid.version}</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>${quartz.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.mchange</groupId>
					<artifactId>c3p0</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>${commons.lang.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>${commons.fileupload.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons.io.version}</version>
		</dependency>

		<dependency>
			<groupId>commons-configuration</groupId>
			<artifactId>commons-configuration</artifactId>
			<version>${commons.configuration.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-core</artifactId>
			<version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
			<version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>${jwt.version}</version>
		</dependency>
		<dependency>
			<groupId>com.github.axet</groupId>
			<artifactId>kaptcha</artifactId>
			<version>${kaptcha.version}</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${swagger.version}</version>
			<exclusions>

			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>${swagger.version}</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>${aliyun.oss.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-codec</groupId>
					<artifactId>commons-codec</artifactId>
				</exclusion>

			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.qcloud</groupId>
			<artifactId>cos_api</artifactId>
			<version>${qcloud.cos.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>commons-codec</groupId>
					<artifactId>commons-codec</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool.version}</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>0.4.8</version>
		</dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.youngking</groupId>
				<artifactId>lenmon-activiti</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.youngking</groupId>
				<artifactId>lenmon-sys</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.youngking</groupId>
				<artifactId>lenmon-core</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.youngking</groupId>
				<artifactId>lenmon-admin</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.youngking</groupId>
				<artifactId>lenmon-app</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.youngking</groupId>
				<artifactId>rrs-worder</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.11.0</version>
					<configuration>
						<source>${java.version}</source>
						<target>${java.version}</target>
						<compilerArgs>
							<arg>--add-opens</arg>
							<arg>jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>
							<arg>--add-opens</arg>
							<arg>jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>
							<arg>--add-opens</arg>
							<arg>jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED</arg>
							<arg>--add-opens</arg>
							<arg>jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED</arg>
						</compilerArgs>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
					<version>2.1.3.RELEASE</version>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<repositories>
		<repository>
			<id>nexus</id>
			<name>Nexus aliyun</name>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>

</project>
