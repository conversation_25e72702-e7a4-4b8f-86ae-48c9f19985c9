package com.common.pay.zxing.utils;

import com.common.pay.common.utils.MD5Util;
import com.common.pay.common.utils.StringUtils;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 生成不进行存取的方式的二维码
 * <AUTHOR>
 * @description
 */
public class GenerateQrCodeUtil {
    private static final int WHITE = 0xFFFFFFFF;
    private static final int BLACK = 0xFF000000;
    /**
     * 静态生成二维码 存储在磁盘上
     * @param content  //二维码信息
     * @param contextPath //上下文相对路径
     * @param realPath    //磁盘真实路径
     * @return
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public static String generateQrcode(String content,String contextPath,String realPath){
        if(content==null || realPath==null){
            return null;
        }
        String fileName = generateFileName(content)+".png";
        /** 图片在项目中存储的相对路径 */
        String url = "/"+ contextPath + "/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "/" + fileName;
        String filePath = url;
        /** 如果是部署在服务器上的情况，则需要到webapps/下面的upload目录 */
        if (StringUtils.isNotBlank(contextPath) || realPath.endsWith("root")) {
            filePath = ".." + url;
        }
        MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
        Map hints = new HashMap();
        /** 设置字符集编码类型 */
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        BitMatrix bitMatrix = null;
        try {
            bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300,hints);
            /** 创建存储图片的文件 */
            File file1 = new File(realPath,filePath);
            try {
                /** 存储二维码图片 */
                GenerateQrCodeUtil.writeToFile(bitMatrix, "png", file1);
                return filePath;
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (WriterException e1) {
            e1.printStackTrace();
        }
        return null;
    }
    private static void writeToFile(BitMatrix matrix, String format, File file) throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        if (!ImageIO.write(image, format, file)) {
            throw new IOException("Could not write an image of format " + format + " to " + file);
        }
    }
    public static BufferedImage toBufferedImage(BitMatrix matrix) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
            }
        }
        return image;
    }
    /**
     * md5加密
     * */
    private static String generateFileName(String content) {
        return MD5Util.MD5Encode(content,"UTF-8");
    }

    /**
     * 生成二维码图片 不存储 直接以流的形式输出到页面
     * @param content
     * @param response
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public static void encodeQrcode(String content, HttpServletResponse response){
        if(StringUtils.isBlank(content)){
            return;
        }
        MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
        Map hints = new HashMap();
        /** 设置字符集编码类型 */
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        BitMatrix bitMatrix = null;
        try {
            bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300,hints);
            BufferedImage image = toBufferedImage(bitMatrix);
            /** 输出二维码图片流 */
            try {
                ImageIO.write(image, "png", response.getOutputStream());
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (WriterException e1) {
            e1.printStackTrace();
        }
    }
    public static String encodeQrcodeBase64(String content){
        if(StringUtils.isBlank(content)){
            throw new RuntimeException("内容不能为空");
        }
        MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Map hints = new HashMap();
        /** 设置字符集编码类型 */
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        BitMatrix bitMatrix = null;
        try {
            bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300,hints);
            BufferedImage image = toBufferedImage(bitMatrix);
            /** 输出二维码图片流 */
            try {
                ImageIO.write(image, "png",baos);
                /** 转换成字节 */
                byte[] bytes = baos.toByteArray();
                BASE64Encoder encoder = new BASE64Encoder();
                /** 转换成base64串 */
                String pngBase64 = encoder.encodeBuffer(bytes).trim();
                pngBase64 = pngBase64.replaceAll("\n", "").replaceAll("\r", "");
                StringBuffer sb = new StringBuffer("data:image/jpg;base64,");
                 return  sb.append(pngBase64).toString();
            } catch (IOException e) {
                throw new RuntimeException("生成二维码失败");
            }
        } catch (WriterException e1) {
            throw new RuntimeException("生成二维码失败");
        }
    }
}
