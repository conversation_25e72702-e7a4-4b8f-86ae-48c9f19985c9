package com.common.pay.zxing.config;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 *  zxing的默认配置
 * <AUTHOR> 2020年3月26日
 * @description
 */
public class ZxingConifgs {
    private static Logger logger = LoggerFactory.getLogger(ZxingConifgs.class);

    private static Configuration config = null;
    /** 二维码内容 */
    private static String imgVisiPath;
    /** 二维码图片宽度 */
    private static int with;
    /** 二维码图片高度 */
    private static int height;
    /** 二维码图片保存路径 */
    private static String imgSavePath;
    /** 是否开启过期时间 */
    private static boolean timeOutStatus;
    /** 过期时间时长 */
    private static long timeOutLen;


    private  ZxingConifgs(){}

    public synchronized static void init(String filePath){
        if(config != null){
            return ;
        }
        try {
            config = new PropertiesConfiguration(filePath);
        } catch (ConfigurationException e) {
            e.printStackTrace();
        }
        String sysType =  System.getProperty("os.name");
        imgVisiPath = config.getString("imgVisiPath");
        if(sysType.toLowerCase().contains("win")){
            imgSavePath = config.getString("windows.imgSavePath");
        }else if(sysType.toLowerCase().contains("linux")){
            imgSavePath = config.getString("linux.imgSavePath");
        }else{
            logger.error("不支持当前系统:"+sysType);
            throw new RuntimeException("当前的系统不支持:"+sysType);
        }
        with = config.getInt("with");
        height = config.getInt("height");
        timeOutStatus = config.getBoolean("timeout.status");
        timeOutLen = config.getLong("timeout.len");
        logger.info("配置文件:"+filePath);
    }

    public static Configuration getConfig() {
        return config;
    }

    public static String getImgVisiPath() {
        return imgVisiPath;
    }

    public static int getHeight() {
        return height;
    }

    public static int getWith() {
        return with;
    }

    public static String getImgSavePath() {
        return imgSavePath;
    }
    public static long getTimeOutLen() {
        return timeOutLen;
    }
    public static boolean getTimeOutStatus(){
        return timeOutStatus;
    }
    public static void setConfig(Configuration config) {
        ZxingConifgs.config = config;
    }


    public static void setImgVisiPath(String imgVisiPath) {
        ZxingConifgs.imgVisiPath = imgVisiPath;
    }

    public static void setHeight(int height) {
        ZxingConifgs.height = height;
    }

    public static void setImgSavePath(String imgSavePath) {
        ZxingConifgs.imgSavePath = imgSavePath;
    }

    public static void setLogger(Logger logger) {
        ZxingConifgs.logger = logger;
    }

    public static void setWith(int with) {
        ZxingConifgs.with = with;
    }

    public static void setTimeOutLen(long timeOutLen) {
        ZxingConifgs.timeOutLen = timeOutLen;
    }

    public static void setTimeOutStatus(boolean timeOutStatus) {
        ZxingConifgs.timeOutStatus = timeOutStatus;
    }
}
