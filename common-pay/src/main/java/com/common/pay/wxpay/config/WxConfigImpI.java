package com.common.pay.wxpay.config;

import com.common.pay.wxpay.util.IWXPayDomain;
import com.common.pay.wxpay.util.IWXPayDomainImpI;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.common.pay.wxpay.config
 * @description:
 * @date 2022/1/26 15:50
 */
public class WxConfigImpI extends WXPayConfig {

    private byte[] certData;
    public WxConfigImpI() throws Exception {
        String certPath = WxConfig.getCertPath();
        File file = new File(certPath);
        InputStream certStream = new FileInputStream(file);
        this.certData = new byte[(int) file.length()];
        certStream.read(this.certData);
        certStream.close();
    }
    @Override
    public String getAppID() {
        return WxConfig.getAppId();
    }

    @Override
    public String getMchID() {
        return WxConfig.getMchId();
    }

    @Override
    public String getKey() {
        return WxConfig.getApiKey();
    }

    @Override
    public InputStream getCertStream() {
        ByteArrayInputStream certBis;
        certBis = new ByteArrayInputStream(this.certData);
        return certBis;
    }

    @Override
    public IWXPayDomain getWXPayDomain() {
        return new IWXPayDomainImpI();
    }
}