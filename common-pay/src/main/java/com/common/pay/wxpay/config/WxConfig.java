package com.common.pay.wxpay.config;

import com.common.pay.common.utils.StringUtils;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * <AUTHOR>
 * @description
 * @see
 */
public class WxConfig {

    private  static Logger LOGGER = LoggerFactory.getLogger(WxConfig.class);

    private static String prefix;
    private static String appId;
    private static String mchId;
    private static String appSecrect;
    private static String apiKey;
    private static String sginType;
    private static String certPath;
    private static String notifyUrl;
    private static String tradeType;

    private static String unifiedOrderUrl;

    private static String orderQueryUrl;

    private static String closeOrder;

    private static String downloadBillUrl;

    private static String downloadBillType;

    private static String orderRefundsUrl;

    private static Configuration configs;

    private static String orderRefundsSelectUrl;




    private WxConfig(){}

    public static void  init(String filePath){
        if(StringUtils.isEmpty(filePath)){
            throw new RuntimeException("初始化数据失败,路径不能为空");
        }
        /**  初始化配置文件  */
        if(configs == null){
            try {
                configs = new PropertiesConfiguration(filePath);
            } catch (ConfigurationException e) {
                e.printStackTrace();
            }
        }
        WxConfig.prefix =  configs.getString("prefix");
        WxConfig.appId = configs.getString(prefix+".APPID");
        WxConfig.mchId = configs.getString(prefix+".MCH_ID");
        WxConfig.appSecrect = configs.getString(prefix+".APP_SECRECT");
        WxConfig.apiKey = configs.getString(prefix+".API_KEY");
        WxConfig.sginType = configs.getString(prefix+".SGIN_TYPE");
        WxConfig.certPath = configs.getString(prefix+".CERT_PATH");
        WxConfig.notifyUrl = configs.getString(prefix+".NOTIFY_URL");
        WxConfig.tradeType = configs.getString(prefix+".TRADE_TYPE");
        WxConfig.unifiedOrderUrl = configs.getString(prefix+".UNIFIED_ORDER_URL");
        WxConfig.orderQueryUrl = configs.getString(prefix+".ORDER_QUERY_URL");
        WxConfig.orderRefundsUrl = configs.getString(prefix+".ORDER_REFUNDS_URL");
        WxConfig.orderRefundsSelectUrl = configs.getString(prefix+".ORDER_REFUNDS_SELECT_URL");
        WxConfig.closeOrder = configs.getString(prefix+".CLOSE_ORDER");
        WxConfig.downloadBillUrl = configs.getString(prefix+".DOWNLOAD_BILL_URL");
        WxConfig.downloadBillType = configs.getString(prefix+".DOWNLOAD_BILL_TYPE");
        LOGGER.info(description());
    }
    public static String description() {
        StringBuilder sb = new StringBuilder("Configs{");
        sb.append("------------------微信支付配置---------------------").append("\n");
        sb.append("APPID= ").append(appId).append("\n");
        sb.append(", mchId= ").append(mchId).append("\n");
        sb.append(", API_KEY= ").append(apiKey).append("\n");
        sb.append(", 证书路径 CERT_PATH= ").append(certPath).append("\n");
        sb.append("}");
        return sb.toString();
    }


    public static void setAppId(String appId) {
        WxConfig.appId = appId;
    }

    public static void setApiKey(String apiKey) {
        WxConfig.apiKey = apiKey;
    }

    public static void setAppSecrect(String appSecrect) {
        WxConfig.appSecrect = appSecrect;
    }

    public static void setMchId(String mchId) {
        WxConfig.mchId = mchId;
    }

    public static void setCertPath(String certPath) {
        WxConfig.certPath = certPath;
    }

    public static void setSginType(String sginType) {
        WxConfig.sginType = sginType;
    }

    public static void setTradeType(String tradeType) {
        WxConfig.tradeType = tradeType;
    }

    public static void setNotifyUrl(String notifyUrl) {
        WxConfig.notifyUrl = notifyUrl;
    }

    public static void setUnifiedOrderUrl(String unifiedOrderUrl) {
        WxConfig.unifiedOrderUrl = unifiedOrderUrl;
    }

    public static void setOrderRefundsUrl(String orderRefundsUrl) {
        WxConfig.orderRefundsUrl = orderRefundsUrl;
    }

    public static void setOrderRefundsSelectUrl(String orderRefundsSelectUrl) {
        WxConfig.orderRefundsSelectUrl = orderRefundsSelectUrl;
    }
    public static void setOrderQueryUrl(String orderQueryUrl) {
        WxConfig.orderQueryUrl = orderQueryUrl;
    }

    public static void setCloseOrder(String closeOrder) {
        WxConfig.closeOrder = closeOrder;
    }

    public static void setDownloadBillType(String downloadBillType) {
        WxConfig.downloadBillType = downloadBillType;
    }

    public static void setDownloadBillUrl(String downloadBillUrl) {
        WxConfig.downloadBillUrl = downloadBillUrl;
    }

    public static void setPrefix(String prefix) {
        WxConfig.prefix = prefix;
    }

    public static String getPrefix() {
        return prefix;
    }

    public static String getAppId() {
        return appId;
    }

    public static String getApiKey() {
        return apiKey;
    }

    public static String getAppSecrect() {
        return appSecrect;
    }

    public static String getMchId() {
        return mchId;
    }

    public static String getCertPath() {
        return certPath;
    }

    public static String getSginType() {
        return sginType;
    }

    public static String getTradeType() {
        return tradeType;
    }

    public static String getNotifyUrl() {
        return notifyUrl;
    }

    public static String getUnifiedOrderUrl() {
        return unifiedOrderUrl;
    }

    public static String getOrderQueryUrl() {
        return orderQueryUrl;
    }

    public static String getOrderRefundsUrl() {
        return orderRefundsUrl;
    }

    public static String getOrderRefundsSelectUrl() {
        return orderRefundsSelectUrl;
    }

    public static String getCloseOrder() {
        return closeOrder;
    }

    public static String getDownloadBillType() {
        return downloadBillType;
    }

    public static String getDownloadBillUrl() {
        return downloadBillUrl;
    }
}
