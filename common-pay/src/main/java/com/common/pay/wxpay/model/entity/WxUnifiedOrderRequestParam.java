package com.common.pay.wxpay.model.entity;

import lombok.Data;

/**
 * 微信 企业号 和 公众号的共同请求字段
 * <AUTHOR> 2020-3-27
 * @description
 */
@Data
public class WxUnifiedOrderRequestParam {
    private String appid;
    /** 商户号 */
    private String mch_id;
    /** 随机字符串 */
    private String nonce_str;
    /** 签名 */
    private String sign;
    /** 商品描述 */
    private String body;
    /** 商户订单号 */
    private String out_trade_no;
    /** 标价金额 */
    private String total_fee;
    /** 终端IP */
    private String spbill_create_ip;
    /** 交易起始时间 */
    private String time_start;
    /** 交易结束时间 */
    private String 	time_expire;
    /** 通知地址 */
    private String notify_url;
    /** 交易类型 */
    private String trade_type;
    /** 商品ID */
    private String product_id;
    /** 商品ID */
    private String api_key;
    /** 签名类型  */
    private String sign_type;
}
