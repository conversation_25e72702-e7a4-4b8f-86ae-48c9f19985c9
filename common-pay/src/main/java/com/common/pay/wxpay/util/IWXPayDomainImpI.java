package com.common.pay.wxpay.util;

import com.common.pay.wxpay.config.WXPayConfig;
import com.common.pay.wxpay.sdk.WXPayConstants;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.common.pay.wxpay.util
 * @description:
 * @date 2022/1/26 17:12
 */
public class IWXPayDomainImpI implements IWXPayDomain{
    @Override
    public void report(String domain, long elapsedTimeMillis, Exception ex) {

    }
    @Override
    public DomainInfo getDomain(WXPayConfig config) {
        DomainInfo domainInfo=new DomainInfo(WXPayConstants.DOMAIN_API,true);

        return domainInfo;
    }

}