package com.common.pay.alipay.service;

import com.alipay.api.response.MonitorHeartbeatSynResponse;
import com.common.pay.alipay.model.builder.AlipayHeartbeatSynRequestBuilder;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>ly on 15/10/22.
 */
public interface AlipayMonitorService {

    // 交易保障接口 https://openhome.alipay.com/platform/document.htm#mobileApp-barcodePay-API-heartBeat

    // 可以提供给系统商/pos厂商使用
    public MonitorHeartbeatSynResponse heartbeatSyn(AlipayHeartbeatSynRequestBuilder builder);
}
