package com.common.pay.alipay.config;

import com.sun.org.apache.xpath.internal.operations.Bool;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * Created by liuyangkly on 15/6/27.
 */
public class Configs {
    private static Log log = LogFactory.getLog(Configs.class);
    private static Configuration configs;
    /**是否启动沙盒模式 */
    private static Boolean enable;
    /** 支付宝openapi域名 */
    private static String openApiDomain;
    /** 支付宝mcloudmonitor域名 */
    private static String mcloudApiDomain;
    /** 商户partner id */
    private static String pid;
    /** 商户应用id */
    private static String appid;
    /** RSA私钥，用于对商户请求报文加签 */
    private static String privateKey;
    /** RSA公钥，仅用于验证开发者网关 */
    private static String publicKey;
    /** 普通证书路径 */
    private static String appCertPath;
    /** 支付宝公钥证书的路径 */
    private static String aliPayPublicCertPath;

    private static String alipayRootCertPath;
    /** 签名类型  */
    private static String signType;
    /**  最大查询次数 */
    private static int maxQueryRetry;
    /** 查询间隔（毫秒） */
    private static long queryDuration;
    /** 最大撤销次数 */
    private static int maxCancelRetry;
    /**  撤销间隔（毫秒） */
    private static long cancelDuration;
    /** 交易保障线程第一次调度延迟（秒） */
    private static long heartbeatDelay ;
    /**  交易保障线程调度间隔（秒） */
    private static long heartbeatDuration ;
    /** 回调地址 */
    private static String notifyUrl;
    /** 字符集 */
    private static String charset;
    /** 数据类型 */
    private static String dataType;

    private static Integer timeExpire;

    private static String billUnzipPath;

    private static String billDownloadPath;
    private static String sysTypeName = "";
    private Configs() {
        // No Constructor
    }

    /** 根据文件名读取配置文件，文件后缀名必须为.properties  */
    public synchronized static void init(String filePath) {


        if (configs != null) {
            return;
        }

        try {
            configs = new PropertiesConfiguration(filePath);
        } catch (ConfigurationException e) {
            e.printStackTrace();
        }

        if (configs == null) {
            throw new IllegalStateException("can`t find file by path:" + filePath);
        }

        String sysType = System.getProperty("os.name").toLowerCase();

        if(sysType.indexOf("win")!=-1){
            sysTypeName = "win";
        }else{
            sysTypeName = "linux";
        }

        String enable = System.getProperty("enable");
        if (StringUtils.isNotEmpty(enable)){
            boolean aBoolean = Boolean.valueOf(enable);
            Configs.setEnable(aBoolean);
        }else{
            Configs.enable = configs.getBoolean("enable");
        }

        String prefix = Configs.enable ?"prod":"test";
        openApiDomain = configs.getString(prefix + ".open_api_domain");
        mcloudApiDomain = configs.getString(prefix +".mcloud_api_domain");

        pid = configs.getString(prefix +".pid");
        appid = configs.getString(prefix +".appid");

        // RSA
        privateKey = configs.getString(prefix +".private_key");
        publicKey = configs.getString(prefix +".public_key");
        appCertPath = configs.getString(prefix +".app_cert_path_"+sysTypeName);
        aliPayPublicCertPath =configs.getString(prefix +".alipay_public_cert_path_"+sysTypeName);
        alipayRootCertPath = configs.getString(prefix +".alipay_root_cert_path_"+sysTypeName);
        signType = configs.getString(prefix +".sign_type");

        // 查询参数
        maxQueryRetry = configs.getInt(prefix +".max_query_retry");
        queryDuration = configs.getLong(prefix +".query_duration");
        maxCancelRetry = configs.getInt(prefix +".max_cancel_retry");
        cancelDuration = configs.getLong(prefix +".cancel_duration");

        // 交易保障调度线程
        heartbeatDelay = configs.getLong(prefix +".heartbeat_delay");
        heartbeatDuration = configs.getLong(prefix +".heartbeat_duration");
        notifyUrl = configs.getString(prefix +".notify_url");
        charset = configs.getString(prefix +".charset");
        dataType = configs.getString(prefix +".data_type");
        timeExpire = configs.getInteger(prefix +".time_expire",30);
        billUnzipPath = configs.getString(prefix + ".bill_unzip_path_"+sysTypeName);
        billDownloadPath = configs.getString(prefix + ".bill_download_path_"+sysTypeName);
        log.info("配置文件名: " + filePath);
        log.info(description());
    }

    public static String description() {
        StringBuilder sb = new StringBuilder("Configs{");
        sb.append("支付宝openapi网关: ").append(openApiDomain).append("\n");
        if (StringUtils.isNotEmpty(mcloudApiDomain)) {
            sb.append(", 支付宝mcloudapi网关域名: ").append(mcloudApiDomain).append("\n");
        }

        if (StringUtils.isNotEmpty(pid)) {
            sb.append(", pid: ").append(pid).append("\n");
        }
        String enableDesc = enable?"----------关闭沙盒模式----------":"----------开启沙盒模式----------";
        sb.append(",").append(enableDesc).append("\n");

        sb.append(", appid: ").append(appid).append("\n");

        sb.append(", 商户RSA私钥: ").append(getKeyDescription(privateKey)).append("\n");
        sb.append(", 商户RSA公钥: ").append(getKeyDescription(publicKey)).append("\n");

        sb.append(", 应用公钥证书路径: ").append(appCertPath).append("\n");
        sb.append(", 当前系统: ").append(sysTypeName).append("\n");
        sb.append(", 支付宝公钥证书路径: ").append(aliPayPublicCertPath).append("\n");
        sb.append(", 支付宝根证书路径: ").append(alipayRootCertPath).append("\n");
        sb.append(", 签名类型: ").append(signType).append("\n");

        sb.append(", 查询重试次数: ").append(maxQueryRetry).append("\n");
        sb.append(", 查询间隔(毫秒): ").append(queryDuration).append("\n");
        sb.append(", 撤销尝试次数: ").append(maxCancelRetry).append("\n");
        sb.append(", 撤销重试间隔(毫秒): ").append(cancelDuration).append("\n");

        sb.append(", 交易保障调度延迟(秒): ").append(heartbeatDelay).append("\n");
        sb.append(", 交易保障调度间隔(秒): ").append(heartbeatDuration).append("\n");
        sb.append(", 交易保障调度间隔(秒): ").append(heartbeatDuration).append("\n");

        sb.append(",字符集类型: ").append(charset).append("\n");
        sb.append(",数据类型: ").append(dataType).append("\n");
        sb.append(",账单下载路径: ").append(billDownloadPath).append("\n");
        sb.append(",账单解压缩路径: ").append(billUnzipPath).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private static String getKeyDescription(String key) {
        int showLength = 6;
        if (StringUtils.isNotEmpty(key) &&
                key.length() > showLength) {
            return new StringBuilder(key.substring(0, showLength))
                    .append("******")
                    .append(key.substring(key.length() - showLength))
                    .toString();
        }
        return null;
    }

    public static Configuration getConfigs() {
        return configs;
    }

    public static Boolean getEnable() {
        return enable;
    }


    public static String getOpenApiDomain() {
        return openApiDomain;
    }

    public static String getMcloudApiDomain() {
        return mcloudApiDomain;
    }

    public static void setMcloudApiDomain(String mcloudApiDomain) {
        Configs.mcloudApiDomain = mcloudApiDomain;
    }

    public static String getPid() {
        return pid;
    }

    public static String getAppid() {
        return appid;
    }

    public static String getPrivateKey() {
        return privateKey;
    }

    public static String getPublicKey() {
        return publicKey;
    }

    public static Integer getTimeExpire() {
        return timeExpire;
    }

    public static void setTimeExpire(Integer timeExpire) {
        Configs.timeExpire = timeExpire;
    }

    public static String getSignType() {
        return signType;
    }

    public static int getMaxQueryRetry() {
        return maxQueryRetry;
    }

    public static long getQueryDuration() {
        return queryDuration;
    }

    public static int getMaxCancelRetry() {
        return maxCancelRetry;
    }

    public static long getCancelDuration() {
        return cancelDuration;
    }

    public static String getNotifyUrl() {
        return notifyUrl;
    }

    public static String getCharset() {
        return charset;
    }

    public static String getDataType() {
        return dataType;
    }

    public static String getAppCertPath() {
        return appCertPath;
    }

    public static void setAppCertPath(String appCertPath) {
        Configs.appCertPath = appCertPath;
    }

    public static String getAliPayPublicCertPath() {
        return aliPayPublicCertPath;
    }

    public static String getBillUnzipPath() {
        return billUnzipPath;
    }

    public static void setBillUnzipPath(String billUnzipPath) {
        Configs.billUnzipPath = billUnzipPath;
    }

    public static String getBillDownloadPath() {
        return billDownloadPath;
    }

    public static void setBillDownloadPath(String billDownloadPath) {
        Configs.billDownloadPath = billDownloadPath;
    }

    public static void setAliPayPublicCertPath(String aliPayPublicCertPath) {
        Configs.aliPayPublicCertPath = aliPayPublicCertPath;
    }

    public static String getAlipayRootCertPath() {
        return alipayRootCertPath;
    }

    public static void setAlipayRootCertPath(String alipayRootCertPath) {
        Configs.alipayRootCertPath = alipayRootCertPath;
    }

    public static void setEnable(Boolean enable) {
        Configs.enable = enable;
    }

    public static void setConfigs(Configuration configs) {
        Configs.configs = configs;
    }


    public static void setOpenApiDomain(String openApiDomain) {
        Configs.openApiDomain = openApiDomain;
    }

    public static void setPid(String pid) {
        Configs.pid = pid;
    }

    public static void setAppid(String appid) {
        Configs.appid = appid;
    }

    public static void setPrivateKey(String privateKey) {
        Configs.privateKey = privateKey;
    }

    public static void setPublicKey(String publicKey) {
        Configs.publicKey = publicKey;
    }


    public static void setSignType(String signType) {
        Configs.signType = signType;
    }
    
    public static void setMaxQueryRetry(int maxQueryRetry) {
        Configs.maxQueryRetry = maxQueryRetry;
    }

    public static void setQueryDuration(long queryDuration) {
        Configs.queryDuration = queryDuration;
    }

    public static void setMaxCancelRetry(int maxCancelRetry) {
        Configs.maxCancelRetry = maxCancelRetry;
    }

    public static void setCancelDuration(long cancelDuration) {
        Configs.cancelDuration = cancelDuration;
    }

    public static long getHeartbeatDelay() {
        return heartbeatDelay;
    }

    public static void setHeartbeatDelay(long heartbeatDelay) {
        Configs.heartbeatDelay = heartbeatDelay;
    }

    public static long getHeartbeatDuration() {
        return heartbeatDuration;
    }

    public static void setHeartbeatDuration(long heartbeatDuration) {
        Configs.heartbeatDuration = heartbeatDuration;
    }

    public static void setNotifyUrl(String notifyUrl) {
        Configs.notifyUrl = notifyUrl;
    }

    public static void setCharset(String charset) {
        Configs.charset = charset;
    }

    public static void setDataType(String dataType) {
        Configs.dataType = dataType;
    }
}

