package com.common.pay.alipay.model.builder;

import com.google.gson.annotations.SerializedName;
import org.apache.commons.lang.StringUtils;

/**
 * @ProjectName: lenmon-parent
 * @Package: com.common.pay.alipay.model.builder
 * @ClassName: AlipayTradeCloseRequestBuilder
 * @Author: admin
 * @Description: 订单关闭内容对象
 * @Date: 2020/8/25 13:49
 * @Version: 1.0
 */
public class AlipayTradeCloseRequestBuilder extends RequestBuilder{

    private BizContent bizContent = new BizContent();

    @Override
    public boolean validate() {
        if (StringUtils.isEmpty(bizContent.outTradeNo)) {
            throw new NullPointerException("out_trade_no should not be NULL!");
        }
        return true;
    }

    @Override
    public Object getBizContent() {
        return null;
    }


    public String getOutTradeNo() {
        return bizContent.outTradeNo;
    }

    public AlipayTradeCloseRequestBuilder setOutTradeNo(String outTradeNo) {
        bizContent.outTradeNo = outTradeNo;
        return this;
    }

    protected class BizContent{
        // 商户网站订单系统中唯一订单号，64个字符以内，只能包含字母、数字、下划线，
        // 需保证商户系统端不能重复，建议通过数据库sequence生成，
        @SerializedName("out_trade_no")
        private String outTradeNo;
    }

}
