package com.common.pay.alipay.utils;


import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * @ProjectName: lenmon-parent
 * @Package: com.common.pay.alipay.utils
 * @ClassName: BillToolUtils
 * @Author: admin
 * @Description:
 * @Date: 2020/8/28 14:37
 * @Version: 1.0
 */
public class BillToolUtils {

    private static Logger LOGGER = LoggerFactory.getLogger(BillToolUtils.class);

    /**
     * 下载压缩包
     * @param urlStr
     * @param filePath
     */
    public static void downLoadAliPayBill(String urlStr,String filePath){
        //指定希望保存的文件路径
        URL url = null;
        HttpURLConnection httpUrlConnection = null;
        InputStream fis = null;
        FileOutputStream fos = null;
        try {
            url = new URL(urlStr);
            httpUrlConnection = (HttpURLConnection) url.openConnection();
            httpUrlConnection.setConnectTimeout(5 * 1000);
            httpUrlConnection.setDoInput(true);
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setUseCaches(false);
            httpUrlConnection.setRequestMethod("GET");
            httpUrlConnection.setRequestProperty("CHARSET", "UTF-8");
            httpUrlConnection.connect();
            fis = httpUrlConnection.getInputStream();
            byte[] temp = new byte[1024];
            int b;

            fos = new FileOutputStream(new File(filePath));
            while ((b = fis.read(temp)) != -1) {
                fos.write(temp, 0, b);
                fos.flush();
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if(fis!=null) {
                    fis.close();
                }
                if(fos!=null){
                    fos.close();
                }
                if(httpUrlConnection!=null){
                    httpUrlConnection.disconnect();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 解压文件zip
     * @param zipFilePath 需要解压文件路径
     * @param descDir 解压完成之后输出的文件夹
     * @throws IOException
     */
    public static void zipDecompressing(String zipFilePath, String descDir){
        try {
            Charset gbk = Charset.forName("gbk");
            ZipInputStream Zin=new ZipInputStream(new FileInputStream(new File(zipFilePath)),gbk);
            BufferedInputStream Bin=new BufferedInputStream(Zin);
            /** 输出路径（文件夹目录） */
            String Parent=descDir;
            File Fout=null;
            ZipEntry entry;
            try {
                while((entry = Zin.getNextEntry())!=null && !entry.isDirectory()){
                    Fout=new File(Parent,entry.getName());
                    if(!Fout.exists()){
                        (new File(Fout.getParent())).mkdirs();
                    }
                    FileOutputStream out=new FileOutputStream(Fout);
                    BufferedOutputStream Bout=new BufferedOutputStream(out);
                    int b;
                    while((b=Bin.read())!=-1){
                        Bout.write(b);
                    }
                    Bout.close();
                    out.close();
                    LOGGER.info("{},解压成功",Fout);
                }
                Bin.close();
                Zin.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("解压文件异常");
            throw new RuntimeException("解压文件异常",e);
        }
    }
    /** */

    public static void parse( Map<String,List<List<String>>> collect,String filePath){
        /** 存放文件的目录 */
        List<String> csvNames = new ArrayList<>();
        File fileDir = new File(filePath);
        File[] tempList = fileDir.listFiles();
        for (int i = 0; i < tempList.length; i++) {
            csvNames.add(tempList[i].getName());
        }
        readExcel(collect,  filePath, csvNames);
    }

    private static void readExcel(Map<String, List<List<String>>> collect, String filePath, List<String> csvNames){
        if(CollectionUtils.isEmpty(csvNames)){
            return;
        }
        for(String csvName : csvNames){
            List<List<String>> dataList = new ArrayList<>();
            doExceLStream( dataList, filePath ,  csvName);
            if (csvName.contains("汇总")&&!csvName.contains("zip")) {
                collect.put("gather",dataList);
            }else {
                collect.put("details",dataList);
            }
        }
    }

    private static void doExceLStream(List<List<String>> dataList, String filePath, String csvName){
        File excel  = new File(filePath + "/" + csvName);
        Charset gbk = Charset.forName("GBK");
        InputStreamReader inputStreamReader = null;
        InputStream fiStream = null;
        BufferedReader br = null;
        //行文件中所有数据
        //暂时存放每一行的数据
        String rowRecord = "";
        try {
            /** 文件流对象 */
            fiStream = new FileInputStream(excel);
            inputStreamReader = new InputStreamReader(fiStream, Charset.forName("GBK"));
            br = new BufferedReader(inputStreamReader);
            while ((rowRecord = br.readLine()) != null) {
                String  []  lineList = rowRecord.split(",");
                // && !lineList[0].contains("门店编号")
                if(lineList.length > 4){
                    LOGGER.info("返回结果：{},长度：{}",rowRecord,lineList.length);
                    LOGGER.info("返回结果：{},长度：{}",Arrays.asList(lineList),lineList.length);
                    dataList.add(Arrays.asList(lineList));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeStream(br, inputStreamReader, fiStream);
        }
    }

    /**
     * 按顺序关闭流
     */
    private static   void closeStream(BufferedReader bufferedReader, InputStreamReader inputStreamReader, InputStream inputStream) {
        try {
            if (bufferedReader != null) {
                bufferedReader.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (inputStreamReader != null) {
            try {
                inputStreamReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    public static void deleteFile(String filePath){
        File file = new File(filePath);
        //判断文件不为null或文件目录存在
        if (file == null || !file.exists()){
            LOGGER.warn("文件删除失败,请检查文件路径是否正确");
            return;
        }
        //取得这个目录下的所有子文件对象
        File[] files = file.listFiles();
        //遍历该目录下的文件对象
        for (File f: files){
            //打印文件名
            String name = file.getName();
           LOGGER.info("文件名称:{}",name);
            //判断子目录是否存在子目录,如果是文件则删除
            if (!f.isDirectory()){
                LOGGER.info("删除文件名称:{}",name);
                f.delete();
            }
        }
    }
}
