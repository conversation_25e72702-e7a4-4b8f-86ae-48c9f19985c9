package com.common.pay.common.utils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 将对象转换成map
 */
public class MapUtils {

    /**
     * 将Object对象里面的属性和值转化成Map对象
     * @param obj
     * @param flag 是否允许参数为空转化
     * @return
     */
    public static Map<String, String> objectToMap(Object obj,boolean flag) {
        try {
            Map<String, String> map = new HashMap<String,String>();
            Class<?> clazz = obj.getClass();
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object object  = field.get(obj);
                if(object == null && !flag){
                    continue;
                }
                map.put(fieldName, String.valueOf(field.get(obj)));
            }
            return map;
        } catch (IllegalAccessException e) {
           throw new RuntimeException();
        }
    }
    public static String GetMapToXML(Map<String,String> param){
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        for (Map.Entry<String,String> entry : param.entrySet()) {
            sb.append("<"+ entry.getKey() +">");
            sb.append(entry.getValue());
            sb.append("</"+ entry.getKey() +">");
        }
        sb.append("</xml>");
        return sb.toString();
    }

}
