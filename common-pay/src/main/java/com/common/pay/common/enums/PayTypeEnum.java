package com.common.pay.common.enums;

/**
 * <AUTHOR>
 * @description
 * @see
 */
public enum PayTypeEnum {

    ALIPAYALIPAY(1,"支付宝"),
    WXPAY(2,"微信"),
    UNIONPAY(3,"银联支付"),
    XMPAY(4,"小米支付"),
    ;
    private int type;
    private String desc;

    PayTypeEnum(Integer type,String desc){
        this.type= type;
        this.desc = desc;
    }
    public static String getDes(Integer type){
        for(PayTypeEnum typeEnum : PayTypeEnum.values()){
            if(typeEnum.type == type){
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
