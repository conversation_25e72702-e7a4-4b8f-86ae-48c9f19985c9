package com.common.pay.api;

import com.alipay.api.*;

/**
 * @ProjectName: lenmon-parent
 * @Package: com.common.pay.api
 * @ClassName: CertAlipayClient
 * @Author: admin
 * @Description: 证书的数据初始化
 * @Date: 2020/8/26 9:18
 * @Version: 1.0
 */
public class CertAlipayClient  extends AbstractAlipayClient {


    public CertAlipayClient(String serverUrl, String appId, String format, String charset, String signType) {
        super(serverUrl, appId, format, charset, signType);
    }

    public CertAlipayClient(String serverUrl, String appId, String format, String charset, String signType, String proxyHost, int proxyPort) {
        super(serverUrl, appId, format, charset, signType, proxyHost, proxyPort);
    }

    public CertAlipayClient(String serverUrl, String appId, String format, String charset, String signType, String encryptType) {
        super(serverUrl, appId, format, charset, signType, encryptType);
    }

    @Override
    public Signer getSigner() {
        return null;
    }

    @Override
    public SignChecker getSignChecker() {
        return null;
    }

    @Override
    public Encryptor getEncryptor() {
        return null;
    }

    @Override
    public Decryptor getDecryptor() {
        return null;
    }
}
