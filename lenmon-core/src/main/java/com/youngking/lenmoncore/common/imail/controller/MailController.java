package com.youngking.lenmoncore.common.imail.controller;

import com.youngking.lenmoncore.common.imail.properties.MailProperties;
import com.youngking.lenmoncore.common.imail.service.IMailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> at 2020:05:06 16:27
 */
@RestController
@RequestMapping("/mail")
public class MailController {

    @Autowired
    MailProperties mailProperties;
    @Autowired
    IMailService iMailService;

    @RequestMapping("/properties")
    public String getMailProperties() {
        return mailProperties.getMail().toString();
    }

    @RequestMapping("/send")
    public void send(String to, String subject, String content) {
        iMailService.sendSimpleMail(to, subject, content);
    }

    @RequestMapping("/send2")
    public void send2(String to, String subject, String content) {
        iMailService.sendSimpleMail2(to, subject, content);
    }

}
