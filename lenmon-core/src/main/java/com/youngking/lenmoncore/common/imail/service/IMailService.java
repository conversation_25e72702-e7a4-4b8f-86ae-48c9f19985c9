package com.youngking.lenmoncore.common.imail.service;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> at 2020:05:06 14:03
 * 邮件服务
 */
public interface IMailService {

    /**
     * 发送文本邮件
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     */
    void sendSimpleMail(String to, String subject, String content);

    void sendSimpleMail2(String to, String subject, String content);

    /**
     * 发送带附件邮件
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param filePath 附件
     */
    void sendAttachmentMail(String to, String subject, String content, String filePath);

    void sendSimpleMailMulti(String[] to, String subject, String content);
}
