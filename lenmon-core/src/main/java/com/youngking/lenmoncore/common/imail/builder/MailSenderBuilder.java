package com.youngking.lenmoncore.common.imail.builder;

import com.youngking.lenmoncore.common.imail.entity.MailEntity;
import com.youngking.lenmoncore.common.imail.properties.MailProperties;
import com.youngking.lenmoncore.common.imail.sender.IMailSender;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> at 2020:05:06 17:15
 */
@Component
@Data
public class MailSenderBuilder {

    @Autowired
    MailProperties mailProperties;

    private static Integer index = 0;

    public IMailSender getInstance(String to) {
        String domain = to.split("@")[1];
        IMailSender iMailSender = new IMailSender();
        Map<String, Map<Integer, MailEntity>> mail = mailProperties.getMail();
        if (mail == null){
            throw new RuntimeException("请配置mail.properties文件");
        }
        // qq
        if ("qq.com".equalsIgnoreCase(domain)) {
            Map<Integer, MailEntity> qqMap = mail.get("qq");
            if (qqMap.size() > 0) {
                index = index % qqMap.size() + 1;
                MailEntity mailEntity = qqMap.get(index);
                if (mailEntity == null) {
                    throw new RuntimeException("第" + index + "条qq配置不存在");
                }
                iMailSender.setSender(mailEntity);
                System.out.println("index: " + index);
            } else {
                throw new RuntimeException("没有qq邮箱配置");
            }
        }
        return iMailSender;
    }
}
