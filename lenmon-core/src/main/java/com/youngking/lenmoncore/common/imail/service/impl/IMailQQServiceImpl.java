package com.youngking.lenmoncore.common.imail.service.impl;

import com.youngking.lenmoncore.common.imail.builder.MailSenderBuilder;
import com.youngking.lenmoncore.common.imail.sender.IMailSender;
import com.youngking.lenmoncore.common.imail.service.IMailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;

/**
 * created by z<PERSON><PERSON><PERSON> at 2020:05:06 14:08
 * QQ邮件服务
 */
@Service
public class IMailQQServiceImpl implements IMailService {

    @Autowired(required = false)
    JavaMailSender mailSender;
    @Autowired(required = false)
    MailSenderBuilder mailSenderBuilder;

    @Override
    public void sendSimpleMail2(String to, String subject, String content) {
        SimpleMailMessage message = new SimpleMailMessage();
        IMailSender sender = mailSenderBuilder.getInstance(to);
        message.setFrom(sender.getFrom());
        //message.setFrom("<EMAIL>");
        message.setTo(to);
        message.setSubject(subject);
        message.setText(content);
        sender.send(message);
    }
    @Override
    public void sendSimpleMail(String to, String subject, String content) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom("<EMAIL>");
        //message.setFrom("<EMAIL>");
        message.setTo(to);
        message.setSubject(subject);
        message.setText(content);
        mailSender.send(message);
    }

    //群发邮件
    public void sendSimpleMailMulti(String[] to, String subject, String content) {
        SimpleMailMessage message = new SimpleMailMessage();
        IMailSender sender = mailSenderBuilder.getInstance("<EMAIL>");
        message.setFrom("<EMAIL>");
        //message.setFrom("<EMAIL>");
        message.setTo(to);
        message.setSubject(subject);
        message.setText(content);
        sender.send(message);
    }


    @Override
    public void sendAttachmentMail(String to, String subject, String content, String filePath) {
        MimeMessage message = mailSender.createMimeMessage();
        try {
            MimeMessageHelper messageHelper = new MimeMessageHelper(message, true);
            messageHelper.setFrom("");
            messageHelper.setTo(to);
            messageHelper.setSubject(subject);
            messageHelper.setText(content);
            FileSystemResource file = new FileSystemResource(new File(filePath));
            String fileName = filePath.substring(filePath.lastIndexOf(File.separator));
            messageHelper.addAttachment(fileName, file);
            mailSender.send(message);
        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }
}
