package com.youngking.lenmoncore.common.constant;

import com.youngking.lenmoncore.common.exception.RRException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum WorderExecStatusEnum {
    WEIPAIDAN("未派单", 0),
    WANGDIANYIJIEDAN("网点已接单", 1),
    DAIKANCEYUYUE("待勘测预约", 2),
    DAIKANCE("待勘测", 3),
    KANCEZILIAOWEITIJIAO("勘测资料未提交", 4),
    KANCEZILIAOTIJIAODAISHENHE("勘测资料提交待审核", 5),
    KANCEZILIAOZHENGGAIZHONG("勘测资料整改中", 6),
    DAIKEFUQUEREN("待客服确认", 7),
    KANCEZILIAOWUWUDAISHANGCHUANCHEQI("勘测资料无误待上传车企", 8),
    DENGDAICHONGDIANZHUANGJIPEIJIAN("等待充电桩及配件", 9),
    DAIANZHUANGYUYUE("待安装预约", 10),
    DAIANZHUANG("待安装", 11),
    ANZHUANGZILIAOWEITIJIAO("安装资料未提交", 12),
    ANZHUANGZILIAOYITIJIAODAISHENHE("安装资料已提交待审核", 13),
    ANZHUANGZILIAOZHENGGAIZHONG("安装资料整改中", 14),
    ANZHUANGZILIAODAIKEFUQUEREN("安装资料待客服确认", 15),
    ANZHUANGZILIAOWUWUDAISHANGCHUANCHEQI("安装资料无误待上传车企", 16),
    ANZHUANGWANCHENG("安装完成", 17),
    FUWUJINGLIWEIPAIDAN("服务经理未派单", 18),
    WANGDIANWEIPAIDAN("网点未派单", 19),
    KANCEJIEDAN("勘测结单", 20),
    YIQUXIAO("已取消", 21),
    ANZHUANGJIEDAN("安装结单", 22),
    CHEQIHUITUIDAIKEFUQUEREN("车企回退待客服确认", 23);

    private final String name;
    private final int code;

    private static final Map<Integer, WorderExecStatusEnum> lookup = new HashMap<>();

    static {
        for (WorderExecStatusEnum status : WorderExecStatusEnum.values()) {
            lookup.put(status.code, status);
        }
    }

    public static WorderExecStatusEnum fromCode(int code) {
        return lookup.get(code);
    }

    public static String getNameFromCode(int code) {
        WorderExecStatusEnum status = fromCode(code);
        if (status == null) {
            throw new RRException("工单执行状态编码不正确");
        }
        return status.getName();
    }

}
