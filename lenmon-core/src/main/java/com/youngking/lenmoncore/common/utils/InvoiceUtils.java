package com.youngking.lenmoncore.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * Created by <PERSON><PERSON>yi<PERSON> on 2020-12-07 14:49
 */

public class InvoiceUtils {

    /**
     * 根据开票回调返回结果获得发票浏览地址
     * @param invoiceResult
     * @return
     */
    public static String getViewUrl(String invoiceResult){
        String viewUrl = null;
        JSONObject invoice = getInvoice(invoiceResult);
        viewUrl = invoice.getString("viewUrl");
        return viewUrl;
    }

    /**
     * 根据开票回调返回结果获得发票信息
     * @param invoiceResult
     * @return
     */
    public static JSONObject getInvoice(String invoiceResult){
        JSONObject jsonObject = JSON.parseObject(invoiceResult);
        return getInvoice(jsonObject);
    }


    public static JSONObject getInvoice(JSONObject jsonObject){
        JSONObject invoiceJson = null;
        if(jsonObject.containsKey("invoices")){
            String invoices = jsonObject.getString("invoices");
            JSONArray jsonArray = JSON.parseArray(invoices);
            if(jsonArray.size() > 0){
                invoiceJson = jsonArray.getJSONObject(0);
            }
        }
        return invoiceJson;
    }

}
