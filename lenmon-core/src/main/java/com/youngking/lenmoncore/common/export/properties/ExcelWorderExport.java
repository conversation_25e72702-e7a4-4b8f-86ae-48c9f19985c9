package com.youngking.lenmoncore.common.export.properties;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;


@PropertySource(value = {"classpath:excelExport.properties"}, encoding ="gbk")
@ConfigurationProperties(prefix = "export")
@Component
@Data
public class ExcelWorderExport {

    private List<String> worderList1;

    private HashMap<String, String> show;

    private List<String> worderList2;

    private HashMap<String, String> show2;

    private List<String> worderList3;

    private HashMap<String, String> show3;

    private List<String> worderList4;

    private HashMap<String, String> show4;

    private List<String> worderList5;

    private HashMap<String, String> show5;

    private List<String> worderList6;

    private HashMap<String, String> show6;
}
