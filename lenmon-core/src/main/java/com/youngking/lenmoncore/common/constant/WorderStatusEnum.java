package com.youngking.lenmoncore.common.constant;

import com.youngking.lenmoncore.common.exception.RRException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Getter
@RequiredArgsConstructor
public enum WorderStatusEnum {

    FENPEIZHONG("分配中", 0),
    KANCEZHONG("勘测中", 1),
    ANZHUANGZHONG("安装中", 2),
    JIESUANZHONG("结算中", 3),
    DINGDANWANCHENG("订单完成", 4),
    KANCEJIEDAN("勘测结单", 5),
    QUXIAOFUWU("取消服务", 6);

    private final String name;
    private final int code;

    private static final Map<Integer, WorderStatusEnum> lookup = new HashMap<>();

    static {
        for (WorderStatusEnum status : WorderStatusEnum.values()) {
            lookup.put(status.code, status);
        }
    }

    public static WorderStatusEnum fromCode(int code) {
        return lookup.get(code);
    }

    public static String getNameFromCode(int code) {
        WorderStatusEnum status = fromCode(code);
        if (status == null) {
            throw new RRException("工单状态编码不正确");
        }
        return status.getName();
    }

}
