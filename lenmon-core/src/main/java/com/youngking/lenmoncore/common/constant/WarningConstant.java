package com.youngking.lenmoncore.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * Created by <PERSON><PERSON>yi<PERSON> on 2020-03-20 17:11
 */

public class WarningConstant {


    public static final Integer INTEGER_ZERO = 0;
    public static final String STRING_ZERO = "0";
    public static final String STRING_ONE = "1";

    public static final String a = "${name}";

    // 客服
    public static final String TRIGGER_TEMPLATE_CS_NAME = "${cs_name}";
    // 服务经理
    public static final String TRIGGER_TEMPLATE_PM_NAME = "${pm_name}";
    // 网点
    public static final String TRIGGER_TEMPLATE_INET_NAME = "${inet_name}";
    // 网点管理员
    public static final String TRIGGER_TEMPLATE_INETM_NAME = "${inetm_name}";
    // 服务兵
    public static final String TRIGGER_TEMPLATE_SERVICE_NAME = "${service_name}";
    // 工单编号
    public static final String TRIGGER_TEMPLATE_ORDER_NO = "${order_no}";
    // 系统时间
    public static final String TRIGGER_TEMPLATE_SYSTEM_TIME = "${system_time}";
    // 品牌
    public static final String TRIGGER_TEMPLATE_BRAND_NAME = "${brand_name}";
    // 片区长
    public static final String TRIGGER_TEMPLATE_DISTRICT_USER_NAME = "${district_user_name}";
    // 运营负责人
    public static final String TRIGGER_TEMPLATE_OPERATOR_NAME = "${operator_name}";
    // 客户姓名
    public static final String CUSTOMER_NAME = "${customer_name}";
    // 客户联系方式
    public static final String CUSTOMER_CONTACT = "${customer_contact}";

    public static final String SERVER_COUNT = "server_count";


    // 片区长角色ID
    public static final String DISTRICT_ROLE_ID = "35";
    // 运营负责人角色ID
    public static final String OPERATION_ROLE_ID = "36";

    public static final String TRIGGER_SEND_TIME = "trigger_send_time";
    public static final String TRIGGER_VALIDATE = "trigger_validate";
    public static final String CODE = "code";
    public static final String CURRENTTIME = "current_time";
    public static final String WORDER_NO = "worderNo";
    public static final String WORDER_TRIGGER_EVENT = "worderTriggerEvent";
    public static final String WORDER_EXEC_STATUS = "worderExecStatus";

    // 首次联系用户
    public static final String FIRST_CONTACT = "first_contact";
    // 不再验证工单状态
    public static final String NOT_VALIDATE_WORDER_STATUS = "not_validate_worder_status";

    // 派单给服务经理
    public static final String TRIGGER_EVENT_SEND_WORDER_TO_MANAGER = "send_worder_to_manager";
    // 派单给网点
    public static final String TRIGGER_EVENT_SEND_WORDER_TO_BRANCH = "send_worder_to_branch";
    // 派单给服务兵
    public static final String TRIGGER_EVENT_SEND_WORDER_TO_SOLDIER = "send_worder_to_soldier";
    // 更新下次联系时间
    public static final String UPDATE_NEXT_CONTACT_TIME = "update_next_contact_time";

    // 勘测预约
    public static final String CONVEY_APPOINT = "convey_appoint";
    // 勘测签到
    public static final String CONVEY_SIGNIN = "convey_signin";
    // 勘测资料提交
    public static final String CONVEY_DATA_SUBMIT = "convey_data_submit";
    // 勘测资料服务经理审核未通过
    public static final String CONVEY_MANAGER_AUDIT_NOT_PASS = "convey_manager_audit_not_pass";
    // 勘测资料服务经理审核通过
    public static final String CONVEY_MANAGER_AUDIT_PASS = "convey_manager_audit_pass";
    // 勘测签退
    public static final String CONVEY_SIGN_OUT = "convey_signout";

    public static final String COMPANY_FINISH_CONVEY = "company_finish_convey";


    // 安装预约
    public static final String INSTALL_APPOINT = "install_appoint";
    // 安装签到
    public static final String INSTALL_SIGNIN = "install_signin";
    // 安装资料提交
    public static final String INSTALL_DATA_SUBMIT = "install_data_submit";
    // 安装资料服务经理审核未通过
    public static final String INSTALL_MANAGER_AUDIT_NOT_PASS = "install_manager_audit_not_pass";
    // 安装资料服务经理审核通过
    public static final String INSTALL_MANAGER_AUDIT_PASS = "install_manager_audit_pass";
    // 安装签退
    public static final String INSTALL_SIGN_OUT = "install_signout";

    @Getter
    @AllArgsConstructor
    public enum RoleType {
        /**
         * 客服
         */
        ROLETYPE_KF("1", "客服"),
        /**
         * 服务经理
         */
        ROLETYPE_KFJL("2", "服务经理"),
        /**
         * 网点负责人
         */
        ROLETYPE_WDFZR("3", "网点负责人"),
        /**
         * 服务兵
         */
        ROLETYPE_FWB("4", "服务兵"),
        /**
         * 片区长
         */
        ROLETYPE_PQZ("5", "片区长"),
        /**
         * 运营负责人
         */
        ROLETYPE_YYFZR("6", "运营负责人"),
        /**
         * 默认
         */
        NULL("", "");

        String code;
        String name;

        public static String getName(String code) {
            return Stream.of(RoleType.values()).filter(roleType -> Objects.equals(roleType.getCode(), code)).findFirst().orElse(NULL).getName();
        }
    }

    @Getter
    @AllArgsConstructor
    public enum SendType {

        /**
         * 站内消息
         */
        SENDTYPE_ZN("0", "站内消息"),
        /**
         * 短信通知
         */
        SENDTYPE_DX("1", "短信通知"),
        /**
         * 邮件通知
         */
        SENDTYPE_YJ("2", "邮件通知"),
        /**
         * 默认
         */
        NULL("", "");

        String code;
        String name;

        public static String getName(String code) {
            return Stream.of(SendType.values()).filter(sendType -> Objects.equals(code, sendType.getCode())).findFirst().orElse(NULL).getName();
        }
    }


    public static final String TRIGGER_EVENT_TEST = "test";

}
