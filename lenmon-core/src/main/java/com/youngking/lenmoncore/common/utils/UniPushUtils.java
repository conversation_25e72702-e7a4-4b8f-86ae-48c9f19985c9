package com.youngking.lenmoncore.common.utils;

import com.gexin.rp.sdk.base.IPushResult;
import com.gexin.rp.sdk.base.impl.SingleMessage;
import com.gexin.rp.sdk.base.impl.Target;
import com.gexin.rp.sdk.exceptions.RequestException;
import com.gexin.rp.sdk.http.IGtPush;
import com.gexin.rp.sdk.template.AbstractTemplate;
import com.gexin.rp.sdk.template.style.AbstractNotifyStyle;
import com.youngking.lenmoncore.common.contants.Contants;
import com.youngking.lenmoncore.common.template.PushTemplate;


/**
 * Created by zhangyibo on 2020-04-23 13:46
 */

public class UniPushUtils {

    public static void main(String[] args) {
        //System.setProperty("needOSAsigned", "true");
        //pushToSingleTransmission("56df8e96548f161c682be600d7c61177", "0");
        //pushToSingleTransmission(",3b890ef0c53595b2e74dc79385a8cfd8", "sucess");
        pushToSingle("04436789b4fa9ea5c5fe682dcb92015a", "hi, zhang");

    }

     /**
     * 对单个用户推送消息
     *
     * 场景1：某用户发生了一笔交易，银行及时下发一条推送消息给该用户。
     *
     * 场景2：用户定制了某本书的预订更新，当本书有更新时，需要向该用户及时下发一条更新提醒信息。
     * 这些需要向指定某个用户推送消息的场景，即需要使用对单个用户推送消息的接口。
     */
    public static void pushToSingle(String cid) {
        AbstractTemplate template = PushTemplate.getNotificationTemplate(); //通知模板(点击后续行为: 支持打开应用、发送透传内容、打开应用同时接收到透传 这三种行为)
//        AbstractTemplate template = PushTemplate.getLinkTemplate(); //点击通知打开(第三方)网页模板
//        AbstractTemplate template = PushTemplate.getTransmissionTemplate(); //透传消息模版
//        AbstractTemplate template = PushTemplate.getRevokeTemplate(); //消息撤回模版
//        AbstractTemplate template = PushTemplate.getStartActivityTemplate(); //点击通知, 打开（自身）应用内任意页面
        IGtPush push = new IGtPush(Contants.APPKEY, Contants.MASTERSECRET,true);
        // 单推消息类型
        SingleMessage message = getSingleMessage(template);
        Target target = new Target();
        target.setAppId(Contants.APPID);
        target.setClientId(cid);
//        target.setAlias(ALIAS); //别名需要提前绑定

        IPushResult ret = null;
        try {
            ret = push.pushMessageToSingle(message, target);
        } catch (RequestException e) {
            e.printStackTrace();
            ret = push.pushMessageToSingle(message, target, e.getRequestId());
        }
        if (ret != null) {
            System.out.println(ret.getResponse().toString());
        } else {
            System.out.println("服务器响应异常");
        }
    }
    public static void pushToSingle(String cid,String content) {
        AbstractTemplate template = PushTemplate.getNotificationTemplate( content); //通知模板(点击后续行为: 支持打开应用、发送透传内容、打开应用同时接收到透传 这三种行为)
        IGtPush push = new IGtPush(Contants.APPKEY, Contants.MASTERSECRET,true);
        // 单推消息类型
        SingleMessage message = getSingleMessage(template);
        Target target = new Target();
        target.setAppId(Contants.APPID);
        target.setClientId(cid);
        IPushResult ret = null;
        try {
            ret = push.pushMessageToSingle(message, target);
        } catch (RequestException e) {
            e.printStackTrace();
            ret = push.pushMessageToSingle(message, target, e.getRequestId());
        }
        if (ret != null) {
            System.out.println(ret.getResponse().toString());
        } else {
            System.out.println("服务器响应异常");
        }
    }
    public static void pushToSingle(String cid,AbstractNotifyStyle style) {
        AbstractTemplate template = PushTemplate.getNotificationTemplate( style); //通知模板(点击后续行为: 支持打开应用、发送透传内容、打开应用同时接收到透传 这三种行为)
        IGtPush push = new IGtPush(Contants.APPKEY, Contants.MASTERSECRET,true);
        // 单推消息类型
        SingleMessage message = getSingleMessage(template);
        Target target = new Target();
        target.setAppId(Contants.APPID);
        target.setClientId(cid);
        IPushResult ret = null;
        try {
            ret = push.pushMessageToSingle(message, target);
        } catch (RequestException e) {
            e.printStackTrace();
            ret = push.pushMessageToSingle(message, target, e.getRequestId());
        }
        if (ret != null) {
            System.out.println(ret.getResponse().toString());
        } else {
            System.out.println("服务器响应异常");
        }
    }

    /**
     * 透传消息
     * @param cid
     * @param content
     */
    public static void pushToSingleTransmission(String cid,String content) {
        AbstractTemplate template = PushTemplate.getTransmissionTemplate( content); //通知模板(点击后续行为: 支持打开应用、发送透传内容、打开应用同时接收到透传 这三种行为)
        IGtPush push = new IGtPush(Contants.APPKEY, Contants.MASTERSECRET,true);
        SingleMessage message = getSingleMessage(template);
        // 单推消息类型
//        message.setData(template);
//        message.setOffline(true);
//        message.setOfflineExpireTime(1 * 60 * 60 * 1000);
//        message.setPushNetWorkType(0);
//        message.setStrategyJson("{\"default\":1,\"ios\":1,\"st\":1}");
        Target target = new Target();
        target.setAppId(Contants.APPID);
        target.setClientId(cid);
        IPushResult ret = null;
        try {
            ret = push.pushMessageToSingle(message, target);
        } catch (RequestException e) {
            e.printStackTrace();
            ret = push.pushMessageToSingle(message, target, e.getRequestId());
        }
        if (ret != null) {
            System.out.println(ret.getResponse().toString());
        } else {
            System.out.println("服务器响应异常");
        }
    }

    private static SingleMessage getSingleMessage(AbstractTemplate template) {
        SingleMessage message = new SingleMessage();
        message.setData(template);
        // 设置消息离线，并设置离线时间
        message.setOffline(true);
        // 离线有效时间，单位为毫秒，可选
        message.setOfflineExpireTime(72 * 3600 * 1000);
        message.setPushNetWorkType(0); // 判断客户端是否wifi环境下推送。1为仅在wifi环境下推送，0为不限制网络环境，默认不限
        // APNs下发策略；1: 个推通道优先，在线经个推通道下发，离线经APNs下发(默认);2: 在离线只经APNs下发;3: 在离线只经个推通道下发;4: 优先经APNs下发，失败后经个推通道下发;
        //message.setStrategyJson("{\"ios\":4,\"android\":4}");
        message.setStrategyJson("{\"default\":1,\"ios\":1,\"st\":1}");
        return message;
    }
}


