package com.youngking.lenmoncore.common.imail.properties;

import com.youngking.lenmoncore.common.imail.entity.MailEntity;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> at 2020:05:06 16:17
 */
@ConfigurationProperties(prefix = "")
@PropertySource("classpath:mail.properties")
@Component
@Data
public class MailProperties {

    private Map<String, Map<Integer, MailEntity>> mail;
//    private Map<String, MailEntity> qq;

}
