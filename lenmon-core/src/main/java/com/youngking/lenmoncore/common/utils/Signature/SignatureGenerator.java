package com.youngking.lenmoncore.common.utils.Signature;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.TreeMap;

/**
 * 一个通用的MD5签名生成工具
 * <br/>
 * 参数排序：使用TreeMap自动按键（参数名）排序。<br/>
 * 移除空值参数：使用Guava的Maps.filterValues方法。<br/>
 * 参数拼接：使用Guava的Joiner类来拼接参数。<br/>
 * 添加密钥：在字符串末尾添加client_secret。<br/>
 * MD5加密：使用Apache Commons Codec的DigestUtils进行MD5加密并转为大写。<br/>
 */
@Slf4j
public class SignatureGenerator {

    public static String generateSignatureMD5(String clientSecret, Map<String, String> params) {
        // 步骤1：参数排序
        TreeMap<String, String> sortedParams = new TreeMap<>(params);

        // 移除空值参数
        Map<String, String> filteredParams = Maps.filterValues(sortedParams, StringUtils::isNotEmpty);

        // 步骤2：参数拼接
        String stringA = Joiner.on("&")
                .withKeyValueSeparator("=")
                .join(filteredParams);

        // 步骤3：添加密钥
        String stringSignTemp = stringA + "&key=" + clientSecret;
        log.debug(stringSignTemp);
        return DigestUtils.md5Hex(stringSignTemp).toUpperCase();
    }

}