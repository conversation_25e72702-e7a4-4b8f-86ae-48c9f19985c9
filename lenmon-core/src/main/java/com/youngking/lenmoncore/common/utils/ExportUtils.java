package com.youngking.lenmoncore.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangyibo on 2020-09-14 13:51
 */

public class ExportUtils {

    public static HSSFWorkbook create(List exportList, List<String> list, Map<String, String> map){
        // 创建文档
        HSSFWorkbook wb = new HSSFWorkbook();
        // 创建表单
        HSSFSheet dotSheet = wb.createSheet("网点信息");
        // 设置字体
        HSSFFont font = wb.createFont();
        font.setFontHeightInPoints((short)12);
        font.setFontName("黑体");
        HSSFCellStyle cellTitleStyle = wb.createCellStyle();
        cellTitleStyle.setFont(font);
        cellTitleStyle.setAlignment(HorizontalAlignment.CENTER);
        // 创建一行
        HSSFRow dotNameRow = dotSheet.createRow(0);
        dotNameRow.setHeightInPoints((short) 25);
        // 列名
        for (int i = 0, len = list.size(); i < len; i++) {
            String key = list.get(i);
            HSSFCell cell = dotNameRow.createCell(i);
            cell.setCellValue(map.get(key));
            cell.setCellStyle(cellTitleStyle);
        }
        // 列值
        for (int i = 0, len = exportList.size(); i < len; i++) {
            Object obj = exportList.get(i);
            HSSFRow dotValueRow = dotSheet.createRow(i+1);
            dotValueRow.setHeightInPoints((short) 20);
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(obj));
            for (int j = 0, len2 = list.size(); j < len2; j++) {
                String key = list.get(j);
                dotValueRow.createCell(j).setCellValue(jsonObject.getString(key));
            }
        }
        for (int i = 0, len = list.size(); i < len; i++) {
            dotSheet.autoSizeColumn(i);
        }
        return wb;
    }
}
