/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.lenmoncore.common.utils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * String工具类
 *
 * <AUTHOR>
 */
public class StringUtils {

    /**
     * 集合转字符串
     * @param list
     * @return
     */
    public static String listToString(List list) {
        return replaceSquareToQuetes(list.toString());
    }

    /**
     * 去除中括号
     * @param str
     * @return
     */
    public static String replaceSquareToQuetes(String str) {
        return str.replace("[", "\"").replace("]", "\"");
    }

    public static void main(String[] args) {
        String str = ",1010,";
        boolean b = str.startsWith(",");
        System.out.println(b);
        boolean b1 = str.endsWith(",");
        System.out.println(b1);
        System.out.println(str.substring(1, str.length()-1));
    }

    /**
     * 前后添加大括号
     * @return
     */
    public static String appendSqureBeforeAndAfter(String str) {
        if(StringUtils.isNotBlank(str)){
            str = str.trim();
            if(str.startsWith(",")){
                str = str.substring(1);
            }
            if(str.endsWith(",")){
                str = str.substring(0, str.length()-1);
            }
        }
        return appendSqureAfter(appendSqureBefore(str));
    }

    public static String appendSqureBefore(String str) {
        return "(" + str;
    }

    public static String appendSqureAfter(String str) {
        return str + ")";
    }

    public static boolean isEmpty(Object str) {
        return str == null || str.toString().length() == 0;
    }

    public static boolean isNotBlank(String str) {
        return !StringUtils.isBlank(str);
    }

    public static boolean isBlank(String str) {
        int strLen;
        if (str == null || (strLen = str.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((!Character.isWhitespace(str.charAt(i)))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 加一拼成字符串
     * 如传入 10, 4 返回 1005
     *
     * @param parentId parentId
     * @param maxId    maxId
     * @return String 加1
     */
    public static String addOne(String parentId, String maxId) {
        int ten = 10;
        if ("0".equals(parentId)) {
            parentId = "";
        }
        if (isNullOrEmpty(maxId)) {
            return parentId + "01";
        }

        maxId = maxId.substring(maxId.length() - 2);

        int result = Integer.parseInt(maxId) + 1;

        if (result < ten) {
            return parentId + "0" + result;
        } else {
            return parentId + result + "";
        }
    }

    /**
     * 判断对象或对象数组中每一个对象是否为空: 对象为null，字符序列长度为0，集合类、Map为empty
     *
     * @param obj obj
     * @return boolean
     */
    public static boolean isNullOrEmpty(Object obj) {
        if (obj == null) {
            return true;
        }

        if (obj instanceof CharSequence) {
            return ((CharSequence) obj).length() == 0;
        }

        if (obj instanceof Collection) {
            return ((Collection) obj).isEmpty();
        }

        if (obj instanceof Map) {
            return ((Map) obj).isEmpty();
        }

        if (obj instanceof Object[]) {
            Object[] object = (Object[]) obj;
            if (object.length == 0) {
                return true;
            }
            boolean empty = true;
            for (Object anObject : object) {
                if (!isNullOrEmpty(anObject)) {
                    empty = false;
                    break;
                }
            }
            return empty;
        }
        return false;
    }

}
