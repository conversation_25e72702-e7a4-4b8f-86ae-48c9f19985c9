/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.lenmoncore.common.validator;


import com.youngking.lenmoncore.common.exception.RRException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;

/**
 * 数据校验
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
@Log4j2
public abstract class Assert {

    public static void isBlank(String str, String message) {
        if (StringUtils.isBlank(str)) {
            throw new RRException(message);
        }
    }

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            log.error("===================出现异常：{}======================",message);
            throw new RRException(message);
        }
    }

    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new RRException(message);
        }
    }

    public static void isNull(Object object, String message) {
        if (object == null) {
            throw new RRException(message);
        }
    }
}
