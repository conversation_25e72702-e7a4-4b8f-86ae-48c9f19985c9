package com.youngking.lenmoncore.common.constant;


/**
 * Created by zhangyibo on 2020-08-17 14:08
 */

public enum IntegerEnum {
    ZERO(0),
    ONE(1),
    TWO(2),
    THIRD(3),
    FOUR(4),
    FIVE(5),
    <PERSON>IX(6),
    <PERSON><PERSON><PERSON>(7),
    <PERSON><PERSON><PERSON>(8),
    <PERSON><PERSON><PERSON>(9),
    TEN(10);


    private Integer value;

    IntegerEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}

