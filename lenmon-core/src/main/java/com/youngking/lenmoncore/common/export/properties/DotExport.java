package com.youngking.lenmoncore.common.export.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * Created by zhangyibo on 2020-09-09 14:36
 */

@ConfigurationProperties(prefix = "export")
@PropertySource(value = {"classpath:dotExport.properties"}, encoding = "utf-8")
@Component
@Data
public class DotExport {

    private List<String> dotList;

    private HashMap<String, String> dot;
}
