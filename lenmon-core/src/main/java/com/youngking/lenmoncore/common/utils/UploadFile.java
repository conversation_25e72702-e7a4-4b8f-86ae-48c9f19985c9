package com.youngking.lenmoncore.common.utils;


import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

@Component
public class UploadFile {

    private static String FILE_BASE;

    //初始化属性
    static {
        Properties prop = new Properties();
        try {
            //加载配置文件
            //prop.load(new PropertiesUtil().getClass().getResourceAsStream("/application.yml"));
            FILE_BASE ="base";
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 上传文件
     * @param file 要上传的文件
     * @param business 业务名称card card/imges
     */
    public static String uploadFile(MultipartFile file,String business) throws IOException {

        //数据库中存的是：/图片类型/日期/时间戳.后缀
        String finalPath = null;
        if (null != file && !(file.isEmpty())) {
            InputStream in = null;
            OutputStream out = null;
            try {
                //获取Tomcat绝对路径
                String tomcatPath = System.getProperty("catalina.home")+"/webapps";
                //System.out.println(tomcatPath);
                //tomcat目录下建文件夹
                String fileDir = FILE_BASE + "/" +business;
                //获取当前日期
                String dayPath = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).substring(0, 8);
                //拼写图片保存的路径 File.separator表示符号 "/"  /apache-tomcat-8.5.31/images/logoImages/20180613
                File dir = new File(tomcatPath + File.separator + fileDir + File.separator + dayPath);
                //如果没有该路径，创建之
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                //获取文件后缀
                String fileName = file.getOriginalFilename();
                String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
                //文件保存时的正真名称
                String name1 = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()).substring(8 , 17);
                String name2 = String.valueOf(System.nanoTime()).substring(10,14);
                //时 分 秒 毫秒 + 纳秒的后4位
                String saveName = name1 + name2 + "."+ suffix;
                File serverFile = new File(dir.getAbsolutePath() + File.separator + saveName);
                in = file.getInputStream();
                out = new FileOutputStream(serverFile);
                byte[] b = new byte[1024*1024];
                int len = 0;
                while ((len = in.read(b)) > 0) {
                    out.write(b, 0, len);
                }
                out.close();
                in.close();
                //数据库要保存的完整路径
                finalPath = "/"+ fileDir+"/" + dayPath + "/" + saveName;
            }catch(Exception e) {
                e.printStackTrace();
            }finally{
                if (out != null) {
                    out.close();
                    out = null;
                }
                if (in != null) {
                    in.close();
                    in = null;
                }
            }
        }
        return finalPath;
    }
    public static void delFile(String path){
        //获取Tomcat绝对路径
        String tomcatPath = System.getProperty("catalina.home")+"/webapps";
        File file=new File(tomcatPath + path);
        if(file.exists()&&file.isFile())
            file.delete();
    }
    public void test(){
        System.out.println(FILE_BASE);
    }
}
