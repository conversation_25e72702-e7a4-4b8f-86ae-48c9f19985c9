package com.youngking.lenmoncore.common.utils;

import com.youngking.lenmoncore.common.entity.ExcelInput;
import com.youngking.lenmoncore.common.exception.RRException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangyibo on 2020-09-27 09:33
 */

public class InputUtils {

    public static ExcelInput analysisExcel(MultipartFile file){
        ExcelInput excelInput = new ExcelInput();
        String fileName = file.getOriginalFilename();
        System.out.println(fileName);
        boolean notNull = false;
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            System.out.println("上传文件格式不正确");
            throw new RRException("上传文件格式不正确");
        }
        boolean isExcel2003 = true;
        if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            isExcel2003 = false;
        }
        InputStream is = null;
        List<Map<String,Object>> list = new ArrayList<>();
        Workbook wb = null;
        try {
            is = file.getInputStream();
            if (isExcel2003) {
                wb = new HSSFWorkbook(is);
            } else {
                wb = new XSSFWorkbook(is);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        excelInput.setFileName(fileName);
        excelInput.setWorkbook(wb);
        return excelInput;
    }

    /**
     * 获取Excel表格某一列数据
     * @param wb
     * @param column
     * @return
     */
    public static List getColumnList(Workbook wb, Integer column){
        return getColumnList(wb, column, 0);
    }
    public static List<String> getColumnList(Workbook wb, Integer column, Integer sheetNum){
        List<String> list = new ArrayList();
        Sheet sheet = wb.getSheetAt(sheetNum);
        for (int i = 0, len = sheet.getLastRowNum(); i <= len; i++) {
            Row row = sheet.getRow(i);
            String cellValue = getCellValue(row, column);
            if (null != row && StringUtils.isNotBlank(cellValue)) {
                list.add(cellValue);
            }
        }
        return list;
    }
    public static List getIsNoColumnList(Workbook wb, Integer column){
        return getIsNoColumnList(wb, column, 0);
    }
    public static List<String> getIsNoColumnList(Workbook wb, Integer column, Integer sheetNum){
        List<String> list = new ArrayList();
        Sheet sheet = wb.getSheetAt(sheetNum);
        for (int i = 0, len = sheet.getLastRowNum(); i <= len; i++) {
            Row row = sheet.getRow(i);
            String isNo = getCellValue(row, 16);
            if(isNo!=null && isNo.equals(Constant.Y)){
                String cellValue = getCellValue(row, column);
                if (null != row && StringUtils.isNotBlank(cellValue)) {
                    list.add(cellValue);
                }
            }
        }
        return list;
    }

    public static String getCellValue(Row row, Integer i) {
        if (null == row) {
            return null;
        }
        Cell cell = row.getCell(i);
        if (null == cell) {
            return null;
        }
        CellType cellType = cell.getCellTypeEnum();
        String value = null;
        if ("NUMERIC".equalsIgnoreCase(cell.getCellTypeEnum().toString())) {
            DecimalFormat df = new DecimalFormat("0");
            value = df.format(cell.getNumericCellValue());
        } else if ("STRING".equalsIgnoreCase(cell.getCellTypeEnum().toString())) {
            value = cell.getStringCellValue();
        } else if ("FORMULA".equalsIgnoreCase(cell.getCellTypeEnum().toString())) {
            DecimalFormat df = new DecimalFormat("0");
            value = df.format(cell.getNumericCellValue());
        }
        return value;
    }
}
