package com.youngking.lenmoncore.common.export.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by zhangyibo on 2020-08-19 10:29
 */

@ConfigurationProperties(prefix = "export")
@PropertySource(value = {"classpath:invoiceExport.properties"}, encoding = "utf-8")
@Component
@Data
public class InvoiceExportProperties {

    private List<String> invoiceList;

    private HashMap<String, String> invoice;

    private List<String> worderList;

    private HashMap<String, String> worder;
}
