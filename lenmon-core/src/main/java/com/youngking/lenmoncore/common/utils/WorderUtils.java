package com.youngking.lenmoncore.common.utils;

/**
 * Created by zhangyibo on 2020-12-22 13:07
 */

public class WorderUtils {

    /**
     * RowId为ZLW+1位类型编号+11位工单ID(或激励ID)
     * @param worderId
     * @param type 0: 网点工单结算 1：网点增项结算  2：激励结算 3:网点增项收款 4:网点增项收入 5:网点增项暂估成本
     * @return
     */
    public static String rowId(Integer worderId, Integer type){
        return rowId("ZLW", worderId, type);
    }

    /**
     * RowId为ZLW+1位类型编号+11位工单ID(或激励ID)
     * @param prefix 前缀
     * @param worderId
     * @param type 0: 网点工单结算 1：网点增项结算  2：激励结算 3:网点增项收款 4:网点增项收入 5:网点增项暂估成本
     * @return
     */
    public static String rowId(String prefix, Integer worderId, Integer type){
        //11位工单ID
        String worderIdStr = worderId.toString();
        if(worderIdStr.length() > 11){
            worderIdStr = worderIdStr.substring(worderIdStr.length()-3);
        }else{
            while(worderIdStr.length()<11){
                worderIdStr = "0"+worderIdStr;
            }
        }
        //RowId为ZLW+1位类型编号+11位工单ID
        String rowId = prefix+type+worderIdStr;
//        log.info("商户通接口的对账单号：{}", rowId);
        return rowId;
    }

}
