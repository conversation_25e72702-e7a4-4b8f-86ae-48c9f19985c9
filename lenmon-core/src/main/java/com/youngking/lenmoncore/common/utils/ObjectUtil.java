package com.youngking.lenmoncore.common.utils;

import com.alibaba.fastjson.JSON;

import java.util.Map;

public class ObjectUtil {

    public static Object map2Object(Map<String, Object> map, Class<?> clazz) {
        if (map == null) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(map), clazz);
    }

    // public static Map<?, ?> object2Map(Object object) {
    //     if (object == null) {
    //         return null;
    //     }
    //     return JSON.parseObject(JSON.toJSONString(object), Map.class);
    // }
}
