/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.lenmoncore.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.jni.Local;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期处理
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
public class DateUtils {

    private static final java.time.format.DateTimeFormatter FORMATTER = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public final static String DATE_TIME_PATTERN_SLASH = "MM/dd/yyyy";

    public static LocalDateTime toLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDateTime toLocalDateTime(String date) {
        return LocalDateTime.parse(date, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static LocalDateTime toLocalDateTime(String date, java.time.format.DateTimeFormatter formatter) {
        return LocalDateTime.parse(date, formatter);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_TIME_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 字符串转换成日期
     *
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：DateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (StringUtils.isBlank(strDate)) {
            return null;
        }

        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 字符串转为时间戳
     *
     * @param strDate
     * @return
     */
    public static Long StringToStamp(String strDate) {
        Long res = null;
        SimpleDateFormat df = new SimpleDateFormat(DATE_TIME_PATTERN);
        try {
            Date parse = df.parse(strDate);
            res = parse.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 时间戳转为时间字符串
     *
     * @param timeStamp
     * @return
     */
    public static String timeStampToString(Long timeStamp) {
        SimpleDateFormat df = new SimpleDateFormat(DATE_TIME_PATTERN);
        String format = df.format(timeStamp);
        return format;
    }

    /**
     * 时间戳转为cron表达式
     *
     * @param timeStamp
     * @return
     */
    public static String timeStampToCron(Long timeStamp) {
        String cron = null;
        SimpleDateFormat df = new SimpleDateFormat(DATE_TIME_PATTERN);
        String format = df.format(timeStamp);
        System.out.println(format);
        Date parse = null;
        try {
            parse = df.parse(format);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parse);
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);
            cron = second + " " + minute + " " + hour + " " + day + " " + month + " ? ";
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return cron;
    }

    public static Date stringToDateSlash(String strDate) {
        if (StringUtils.isBlank(strDate)) {
            return null;
        }

        DateTimeFormatter fmt = DateTimeFormat.forPattern(DATE_TIME_PATTERN_SLASH);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 根据周数，获取开始日期、结束日期
     *
     * @param week 周期  0本周，-1上周，-2上上周，1下周，2下下周
     * @return 返回date[0]开始日期、date[1]结束日期
     */
    public static Date[] getWeekStartAndEnd(int week) {
        DateTime dateTime = new DateTime();
        LocalDate date = new LocalDate(dateTime.plusWeeks(week));

        date = date.dayOfWeek().withMinimumValue();
        Date beginDate = date.toDate();
        Date endDate = date.plusDays(6).toDate();
        return new Date[]{beginDate, endDate};
    }

    /**
     * 对日期的【秒】进行加/减
     *
     * @param date    日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 对日期的【分钟】进行加/减
     *
     * @param date    日期
     * @param minutes 分钟数，负数为减
     * @return 加/减几分钟后的日期
     */
    public static Date addDateMinutes(Date date, int minutes) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMinutes(minutes).toDate();
    }

    /**
     * 对日期的【小时】进行加/减
     *
     * @param date  日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static Date addDateHours(Date date, int hours) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusHours(hours).toDate();
    }

    /**
     * 对日期的【天】进行加/减
     *
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDays(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusDays(days).toDate();
    }

    /**
     * 对日期的【周】进行加/减
     *
     * @param date  日期
     * @param weeks 周数，负数为减
     * @return 加/减几周后的日期
     */
    public static Date addDateWeeks(Date date, int weeks) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusWeeks(weeks).toDate();
    }

    /**
     * 对日期的【月】进行加/减
     *
     * @param date   日期
     * @param months 月数，负数为减
     * @return 加/减几月后的日期
     */
    public static Date addDateMonths(Date date, int months) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMonths(months).toDate();
    }

    /**
     * 对日期的【年】进行加/减
     *
     * @param date  日期
     * @param years 年数，负数为减
     * @return 加/减几年后的日期
     */
    public static Date addDateYears(Date date, int years) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusYears(years).toDate();
    }

    /**
     * 获取当前时间戳
     *
     * @return timestamp
     */
    public static Timestamp getSysTimeStamp() {

        return new Timestamp(System.currentTimeMillis());
    }

    /**
     * 获取当前时间
     *
     * @return 时间格式(yyyy - MM - dd HH : mm : ss)
     */
    public static String getCurrentTime() {
        SimpleDateFormat df = new SimpleDateFormat(DATE_TIME_PATTERN);
        return df.format(new Date());
    }

    public static Long getTimeDifferenceWithCurrent(String time) {
        Date date = stringToDate(time.substring(0, 19), DATE_TIME_PATTERN);
        Long resultSeconds = null;
        long time1 = date.getTime() / 1000;
        long time2 = new Date().getTime() / 1000;
        if (time1 > time2) {
            resultSeconds = time1 - time2;
        }
        return resultSeconds;
    }

    public static Long getTimeDifferenceWithCurrent(String time, Long delaySeconds) {
        Long seconds = getTimeDifferenceWithCurrent(time);
        if (seconds == null) {
            seconds = 0L;
        } else {
            seconds += delaySeconds;
        }
        return seconds > 0 ? seconds : null;
    }

    /**
     * 获取之前之后的时间
     *
     * @param days      天数
     * @param format    格式
     * @param LocalTime
     * @return
     */
    public static String getCurrentNeedTime(long days, LocalTime LocalTime, String format) {
        java.time.format.DateTimeFormatter dateTimeFormatter = java.time.format.DateTimeFormatter.ofPattern(format);
        LocalDateTime time = LocalDateTime.of(java.time.LocalDate.now(), LocalTime);
        time = time.plusDays(days);
        return dateTimeFormatter.format(time);
    }

    public static LocalDateTime parseLocalDateTime(String time2, String format) {
        if (com.youngking.lenmoncore.common.utils.StringUtils.isNotBlank(time2)) {
            java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern(format);

            try {
                return LocalDateTime.parse(time2, formatter);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    public static String parseLocalDateTimeSting(LocalDateTime localDateTime, String format) {
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern(format);
        return localDateTime.format(formatter);
    }

    public static String parseLocalDateTimeSting(LocalDateTime localDateTime) {
        return parseLocalDateTimeSting(localDateTime, DATE_TIME_PATTERN);
    }

    public static String parseTimestamp(String timestampInMillis) {
        if (StringUtils.isBlank(timestampInMillis)) {
            return timestampInMillis;
        }
        // 直接使用ZonedDateTime处理时区和格式化，一步到位
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(timestampInMillis)), ZoneId.systemDefault());
        return zonedDateTime.format(FORMATTER);
    }
}
