package com.youngking.lenmoncore.common.imail.sender;

import com.youngking.lenmoncore.common.imail.entity.MailEntity;
import lombok.Data;
import org.springframework.mail.javamail.JavaMailSenderImpl;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> at 2020:05:07 11:11
 */
@Data
public class IMailSender extends JavaMailSenderImpl {

    private String from;

    public void setSender(MailEntity mailEntity) {
        this.setHost(mailEntity.getHost());
        this.setPassword(mailEntity.getPassword());
        this.setProtocol(mailEntity.getProtocol());
        this.setUsername(mailEntity.getUsername());
        this.setDefaultEncoding(mailEntity.getEncoding());
        this.from = mailEntity.getUsername();
    }



}
