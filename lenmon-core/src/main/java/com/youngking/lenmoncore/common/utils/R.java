/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.lenmoncore.common.utils;

import com.youngking.lenmoncore.common.constant.WarningConstant;
import org.apache.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 返回数据
 *
 * <AUTHOR>
 */
public class R extends HashMap<String, Object> {
	private static final long serialVersionUID = 1L;

	public R() {
		put("code", 0);
		put("msg", "success");
	}

	public static R error() {
		return error(HttpStatus.SC_INTERNAL_SERVER_ERROR, "未知异常，请联系管理员");
	}

	public static R error(String msg) {
		return error(HttpStatus.SC_INTERNAL_SERVER_ERROR, msg);
	}

	public static R error(int code, String msg) {
		R r = new R();
		r.put("code", code);
		r.put("msg", msg);
		return r;
	}

	public static R ok(String msg) {
		R r = new R();
		r.put("msg", msg);
		return r;
	}

	public static R ok(Map<String, Object> map) {
		R r = new R();
		r.putAll(map);
		return r;
	}

	public static R ok() {
		return new R();
	}

	public static R result() {
		return new R();
	}

	public static R result(Integer num) {
		return new R().put("updateNum", num);
	}

	public static R resultCount(Integer count) {
		return new R().put("count", count);
	}

	public static R resultCount(String count) {
		return new R().put("count", count);
	}

	public R put(String key, Object value) {
		super.put(key, value);
		return this;
	}

	public R putPage(Object value) {
		super.put("page", value);
		return this;
	}

	public R putList(Object value) {
		super.put("list", value);
		return this;
	}

	public R putNum(Object value) {
		super.put("num", value);
		return this;
	}

	public R putWorderTriggerEvent(Object value) {
		super.put(WarningConstant.WORDER_TRIGGER_EVENT, value);
		return this;
	}

	public R putWorderNo(Object value) {
		super.put(WarningConstant.WORDER_NO, value);
		return this;
	}
	public R putWorderExecStatus(Object value) {
		super.put(WarningConstant.WORDER_EXEC_STATUS, value);
		return this;
	}

	public static R Result(String code, String msg) {
		R r = new R();
		r.put("code", code);
		r.put("msg", msg);
		return r;
	}

	public static R Result(Integer code, String msg) {
		R r = new R();
		r.put("code", code);
		r.put("msg", msg);
		return r;
	}

	public boolean isOk() {
		return Objects.equals(this.get("code"), 0);
	}
}
